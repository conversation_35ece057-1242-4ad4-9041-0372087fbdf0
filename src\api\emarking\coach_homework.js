import ajax from '@/api/ajax'

export function apiGetCoachHomeworkList(requestParams) {
  return ajax.get({
    url: 'mark/coachHomework/list',
    params: {
      semesterId: requestParams.semesterId, // Number*
      term: requestParams.termId, // Number*
      classId: requestParams.classId, // String
      bookId: requestParams.bookId, // Number
      pageNum: requestParams.currentPage || 1, // Number
      pageSize: requestParams.pageSize || 10, // Number
      uploadMode: requestParams.uploadMode,
      key: requestParams.keyword,
    },
    requestName: '获取当前用户相关教辅作业列表',
  })
}

export function apiGetCoachHomeworkInfo(examId) {
  return ajax.get({
    url: 'mark/coachHomework/examInfos',
    params: {
      examId,
    },
    requestName: '获取教辅作业信息',
  })
}

export function apiGetCoachHomeworkScanTemplate(examSubjectId) {
  return ajax.get({
    url: 'mark/coachHomework/getScanTemplate',
    params: {
      examSubjectId,
    },
    requestName: '获取教辅作业扫描模板',
  })
}

export function apiGetReferenceScannerTasks(requestParams) {
  return ajax.get({
    url: 'mark/coachHomework/list/coachBookScanner',
    params: {
      semesterId: requestParams.semesterId, //*Number
      term: requestParams.term, // *Number
      gradeId: requestParams.gradeId, // Number
      bookId: requestParams.bookId, // Number
      uploadMode: requestParams.uploadMode, // Number
      key: requestParams.key, // String
      pageNum: requestParams.currentPage || 1, // Number
      pageSize: requestParams.pageSize || 10, // Number
    },
    requestName: '教辅扫描员查询教辅作业列表',
  })
}
