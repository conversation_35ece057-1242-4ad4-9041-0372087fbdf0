import {
  apiGetExamById,
  apiGetExamSubjectInfoById,
  apiGetExamRehearsalSubjects,
  apiGetExamSchoolSettingConfigs,
} from '@/api/emarking'

import { traverse } from '@/utils/recursion'

import Role from '@/enum/user/role'
import ExamScopeEnum from '@/enum/emarking/exam_scope'
import SchoolTypeEnum from '@/enum/school/school_type'

export default {
  state: {
    exam: null,
    currentExamSubjectId: '', // 全部科目设置为0
    examSchoolCodes: [],
    examSchoolSettings: [],
    copyPaperStructureInformations: [],
  },
  getters: {
    currentUserId(state, getters, rootState, rootGetters) {
      return rootGetters['user/info'].userId
    },

    exam(state) {
      return state.exam
    },

    examSchoolCodes(state) {
      return state.examSchoolCodes
    },

    isMultipleSchool(state) {
      return state.exam && state.exam.examScope.id > ExamScopeEnum.Single.id
    },

    eduSchools(state) {
      let list = []
      traverse(state.exam.schoolTree, school => {
        if (school && school.schoolTypeId === SchoolTypeEnum.Bureau.id) {
          list.push({
            schoolId: school.schoolId,
            schoolName: school.schoolName,
            levelName: school.levelName,
            schoolTypeId: school.schoolTypeId,
          })
        }
      })
      return list
    },

    allSchools(state, getters) {
      return [...getters['eduSchools'], ...state.exam.schools]
    },

    isExamCreator(state, getters) {
      if (!state.exam) {
        return false
      }

      return getters.currentUserId === state.exam.creator.userId
    },

    copyPaperStructureInformations(state) {
      return state.copyPaperStructureInformations
    },

    isExamAdministrator(state, getters, rootState, rootGetters) {
      if (!state.exam) {
        return false
      }

      // 考试创建者或考试管理员
      return (
        state.exam.creator.userId === getters.currentUserId || // 创建者
        state.exam.administrators.some(x => x.userId === getters.currentUserId) || // 在考试管理员名单上
        (!getters.isMultipleSchool && // 单校考试 - 1. 用户学校为考试学校; 2. 用户是该校的学校管理员
          state.exam.schools[0].schoolId === rootGetters['user/info'].schoolId &&
          rootGetters['user/info'].roles.some(role => role === Role.SchoolAdministrator.id))
      )
    },

    // 可管理科目
    adminExamSubjects(state, getters) {
      if (!state.exam) {
        return []
      }

      let subjects = []

      // 考试创建者或考试管理员可管理所有科目
      if (getters.isExamAdministrator) {
        subjects = state.exam.subjects
      } else {
        subjects = state.exam.subjects.filter(s => s.administrators.some(x => x.userId === getters.currentUserId))
      }

      return subjects.map(s => ({
        examSubjectId: s.examSubjectId,
        subjectId: s.subjectId,
        subjectName: s.subjectName,
      }))
    },

    // 多校考试中学校管理员可见本校考生和评卷教师页面
    adminSchool(state, getters, rootState, rootGetters) {
      if (!getters.isMultipleSchool) {
        return null
      }

      let userInfo = rootGetters['user/info']
      if (!userInfo.roles.some(role => role === Role.SchoolAdministrator.id)) {
        return
      }

      return state.exam.schools.find(s => s.schoolId === userInfo.schoolId)
    },

    currentExamSubjectId(state) {
      return state.currentExamSubjectId
    },

    currentExamSubjectType(state) {
      if (!state.exam) {
        return undefined
      }

      if (!state.currentExamSubjectId) {
        return 0
      } else {
        let subject = state.exam.subjects.find(subject => subject.examSubjectId === state.currentExamSubjectId)
        return subject ? subject.subjectType : undefined
      }
    },

    currentExamSubjectName(state) {
      if (!state.exam) {
        return ''
      }

      if (state.currentExamSubjectId === 0) {
        return '全部科目'
      } else {
        let subject = state.exam.subjects.find(s => s.examSubjectId === state.currentExamSubjectId)
        return (subject && subject.subjectName + '科') || ''
      }
    },

    currentExamSubject(state) {
      if (!state.exam) {
        return null
      }

      return state.exam.subjects.find(s => s.examSubjectId === state.currentExamSubjectId)
    },

    examSchoolSettings(state) {
      return state.examSchoolSettings || []
    },
  },
  mutations: {
    updateExam(state, exam) {
      state.exam = exam
    },
    changeCurrentExamSubjectId(state, examSubjectId) {
      if (examSubjectId === 0) {
        state.currentExamSubjectId = 0
      } else if (state.exam && state.exam.subjects.some(s => s.examSubjectId === examSubjectId)) {
        state.currentExamSubjectId = examSubjectId
      }
    },
    updateExamSchoolCodes(state, examSchoolCodes) {
      state.examSchoolCodes = examSchoolCodes
    },

    updateExamSubject(state, s) {
      let idx = state.exam.subjects.findIndex(x => x.examSubjectId == s.examSubjectId)
      if (idx >= 0) {
        state.exam.subjects.splice(idx, 1, s)
      }
    },
    changeExamSchoolSettings(state, settings) {
      if (settings && settings.length) {
        settings.sort((a, b) => Number(a.examSchoolCode || 0) - Number(b.examSchoolCode || 0))
        state.examSchoolSettings = settings
      } else {
        state.examSchoolSettings = []
      }
    },
    updateCopyPaperStructureInformations(state, information) {
      const TargetPaperStructureInformation = state.copyPaperStructureInformations.find(
        info => info.examId === information.examId
      )
      if (TargetPaperStructureInformation) {
        TargetPaperStructureInformation.institutionId = information.institutionId
        TargetPaperStructureInformation.sourceExamId = information.sourceExamId
        TargetPaperStructureInformation.semesterId = information.semesterId
        TargetPaperStructureInformation.termId = information.termId
        TargetPaperStructureInformation.semesterTermId = information.semesterTermId
      } else {
        state.copyPaperStructureInformations.push(information)
      }
    },
    setExamEnableGenRoomSeat(state, value = false) {
      if (state.exam) {
        state.exam.enableGenRoomSeat = value
      }
    },
    setExamEnableSetRoomSeat(state, value) {
      if (state.exam) {
        state.exam.enableSetRoomSeat = value
      }
    },
  },
  actions: {
    setAdminExamById(context, examId) {
      return apiGetExamById({ examId }).then(async exam => {
        const ExamRehearsalSubjects = (await apiGetExamRehearsalSubjects(examId)) || []
        exam.subjects.forEach(subject => (subject.isRehearsal = ExamRehearsalSubjects.includes(subject.examSubjectId)))

        context.commit('updateExam', exam)
        if (
          context.state.currentExamSubjectId !== 0 &&
          exam.subjects.every(subject => subject.examSubjectId !== context.state.currentExamSubjectId)
        ) {
          context.state.currentExamSubjectId = ''
        }
      })
    },
    updateExam(context) {
      return context.dispatch('setAdminExamById', context.state.exam.examId)
    },
    updateExamSubject(context, examSubjectId) {
      return apiGetExamSubjectInfoById(examSubjectId, context.state.exam.grade.id).then(s => {
        context.commit('updateExamSubject', s)
      })
    },
    updateExamSchoolSetting(context, examId) {
      if (examId) {
        return apiGetExamSchoolSettingConfigs(examId)
          .then(response => context.commit('changeExamSchoolSettings', response || []))
          .catch(() => context.commit('changeExamSchoolSettings', []))
      } else {
        context.commit('changeExamSchoolSettings', [])
        return Promise.resolve()
      }
    },
  },
}
