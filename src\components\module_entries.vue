<template>
  <div class="module-entries">
    <div v-for="g in entryCardGroups" :key="g.title" class="quick-entry-card" :class="g.cls">
      <div class="title">{{ g.title }}</div>
      <div class="entry-list">
        <div
          v-for="entry in g.entries"
          :key="entry.name"
          class="entry-item"
          :class="{ placeholder: !entry.icon }"
          @click="handleEntryClick(entry)"
        >
          <template v-if="entry.icon">
            <img :src="entry.icon" class="entry-icon" />
            <span class="entry-name">{{ entry.name }}</span>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import ModuleEnum from '@/enum/user/module'

  import IconWrongQuestionScan from '@/assets/images/home/<USER>/module_icons/错题扫描.png'
  import IconFeedback from '@/assets/images/home/<USER>/module_icons/错题采集.png'
  import IconQLib from '@/assets/images/home/<USER>/module_icons/智能题库.svg'
  import IconInSchoolExam from '@/assets/images/home/<USER>/module_icons/校内检测.svg'
  import IconMonitor from '@/assets/images/home/<USER>/module_icons/评卷监控.svg'
  import IconScan from '@/assets/images/home/<USER>/module_icons/答卷扫描.svg'
  import IconAnswerSheet from '@/assets/images/home/<USER>/module_icons/答题卡.svg'
  import IconClassPractise from '@/assets/images/home/<USER>/module_icons/AI测练.svg'
  import IconCoachBookExercise from '@/assets/images/home/<USER>/module_icons/教辅作业.svg'
  import IconHomework from '@/assets/images/home/<USER>/module_icons/在线作业.svg'
  import IconUnionExam from '@/assets/images/home/<USER>/module_icons/区域联考.svg'
  import IconExamAudio from '@/assets/images/home/<USER>/module_icons/考试语音.svg'
  import IconRemarks from '@/assets/images/home/<USER>/module_icons/教师寄语.svg'
  import IconSchool from '@/assets/images/home/<USER>/module_icons/学校管理.svg'
  import IconReport from '@/assets/images/home/<USER>/module_icons/学情分析.svg'
  import IconTeaching from '@/assets/images/home/<USER>/module_icons/精准教学.svg'
  import IconWrongQuestionDownload from '@/assets/images/home/<USER>/module_icons/错题下载.svg'
  import IconCustomReport from '@/assets/images/home/<USER>/module_icons/定制报告.svg'
  import IconReview from '@/assets/images/home/<USER>/module_icons/评审活动.svg'

  export default {
    computed: {
      schoolId() {
        return this.$store.getters['user/info'].schoolId
      },
      hasModuleFeedback() {
        return this.$store.getters['user/isModuleNormal'](ModuleEnum.QuestionFeedBack.id)
      },
      hasModuleClassPractise() {
        return this.$store.getters['user/isModuleNormal'](ModuleEnum.ClassPractise.id)
      },
      entryCardGroups() {
        let groups = [
          {
            title: '常用功能',
            cls: 'modules',
            rows: [
              [
                {
                  icon: IconWrongQuestionScan,
                  name: '错题扫描',
                  to: {
                    name: 'scan-wrongQuestionSheet',
                  },
                  visible: this.hasModuleFeedback,
                },
                {
                  icon: IconFeedback,
                  name: '错题采集',
                  to: {
                    name: 'emarking-topic-feedback',
                  },
                  visible: this.hasModuleFeedback,
                },
              ],
              [
                {
                  icon: IconQLib,
                  name: '智能题库',
                  to: {
                    name: 'qlib',
                  },
                },
                {
                  icon: IconInSchoolExam,
                  name: '校内检测',
                  to: {
                    name: 'emarking-exam-normal',
                  },
                },
                {
                  icon: IconMonitor,
                  name: '评卷监控',
                  to: {
                    name: 'emarking-exam-monitor',
                  },
                },
                {
                  icon: IconScan,
                  name: '答卷扫描',
                  to: {
                    name: 'emarking-scan-task',
                  },
                },
              ],
              [
                {
                  icon: IconAnswerSheet,
                  name: '答题卡',
                  to: {
                    name: 'answerSheet-list',
                  },
                },
                {
                  icon: IconClassPractise,
                  name: 'AI测练',
                  to: {
                    name: 'class-practise',
                  },
                  visible: this.hasModuleClassPractise,
                },
                {
                  icon: IconCoachBookExercise,
                  name: '数智教辅',
                  to: {
                    name: 'supplementaryExercise',
                  },
                },
                {
                  icon: IconHomework,
                  name: '在线作业',
                  to: {
                    name: 'homework',
                  },
                },
              ],
              [
                {
                  icon: IconUnionExam,
                  name: '区域联考',
                  to: {
                    name: 'emarking-exam-union',
                  },
                },
                {
                  icon: IconExamAudio,
                  name: '考试语音',
                  to: {
                    name: 'emarking-exam-audio',
                  },
                },
                {
                  icon: IconRemarks,
                  name: '教师寄语',
                  to: {
                    name: 'remarks',
                  },
                },
                {
                  icon: IconReview,
                  name: '评审活动',
                  to: {
                    name: 'review',
                  },
                },
                // 学校管理和我的班级路由互斥，不会同时出现
                {
                  icon: IconSchool,
                  name: '学校管理',
                  to: {
                    name: 'school',
                  },
                },
                {
                  icon: IconSchool,
                  name: '我的班级',
                  to: {
                    name: 'teacherClasses',
                  },
                },
              ],
            ],
            entries: [],
          },
          {
            title: '统计下载',
            cls: 'stats',
            rows: [
              [
                {
                  icon: IconReport,
                  name: '学情分析',
                  to: {
                    name: 'report',
                  },
                },
                {
                  icon: IconTeaching,
                  name: '精准教学',
                  to: {
                    name: 'teaching',
                  },
                },
                {
                  icon: IconWrongQuestionDownload,
                  name: '错题下载',
                  to: {
                    name: 'emarking-feedback-wrongquestion-download',
                  },
                },
                {
                  icon: IconCustomReport,
                  name: '定制报告',
                  to: {
                    name: 'staged-report',
                  },
                },
              ],
            ],
            entries: [],
          },
        ]

        const CurrentUserSchoolId = this.$store.getters['user/info'].schoolId
        if (CurrentUserSchoolId === '14vf6a24qvwg') {
          // 小林去演示 同级生九校 不想出现错题相关内容
          groups[0].rows.shift()
          groups[1].rows[0] = groups[1].rows[0].filter(rowItem => !rowItem.name.includes('错题'))
        }

        // 删除不存在的路由，添加占位
        const ItemsPerRow = 4
        groups.forEach(group => {
          group.rows.forEach((row, rowIdx) => {
            let count = 0
            row.forEach(entry => {
              if (this.$router.hasRoute(entry.to.name) && (entry.visible == null || entry.visible)) {
                group.entries.push(entry)
                count++
              }
            })
            if (count > 0) {
              while (count < ItemsPerRow) {
                count++
                group.entries.push({
                  name: `占位${rowIdx + 1}-${count}`,
                })
              }
            }
          })
        })
        return groups
      },
    },
    methods: {
      handleEntryClick(entry) {
        if (entry.to && entry.to.name != this.$route.name) {
          this.$router.push(entry.to)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .module-entries {
    width: 30vw;
    min-width: 370px;
    max-width: 500px;
    line-height: 1.5;
    background-color: white;
  }

  .quick-entry-card {
    padding: 20px;
    text-align: center;

    .title {
      display: inline-block;
      margin-bottom: 20px;
      padding-bottom: 4px;
      font-size: 18px;
      line-height: 1;
    }

    .entry-list {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      margin-top: 10px;
      column-gap: 30px;
      row-gap: 40px;

      .entry-item {
        cursor: pointer;

        &.placeholder {
          cursor: auto;
        }

        .entry-icon {
          width: 36px;
          margin-bottom: 5px;
          transform-origin: center;
          filter: drop-shadow(0px 4px 2px #ccc);
          transition: transform 0.3s ease;
        }

        .entry-name {
          display: block;
          line-height: 1.2;
        }

        &:hover .entry-icon {
          transform: scale(1.4);
        }
      }
    }

    &.modules {
      .title {
        border-bottom: 2px solid #2a96fa;
        color: #2a96fa;
      }
    }

    &.stats {
      .title {
        border-bottom: 2px solid #fc6f6f;
        color: #fc6f6f;
      }
    }

    &:not(:first-child) {
      margin-top: 10px;
    }
  }
</style>
