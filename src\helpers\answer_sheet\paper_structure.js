/**
 * 试卷结构
 */

import { sortQuestionFunction } from '@/helpers/emarking/miscellaneous'
import { splitBlankScores } from '@/helpers/qlib/miscellaneous'

import { randomId } from '@/utils/string'
import { uniq, groupArray, chunkAdjacent } from '@/utils/array'
import { roundNumber } from '@/utils/math'
import BranchTypeEnum from '@/enum/qlib/branch_type'

export default class PaperStructure {
  constructor(structure = {}) {
    this.objectiveQuestions = structure.objectiveQuestions || []
    this.subjectiveQuestions = structure.subjectiveQuestions || []
    this.blocks = structure.blocks || []

    // 替换题块中题目
    this.blocks.forEach(block => {
      let questions = []
      block.questions.forEach(q => {
        let subj = this.subjectiveQuestions.find(x => x.questionCode == q.questionCode && x.branchCode == q.branchCode)
        if (subj) {
          questions.push(subj)
        }
      })
      block.questions = questions
    })

    // 添加原题
    if (structure.paper) {
      ;[...this.objectiveQuestions, ...this.subjectiveQuestions].forEach(q => {
        let originalQuestion = structure.paper.questionList.find(x => x.id == q.originalQuestionId)
        if (!originalQuestion) {
          return
        }
        q.originalQuestion = originalQuestion
        q.originalBranch = originalQuestion.branches.find(x => x.id == q.originalBranchId)
        // 绑定试卷再由试卷结构做答题卡，填空题未记录填空个数，需补充
        if (
          q.originalBranch &&
          q.originalBranch.branchType.id == BranchTypeEnum.FillBlank.id &&
          q.blankScores.length == 0
        ) {
          q.blankScores = splitBlankScores(q.fullScore, q.originalBranch.answer.fillBlank.length)
        }
      })
    }

    this.sort()
  }

  // 题目、题块排序
  sort() {
    this.objectiveQuestions.sort(sortQuestionFunction)
    this.subjectiveQuestions.sort(sortQuestionFunction)
    this.blocks.forEach(block => {
      block.questions.sort(sortQuestionFunction)
    })
    this.blocks.sort((a, b) => sortQuestionFunction(a.questions[0], b.questions[0]))
  }

  // 更新题块名称
  updateBlockName(block) {
    let mergeBranchNames = branches => {
      // 小题号连续的合并为一段
      let chunks = chunkAdjacent(branches, (b1, b2) => b1.branchCode + 1 == b2.branchCode)
      return chunks
        .map(chunk => {
          let firstBranch = chunk[0]
          let firstBranchName =
            firstBranch.branchCode == 0
              ? `${firstBranch.questionCode}`
              : `${firstBranch.questionCode}.${firstBranch.branchCode}`
          let lastBranch = chunk[chunk.length - 1]
          let lastBranchName =
            lastBranch.branchCode == 0
              ? `${lastBranch.questionCode}`
              : `${lastBranch.questionCode}.${lastBranch.branchCode}`
          return chunk.length == 1 ? firstBranchName : `${firstBranchName}-${lastBranchName}`
        })
        .join('，')
    }
    let mergeQuestionNames = questions => {
      // 题号连续、各题全选所有小题的，合并为一段
      let chunks = chunkAdjacent(questions, (q1, q2) => {
        return q1.containAllBranch && q2.containAllBranch && q1.questionCode + 1 == q2.questionCode
      })
      return chunks
        .map(chunk => {
          let firstQuestion = chunk[0]
          let lastQuestion = chunk[chunk.length - 1]
          return chunk.length == 1
            ? firstQuestion.branchNames
            : `${firstQuestion.branchNames}-${lastQuestion.branchNames}`
        })
        .join('，')
    }

    block.questions.sort(sortQuestionFunction)
    let questions = groupArray(block.questions, q => q.questionCode).map(g => {
      let branchCount = this.subjectiveQuestions.filter(x => x.questionCode == g.key).length
      let containAllBranch = g.group.length == branchCount
      return {
        questionCode: g.key,
        containAllBranch,
        branchNames: containAllBranch ? `${g.key}` : mergeBranchNames(g.group),
      }
    })
    block.blockName = `题${mergeQuestionNames(questions)}`
  }

  // 所有题目
  getQuestions() {
    return [...this.objectiveQuestions, ...this.subjectiveQuestions]
  }
  // 题号列表
  getQuestionCodes() {
    return uniq(this.getQuestions().map(q => q.questionCode)).sort((a, b) => a - b)
  }
  // 各题小题数
  getQuestionCodeBranchCountMap() {
    let map = new Map()
    groupArray(this.getQuestions(), q => q.questionCode).forEach(g => {
      map.set(g.key, g.group.length)
    })
    return map
  }
  // 最大题号
  getMaxQuestionCode() {
    let questionCodes = this.getQuestionCodes()
    if (questionCodes.length == 0) {
      return 0
    } else {
      return Math.max(...questionCodes)
    }
  }
  // 最大大题号
  getMaxTopicCode() {
    let questions = this.getQuestions()
    if (questions.length == 0) {
      return 0
    } else {
      return Math.max(...questions.map(q => q.topicCode))
    }
  }
  // 已存在的大题名
  getTopicCodeNames() {
    let list = []
    let questions = this.getQuestions()
    questions.forEach(q => {
      if (list.every(t => t.topicCode != q.topicCode)) {
        list.push({
          topicCode: q.topicCode,
          topicName: q.topicName,
        })
      }
    })
    list.sort((a, b) => a.topicCode - b.topicCode)
    return list
  }
  // 题目所属题块
  getBlockByQuestionId(questionId) {
    return this.blocks.find(block => block.questions.some(q => q.questionId == questionId))
  }
  // 选做题组对应的多个题块
  getSelectGroupBlocks(selectGroupName) {
    return this.blocks.filter(block => block.selectGroupName == selectGroupName)
  }

  // 添加客观题
  addObjectiveQuestions(objectiveQuestions) {
    this.objectiveQuestions.push(...objectiveQuestions)
    this.sort()
  }
  // 添加主观题并创建题块
  addSubjectiveQuestions(subjectiveQuestions) {
    this.subjectiveQuestions.push(...subjectiveQuestions)
    let block = createBlock(subjectiveQuestions)
    this.blocks.push(block)
    this.sort()
    this.updateBlockName(block)
    return block
  }
  // 删除题目
  deleteQuestions(questionIds) {
    this.objectiveQuestions = this.objectiveQuestions.filter(q => !questionIds.includes(q.questionId))
    this.subjectiveQuestions = this.subjectiveQuestions.filter(q => !questionIds.includes(q.questionId))
    this.blocks.forEach(block => {
      let questionsChanged = false
      for (let qIdx = block.questions.length - 1; qIdx >= 0; qIdx--) {
        if (questionIds.includes(block.questions[qIdx].questionId)) {
          block.questions.splice(qIdx, 1)
          questionsChanged = true
        }
      }
      if (questionsChanged && block.questions.length > 0) {
        this.updateBlockName(block)
      }
    })
    this.blocks = this.blocks.filter(block => block.questions.length > 0)
  }
  // 改题目分数
  changeQuestionScores(objectives, subjectives) {
    objectives.forEach(obj => {
      let q = this.objectiveQuestions.find(x => x.questionId == obj.questionId)
      if (q) {
        q.fullScore = obj.fullScore
      }
    })
    subjectives.forEach(subj => {
      let q = this.subjectiveQuestions.find(x => x.questionId == subj.questionId)
      if (q) {
        q.fullScore = subj.fullScore
        q.blankScores = subj.blankScores.slice()
      }
    })
  }

  // 合并题块
  mergeBlock(blockId1, blockId2) {
    let block1Idx = this.blocks.findIndex(x => x.blockId == blockId1)
    let block2Idx = this.blocks.findIndex(x => x.blockId == blockId2)
    if (block1Idx < 0 || block2Idx < 0) {
      throw '参数错误'
    }
    let block1 = this.blocks[block1Idx]
    let block2 = this.blocks[block2Idx]
    let subjectiveQuestions = [...block1.questions, ...block2.questions]
    let newBlock = createBlock(subjectiveQuestions)
    this.blocks.splice(block1Idx, 1, newBlock)
    this.blocks.splice(block2Idx, 1)
    this.sort()
    this.updateBlockName(newBlock)
  }
  // 拆分题块, splitIntoBranches: 是否拆分到小题
  splitBlock(blockId, splitIntoBranches) {
    let blockIdx = this.blocks.findIndex(x => x.blockId == blockId)
    if (blockIdx < 0) {
      throw '参数错误'
    }
    let block = this.blocks[blockIdx]
    let newBlocks = []
    if (splitIntoBranches) {
      block.questions.forEach(q => {
        newBlocks.push(createBlock([q]))
      })
    } else {
      groupArray(block.questions, q => q.questionCode).forEach(g => {
        newBlocks.push(createBlock(g.group))
      })
    }
    this.blocks.splice(blockIdx, 1, ...newBlocks)
    this.sort()
    newBlocks.forEach(block => {
      this.updateBlockName(block)
    })
  }
  // 设为选做
  setSelect(questionCodes, selectGroupName, selectCount) {
    this.blocks.forEach(block => {
      if (block.questions.every(q => questionCodes.includes(q.questionCode))) {
        block.selectGroupName = selectGroupName
        block.selectCount = selectCount
      }
    })
  }
  // 取消选做
  unsetSelect(selectGroupName) {
    this.blocks.forEach(block => {
      if (block.selectGroupName == selectGroupName) {
        block.selectGroupName = ''
        block.selectCount = 0
      }
    })
  }

  /**
   * 由题库试卷创建
   * @param paper 试卷
   * @param showQuestionContent 是否显示题目内容
   */
  static extractFromPaper(paper, showQuestionContent = false) {
    let { objectiveQuestions, subjectiveQuestions } = paper.extractEMarkingQuestions()
    let blocks = []
    groupArray(subjectiveQuestions, q => q.topicCode).forEach(gt => {
      let topicSubjectives = gt.group
      // 按原题分组
      let groupByOriginalQuestion = groupArray(topicSubjectives, q => q.originalQuestionId).map(gq => gq.group)
      // 显示题目内容时，主观题可能被客观题间隔开，连续的题目合并为一个题块
      if (showQuestionContent) {
        groupByOriginalQuestion.forEach(gq => {
          let objs = objectiveQuestions.filter(q => q.originalQuestionId == gq[0].originalQuestionId)
          let objIds = objs.map(q => q.questionId)
          let objsAndSubjs = [...objs, ...gq].sort(sortQuestionFunction)
          let partitions = []
          let part = null
          objsAndSubjs.forEach(q => {
            if (objIds.includes(q.questionId)) {
              part = null
            } else {
              if (!part) {
                part = []
                partitions.push(part)
              }
              part.push(q)
            }
          })
          partitions.forEach(subjs => {
            blocks.push(createBlock(subjs))
          })
        })
      }
      // 所有主观题均为填空题且每题均无小题，则整个大题一个题块
      else if (
        topicSubjectives.every(subj => subj.blankScores.length > 0) &&
        groupByOriginalQuestion.every(gq => gq.length == 1)
      ) {
        blocks.push(createBlock(topicSubjectives))
      }
      // 否则每道原题一个题块
      else {
        groupByOriginalQuestion.forEach(gq => {
          blocks.push(createBlock(gq))
        })
      }
    })
    let structure = new PaperStructure()
    structure.objectiveQuestions = objectiveQuestions
    structure.subjectiveQuestions = subjectiveQuestions
    structure.blocks = blocks
    structure.blocks.forEach(block => {
      structure.updateBlockName(block)
    })
    return { objectiveQuestions, subjectiveQuestions, blocks, paper }
  }

  export(aiScoreBlocks) {
    return {
      objectives: this.objectiveQuestions.map(exportObjectiveQuestion),
      blocks: this.blocks.map(block => exportBlock(block, aiScoreBlocks)),
    }
  }
}

function createBlock(subjectiveQuestions) {
  return {
    blockId: randomId(),
    blockName: '',
    selectGroupName: '',
    selectCount: 0,
    questions: subjectiveQuestions,
  }
}

function exportQuestion(q) {
  return {
    questionId: q.questionId,
    questionCode: q.questionCode,
    questionName: q.questionName,
    branchCode: q.branchCode,
    branchName: q.branchName,
    topicCode: q.topicCode,
    topicName: q.topicName,
    fullScore: roundNumber(q.fullScore, 2),
    originalQuestionId: q.originalQuestionId,
    originalBranchId: q.originalBranchId,
    isAdditional: q.isAdditional,
  }
}
function exportObjectiveQuestion(q) {
  return {
    ...exportQuestion(q),
    branchTypeId: q.branchTypeId,
    optionCount: q.optionCount,
    standardAnswer: q.standardAnswer,
    answerScoreList: q.answerScoreList,
    autoScoreTypeId: q.autoScoreTypeId,
  }
}
function exportSubjectiveQuestion(q) {
  return {
    ...exportQuestion(q),
    blankScores: q.blankScores.map(s => roundNumber(s, 2)),
  }
}

export function exportBlock(block, aiScoreBlocks) {
  let exportBlock = {
    blockId: block.blockId,
    blockName: block.blockName,
    selectGroupName: block.selectGroupName || null,
    selectCount: block.selectCount || null,
    questions: block.questions.map(exportSubjectiveQuestion),
  }
  if (aiScoreBlocks && aiScoreBlocks.length > 0) {
    let aiScoreBlock = aiScoreBlocks.find(
      x =>
        x.questions.length == block.questions.length &&
        x.questions.every(q1 =>
          block.questions.some(q2 => q2.questionCode == q1.questionCode && q2.branchCode == q1.branchCode)
        )
    )
    if (aiScoreBlock) {
      exportBlock.aiScoreTypeId = aiScoreBlock.aiScoreTypeId
      exportBlock.aiScoreExtraJson = aiScoreBlock.aiScoreExtraJson
    }
  }
  return exportBlock
}
