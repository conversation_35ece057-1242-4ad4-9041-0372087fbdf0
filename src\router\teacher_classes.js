import Role from '@/enum/user/role'

export default {
  name: 'teacherClasses',
  path: 'teacherclasses',
  meta: {
    title: '我的班级',
    menu: '我的班级',
    name: '我的班级',
    roles: user => {
      return (
        user.schoolType === 0 &&
        !user.roles.some(role => [Role.SchoolAdministrator.id, Role.SchoolLeader.id].includes(role)) &&
        (user.roles.some(role => Role.ClassLeader.id === role) ||
          (user.roles.some(role => Role.Teacher.id === role) && user.teachings.length > 0))
      )
    },
  },
  component: () => import('@/views/teacher_classes/index.vue'),
}
