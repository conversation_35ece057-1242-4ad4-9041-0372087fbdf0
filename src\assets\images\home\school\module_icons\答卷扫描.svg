<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 427318975">
<g id="Group 427318833">
<g id="Frame 1000006538">
<g id="Group 427318828">
<g id="Rectangle 346240778" filter="url(#filter0_b_2283_6860)">
<rect x="7" y="2" width="10" height="5" fill="#8EE164"/>
</g>
<g id="Rectangle 346240779" filter="url(#filter1_b_2283_6860)">
<rect x="7" y="17" width="10" height="5" fill="#8EE164"/>
</g>
<g id="Rectangle 346240777" filter="url(#filter2_b_2283_6860)">
<rect x="5.66672" y="5.33347" width="12.6667" height="5.33333" rx="0.666667" fill="#BDDEAD" fill-opacity="0.4"/>
<rect x="5.83338" y="5.50013" width="12.3333" height="5" rx="0.5" stroke="url(#paint0_linear_2283_6860)" stroke-width="0.333333"/>
</g>
<rect id="Rectangle 346240772" x="3" y="7.33311" width="18" height="10" rx="1.33333" fill="url(#paint1_linear_2283_6860)"/>
<circle id="Ellipse 211" cx="16.3333" cy="9.33316" r="0.666667" fill="white"/>
<circle id="Ellipse 212" cx="18.9999" cy="9.33316" r="0.666667" fill="white"/>
<rect id="Rectangle 346240774" x="9" y="18" width="3.33333" height="0.666667" rx="0.333333" fill="white"/>
<rect id="Rectangle 346240780" x="9" y="19.2" width="2.4" height="0.666667" rx="0.333333" fill="white"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_b_2283_6860" x="4.33333" y="-0.666667" width="15.3333" height="10.3333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="1.33333"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2283_6860"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_2283_6860" result="shape"/>
</filter>
<filter id="filter1_b_2283_6860" x="4.33333" y="14.3333" width="15.3333" height="10.3333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="1.33333"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2283_6860"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_2283_6860" result="shape"/>
</filter>
<filter id="filter2_b_2283_6860" x="-18.5333" y="-18.8665" width="61.0667" height="53.7333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="12.1"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2283_6860"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_2283_6860" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2283_6860" x1="7.07982" y1="6.0744" x2="10.2135" y2="13.5169" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.535" stop-color="#8EE164" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint1_linear_2283_6860" x1="-6" y1="6.99998" x2="-2.63406" y2="21.1299" gradientUnits="userSpaceOnUse">
<stop stop-color="#BDDEAD"/>
<stop offset="0.967665" stop-color="#3DB302"/>
</linearGradient>
</defs>
</svg>
