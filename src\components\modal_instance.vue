<template>
  <Modal :model-value="modelValue" title="" :closable="false" :mask-closable="false" :width="width" footer-hide>
    <div class="modal-inner">
      <div class="modal-inner-head">
        <div class="modal-head-icon" :class="[type]">
          <Icon :type="iconType"></Icon>
        </div>
        <div class="modal-head-title">{{ title }}</div>
      </div>
      <div class="modal-inner-body">
        <slot></slot>
      </div>
      <div class="modal-inner-footer">
        <Button v-if="isConfirm || showCancel" type="text" @click="handleCancel">{{ cancelText }}</Button>
        <Button type="primary" @click="handleOk">{{ okText }}</Button>
      </div>
    </div>
  </Modal>
</template>

<script>
  export default {
    props: {
      modelValue: Boolean,
      width: Number,
      type: {
        validator(value) {
          return ['info', 'success', 'warning', 'error', 'confirm'].includes(value)
        },
      },
      title: String,
      cancelText: {
        type: String,
        default: '取消',
      },
      showCancel: Boolean,
      okText: {
        type: String,
        default: '确定',
      },
    },
    emits: ['update:model-value', 'cancel', 'ok'],
    computed: {
      iconType() {
        let iconTypeMap = {
          info: 'ios-information-circle',
          success: 'ios-checkmark-circle',
          warning: 'ios-alert',
          error: 'ios-close-circle',
          confirm: 'ios-help-circle',
        }
        return iconTypeMap[this.type]
      },
      isConfirm() {
        return this.type == 'confirm'
      },
    },
    methods: {
      handleCancel() {
        this.$emit('cancel')
      },
      handleOk() {
        this.$emit('ok')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .modal-inner {
    padding: 6px 16px 8px;
  }

  .modal-inner-head {
    @include flex(row, flex-start, center);
    padding: 0 12px 0 0;

    .modal-head-icon {
      position: relative;
      top: -2px;
      display: inline-block;
      font-size: 28px;
      vertical-align: middle;

      &.info {
        color: $color-primary;
      }

      &.success {
        color: $color-success;
      }

      &.warning {
        color: $color-warning;
      }

      &.error {
        color: $color-error;
      }

      &.confirm {
        color: $color-warning;
      }
    }

    .modal-head-title {
      display: inline-block;
      margin-left: 12px;
      color: #17233d;
      font-size: $font-size-medium-x;
      vertical-align: middle;
    }
  }

  .modal-inner-body {
    position: relative;
    padding-left: 40px;
    color: $color-content;
    font-size: $font-size-medium;
  }

  .modal-inner-footer {
    margin-top: 20px;
    text-align: right;

    & button + button {
      margin-bottom: 0;
      margin-left: 8px;
    }
  }
</style>
