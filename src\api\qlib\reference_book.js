// 定制教辅
import ajax from '@/api/ajax'
import { PaperInfo } from '@/helpers/qlib/paper_info'
import { Question } from '@/helpers/qlib/question'

import { transformCoachBookPaper } from './coach_book'

export function apiGetSchoolReferenceBookByPage(requestParams) {
  return ajax.get({
    url: 'ques/schoolCoach/getBookPage',
    params: {
      subjectId: requestParams.subjectId,
      gradeLevel: requestParams.gradeLevel,
      gradeId: requestParams.gradeId,
      semesterId: requestParams.semesterId,
      term: requestParams.term,
      bookName: requestParams.bookName,
      pressName: requestParams.pressName,
      page: requestParams.currentPage,
      size: requestParams.pageSize,
      onlyOpen: requestParams.onlyOpen || false,
    },
    requestName: '分页获取学校教辅列表',
  })
}

export function apiGetSchoolBespokePaperByPage(requestParams) {
  return ajax
    .get({
      url: 'ques/schoolCoach/getPaperPage',
      params: {
        subjectId: requestParams.subjectId, // [integer]
        paperTypeId: requestParams.paperTypeId, // [integer]
        gradeLevel: requestParams.gradeLevel, // [integer]
        gradeId: requestParams.gradeId, // [integer]
        semesterId: requestParams.semesterId, // [integer]
        term: requestParams.termId, // [integer]
        year: requestParams.year, // [integer]
        keyword: requestParams.keyword, // [string]
        orderBy: requestParams.orderBy, // [string]: time - 按时间排序 | browseCount - 按浏览次数排序
        page: requestParams.currentPage, // [integer]
        size: requestParams.pageSize, // [integer]
      },
      requestName: '分页获取学校定制试卷列表',
    })
    .then(data => {
      return {
        total: data.total || 0,
        papers: (data.records || []).map(p => PaperInfo.import(p)),
      }
    })
}

export function apiGetSchoolBespokeQuestionPageByKnowledge(requestParams) {
  return ajax
    .get({
      url: 'ques/schoolCoach/getQuesPageByKnows',
      params: {
        gradeLevel: requestParams.gradeLevel, // [integer]
        subjectId: requestParams.subjectId, //  [integer]
        year: requestParams.year, // [integer]
        knowledge: requestParams.knowledge, // [integer]
        difficulty: requestParams.difficulty, // [integer]
        quesType: requestParams.questionType, // [integer]
        page: requestParams.currentPage || 1, // [integer]
        size: requestParams.pageSize || 10, // [integer]
      },
      requestName: '分页获取学校定制试题列表（按知识点）',
    })
    .then(response => ({
      total: response.total || 0,
      questionList: (response.records || []).map(record => {
        return Question.import(record)
      }),
    }))
}

export function apiGetSchoolBespokeQuestionPageByChapter(requestParams) {
  return ajax
    .get({
      url: 'ques/schoolCoach/getQuesPageByChapter',
      params: {
        gradeLevel: requestParams.gradeLevel, // [integer]
        subjectId: requestParams.subjectId, // [integer]
        knowledge: requestParams.knowledge, // [integer]
        bookId: requestParams.bookId, // [integer]
        chapter: requestParams.chapter, // [integer]
        year: requestParams.year, // [integer]
        difficulty: requestParams.difficulty, // [integer]
        quesType: requestParams.questionType, // [integer]
        page: requestParams.currentPage || 1, // [integer]
        size: requestParams.pageSize || 10, // [integer]
      },
      requestName: '分页获取学校定制试题列表（按章节）',
    })
    .then(response => ({
      total: response.total || 0,
      questionList: (response.records || []).map(record => {
        return Question.import(record)
      }),
    }))
}

export function apiGetPageReferenceBookShareSchoolList(requestParams) {
  return ajax.get({
    url: 'ques/schoolCoach/getSchoolPage',
    params: {
      coachBookId: requestParams.bookId,
      keyword: requestParams.keyword,
      page: requestParams.currentPage,
      size: requestParams.pageSize,
    },
    requestName: '分页获取定制教辅被分享学校列表',
  })
}

export function apiSetReferenceBookCustomedSchools(requestParams) {
  return ajax.post({
    url: 'ques/schoolCoach/addBookToSchools',
    params: {
      coachBookId: requestParams.bookId, // [integer]
      semesterId: requestParams.semesterId, // [integer]
    },
    data: requestParams.schoolIds, // [Array:String]
    requestName: '教辅设置定制学校',
  })
}

export function apiEditReferenceBookCustemdSchoolInfomation(requestParams) {
  return ajax.put({
    url: 'ques/schoolCoach/editSchoolBook',
    data: {
      coachBookId: requestParams.bookId,
      coachBookName: requestParams.bookName,
      createBy: requestParams.createBy,
      createTime: requestParams.createTime,
      gradeId: requestParams.gradeId,
      gradeLevel: requestParams.gradeLevel,
      id: requestParams.id,
      schoolId: requestParams.schoolId,
      semesterId: requestParams.semesterId,
      subjectId: requestParams.subjectId,
      term: requestParams.term,
      updateBy: requestParams.updateBy,
      updateTime: requestParams.updateTime,
    },
    requestName: '编辑教辅定制学校信息',
  })
}

export function apiRemoveReferenceBookCustomedSchools(requestParams) {
  return ajax.delete({
    url: 'ques/schoolCoach/delSchoolByBook',
    params: {
      coachBookId: requestParams.bookId, // [integer]
    },
    data: requestParams.schoolIds, // [Array:String]
    requestName: '教辅批量删除定制学校',
  })
}

export function apiSetSchoolCoachbookWrongQuestionMark(requestParams) {
  return ajax.post({
    url: 'ques/schoolCoach/setWrongQuesMark',
    data: requestParams, // Array [{id: Number, wrongQuesPaperMark: Boolean, wrongQuesQuickMark: Boolean }]
    requestName: '设置教辅错题标记方式（学校）',
  })
}

export function apiGetSchoolCoachBookPapers(coachBookId) {
  return ajax
    .get({
      url: 'ques/schoolCoach/listMySchoolBookPapers',
      params: {
        coachBookId,
      },
      requestName: '获取学校定制教辅试卷',
    })
    .then(papers => papers.map(transformCoachBookPaper))
}
