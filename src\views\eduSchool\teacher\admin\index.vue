<template>
  <div class="container-school-school-administrator">
    <div class="header">
      <div class="title">管理员</div>
      <div v-if="canEditSchoolData" class="actions">
        <Button type="primary" @click="handleCellClick">添加</Button>
      </div>
    </div>
    <div class="body">
      <div v-if="schoolAdministrators.length > 0" class="tags">
        <Tag
          v-for="t in schoolAdministrators"
          :key="t.userId"
          size="large"
          :name="t.userId"
          :closable="canEditSchoolData"
          @on-close="removeTeacher($event, { id: t.userId, name: t.realName })"
        >
          {{ t.realName }}</Tag
        >
      </div>
      <div v-if="schoolAdministrators.length === 0 && loading === false" class="empty">尚未添加管理员</div>
    </div>
    <RoleEditCard
      :show="showEditCard"
      :teachers="schoolAdministrators"
      title="管理员"
      @cancel="handleEditCancel"
      @ok="handleEditOK"
    ></RoleEditCard>
  </div>
</template>

<script>
  import RoleEditCard from '../components/role_edit_card.vue'

  import { apiGetSchoolAdministrators, apiSetSchoolAdministrators } from '@/api/user'

  export default {
    components: {
      RoleEditCard,
    },
    data() {
      return {
        schoolAdministrators: [],
        showEditCard: false,
        loading: false,
      }
    },
    computed: {
      canEditSchoolData() {
        return this.$store.getters['user/canEditSchoolData']
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      fetchData() {
        this.loading = true
        apiGetSchoolAdministrators().then(schoolAdministrators => {
          this.schoolAdministrators = schoolAdministrators
          this.loading = false
        })
      },
      handleCellClick() {
        this.showEditCard = true
      },
      handleEditCancel() {
        this.showEditCard = false
      },
      handleEditOK(teachers) {
        apiSetSchoolAdministrators(teachers)
          .then(() => {
            this.$Message.success({
              content: '已修改',
            })
          })
          .finally(() => {
            this.fetchData()
            this.showEditCard = false
          })
      },
      removeTeacher(e, user) {
        this.$Modal.confirm({
          title: '提醒',
          content: '确定要把 ' + user.name + ' 从管理员中移除？',
          onOk: () => {
            let index = this.schoolAdministrators.findIndex(x => x.userId === user.id)
            if (index < 0) {
              return
            }

            this.schoolAdministrators.splice(index, 1)
            apiSetSchoolAdministrators(this.schoolAdministrators)
              .then(() => {
                this.$Message.success({
                  content: '已修改',
                })
                this.showEditCard = false
              })
              .finally(() => {
                this.fetchData()
              })
          },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-school-school-administrator {
    padding: 20px;
    background-color: white;
  }

  .header {
    @include flex(row, space-between, center);
    margin-bottom: 20px;

    .title {
      font-size: 30px;
    }
  }

  .empty {
    line-height: 100px;
    text-align: center;
  }
</style>
