import ajax from '@/api/ajax'
import { transformScanUnit } from './scanUnit'
import { transformStudentScanUnits } from './scanStu'

export function apiGetAllBatchNames({ examSubjectId, scanStationId }) {
  return ajax.get({
    url: 'scan/multiFeeding/allBatchNames',
    params: {
      examSubjectId,
      scanStationId: scanStationId || undefined,
    },
    requestName: '获取所有批次名称',
  })
}

export function apiGetMultiFeedingStat({ examSubjectId, scanStationId }) {
  return ajax.get({
    url: 'scan/multiFeeding/stat',
    params: {
      examSubjectId,
      scanStationId: scanStationId || undefined,
    },
    requestName: '获取统计',
  })
}

export function apiGetBatchPage(params) {
  return ajax.get({
    url: 'scan/multiFeeding/batchPage',
    params: {
      examSubjectId: params.examSubjectId,
      page: params.page || 1,
      size: params.size || 20,
      scanStationId: params.scanStationId || undefined,
      statusList: params.status || undefined,
      existsCheckResult: params.existsCheckResult,
      orderBy: params.orderBy,
    },
    requestName: '获取批次',
  })
}

export function apiGetCheckResultPage(params) {
  return ajax.get({
    url: 'scan/multiFeeding/checkResultPage',
    params: {
      examSubjectId: params.examSubjectId,
      page: params.page || 1,
      size: params.size || 20,
      scanStationId: params.scanStationId || undefined,
      batchId: params.batchId || undefined,
      reScanCheckResult: params.reScanCheckResult,
      reScanned: params.reScanned,
      checkResult: params.checkResult,
    },
    requestName: '获取检查结果',
  })
}

export function apiGetCheckResultDetail({
  id,
  examSubjectId,
  scanStationId,
  batchId,
  checkResult,
  reScanned,
  reScanCheckResult,
}) {
  return ajax
    .get({
      url: 'scan/multiFeeding/oneCheckResultDetail',
      params: { id, examSubjectId, scanStationId, batchId, checkResult, reScanned, reScanCheckResult },
      requestName: '获取检查结果详情',
    })
    .then(data => {
      transformCheckResultDetail(data.record)
      return data
    })
}

export function apiGetNextCheckResultDetail({
  id,
  examSubjectId,
  scanStationId,
  batchId,
  checkResult,
  reScanned,
  reScanCheckResult,
}) {
  return ajax
    .get({
      url: 'scan/multiFeeding/nextCheckResultDetail',
      params: { id, examSubjectId, scanStationId, batchId, checkResult, reScanned, reScanCheckResult },
      requestName: '获取下一份检查结果详情',
    })
    .then(data => {
      transformCheckResultDetail(data.record)
      return data
    })
}

export function apiGetPreviousCheckResultDetail({
  id,
  examSubjectId,
  scanStationId,
  batchId,
  checkResult,
  reScanned,
  reScanCheckResult,
}) {
  return ajax
    .get({
      url: 'scan/multiFeeding/previousCheckResultDetail',
      params: { id, examSubjectId, scanStationId, batchId, checkResult, reScanned, reScanCheckResult },
      requestName: '获取上一份检查结果详情',
    })
    .then(data => {
      transformCheckResultDetail(data.record)
      return data
    })
}

function transformCheckResultDetail(checkResultDetail) {
  if (!checkResultDetail) {
    return
  }
  transformScanUnit(checkResultDetail.scanUnit)
  transformScanUnit(checkResultDetail.nextScanUnit)
  transformStudentScanUnits(checkResultDetail.student)
  transformStudentScanUnits(checkResultDetail.nextStudent)
  checkResultDetail.skipStudents?.forEach(transformStudentScanUnits)
}

export function apiChangeCheckResult({ examSubjectId, id, checkResult }) {
  return ajax.put({
    url: 'scan/multiFeeding/checkResult',
    params: { examSubjectId, id, checkResult },
    requestName: '修改人工确认状态',
  })
}

export function apiChangeReScanCheckResult({ examSubjectId, id, reScanCheckResult }) {
  return ajax.put({
    url: 'scan/multiFeeding/reScanCheckResult',
    params: { examSubjectId, id, reScanCheckResult },
    requestName: '修改重扫复核状态',
  })
}

export function apiExportCheckResult(params) {
  return ajax.download({
    url: 'scan/multiFeeding/exportCheckResult',
    params,
    requestName: '导出检查结果',
  })
}

export function apiRetryCheckBatch({ examSubjectId, batchId }) {
  return ajax.put({
    url: 'scan/multiFeeding/retryCheckBatch',
    params: { examSubjectId, batchId },
    requestName: '重试批次检查',
  })
}
