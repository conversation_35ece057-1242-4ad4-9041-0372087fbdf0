import Enum from '@/enum/enum'
import MultiFeedingManualCheckResultEnum from './multi_feeding_manual_check_result'
import MultiFeedingReScanCheckResultEnum from './multi_feeding_rescan_check_result'

const StatusEnum = new Enum({
  Init: {
    id: 'init',
    name: '待人工确认',
    backgroundColor: '#fffbe6',
    color: '#049587',
  },
  CheckNormal: {
    id: 'check-normal',
    name: '已确认正常',
    backgroundColor: '#f6ffed',
    color: '#52c41a',
  },
  NotReScanned: {
    id: 'not-rescanned',
    name: '确认重张待重扫',
    backgroundColor: '#fff2e8',
    color: '#fa8c16',
  },
  ReScanCheckInit: {
    id: 'rescan-check-init',
    name: '待重扫复核',
    backgroundColor: '#e6f7ff',
    color: '#1890ff',
  },
  ReScanCheckYes: {
    id: 'rescan-check-yes',
    name: '重扫复核通过',
    backgroundColor: '#e6f7f2',
    color: '#00a854',
  },
  ReScanCheckNo: {
    id: 'rescan-check-no',
    name: '重扫复核不通过',
    backgroundColor: '#fff2e8',
    color: '#fa8c16',
  },
})

export default StatusEnum

export function getStatus({ checkResult, reScanned, reScanCheckResult }) {
  if (checkResult == MultiFeedingManualCheckResultEnum.Init.id) {
    return StatusEnum.Init
  } else if (checkResult == MultiFeedingManualCheckResultEnum.Normal.id) {
    return StatusEnum.CheckNormal
  } else if (checkResult == MultiFeedingManualCheckResultEnum.MultiFeeding.id) {
    if (!reScanned) {
      return StatusEnum.NotReScanned
    } else {
      if (reScanCheckResult == MultiFeedingReScanCheckResultEnum.Init.id) {
        return StatusEnum.ReScanCheckInit
      } else if (reScanCheckResult == MultiFeedingReScanCheckResultEnum.Yes.id) {
        return StatusEnum.ReScanCheckYes
      } else if (reScanCheckResult == MultiFeedingReScanCheckResultEnum.No.id) {
        return StatusEnum.ReScanCheckNo
      }
    }
  }
  return null
}
