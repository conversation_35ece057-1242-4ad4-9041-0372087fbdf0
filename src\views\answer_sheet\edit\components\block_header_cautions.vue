<template>
  <div class="cautions">
    <div class="label" :style="labelStyle">注意事项</div>
    <div
      class="content"
      :class="{ editable }"
      style="position: relative"
      :style="contentStyle"
      @mousedown="showTextArea"
    >
      <div>{{ cautions }}<span v-if="editable && !cautions" class="placeholder">请输入注意事项</span></div>
      <textarea
        v-if="editable"
        ref="textArea"
        class="input no-print"
        :style="contentStyle"
        @blur="hideTextArea"
      ></textarea>
    </div>
  </div>
</template>

<script>
  import { Cautions } from '@/helpers/answer_sheet/header'

  export default {
    props: {
      editable: {
        type: Boolean,
        required: true,
      },
      size: {
        type: Object,
        required: true,
      },
    },
    computed: {
      cautions() {
        return this.$store.getters['answerSheet/cautions']
      },
      labelStyle() {
        return Cautions.getLabelStyle()
      },
      contentStyle() {
        return Cautions.getContentStyle()
      },
    },
    methods: {
      showTextArea(e) {
        // 找光标位置
        let offset = null
        if (document.caretRangeFromPoint) {
          let range = document.caretRangeFromPoint(e.clientX, e.clientY)
          let textNode = range && range.startContainer
          if (textNode && textNode.nodeType == 3) {
            offset = range.startOffset
            // 考虑文本节点内容空白前缀
            let textNodeValue = textNode.nodeValue
            let textNodeValueTrimStart = textNodeValue.trimStart()
            offset -= textNodeValue.length - textNodeValueTrimStart.length
          }
        }
        let elTextArea = this.$refs['textArea']
        if (elTextArea) {
          elTextArea.value = this.cautions
          elTextArea.style.display = 'block'
          setTimeout(() => {
            elTextArea.focus()
            if (offset != null) {
              elTextArea.setSelectionRange(offset, offset)
            }
          }, 0)
        }
      },
      hideTextArea() {
        let elTextArea = this.$refs['textArea']
        if (!elTextArea) {
          return
        }
        this.$store.commit('answerSheet/changeCautions', elTextArea.value)
        elTextArea.style.display = 'none'
      },
    },
  }
</script>

<style lang="scss" scoped>
  .content.editable:hover {
    background-color: $color-iview-table-active-row;
    cursor: text;
  }

  .input {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
    display: none;
    border: none;
    background-color: $color-iview-table-active-row;
    outline: 1px solid $color-info;
  }

  .placeholder {
    color: $color-disabled;
  }
</style>
