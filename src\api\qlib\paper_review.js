import ajax from '@/api/ajax'
import { Question } from '@/helpers/qlib/question'
import { Paper } from '@/helpers/qlib/paper'
import { PaperInfo } from '@/helpers/qlib/paper_info'

/**
 * 获取审卷试卷内容
 */
export function apiGetReviewPaperContent(paperId) {
  return ajax
    .get({
      url: `ques/paperReview/reviewPaperContent`,
      params: {
        paperId,
      },
      requestName: '获取审卷试卷内容',
    })
    .then(data => {
      let questions = (data.questions || []).map(q => Question.import(q))
      return new Paper(data.info, null, data.structureJson, questions)
    })
}

/**
 * 审卷添加题目
 */
export function apiAddReviewPaperQuestions({ paperId, paperStructure, questions }) {
  return Promise.all(questions.map(q => q.export())).then(exportedQuestions => {
    return ajax
      .post({
        url: `ques/paperReview/reviewPaperQuestions`,
        params: { paperId },
        data: {
          paperStructure: JSON.stringify(paperStructure.export()),
          questions: exportedQuestions,
        },
        requestName: '审卷添加题目',
      })
      .then(resQuestions => resQuestions.map(q => Question.import(q)))
  })
}

/**
 * 审卷修改题目
 */
export function apiChangeReviewPaperQuestions({ paperId, paperStructure, questions }) {
  return Promise.all(questions.map(q => q.export())).then(exportedQuestions => {
    return ajax
      .put({
        url: `ques/paperReview/reviewPaperQuestions`,
        params: { paperId },
        data: {
          paperStructure: JSON.stringify(paperStructure.export()),
          questions: exportedQuestions,
        },
        requestName: '审卷修改题目',
      })
      .then(resQuestions => resQuestions.map(q => Question.import(q)))
  })
}

/**
 * 审卷删除题目
 */
export function apiDeleteReviewPaperQuestions({ paperId, paperStructure, questionIds }) {
  return ajax.delete({
    url: `ques/paperReview/reviewPaperQuestions`,
    params: {
      paperId,
    },
    data: {
      paperStructure: JSON.stringify(paperStructure.export()),
      questionIds,
    },
    requestName: '审卷删除题目',
  })
}

/**
 * 审卷修改试卷结构
 */
export function apiChangeReviewPaperStructure(paper) {
  let paperId = paper.paperInfo.id
  let paperStructure = JSON.stringify(paper.paperStructure.export())
  return ajax.request({
    url: `ques/paperReview/reviewPaperStructure`,
    method: 'put',
    params: {
      paperId,
    },
    data: {
      paperStructure,
    },
    useFormData: true,
    requestName: '审卷修改试卷结构',
  })
}

/**
 * 获取未分配审核试卷任务
 */
export function apiGetPaperReviewUnassigned(params) {
  return ajax
    .get({
      url: `ques/paperReview/unassigned`,
      params: buildSendParams(params),
      requestName: '获取未分配审核试卷任务',
    })
    .then(buildResponseRecords)
}

/**
 * 获取已分配审核试卷任务
 */
export function apiGetPaperReviewAssigned(params) {
  return ajax
    .get({
      url: `ques/paperReview/assignedUnsubmitted`,
      params: buildSendParams(params),
      requestName: '获取已分配审核试卷任务',
    })
    .then(buildResponseRecords)
}

/**
 * 获取待发布试卷
 */
export function apiGetPaperReviewUnpublished(params) {
  return ajax
    .get({
      url: `ques/paperReview/submittedUnpublished`,
      params: buildSendParams(params),
      requestName: '获取待发布试卷',
    })
    .then(buildResponseRecords)
}

/**
 * 获取个人未提交审核任务
 */
export function apiGetPaperReviewSelfUnsubmitted(params) {
  return ajax
    .get({
      url: `ques/paperReview/selfUnsubmitted`,
      params: buildSendParams(params),
      requestName: '获取未提交审核试卷任务',
    })
    .then(buildResponseRecords)
}

/**
 * 获取个人已提交审核任务
 */
export function apiGetPaperReviewSelfSubmitted(params) {
  return ajax
    .get({
      url: `ques/paperReview/selfSubmitted`,
      params: buildSendParams(params),
      requestName: '获取已提交审核试卷任务',
    })
    .then(buildResponseRecords)
}

function buildSendParams(params) {
  return {
    stage: params.stageId,
    subject: params.subjectId,
    type: params.paperTypeId || null,
    gradeId: params.gradeId || null,
    term: params.term || null,
    year: params.year || null,
    size: params.pageSize,
    page: params.currentPage,
    region: params.region || null,
    keyword: params.keyword,
  }
}

function buildResponseRecords(data) {
  return {
    total: data.total || 0,
    papers: (data.records || []).map(p => PaperInfo.import(p)),
  }
}

/**
 * 分配审核试卷任务
 */
export function apiAssignPaperReview({ editorId, paperIds }) {
  return ajax.put({
    url: 'ques/paperReview/assign',
    params: {
      editorId,
    },
    data: paperIds,
    requestName: '分配审核试卷任务',
  })
}

/**
 * 撤回已分配试卷
 */
export function apiRemoveAssignPaperReview(paperIds) {
  return ajax.put({
    url: 'ques/paperReview/removeAssignment',
    data: paperIds,
    requestName: '撤回试卷审核任务',
  })
}

/**
 * 提交试卷审核
 */
export function apiSubmitPaperReview(paperId) {
  return ajax.put({
    url: 'ques/paperReview/submit',
    params: {
      paperId,
      allowNoScore: false,
    },
    requestName: '提交试卷审核',
  })
}

/**
 * 驳回编辑提交试卷
 */
export function apiRejectPaperReview(paperIds) {
  return ajax.put({
    url: 'ques/paperReview/rejectSubmission',
    data: paperIds,
    requestName: '驳回试卷审核',
  })
}

/**
 * 发布试卷
 */
export function apiPublishPaper(paperId) {
  return ajax.put({
    url: 'ques/paperReview/publish',
    params: {
      paperId,
      allowNoScore: true,
    },
    requestName: '发布试卷',
  })
}

/**
 * 获取编辑个人看题统计
 */
export function apiGetEditorSelfReviewPaperStats({ stageId, subjectId }) {
  return ajax
    .get({
      url: 'ques/paperReview/editorStats/review/self',
      params: {
        gradeLevel: stageId,
        subjectId,
      },
      requestName: '获取个人看题统计',
    })
    .then(transformStats)
}

/**
 * 获取各编辑员看题统计
 */
export function apiGetEditorsReviewPaperStats({ stageId, subjectId }) {
  return ajax
    .get({
      url: 'ques/paperReview/editorStats/review',
      params: {
        gradeLevel: stageId,
        subjectId,
      },
      requestName: '获取编辑员看题统计',
    })
    .then(data => data.map(transformStats))
}

/**
 * 获取编辑个人组卷统计
 */
export function apiGetEditorSelfComposePaperStats({ stageId, subjectId, bookType }) {
  return ajax
    .get({
      url: 'ques/paperReview/editorStats/make/self',
      params: {
        gradeLevel: stageId,
        subjectId,
        bookType,
      },
      requestName: '获取个人组卷统计',
    })
    .then(transformStats)
}

/**
 * 获取各编辑员组卷统计
 */
export function apiGetEditorsComposePaperStats({ stageId, subjectId, bookType }) {
  return ajax
    .get({
      url: 'ques/paperReview/editorStats/make',
      params: {
        gradeLevel: stageId,
        subjectId,
        bookType,
      },
      requestName: '获取编辑员组卷统计',
    })
    .then(data => data.map(transformStats))
}

/**
 * 获取编辑个人上传统计
 */
export function apiGetEditorSelfUploadPaperStats({ stageId, subjectId, bookType }) {
  return ajax
    .get({
      url: 'ques/paperReview/editorStats/upload/self',
      params: {
        gradeLevel: stageId,
        subjectId,
        bookType,
      },
      requestName: '获取个人上传统计',
    })
    .then(transformStats)
}

/**
 * 获取各编辑员上传统计
 */
export function apiGetEditorsUploadPaperStats({ stageId, subjectId, bookType }) {
  return ajax
    .get({
      url: 'ques/paperReview/editorStats/upload',
      params: {
        gradeLevel: stageId,
        subjectId,
        bookType,
      },
      requestName: '获取编辑员上传统计',
    })
    .then(data => data.map(transformStats))
}

function transformStats(item) {
  if (!item) {
    return null
  }
  let data = {
    editorId: item.editorId,
    editorName: item.editorName,
    assignedPaperCount: item.assignedPaperCount,
  }
  ;['total', 'today', 'yesterday', 'thisWeek', 'lastWeek', 'thisMonth', 'lastMonth'].forEach(period => {
    data[`${period}PaperCount`] = (item[period] && item[period].paperCount) || 0
    data[`${period}QuestionCount`] = (item[period] && item[period].questionCount) || 0
    data[`${period}BranchCount`] = (item[period] && item[period].smallQuestionCount) || 0
  })
  data.unSubmittedPaperCount = data.assignedPaperCount - data.totalPaperCount
  return data
}
