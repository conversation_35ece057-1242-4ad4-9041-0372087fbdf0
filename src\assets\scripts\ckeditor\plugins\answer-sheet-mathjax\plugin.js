﻿/**
 * 可复制粘贴mathjax渲染后的公式，以及插入新公式。
 * 本插件修改自ckeditor官方mathematical formula插件，取消iframe，并使用外部mathjax
 */

CKEDITOR.plugins.add( 'answer-sheet-mathjax', {
		requires: 'widget,dialog',
		icons: 'answer-sheet-mathjax',
		hidpi: true, // %REMOVE_LINE_CORE%

		init: function( editor ) {
			editor.ui.addButton('answer-sheet-mathjax', {
        label: '输入代码插入公式',
        command: 'answer-sheet-mathjax',
        toolbar: 'answer-sheet-mathjax'
      });

			var cls = 'igrade-answer-sheet-math';

			editor.widgets.add( 'answer-sheet-mathjax', {
				inline: true,
				dialog: 'answer-sheet-mathjax',
				button: '输入代码插入公式',
				mask: true,
				allowedContent: 'span(!' + cls + ')',
				template: '<span class="' + cls + '"></span>',

				defaults: {
					math: ''
				},

				upcast: function(el, data) {
					if (el.name == 'span' && el.hasClass(cls)) {
						data.math = el.getHtml();
						return true;
					}
					else {
						return false;
					}
				},

				downcast: function(el) {
					el.setHtml(this.data.math);
					return el;
				},
			});

			// 添加dialog
			CKEDITOR.dialog.add( 'answer-sheet-mathjax', this.path + 'dialogs/answer-sheet-mathjax.js' );
		}
	}
)