/**
 * 取卷时接口返回的错误类型
 */

import Enum from '@/enum/enum'

export default new Enum({
  Prepare: {
    id: 4000,
    name: '正在备题',
  },
  FinishAll: {
    id: 4001,
    name: '题块评卷已完成',
  },
  FinishSelf: {
    id: 4002,
    name: '个人任务已完成',
  },
  FinishWaitOther: {
    id: 4003,
    name: '待其他老师完成',
  },
  NoTask: {
    id: 4004,
    name: '无评卷任务',
  },
  Suspended: {
    id: 4005,
    name: '题块评卷已暂停',
  },
  NoPaper: {
    id: 4006,
    name: '无试卷',
  },
  SubjectSuspended: {
    id: 4020,
    name: '科目评卷已暂停',
  },
  SubjectFinish: {
    id: 4021,
    name: '科目评卷已完成',
  },
})
