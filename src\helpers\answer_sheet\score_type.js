/**
 * 打分类型
 */

import ScoreTypeEnum from '@/enum/answer_sheet/score_type'
import { BarScoreHeight } from '@/const/answer_sheet'

export default class ScoreType {
  constructor(t = {}) {
    // 打分类型
    this.name = ScoreTypeEnum.hasId(t.name) ? t.name : ScoreTypeEnum.Bar.id
    // 是否支持0.5分
    this.halfScore = Boolean(t.halfScore)
    this.height = t.height > 0 ? t.height : 0
  }

  change(t) {
    if (t.name && ScoreTypeEnum.hasId(t.name)) {
      this.name = t.name
    }
    if (typeof t.halfScore == 'boolean') {
      this.halfScore = t.halfScore
    }
    if (t.name == ScoreTypeEnum.Bar.id) {
      this.height = BarScoreHeight
    }
  }
  changeHalfScore(halfScore) {
    this.halfScore = Boolean(halfScore)
  }
  changeHeight(height) {
    this.height = height
  }
}
