<template>
  <div class="tab-objective">
    <div class="objective-description" style="margin-bottom: 10px">
      客观题满分<span class="span-color-primary">{{ objectivesTotalScore }}</span
      >分
      <TextButton
        v-if="enableChangeScoreAnswer"
        type="primary"
        style="margin-left: 10px"
        @click="modalObjectivesShowed = true"
        >修改答案分数</TextButton
      >
    </div>

    <Table :data="objectives" :columns="objectivesTableColumns" :span-method="handleObjectivesSpan" border></Table>

    <ModalObjectives
      v-model="modalObjectivesShowed"
      :objectives="objectives"
      @change-objectives="updateObjectives"
    ></ModalObjectives>
  </div>
</template>

<script>
  import { Tooltip } from 'view-ui-plus'
  import ModalObjectives from '../../components/modal_objectives'

  import { apiSaveTestObjectiveQuestions } from '@/api/emarking'

  import BranchType from '@/enum/qlib/branch_type'
  import { numberToChinese } from '@/utils/number'

  export default {
    components: {
      ModalObjectives,
    },
    props: {
      examId: String,
      examSubjectId: String,
      objectives: Array,
      objectivesTotalScore: Number,
      enableChangeScoreAnswer: {
        type: Boolean,
        default: true,
      },
    },
    emits: ['update-objectives'],
    data() {
      return {
        modalObjectivesShowed: false,
      }
    },
    computed: {
      objectivesTableColumns() {
        let columns = [
          {
            title: '大题',
            // key: 'topicName',
            align: 'center',
            render: (h, params) => h('span', {}, numberToChinese(params.row.topicCode)),
          },
          {
            title: '题号',
            key: 'questionCode',
            align: 'center',
          },
          {
            title: '题型',
            align: 'center',
            render: (h, params) => h('span', {}, BranchType.getNameById(params.row.branchTypeId)),
          },
          {
            title: '选项',
            key: 'optionCount',
            align: 'center',
          },
          {
            title: '分数',
            key: 'fullScore',
            align: 'center',
          },
          {
            title: '答案',
            key: 'standardAnswer',
            align: 'center',
            render: (h, params) => {
              if (params.row.branchTypeId === BranchType.MultipleChoice.id) {
                let answerStr = params.row.standardAnswer
                if (params.row.answerScoreList) {
                  answerStr += `(${params.row.answerScoreList})`
                }
                return h(
                  Tooltip,
                  {
                    placement: 'top',
                    content: answerStr,
                    'max-width': '700',
                  },
                  [h('span', {}, answerStr.length > 9 ? answerStr.slice(0, 9) + '……' : answerStr)]
                )
              } else {
                return h('span', {}, params.row.standardAnswer)
              }
            },
          },
        ]

        return columns
      },
    },
    methods: {
      handleObjectivesSpan({ row, rowIndex, columnIndex }) {
        let index = this.objectives.findIndex(x => x.topicCode === row.topicCode)
        let count = this.objectives.filter(x => x.topicCode === row.topicCode).length
        if (index === -1) {
          return [1, 1]
        } else {
          if (!columnIndex && rowIndex === index) {
            return [count, 1]
          } else if (!columnIndex && rowIndex > index) {
            return [0, 0]
          }
        }
      },
      updateObjectives(newObjectives) {
        apiSaveTestObjectiveQuestions({
          examId: this.examId,
          examSubjectId: this.examSubjectId,
          objectives: newObjectives.map(x => x.export()),
        })
          .then(() => {
            this.$Message.success({
              duration: 3,
              content: '设置成功',
            })
          })
          .finally(() => {
            this.$emit('update-objectives')
          })
      },
    },
  }
</script>
