/**
 * 考生答卷操作
 */

import Enum from '@/enum/enum'

export default new Enum({
  VerifyObjective: {
    id: 'VO',
    name: '忽略客观题',
  },
  EditObjective: {
    id: 'EO',
    name: '修改客观题',
  },
  VerifyAbsent: {
    id: 'VQ',
    name: '确认缺考',
  },
  EditAbsent: {
    id: 'EQ',
    name: '修改缺考',
  },
  EditAdmissionNum: {
    id: 'EZ',
    name: '修改准考号',
  },
  VerifyCorner: {
    id: 'VC',
    name: '忽略折角',
  },
  VerifySelect: {
    id: 'VS',
    name: '忽略选做题',
  },
  EditSelect: {
    id: 'ES',
    name: '修改选做题',
  },
  DeletePaper: {
    id: 'D',
    name: '删除卷',
  },
  RecoverPaper: {
    id: 'R',
    name: '恢复卷',
  },
})
