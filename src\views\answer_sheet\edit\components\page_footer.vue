<template>
  <div class="page-footer" :style="pageFooterStyle">
    <span class="page-number no-print">{{ pageNumberContent }}</span>
    <div v-if="showPageSign" class="page-sign" :style="pageSignStyle">
      <div v-for="box in pageSignBoxStyles" :key="box.order" class="page-sign-box" :style="box.style"></div>
    </div>
    <div v-if="showAnchorBottomLeft" :style="anchorBottomLeftStyle"></div>
    <div v-if="showAnchorBottomRight" :style="anchorBottomRightStyle"></div>
  </div>
</template>

<script>
  import TemplateRect from '@/helpers/scan_template_define/template_rect'
  import TemplateMarkArea from '@/helpers/scan_template_define/template_mark_area'

  export default {
    props: {
      page: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        // 页码标记宽度
        pageSignWidth: 20,
        // 页码标记高度
        pageSignHeight: 8,
        // 页码标记内部间距
        pageSignGap: 12,
        // 页码标记左右边距
        pageSignMarginX: 20,
        // 定位点
        anchorWidth: 16,
        anchorHeight: 20,
        // 定位点与页面左下角、右下角的距离
        anchorMargin: 28,
      }
    },
    computed: {
      // 总页数
      totalPages() {
        return this.$store.getters['answerSheet/totalPages']
      },
      // 总面数
      totalSides() {
        return this.$store.getters['answerSheet/pageSize'](this.totalPages - 1).sideIndex + 1
      },
      // 页面尺寸
      pageSize() {
        return this.$store.getters['answerSheet/pageSize'](this.page.pageIndex)
      },
      // 页脚尺寸
      pageFooterSize() {
        return {
          left: this.pageSize.left,
          bottom: 0,
          width: this.pageSize.contentWidth,
          height: this.pageSize.bottom,
        }
      },
      // 页脚样式
      pageFooterStyle() {
        let { left, bottom, width, height } = this.pageFooterSize
        return {
          position: 'absolute',
          left: left + 'px',
          bottom: bottom + 'px',
          width: width + 'px',
          height: height + 'px',
          lineHeight: height + 'px',
          textAlign: 'center',
        }
      },
      // 页脚文字
      pageNumberContent() {
        let { sideIndex, sideColumnCount, sideColumnIndex } = this.pageSize
        if (sideColumnCount == 1) {
          return `第 ${sideIndex + 1} 页`
        } else {
          return `第 ${sideIndex + 1} 页，第 ${sideColumnIndex + 1} 栏`
        }
      },
      // 显示页码标记：正面第一栏、反面最后一栏
      showPageSign() {
        let { isFrontSide, sideColumnCount, sideColumnIndex } = this.pageSize
        return (isFrontSide && sideColumnIndex == 0) || (!isFrontSide && sideColumnIndex == sideColumnCount - 1)
      },
      // 页码标记矩形块个数
      pageSignBoxCount() {
        return this.totalSides
      },
      // 页码标记样式
      pageSignStyle() {
        let style = {
          position: 'absolute',
          top: Math.floor((this.pageFooterSize.height - this.pageSignHeight) / 2) + 'px',
          width: this.pageSignWidth * this.pageSignBoxCount + this.pageSignGap * (this.pageSignBoxCount - 1) + 'px',
          height: this.pageSignHeight + 'px',
        }
        // 正面居左、反面居右
        if (this.pageSize.isFrontSide) {
          style.left = this.pageSignMarginX + 'px'
        } else {
          style.right = this.pageSignMarginX + 'px'
        }
        return style
      },
      pageSignBoxStyles() {
        let boxes = []
        for (let i = 0; i < this.totalSides; i++) {
          boxes.push({
            order: i + 1,
            style: {
              position: 'absolute',
              left: (this.pageSignWidth + this.pageSignGap) * i + 'px',
              top: 0,
              width: this.pageSignWidth + 'px',
              height: this.pageSignHeight + 'px',
              border: '1px solid black',
              backgroundColor: i == this.pageSize.sideIndex ? 'black' : 'white',
            },
          })
        }
        return boxes
      },
      // 每面第一栏显示左下定位点
      showAnchorBottomLeft() {
        return this.pageSize.sideColumnIndex == 0
      },
      anchorBottomLeftStyle() {
        return {
          position: 'absolute',
          left: `-${this.pageSize.left - this.anchorMargin}px`,
          bottom: this.anchorMargin + 'px',
          width: this.anchorWidth + 'px',
          height: this.anchorHeight + 'px',
          backgroundColor: 'black',
        }
      },
      // 每面最后一栏显示右下定位点
      showAnchorBottomRight() {
        return this.pageSize.sideColumnIndex == this.pageSize.sideColumnCount - 1
      },
      anchorBottomRightStyle() {
        return {
          position: 'absolute',
          right: `-${this.pageSize.right - this.anchorMargin}px`,
          bottom: this.anchorMargin + 'px',
          width: this.anchorWidth + 'px',
          height: this.anchorHeight + 'px',
          backgroundColor: 'black',
        }
      },
    },
    created() {
      this.changeInstance()
    },
    updated() {
      this.changeInstance()
    },
    methods: {
      changeInstance() {
        this.$store.commit('answerSheet/changePageFooterInstance', {
          pageIndex: this.page.pageIndex,
          instance: this,
        })
      },
      getTemplateElements(pageLeft) {
        let data = {}
        if (this.showAnchorBottomLeft) {
          let rect = TemplateRect.createFromAnswerSheetPageRect(
            pageLeft + this.anchorMargin,
            this.pageSize.height - this.anchorMargin - this.anchorHeight,
            this.anchorWidth,
            this.anchorHeight
          )
          data.anchorBottomLeft = TemplateMarkArea.create('AnchorBottomLeft', rect)
        }
        if (this.showAnchorBottomRight) {
          let rect = TemplateRect.createFromAnswerSheetPageRect(
            pageLeft + this.pageSize.width - this.anchorMargin - this.anchorWidth,
            this.pageSize.height - this.anchorMargin - this.anchorHeight,
            this.anchorWidth,
            this.anchorHeight
          )
          data.anchorBottomRight = TemplateMarkArea.create('AnchorBottomRight', rect)
        }

        if (this.showPageSign) {
          let left = pageLeft
          if (this.pageSize.isFrontSide) {
            left += this.pageSize.left + Number.parseInt(this.pageSignStyle.left)
          } else {
            left +=
              this.pageSize.width -
              this.pageSize.right -
              Number.parseInt(this.pageSignStyle.right) -
              Number.parseInt(this.pageSignStyle.width)
          }
          let top = this.pageSize.height - this.pageSize.bottom + Number.parseInt(this.pageSignStyle.top)

          let pageMarkRect = TemplateRect.createFromAnswerSheetPageRect(
            left,
            top,
            Number.parseInt(this.pageSignStyle.width),
            Number.parseInt(this.pageSignStyle.height)
          )
          data.pageMark = TemplateMarkArea.create('PageMark', pageMarkRect)
        }
        return data
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-number.no-print {
    display: inline-block;
    padding: 4px 6px;
    border-radius: 2px;
    color: $color-primary;
    font-size: $font-size-medium-s;
    line-height: 1;
    background-color: $color-background-light;
  }
</style>
