export default class MathJaxUtil {
  static render(el) {
    return new Promise(resolve => {
      if (!(window.MathJax && window.MathJax.Hub)) {
        resolve()
      }

      window.MathJax.Hub.Queue([
        'Typeset',
        window.MathJax.Hub,
        el,
        () => {
          resolve()
        },
      ])
    })
  }

  static clearRenderQueue() {
    if (!(window.MathJax && window.MathJax.Hub)) {
      return
    }
    window.MathJax.Hub.queue.queue = []
  }
}
