import { sumByDefaultZero } from '@/utils/math'
import { splitQuestionScore } from './miscellaneous'

// 构建科目试卷、题块、满分定义
export function buildSubjectDefine(examSubjectId, objs, subjs, blocks, fullScores) {
  // 客观题、主观题、笔试分若已有满分则直接使用，否则按题目累加
  let objectiveFullScore, subjectiveFullScore, writtenFullScore, otherFullScore, fullScore
  if (fullScores.objectiveFullScore != null) {
    objectiveFullScore = fullScores.objectiveFullScore
  } else {
    objectiveFullScore = sumByDefaultZero(
      objs.filter(q => !q.isAdditional),
      'fullScore'
    )
  }
  if (fullScores.subjectiveFullScore != null) {
    subjectiveFullScore = fullScores.subjectiveFullScore
  } else {
    subjectiveFullScore = 0
    let selectGroups = []
    blocks.forEach(block => {
      // 题块总分
      let blockScore = 0
      block.questions.forEach(q => {
        let subj = subjs.find(x => x.questionId == q.questionId)
        if (subj && !subj.isAdditional) {
          blockScore += subj.fullScore
        }
      })
      subjectiveFullScore += blockScore
      // 选做题
      if (block.selectGroupName) {
        let selectGroup = selectGroups.find(s => s.selectGroupName == block.selectGroupName)
        if (selectGroup) {
          selectGroup.totalCount++
        } else {
          selectGroup = {
            selectGroupName: block.selectGroupName,
            selectCount: block.selectCount,
            totalCount: 1,
            fullScore: blockScore,
          }
          selectGroups.push(selectGroup)
        }
      }
    })
    selectGroups.forEach(s => {
      subjectiveFullScore -= s.fullScore * (s.totalCount - s.selectCount)
    })
  }
  if (fullScores.writtenFullScore != null) {
    writtenFullScore = fullScores.writtenFullScore
  } else {
    writtenFullScore = objectiveFullScore + subjectiveFullScore
  }
  otherFullScore = fullScores.otherFullScore || 0
  // 此处科目满分未考虑笔试分与其他分比例
  fullScore = writtenFullScore + otherFullScore

  return {
    examSubjectId: examSubjectId,
    objectiveDefine: objs,
    subjectiveDefine: subjs,
    blockDefine: blocks,
    objectiveFullScore,
    subjectiveFullScore,
    writtenFullScore,
    otherFullScore,
    fullScore,
  }
}

// 空的科目试卷、题块、满分定义
export function getEmptySubjectDefine() {
  return {
    examSubjectId: '',
    objectiveDefine: [],
    subjectiveDefine: [],
    blockDefine: [],
    objectiveFullScore: 0,
    subjectiveFullScore: 0,
    writtenFullScore: 0,
    otherFullScore: 0,
    fullScore: 0,
  }
}

// 构建包含题目定义和学生作答的客观题
export function buildStudentObjectiveQuestions(objs, objectiveDefine) {
  let result = []
  objectiveDefine.forEach(obj => {
    let q = objs.find(x => x.code == obj.questionCode)
    if (q) {
      let copyObjective = Object.assign({}, obj)
      copyObjective.myAnswer = q.myAnswer
      copyObjective.score = q.score
      result.push(copyObjective)
    }
  })
  return result
}

// 构建包含题目定义和学生分数的主观题
export function buildStudentSubjectiveQuestions(subjs, blockDefine) {
  let result = []
  blockDefine.forEach(block => {
    let q = subjs.find(x => x.blockId == block.blockId)
    if (q) {
      let copyBlock = Object.assign({}, block)
      let subScoreList = splitQuestionScore(q.subScore)
      copyBlock.questions = block.questions.map(x => {
        let copySubjective = Object.assign({}, x)
        // 复制计算属性值
        copySubjective.fullCode = x.fullCode
        let item = subScoreList.find(y => y.name == x.fullCode)
        copySubjective.score = (item && item.score) || 0
        return copySubjective
      })
      copyBlock.fullScore = sumByDefaultZero(copyBlock.questions, 'fullScore')
      copyBlock.score = sumByDefaultZero(copyBlock.questions, 'score')
      copyBlock.branchNameScoreText = copyBlock.questions.map(q => `${q.branchName}=${q.score}`).join(';')
      result.push(copyBlock)
    }
  })
  return result
}
