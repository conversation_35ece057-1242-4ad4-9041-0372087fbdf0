import { apiGetStudentScoreExcel, apiGetStudentScoreExcelOfSeniorHigh } from '@/api/report'

import Store from '@/store/index'

// 学生成绩
export function generateExcelReportScoreBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let requestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    examSubjectId: (subject && subject.examSubjectId) || undefined,
    reportName: isMultipleSchoolLevel ? 'union_stuScore' : 'sch_stuScore',
    excelShowRank: excelShowRank,
    showSubOrgRank: false,
  }

  // 用户保存的是否查看下级机构排名设置
  const CurrentUserId = Store.getters['user/info'].userId
  const LocalCustomScoreSettings =
    (window.localStorage.getItem('report_score') && JSON.parse(window.localStorage.getItem('report_score'))) || []
  const TargetUserSetting = LocalCustomScoreSettings.find(setting => setting.userId === CurrentUserId) || null
  const TargetExamSetting =
    (TargetUserSetting &&
      TargetUserSetting.exams &&
      TargetUserSetting.exams.find(exam => exam.examId === requestParams.examId)) ||
    null
  const TargetTemplateSetting =
    (TargetExamSetting &&
      TargetExamSetting.templates &&
      TargetExamSetting.templates.find(template => template.templateId === requestParams.templateId)) ||
    null
  if (TargetTemplateSetting && TargetTemplateSetting.settings) {
    requestParams.showSubOrgRank = TargetTemplateSetting.settings.showSubOrgRank
  }

  let requestAPI
  let fileName = Store.getters['report/examName'] + '_'
  if (category) {
    requestAPI = apiGetStudentScoreExcelOfSeniorHigh
    requestParams.categoryId = category.categoryId
    requestParams.examSubjectId = (combination && combination.examSubjectId) || undefined
    requestParams.foreignSubjectId = (combination && combination.foreignSubjectId) || undefined
    requestParams.subjectCode = (combination && combination.subjectCode) || undefined

    const CombinationName = (combination && combination.subjectName) || ''

    if (isMultipleSchoolLevel) {
      if (school) {
        requestParams.schoolId = school && school.schoolId
        requestParams.reportName = 'sch_stuScore'

        const SchoolName = (school && school.schoolName) || '??'
        fileName = `学校报表/${SchoolName}/${category.categoryName}/${
          CombinationName ? CombinationName + '/' : ''
        }${fileName}学生成绩_${SchoolName}_${category.categoryName}${CombinationName ? '_' + CombinationName : ''}`
      } else {
        requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']

        fileName =
          category.categoryName +
          '/' +
          `${CombinationName ? CombinationName + '/' : ''}` +
          `${fileName}学生成绩_联考_${category.categoryName}${CombinationName ? '_' + CombinationName : ''}`
      }
    } else {
      requestParams.schoolId = Store.getters['report/currentSchoolId']

      fileName =
        category.categoryName +
        '/' +
        `${CombinationName ? CombinationName + '/' : ''}` +
        `${fileName}学生成绩_${Store.getters['report/currentSchoolName']}_${category.categoryName}${
          CombinationName ? '_' + CombinationName : ''
        }`
    }
  } else {
    requestAPI = apiGetStudentScoreExcel

    if (isMultipleSchoolLevel) {
      if (isNextLevel) {
        if (school) {
          fileName = subject
            ? `下级机构或学校报表/(学校)${school.schoolName}/${subject.subjectName}/${fileName}学生成绩_${school.schoolName}_${subject.subjectName}`
            : `下级机构或学校报表/(学校)${school.schoolName}/${fileName}学生成绩_${school.schoolName}`
          requestParams.schoolId = school.schoolId
          requestParams.reportName = 'sch_stuScore'
        } else {
          fileName = subject
            ? `下级机构或学校报表/(机构)${institution.name}/${subject.subjectName}/${fileName}学生成绩_${institution.name}_${subject.subjectName}`
            : `下级机构或学校报表/(机构)${institution.name}/${fileName}学生成绩_${institution.name}`
          requestParams.organizationId = institution.id
        }
      } else {
        if (school) {
          fileName = subject
            ? `学校报表/${school.schoolName}/${subject.subjectName}/${fileName}学生成绩_${school.schoolName}_${subject.subjectName}`
            : `学校报表/${school.schoolName}/${fileName}学生成绩_${school.schoolName}`
          requestParams.schoolId = school.schoolId
          requestParams.reportName = 'sch_stuScore'
        } else {
          fileName = subject
            ? `${subject.subjectName}/${fileName}学生成绩_联考_${subject.subjectName}`
            : `${fileName}学生成绩_联考`
          requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']
        }
      }
    } else {
      fileName = subject
        ? `${subject.subjectName}/${fileName}学生成绩_${Store.getters['report/currentSchoolName']}_${subject.subjectName}`
        : `${fileName}学生成绩_${Store.getters['report/currentSchoolName']}`
      requestParams.schoolId = Store.getters['report/currentSchoolId']
    }
  }
  fileName += '.xlsx'

  return requestAPI(requestParams)
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}
