<template>
  <div class="container-com-sort">
    <span class="sort-label">
      {{ label }}
    </span>
    <span
      v-for="sort in list"
      :key="sort.id"
      class="sort-item"
      :class="{ 'sort-item-active': sort.active }"
      @click="onClick(sort.id)"
    >
      {{ sort.name }}
      <Icon
        size="20"
        :type="sort.order === 'asc' ? 'ios-arrow-round-up' : 'ios-arrow-round-down'"
        :style="{ visibility: sort.active ? 'visible' : 'hidden' }"
      ></Icon>
    </span>
  </div>
</template>

<script>
  export default {
    props: {
      label: {
        type: String,
        default: '排序',
      },
      sortList: {
        type: Array,
        default: () => [],
        required: true,
      },
      value: {
        type: Object,
        default: () => ({
          id: '',
          order: 'desc',
        }),
        required: true,
      },
    },
    emits: ['on-change'],
    computed: {
      list() {
        return this.sortList.map(x => ({
          id: x.id || x.name || '',
          name: x.name,
          active: x.id === this.value.id,
          order: x.id === this.value.id ? this.value.order : 'desc',
        }))
      },
    },
    methods: {
      onClick(id) {
        let sort = this.list.find(x => x.id === id)
        if (!sort) {
          return
        }
        let activeSort = this.list.find(x => x.active)
        if (!activeSort) {
          return
        }

        this.$emit('on-change', {
          id,
          order: sort === activeSort ? (sort.order === 'desc' ? 'asc' : 'desc') : sort.order,
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .sort-label {
    margin-right: 20px;
  }

  .sort-item {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin-right: 10px;
    cursor: pointer;
  }

  .sort-item:last-child {
    margin-right: 0;
  }

  .sort-item.sort-item-active {
    color: $color-primary;
  }
</style>
