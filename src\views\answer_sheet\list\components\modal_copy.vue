<template>
  <Modal :model-value="modelValue" title="复制答题卡" width="600" @on-visible-change="handleVisibleChange">
    <Form :label-width="100">
      <FormItem label="新答题卡名称">
        <Input v-model="copyAnswerSheetName" class="panel-input" clearable maxlength="60"></Input>
      </FormItem>
      <!-- <FormItem label="复制学段">
        <Checkbox v-model="copyAnswerSheetStage"></Checkbox>
      </FormItem>
      <FormItem label="复制学科">
        <Checkbox v-model="copyAnswerSheetSubject"></Checkbox>
      </FormItem> -->
    </Form>
    <template #footer>
      <div>
        <Button type="text" @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleOK">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { apiCopyAnswerSheet } from '@/api/emarking/answer_sheet'

  export default {
    props: {
      modelValue: <PERSON><PERSON>an,
      sourceAnswerSheet: Object,
    },
    emits: ['update:model-value', 'fetch-personal-answer-sheet-list'],
    data() {
      return {
        copyAnswerSheetName: '',
        copyAnswerSheetStageId: 0,
        copyAnswerSheetSubjectId: 0,
        copyAnswerSheetStage: true,
        copyAnswerSheetSubject: true,
      }
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          if (!this.sourceAnswerSheet) {
            return
          }
          this.copyAnswerSheetName = this.sourceAnswerSheet.name || ''
          this.copyAnswerSheetStageId = this.sourceAnswerSheet.gradeLevel
          this.copyAnswerSheetSubjectId = this.sourceAnswerSheet.subjectId
          this.copyAnswerSheetStage = true
          this.copyAnswerSheetSubject = true
        }
      },
    },
    methods: {
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
      handleCancel() {
        this.$emit('update:model-value', false)
      },
      handleOK() {
        if (!this.sourceAnswerSheet) {
          return
        }
        this.copyAnswerSheetName = (this.copyAnswerSheetName || '').trim()
        if (!this.copyAnswerSheetName) {
          this.$Message.warning({
            duration: 4,
            content: '请输入答题卡名称',
          })
          return
        }
        if (this.copyAnswerSheetName == this.sourceAnswerSheet.name) {
          this.$Message.warning({
            duration: 4,
            content: '名称不能与原答题卡名称相同',
          })
          return
        }
        apiCopyAnswerSheet({
          name: this.copyAnswerSheetName,
          answerSheetId: this.sourceAnswerSheet.id,
          stageId: (this.copyAnswerSheetStage && this.copyAnswerSheetStageId) || undefined,
          subjectId: (this.copyAnswerSheetSubject && this.copyAnswerSheetSubjectId) || undefined,
        })
          .then(() => {
            this.$Message.success({
              duration: 3,
              content: '复制成功',
            })
            this.handleCancel()
          })
          .finally(() => {
            this.$emit('fetch-personal-answer-sheet-list')
          })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .ivu-form-item:last-child {
    margin-bottom: 0;
  }
</style>
