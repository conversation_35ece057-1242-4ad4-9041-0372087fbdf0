import { numberToChinese } from '@/utils/number'
import {
  Max_Question_Code,
  Max_Branch_Score,
  Max_Question_Branch_Name_Length,
  Question_Branch_Name_Exclude_Chars,
} from '@/const/emarking'
export default class Question {
  constructor() {
    // 题目Id
    this.questionId = ''
    // 题号
    this.questionCode = 0
    // 题名
    this.questionName = ''
    // 小题号
    this.branchCode = 0
    // 小题名
    this.branchName = ''
    // 大题号
    this.topicCode = 0
    // 大题名
    this.topicName = ''
    // 分数
    this.fullScore = 0
    // 对应题库试题的题目Id(questionId)，绑定试卷后有值，未绑定试卷则为空字符串
    this.originalQuestionId = ''
    // 对应题库试题的小题Id(branchId)，绑定试卷后有值，未绑定试卷则为空字符串
    this.originalBranchId = ''
    // 是否附加题
    this.isAdditional = false
  }

  // 中文大题号
  get topicCodeInChinese() {
    return numberToChinese(this.topicCode)
  }

  // 完整题号，如：1.2
  get fullCode() {
    return this.branchCode > 0 ? `${this.questionCode}.${this.branchCode}` : `${this.questionCode}`
  }

  // 默认题名
  get defaultQuestionName() {
    return `${this.questionCode}`
  }

  // 默认小题名
  get defaultBranchName() {
    return this.branchCode > 0 ? `${this.questionName}.${this.branchCode}` : `${this.questionName}`
  }

  import(q) {
    this.questionId = q.id
    this.questionCode = Number(q.questionCode)
    this.questionName = q.questionName
    this.branchCode = Number(q.branchCode) || 0
    this.branchName = q.branchName
    this.topicCode = Number(q.topicCode)
    this.topicName = q.topicName
    this.fullScore = q.fullScore
    this.originalQuestionId = q.questionId || ''
    this.originalBranchId = q.branchId || ''
    this.isAdditional = Boolean(q.isAdditional)
  }

  check() {
    // 题号
    this.questionCode = Number(this.questionCode)
    if (!this.questionCode) {
      return '无题号'
    } else if (!(Number.isInteger(this.questionCode) && this.questionCode >= 1)) {
      return '题号必须是正整数'
    } else if (this.questionCode > Max_Question_Code) {
      return '题号过大'
    }

    // 题名
    this.questionName = String(this.questionName || '').trim()
    if (!this.questionName) {
      return '无题名'
    } else if (this.questionName.length > Max_Question_Branch_Name_Length) {
      return '题名过长'
    } else if (Question_Branch_Name_Exclude_Chars.some(char => this.questionName.includes(char))) {
      return `题名不能包含特殊符号：${Question_Branch_Name_Exclude_Chars.join('、')}等`
    }

    // 小题号
    this.branchCode = Number(this.branchCode)
    if (!(Number.isInteger(this.branchCode) && this.branchCode >= 0)) {
      return '小题号错误'
    }

    // 小题名
    this.branchName = String(this.branchName || '').trim()
    if (!this.branchName) {
      return '无题名'
    } else if (this.branchName.length > Max_Question_Branch_Name_Length) {
      return '题名过长'
    } else if (Question_Branch_Name_Exclude_Chars.some(char => this.branchName.includes(char))) {
      return `题名不能包含特殊符号：${Question_Branch_Name_Exclude_Chars.join('、')}等`
    }

    // 大题号
    this.topicCode = Number(this.topicCode)
    if (!this.topicCode) {
      return '无大题号'
    } else if (!(Number.isInteger(this.topicCode) && this.topicCode >= 1)) {
      return '大题号必须是正整数'
    }

    // 大题名
    this.topicName = String(this.topicName || '').trim()
    if (!this.topicName) {
      return '无大题名'
    }

    // 满分
    this.fullScore = Number(this.fullScore)
    if (!(this.fullScore > 0)) {
      return '满分错误'
    } else if (this.fullScore > Max_Branch_Score) {
      return '满分过大'
    } else if (Math.floor(this.fullScore * 100) != this.fullScore * 100) {
      return '满分最多精确到2位小数'
    }

    // 附加题
    this.isAdditional = Boolean(this.isAdditional)
  }
}
