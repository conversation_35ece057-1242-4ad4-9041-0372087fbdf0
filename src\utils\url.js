export function addImageUrlParam(url) {
  if (!url) {
    return ''
  }

  let prefix = url.indexOf('?') >= 0 ? '&' : '?'
  return `${url}${prefix}t=${Date.now()}`
}

export function getUrlParam(paramName) {
  let reg = new RegExp(`[?&]${paramName}=[^&#?]+`)
  let match = window.location.search.match(reg)
  if (!match) {
    return ''
  }
  let keyAndName = match[0].split('=')
  if (!keyAndName || keyAndName.length !== 2) {
    return ''
  }

  return keyAndName[1]
}
