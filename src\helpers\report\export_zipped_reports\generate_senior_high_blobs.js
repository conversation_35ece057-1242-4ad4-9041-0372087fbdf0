import {
  apiGetReportExamCombinationsOfInstitutions,
  apiGetHighSchoolGradeSubjectList,
  apiGetIntervalReportExamCombinationsOfInstitutions,
  // apiGetCriticalExamCombinations,
} from '@/api/report'

import Store from '@/store/index'

import DefaultCategoriesAndCombinations from '@/enum/report/subject_combinations'
import { REPORTS as DefaultBatchReportList } from './reports_enum'

export async function generateSeniorHighZippedFiledBlobs(exportParams) {
  const Exam = Store.getters['report/exam'] || null
  const TemplateId = Store.getters['report/templateId']
  const RequestList = []

  if (Store.getters['report/isMultipleSchoolLevel']) {
    if (exportParams.bureauReport) {
      const SelectedReports = (exportParams.bureauReport.reportList || [])
        .filter(item => item.checked)
        .map(x => DefaultBatchReportList.find(y => y.type === x.type))

      if (SelectedReports.length) {
        const CategoriesAndSubjectCombinations = await getCategoriesAndSubjectCombinations(
          false,
          Store.getters['report/currentInstitutionId'],
          exportParams.bureauReport.subjectCategories,
          exportParams.bureauReport.downloadSubjectCombinationExcels,
          exportParams.bureauReport.subjectList
        )

        SelectedReports.forEach(report => {
          if ('seniorHighCategoriesId' in report) {
            const CorrespondingCategories = CategoriesAndSubjectCombinations[report.seniorHighCategoriesId]

            CorrespondingCategories.forEach(category => {
              const CategoryReqeustCombinations = []
              if (report.totalRelated) {
                CategoryReqeustCombinations.push(...category.totals)
              }
              if (report.subjectRelated) {
                CategoryReqeustCombinations.push(...category.combinations, ...category.subjects)
              }
              CategoryReqeustCombinations.forEach(combination => {
                RequestList.push({
                  function: report.function,
                  excelShowRank: exportParams.bureauReport.showRank,
                  subjectCombination: combination,
                  category: {
                    categoryId: category.id,
                    categoryName: category.name,
                  },
                })
              })
            })
          } else {
            if (report.totalRelated) {
              RequestList.push({
                function: report.function,
                category: {
                  categoryId: 0,
                  categoryName: '全体',
                },
              })
            }
            if (report.subjectRelated) {
              const CheckedSubjects =
                exportParams.bureauReport.subjectList && exportParams.bureauReport.subjectList.length
                  ? exportParams.bureauReport.subjectList.filter(item => item.checked)
                  : []
              CheckedSubjects.forEach(s =>
                RequestList.push({
                  function: report.function,
                  subject: {
                    subjectId: s.subjectId,
                    subjectName: s.subjectName,
                    examSubjectId: s.examSubjectId,
                  },
                  category: {
                    categoryId: 0,
                    categoryName: '全体',
                  },
                })
              )
            }
          }
        })
      }
    }

    if (exportParams.schoolsReport) {
      const ExamSchoolList = ((Exam && Exam.schools) || []).map(s => ({
        schoolId: s.schoolId,
        schoolName: s.schoolName || '??',
      })) // 若是要导出学校报表 即导出所有考试学校的报表 不予选择
      const SelectedReports = (exportParams.schoolsReport.reportList || [])
        .filter(item => item.checked)
        .map(x => DefaultBatchReportList.find(y => y.type === x.type))

      if (ExamSchoolList.length && SelectedReports.length) {
        const ExamSchoolCategoriesAndSubjectCombinations = await Promise.all(
          ExamSchoolList.map(school =>
            getCategoriesAndSubjectCombinations(
              true,
              school.schoolId,
              exportParams.schoolsReport.subjectCategories,
              exportParams.schoolsReport.downloadSubjectCombinationExcels,
              exportParams.schoolsReport.subjectList
            )
          )
        )

        ExamSchoolList.forEach(async (school, schoolIndex) => {
          const CategoriesAndSubjectCombinations = ExamSchoolCategoriesAndSubjectCombinations[schoolIndex]
          SelectedReports.forEach(report => {
            if ('seniorHighCategoriesId' in report) {
              const CorrespondingCategories = CategoriesAndSubjectCombinations[report.seniorHighCategoriesId]

              CorrespondingCategories.forEach(category => {
                const CategoryReqeustCombinations = []
                if (report.totalRelated) {
                  CategoryReqeustCombinations.push(...category.totals)
                }
                if (report.subjectRelated) {
                  CategoryReqeustCombinations.push(...category.combinations, ...category.subjects)
                }
                CategoryReqeustCombinations.forEach(combination => {
                  RequestList.push({
                    function: report.function,
                    school: school,
                    excelShowRank: exportParams.schoolsReport.showRank,
                    subjectCombination: combination,
                    category: {
                      categoryId: category.id,
                      categoryName: category.name,
                    },
                  })
                })
              })
            } else {
              if (report.totalRelated) {
                RequestList.push({
                  function: report.function,
                  school: school,
                  category: {
                    categoryId: 0,
                    categoryName: '全体',
                  },
                })
              }
              if (report.subjectRelated) {
                const CheckedSubjects =
                  exportParams.schoolsReport.subjectList && exportParams.schoolsReport.subjectList.length
                    ? exportParams.schoolsReport.subjectList.filter(item => item.checked)
                    : []
                CheckedSubjects.forEach(subject =>
                  RequestList.push({
                    function: report.function,
                    school: school,
                    subject: {
                      subjectId: subject.subjectId,
                      subjectName: subject.subjectName,
                      examSubjectId: subject.examSubjectId,
                    },
                    category: {
                      categoryId: 0,
                      categoryName: '全体',
                    },
                  })
                )
              }
            }
          })
        })
      }
    }
  } else {
    const SelectedReports = ((exportParams.schoolsReport && exportParams.schoolsReport.reportList) || [])
      .filter(item => item.checked)
      .map(x => DefaultBatchReportList.find(y => y.type === x.type))
    if (SelectedReports.length) {
      const CategoriesAndSubjectCombinations = await getCategoriesAndSubjectCombinations(
        true,
        Store.getters['report/currentSchoolId'],
        exportParams.schoolsReport.subjectCategories,
        exportParams.schoolsReport.downloadSubjectCombinationExcels,
        exportParams.schoolsReport.subjectList
      )
      SelectedReports.forEach(report => {
        if ('seniorHighCategoriesId' in report) {
          const CorrespondingCategories = CategoriesAndSubjectCombinations[report.seniorHighCategoriesId]
          const School = {
            schoolId: Store.getters['report/currentSchoolId'],
            schoolName: Store.getters['report/currentSchoolName'],
          }

          CorrespondingCategories.forEach(category => {
            const CategoryReqeustCombinations = []
            if (report.totalRelated) {
              CategoryReqeustCombinations.push(...category.totals)
            }
            if (report.subjectRelated) {
              CategoryReqeustCombinations.push(...category.combinations, ...category.subjects)
            }
            CategoryReqeustCombinations.forEach(combination => {
              RequestList.push({
                function: report.function,
                school: School,
                excelShowRank: exportParams.schoolsReport.showRank,
                subjectCombination: combination,
                category: {
                  categoryId: category.id,
                  categoryName: category.name,
                },
              })
            })
          })
        } else {
          if (report.totalRelated) {
            RequestList.push({
              function: report.function,
              category: {
                categoryId: 0,
                categoryName: '全体',
              },
            })
          }
          if (report.subjectRelated) {
            const CheckedSubjects =
              exportParams.schoolsReport.subjectList && exportParams.schoolsReport.subjectList.length
                ? exportParams.schoolsReport.subjectList.filter(item => item.checked)
                : []
            CheckedSubjects.forEach(subject =>
              RequestList.push({
                function: report.function,
                subject: {
                  subjectId: subject.subjectId,
                  subjectName: subject.subjectName,
                  examSubjectId: subject.examSubjectId,
                },
                category: {
                  categoryId: 0,
                  categoryName: '全体',
                },
              })
            )
          }
        }
      })
    }
  }

  const ResultList = []
  await Promise.allSettled(
    RequestList.map(request =>
      request.function(
        request.subject,
        request.school,
        request.excelShowRank,
        request.category,
        request.subjectCombination
      )
    )
  ).then(responses => {
    ;(responses || []).forEach(r => {
      if (r.status === 'fulfilled') {
        ResultList.push(r)
      } else if (r.status === 'rejected') {
        ResultList.push({
          status: 'rejected',
          value: {
            fileName: r.reason.fileName,
            error: r.reason.error.msg,
          },
        })
      }
    })
  })

  return ResultList

  function getCategoriesAndSubjectCombinations(
    isSchool = true,
    institutionId,
    categoryIds = [],
    downloadSubjectCombinationExcels = false,
    subjectList = []
  ) {
    if (institutionId) {
      const CategoriesAndSubjectsRequests = [
        {
          request: apiGetReportExamCombinationsOfInstitutions,
          params: {
            templateId: TemplateId,
            entityId: institutionId,
          },
        },
        {
          request: apiGetHighSchoolGradeSubjectList,
          params: {
            templateId: TemplateId,
            schoolId: institutionId,
          },
        },
        {
          request: apiGetIntervalReportExamCombinationsOfInstitutions,
          params: {
            templateId: TemplateId,
            organizationId: isSchool ? undefined : institutionId,
            schoolId: isSchool ? institutionId : undefined,
          },
        },
      ]

      return Promise.all(CategoriesAndSubjectsRequests.map(item => item.request(item.params)))
        .then(responses => {
          const CheckedCategoryList = DefaultCategoriesAndCombinations.getIdNames().filter(item =>
            categoryIds.includes(item.id)
          )

          return responses.map(item => {
            const CategoryList = []

            CheckedCategoryList.forEach(x => {
              const TargetCategory = item.find(y => y.categoryId === x.id)
              if (TargetCategory) {
                const TargetCategorySubjects = (TargetCategory.subjects || []).map(s => {
                  const TargetSubject = subjectList.find(ts => ts.subjectId == s.subjectCode)
                  s.examSubjectId = (TargetSubject && TargetSubject.examSubjectId) || undefined
                  s.checked = s.examSubjectId && TargetSubject.checked
                  s.subjectName = (TargetSubject && TargetSubject.subjectName) || s.subjectName
                  return s
                })
                CategoryList.push({
                  id: x.id,
                  name: x.name,
                  totals: TargetCategorySubjects.filter(tcs => ['T', 't'].includes(tcs.subjectCode)),
                  combinations: downloadSubjectCombinationExcels
                    ? TargetCategorySubjects.filter(tcs => !tcs.examSubjectId && !['T', 't'].includes(tcs.subjectCode))
                    : [],
                  subjects: TargetCategorySubjects.filter(tcs => tcs.examSubjectId && tcs.checked),
                })
              }
            })

            return CategoryList
          })
        })
        .catch(() => [])
    } else {
      return Promise.resolve([])
    }
  }
}
