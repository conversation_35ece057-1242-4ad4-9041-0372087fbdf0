import ajax from '@/api/ajax'

export function apiAddJudges(data) {
  return ajax.post({
    url: 'review/activityJudge/addJudges',
    data,
    requestName: '添加评委',
  })
}

export function apiGetJudges(data) {
  return ajax.get({
    url: 'review/activityJudge/list',
    params: data,
    requestName: '查询评委',
  })
}

export function apiDeleteJudges(data) {
  return ajax.delete({
    url: 'review/activityJudge/delete',
    data: data.ids,
    requestName: '删除评委',
  })
}

export function apiUpdateMaxTask(data) {
  return ajax.put({
    url: 'review/activityJudge/updateMaxTask',
    data,
    requestName: '调整评委最高任务量',
  })
}
