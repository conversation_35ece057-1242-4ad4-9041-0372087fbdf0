/**
 * 答题卡头部
 */

import QRCode from 'qrcode'
import { getHTMLHeight, parseHtmlDom } from '@/utils/dom'
import StudentInfoEnum from '@/enum/answer_sheet/student_info'
import TicketNumberTypeEnum from '@/enum/answer_sheet/ticket_number_type'

export default class Header {
  constructor(header = {}) {
    // 头部高度
    this.height = header.height || 0
    // 显示页眉
    this.showPageHeader = Boolean(header.showPageHeader) || false
    // 页眉内容
    this.pageHeaderContent = header.pageHeaderContent || ''
    // 答题卡标题
    this.title = header.title || ''
    // 标题居左
    this.titleAlignLeft = Boolean(header.titleAlignLeft) || false
    // 操作说明
    this.showInstruction = Boolean(header.showInstruction) || false
    // 二维码
    this.showQrcode = header.showQrcode || false
    this.qrcodeText = header.qrcodeText || ''
    this.qrcodeImageSrc = header.qrcodeImageSrc || ''
    // 准考号
    this.ticketNumberType = header.ticketNumberType || TicketNumberTypeEnum.Omr.id
    this.ticketNumberLength = header.ticketNumberLength || 8
    // 默认纵向
    if (!header.ticketNumberType) {
      this.ticketNumberOmrHorizontal = false
    } else {
      this.ticketNumberOmrHorizontal = Boolean(header.ticketNumberOmrHorizontal)
    }

    // 学生需要填写的信息名称
    this.stuInfoLabels = ['姓名', '班级', '考场号', '座位号']
    if (header.stuInfoLabels && header.stuInfoLabels.length > 0) {
      this.changeStuInfoLabels(header.stuInfoLabels)
    }
    // 学生填写信息别名
    this.stuInfoLabelAlias = header.stuInfoLabelAlias || {}
    // 注意事项
    this.showCautions = header.showCautions == null ? true : Boolean(header.showCautions)
    this.cautions =
      '1．答题前请先填写准考号、姓名等信息；答题卡有多张的，每张都要填写。\n2．客观题必须使用2B铅笔填涂，修改时用橡皮擦干净。\n3．主观题必须使用黑色签字笔在答题区域内作答。\n4．保持答卷清洁，不要折叠，不要弄皱。'
    if (header.cautions) {
      this.cautions = header.cautions.replace(/<br>/g, '\n')
    }

    // 缺考标记
    this.showMissingMark = header.showMissingMark == null ? true : Boolean(header.showMissingMark)
    if (this.showQrcode && this.qrcodeText && !this.qrcodeImageSrc) {
      this.generateQrcodeImage()
    }

    /**
     * 排版
     */
    // 页眉高度
    this.pageHeaderHeight = 20
    // 页眉在上与顶部距离
    this.pageHeaderTop = -12
    // 页眉与标题间距
    this.pageHeaderTitleGap = 12
    // 标题在上与顶部距离
    this.titleTop = -32
    // 标题与填写框纵向间距
    this.titleBoxVerticalGap = 12
    // 填写框在上与顶部距离
    this.boxTop = 0
    // 标题、填写框横向间距
    this.titleBoxGap = 8
  }

  changeHeight(height) {
    this.height = height
  }
  changeShowPageHeader(showPageHeader) {
    this.showPageHeader = showPageHeader
  }
  changePageHeaderContent(content) {
    this.pageHeaderContent = content
  }
  changePageHeaderContentCoachBook(bookName, chapterName) {
    let content = `<span class="page-header-book-name" style="font-weight: bold;">${
      bookName || ''
    }</span><span  class="page-header-chapter-name" style="font-weight: bold; float: right">${chapterName || ''}</span>`
    this.changePageHeaderContent(content)
  }
  extractCoachBookInfoFromPageHeaderContent() {
    let bookName = ''
    let chapterName = ''
    if (this.pageHeaderContent) {
      let dom = parseHtmlDom(this.pageHeaderContent)
      let elBookName = dom.querySelector('.page-header-book-name')
      if (elBookName) {
        bookName = elBookName.textContent
      }
      let elChapterName = dom.querySelector('.page-header-chapter-name')
      if (elChapterName) {
        chapterName = elChapterName.textContent
      }
    }
    return {
      bookName,
      chapterName,
    }
  }
  changeTitle(title) {
    this.title = title
  }
  changeTitleAlignLeft(value) {
    this.titleAlignLeft = Boolean(value)
  }
  changeShowInstruction(value) {
    this.showInstruction = Boolean(value)
  }
  changeShowQrcode(showQrcode) {
    this.showQrcode = showQrcode
    if (this.showQrcode && this.qrcodeText && !this.qrcodeImageSrc) {
      this.generateQrcodeImage()
    }
  }
  changeQrcodeText(qrcodeText) {
    this.qrcodeText = qrcodeText
  }
  generateQrcodeImage() {
    if (!this.qrcodeText) {
      this.qrcodeImageSrc = ''
      return Promise.resolve()
    }
    return QRCode.toDataURL(this.qrcodeText, {
      margin: 0,
      errorCorrectionLevel: 'H',
    }).then(url => {
      this.qrcodeImageSrc = url
    })
  }
  changeTicketNumberType(type) {
    this.ticketNumberType = type
  }
  changeTicketNumberLength(length) {
    this.ticketNumberLength = length
  }
  changeTicketNumberOmrHorizontal(value) {
    this.ticketNumberOmrHorizontal = value
  }
  changeStuInfoLabels(stuInfoLabels) {
    this.stuInfoLabels = StudentInfoEnum.getNames().filter(name => stuInfoLabels.includes(name))
  }
  changeStuInfoLabelAlias(aliasMap) {
    let map = {}
    StudentInfoEnum.getNames().forEach(name => {
      if (aliasMap[name]) {
        map[name] = aliasMap[name]
      }
    })
    this.stuInfoLabelAlias = map
  }
  changeShowCautions(showCautions) {
    this.showCautions = showCautions
  }
  changeCautions(cautions) {
    this.cautions = cautions
  }
  changeShowMissingMark(showMissingMark) {
    this.showMissingMark = showMissingMark
  }

  export() {
    return {
      height: this.height,
      showPageHeader: this.showPageHeader,
      pageHeaderContent: this.pageHeaderContent,
      title: this.title,
      titleAlignLeft: this.titleAlignLeft,
      showInstruction: this.showInstruction,
      showQrcode: this.showQrcode,
      qrcodeText: this.qrcodeText,
      qrcodeImageSrc: this.qrcodeImageSrc,
      ticketNumberType: this.ticketNumberType,
      ticketNumberLength: this.ticketNumberLength,
      ticketNumberOmrHorizontal: this.ticketNumberOmrHorizontal,
      stuInfoLabels: this.stuInfoLabels.slice(),
      stuInfoLabelAlias: this.stuInfoLabelAlias,
      showCautions: this.showCautions,
      cautions: this.cautions,
      showMissingMark: this.showMissingMark,
    }
  }

  layoutPageHeader() {
    if (this.showPageHeader) {
      return {
        height: this.pageHeaderHeight,
        top: this.pageHeaderTop,
      }
    }
    return {
      height: 0,
      top: 0,
    }
  }

  layoutTitle(pageBodyWidth) {
    let width = pageBodyWidth
    // 标题居左，填写部分固定宽432px
    if (this.titleAlignLeft) {
      width -= 432 + this.titleBoxGap
    }

    let height = Title.getHeight(width, this.title)

    let top = 0
    if (this.showPageHeader) {
      top = this.pageHeaderTop + this.pageHeaderHeight + this.pageHeaderTitleGap
    } else {
      if (this.titleAlignLeft) {
        top = this.boxTop
      } else {
        top = this.titleTop
      }
    }
    // 标题居左，填写部分固定高98px，标题竖直居中
    if (this.titleAlignLeft) {
      top += Math.floor((98 - height) / 2)
    }

    return {
      width,
      height,
      top,
    }
  }

  // 答题卡头排版，标题除外
  layoutExceptTitle(pageBodyWidth, baseStyle) {
    // 标题
    let title = this.layoutTitle(pageBodyWidth)

    // 顶部距离
    let top
    if (this.titleAlignLeft) {
      if (this.showPageHeader) {
        top = this.pageHeaderTop + this.pageHeaderHeight + this.pageHeaderTitleGap
      } else {
        top = this.boxTop
      }
    } else {
      top = title.top + title.height + this.titleBoxVerticalGap
    }

    // 左边距离
    let left = 0
    if (this.titleAlignLeft) {
      left = title.width + this.titleBoxGap
    }

    // 边框宽度
    let borderWidth = 1
    // 内部宽度
    let headerContentWidth = pageBodyWidth
    if (this.titleAlignLeft) {
      headerContentWidth -= left
    }
    headerContentWidth -= borderWidth * 2

    // 各元素间距
    let gap = 8
    // 底部间距
    let marginBottom = 8

    // 初始化各元素大小位置
    let size = {
      left: 0,
      top: 0,
      width: 0,
      height: 0,
    }
    let qrcode = Object.assign({}, size)
    let studentInfo = Object.assign({}, size)
    let ticketNumber = Object.assign({}, size)
    let cautions = Object.assign({}, size)
    let missing = Object.assign({}, size)
    let instruction = Object.assign({}, size)

    // 计算二维码大小
    if (this.showQrcode) {
      qrcode.width = Qrcode.width
      qrcode.height = Qrcode.width
    }
    // 计算准考号大小
    let ticketNumberSize
    if (this.ticketNumberType == TicketNumberTypeEnum.Omr.id) {
      ticketNumberSize = TicketNumber.getOmrSize(this.ticketNumberLength, this.ticketNumberOmrHorizontal)
    } else if (this.ticketNumberType == TicketNumberTypeEnum.Barcode.id) {
      ticketNumberSize = TicketNumber.getBarcodeSize(this.ticketNumberLength)
    } else if (this.ticketNumberType == TicketNumberTypeEnum.Handwriting.id) {
      let ticketNumberLabel = (this.stuInfoLabelAlias && this.stuInfoLabelAlias['准考号']) || '准考号'
      ticketNumberSize = TicketNumber.getHandwritingSize(this.ticketNumberLength, ticketNumberLabel.length)
    }
    ticketNumber.width = ticketNumberSize.width
    ticketNumber.height = ticketNumberSize.height

    // 按准考号类型选择方法排版
    let layoutMethod =
      this.ticketNumberType == TicketNumberTypeEnum.Handwriting.id
        ? this.layoutTicketNumberHandwriting
        : this.layoutTicketNumberOmrBarcode
    let height = layoutMethod.call(this, {
      headerContentWidth,
      gap,
      qrcode,
      studentInfo,
      ticketNumber,
      cautions,
      missing,
      instruction,
      baseStyle,
    })

    return {
      top,
      left,
      width: headerContentWidth + borderWidth * 2,
      height: height + borderWidth * 2,
      borderWidth,
      marginBottom,
      gap,
      qrcode,
      studentInfo,
      ticketNumber,
      cautions,
      missing,
      instruction,
    }
  }

  // 答题卡头排版（准考号手写）
  layoutTicketNumberHandwriting({
    headerContentWidth,
    gap,
    qrcode,
    studentInfo,
    ticketNumber,
    cautions,
    missing,
    instruction,
    baseStyle,
  }) {
    if (this.showQrcode) {
      let aLineWidth = gap + qrcode.width + gap + ticketNumber.width
      if (this.showInstruction) {
        instruction.width = Instruction.width
        instruction.height = Instruction.height
        aLineWidth += gap + instruction.width
      }
      if (aLineWidth > headerContentWidth) {
        /**
         * ------------------------
         * 准考号
         * ------  -------  -------
         * 二维码  考生填写  操作说明
         * ------  -------  -------
         */
        ticketNumber.left = gap
        ticketNumber.top = gap
        qrcode.left = gap
        qrcode.top = ticketNumber.top + ticketNumber.height + gap
        studentInfo.left = qrcode.left + qrcode.width + gap
        studentInfo.top = qrcode.top
        instruction.left = headerContentWidth - instruction.width
        instruction.top = qrcode.top
      } else {
        /**
         * -----  --------  --------
         *        准考号
         * 二维码  --------  操作说明
         *        考生填写
         * -----  --------  --------
         */
        if (this.showInstruction) {
          qrcode.left = gap
          qrcode.top = gap
          ticketNumber.left = qrcode.left + qrcode.width + gap
          ticketNumber.top = qrcode.top
          studentInfo.left = ticketNumber.left
          studentInfo.top = ticketNumber.top + ticketNumber.height
          instruction.left = headerContentWidth - instruction.width
          instruction.top = qrcode.top
        } else {
          qrcode.left = headerContentWidth - qrcode.width - gap
          qrcode.top = gap
          ticketNumber.left = gap
          ticketNumber.top = gap
          studentInfo.left = ticketNumber.left
          studentInfo.top = ticketNumber.top + ticketNumber.height
        }
      }
    } else {
      /**
       * --------
       * 准考号
       * --------
       * 考生填写
       * --------
       */
      ticketNumber.left = gap
      ticketNumber.top = gap
      studentInfo.left = gap
      studentInfo.top = ticketNumber.top + ticketNumber.height
    }
    studentInfo.width = headerContentWidth - studentInfo.left - gap
    if (this.showQrcode) {
      if (this.showInstruction) {
        studentInfo.width -= instruction.width + gap
      } else {
        studentInfo.width = ticketNumber.width
      }
    }
    studentInfo.height = this.getStudentInfoHeight(studentInfo.width)

    // 注意事项、缺考标记均居左，在二维码和考生填写之下
    let bottom = Math.max(qrcode.top + qrcode.height, studentInfo.top + studentInfo.height)
    if (this.showCautions) {
      cautions.left = gap
      cautions.top = bottom + gap
      cautions.width = headerContentWidth - gap * 2
      cautions.height = Cautions.getHeight(cautions.width, this.cautions, baseStyle)
      bottom = cautions.top + cautions.height
    }
    if (this.showMissingMark) {
      missing.left = gap
      missing.top = bottom + gap
      missing.width = headerContentWidth - gap * 2
      missing.height = MissingMark.getHeight(missing.width)
      bottom = missing.top + missing.height
    }

    return bottom + gap
  }

  // 答题卡头排版（准考号填涂或条码）
  layoutTicketNumberOmrBarcode({
    headerContentWidth,
    gap,
    qrcode,
    studentInfo,
    ticketNumber,
    cautions,
    missing,
    baseStyle,
  }) {
    // 二维码左上角，考生填写在二维码右侧
    this.positionQrcodeStudentInfoLeftTop({ gap, qrcode, studentInfo })

    // 计算注意事项、缺考标记独占整行或居于准考号左侧的尺寸
    let cautionsMissingSize = this.calcCautionsMissingSizeAloneAside({
      headerContentWidth,
      gap,
      ticketNumber,
      baseStyle,
    })

    // 测试各种排版方式，找出区域高度最小的
    let best = { height: Number.MAX_VALUE }
    let methods = [
      this.positionTicketNumberTopCautionsMissingAlone,
      this.positionTicketNumberTopCautionsMissingAside,
      this.positionTicketNumberBottomCautionsMissingAlone,
      this.positionTicketNumberBottomCautionsMissingAside,
    ]
    methods.forEach(method => {
      let varyParams = {
        studentInfo: Object.assign({}, studentInfo),
        ticketNumber: Object.assign({}, ticketNumber),
        cautions: Object.assign({}, cautions),
        missing: Object.assign({}, missing),
      }
      let height = method.call(this, { headerContentWidth, gap, qrcode, ...varyParams, cautionsMissingSize })
      if (height < best.height) {
        best.height = height
        Object.assign(best, varyParams)
      }
    })
    Object.assign(studentInfo, best.studentInfo)
    Object.assign(ticketNumber, best.ticketNumber)
    Object.assign(cautions, best.cautions)
    Object.assign(missing, best.missing)
    return best.height
  }

  // 二维码、考生填写定位到左上角
  positionQrcodeStudentInfoLeftTop({ gap, qrcode, studentInfo }) {
    if (this.showQrcode) {
      qrcode.left = gap
      qrcode.top = gap
    }
    studentInfo.left = qrcode.left + qrcode.width + gap
    studentInfo.top = gap
  }

  // 计算注意事项、缺考标记独占整行或居于准考号左侧的尺寸
  calcCautionsMissingSizeAloneAside({ headerContentWidth, gap, ticketNumber, baseStyle }) {
    let cautionsAlone = {
      width: headerContentWidth - gap * 2,
    }
    let cautionsAside = {
      width: cautionsAlone.width - ticketNumber.width,
    }
    let missingAlone = {
      width: cautionsAlone.width,
    }
    let missingAside = {
      width: cautionsAside.width,
    }
    if (this.showCautions) {
      ;[cautionsAlone, cautionsAside].forEach(cautions => {
        cautions.height = Cautions.getHeight(cautions.width, this.cautions, baseStyle)
      })
    }
    if (this.showMissingMark) {
      ;[missingAlone, missingAside].forEach(missing => {
        missing.height = MissingMark.getHeight(missing.width)
      })
    }
    return {
      cautionsAlone,
      cautionsAside,
      missingAlone,
      missingAside,
    }
  }

  // 准考号右上，注意事项、缺考标记独占整行
  positionTicketNumberTopCautionsMissingAlone({
    headerContentWidth,
    gap,
    qrcode,
    studentInfo,
    ticketNumber,
    cautions,
    missing,
    cautionsMissingSize,
  }) {
    studentInfo.width = headerContentWidth - ticketNumber.width - gap - studentInfo.left
    studentInfo.height = this.getStudentInfoHeight(studentInfo.width)
    ticketNumber.left = headerContentWidth - ticketNumber.width
    ticketNumber.top = 0
    if (this.showCautions || this.showMissingMark) {
      let bottom = Math.max(qrcode.top + qrcode.height, studentInfo.top + studentInfo.height, ticketNumber.height)
      if (this.showCautions) {
        cautions.left = gap
        cautions.top = bottom + gap
        cautions.width = cautionsMissingSize.cautionsAlone.width
        cautions.height = cautionsMissingSize.cautionsAlone.height
        bottom += gap + cautions.height
      }
      if (this.showMissingMark) {
        missing.left = gap
        missing.top = bottom + gap
        missing.width = cautionsMissingSize.missingAlone.width
        missing.height = cautionsMissingSize.missingAlone.height
        bottom += gap + missing.height
      }
      return bottom + gap
    } else {
      return Math.max(
        Math.max(qrcode.top + qrcode.height, studentInfo.top + studentInfo.height) + gap,
        ticketNumber.height
      )
    }
  }

  // 准考号右上，注意事项、缺考标记在准考号左侧
  positionTicketNumberTopCautionsMissingAside({
    headerContentWidth,
    gap,
    qrcode,
    studentInfo,
    ticketNumber,
    cautions,
    missing,
    cautionsMissingSize,
  }) {
    studentInfo.width = headerContentWidth - ticketNumber.width - gap - studentInfo.left
    studentInfo.height = this.getStudentInfoHeight(studentInfo.width)
    ticketNumber.left = headerContentWidth - ticketNumber.width
    ticketNumber.top = 0
    let bottom = Math.max(qrcode.top + qrcode.height, studentInfo.top + studentInfo.height)
    if (this.showCautions) {
      cautions.left = gap
      cautions.top = bottom + gap
      cautions.width = cautionsMissingSize.cautionsAside.width
      cautions.height = cautionsMissingSize.cautionsAside.height
      bottom += gap + cautions.height
    }
    if (this.showMissingMark) {
      missing.left = gap
      missing.top = bottom + gap
      missing.width = cautionsMissingSize.missingAside.width
      missing.height = cautionsMissingSize.missingAside.height
      bottom += gap + missing.height
    }
    return Math.max(bottom + gap, ticketNumber.height)
  }

  // 准考号右下，注意事项、缺考标记独占整行
  positionTicketNumberBottomCautionsMissingAlone({
    headerContentWidth,
    gap,
    qrcode,
    studentInfo,
    ticketNumber,
    cautions,
    missing,
    cautionsMissingSize,
  }) {
    studentInfo.width = headerContentWidth - gap - studentInfo.left
    studentInfo.height = this.getStudentInfoHeight(studentInfo.width)
    let bottom = Math.max(qrcode.top + qrcode.height, studentInfo.top + studentInfo.height)
    ticketNumber.top = bottom + gap
    ticketNumber.left = headerContentWidth - ticketNumber.width
    bottom += ticketNumber.height + gap
    if (this.showCautions) {
      cautions.left = gap
      cautions.top = bottom + gap
      cautions.width = cautionsMissingSize.cautionsAlone.width
      cautions.height = cautionsMissingSize.cautionsAlone.height
      bottom += gap + cautions.height
    }
    if (this.showMissingMark) {
      missing.left = gap
      missing.top = bottom + gap
      missing.width = cautionsMissingSize.missingAlone.width
      missing.height = cautionsMissingSize.missingAlone.height
      bottom += gap + missing.height
    }
    if (this.showCautions || this.showMissingMark) {
      bottom += gap
    }
    return bottom
  }

  // 准考号右下，注意事项、缺考标记在准考号左侧
  positionTicketNumberBottomCautionsMissingAside({
    headerContentWidth,
    gap,
    qrcode,
    studentInfo,
    ticketNumber,
    cautions,
    missing,
    cautionsMissingSize,
  }) {
    studentInfo.width = headerContentWidth - gap - studentInfo.left
    studentInfo.height = this.getStudentInfoHeight(studentInfo.width)
    let bottom = Math.max(qrcode.top + qrcode.height, studentInfo.top + studentInfo.height)
    ticketNumber.top = bottom + gap
    ticketNumber.left = headerContentWidth - ticketNumber.width
    if (this.showCautions) {
      cautions.left = gap
      cautions.top = bottom + gap
      cautions.width = cautionsMissingSize.cautionsAside.width
      cautions.height = cautionsMissingSize.cautionsAside.height
      bottom += gap + cautions.height
    }
    if (this.showMissingMark) {
      missing.left = gap
      missing.top = bottom + gap
      missing.width = cautionsMissingSize.missingAside.width
      missing.height = cautionsMissingSize.missingAside.height
      bottom += gap + missing.height
    }
    if (this.showCautions || this.showMissingMark) {
      bottom += gap
    }
    if (ticketNumber.top + ticketNumber.height < bottom) {
      ticketNumber.top = bottom - ticketNumber.height
    }
    return Math.max(bottom, ticketNumber.top + ticketNumber.height)
  }

  // 指定宽度下考生填写部分的高度
  getStudentInfoHeight(width) {
    return StudentInfo.getHeight(width, this.stuInfoLabels.length)
  }
}

/**
 * 标题
 */
export const Title = {
  lineHeight: 32,
  contentStyle: {
    fontWeight: 'bold',
    fontSize: '24px',
    textAlign: 'center',
    whiteSpace: 'pre-wrap',
  },

  getMaxHeight() {
    return this.lineHeight * 3
  },

  getHeight(pageBodyWidth, title) {
    let style = {
      ...this.getContentStyle(),
      width: `${pageBodyWidth}px`,
    }
    return Math.min(getHTMLHeight(style, title), this.getMaxHeight())
  },

  getContentStyle() {
    return {
      ...this.contentStyle,
      lineHeight: `${this.lineHeight}px`,
      minHeight: `${this.lineHeight}px`,
    }
  },
}

/**
 * 二维码
 */
export const Qrcode = {
  width: 80,
}

/**
 * 考生填写
 */
export const StudentInfo = {
  // 边框
  borderWidth: 1,
  // 每项最小宽度
  itemMinWidth: 160,
  // 每项最大宽度
  itemMaxWidth: 300,
  // 横向间距
  itemGapX: 20,
  // 每项高度
  itemHeight: 40,

  // 区域最小宽度
  getMinWidth() {
    return this.itemMinWidth
  },
  // 指定宽度每行几项
  getItemCountPerRow(width) {
    return Math.floor((width + this.itemGapX) / (this.itemMinWidth + this.itemGapX))
  },
  // 指定宽度区域总高度
  getHeight(width, labelCount) {
    if (width < this.itemMinWidth) {
      return Number.MAX_SAFE_INTEGER
    }
    let itemCountPerRow = this.getItemCountPerRow(width)
    return this.itemHeight * Math.ceil(labelCount / itemCountPerRow)
  },
  // 各项样式
  getItems(width, labels, alias) {
    let itemCountPerRow = this.getItemCountPerRow(width)
    if (itemCountPerRow == 0) {
      return []
    }
    // 计算每项宽度，根据整个考生填写区域宽度伸缩
    let itemWidth = Math.floor((width + this.itemGapX) / itemCountPerRow - this.itemGapX)
    if (itemWidth < this.itemMinWidth) {
      itemWidth = this.itemMinWidth
    }
    if (itemWidth > this.itemMaxWidth) {
      itemWidth = this.itemMaxWidth
    }

    let list = []
    labels.forEach((label, idx) => {
      let row = Math.floor(idx / itemCountPerRow)
      let col = idx % itemCountPerRow
      list.push({
        label,
        text: (alias && alias[label]) || label,
        style: {
          width: `${itemWidth}px`,
          height: `${this.itemHeight}px`,
          position: 'absolute',
          left: `${col * (itemWidth + this.itemGapX)}px`,
          top: `${row * this.itemHeight}px`,
          borderBottom: `${this.borderWidth}px solid var(--base-border-color)`,
        },
      })
    })
    return list
  },
  // 每项文字样式
  getItemLabelStyle() {
    return {
      lineHeight: '1.5',
      position: 'absolute',
      left: '0',
      bottom: `${-1 - this.borderWidth}px`,
      backgroundColor: 'white',
    }
  },
}

/**
 * 准考号
 */
export const TicketNumber = {
  // 边框
  borderWidth: 1,

  // 选项宽度
  cellWidth: 20,
  // 选项高度
  cellHeight: 8,
  // 选项宽度（加左右边距）
  omrWidth: 28,
  // 选项高度（加上下边距）
  omrHeight: 18,

  // 条码宽度
  barWidth: 230,
  // 条码高度
  barHeight: 114,
  // 条码内边距
  barPadding: 8,

  // 手写数字标签每个字宽度
  handwritingLabelFontSize: 14,
  // 手写数字框宽度（高度与宽度一致）
  handwrittingBlankWidth: 30,

  // 填涂大小
  getOmrSize(ticketNumberLength, isOmrHorizontal = false) {
    if (isOmrHorizontal) {
      return {
        width: this.omrWidth * 2 + this.omrWidth * 10 + this.borderWidth * 4,
        height: ticketNumberLength * this.omrHeight + (ticketNumberLength + 1) * this.borderWidth,
      }
    } else {
      return {
        width: ticketNumberLength * this.omrWidth + (ticketNumberLength + 1) * this.borderWidth,
        height: this.omrWidth * 2 + this.omrHeight * 10 + this.borderWidth * 4,
      }
    }
  },
  // 填涂区样式
  getOmrStyle() {
    return {
      position: 'relative',
      border: `${this.borderWidth}px solid var(--base-border-color)`,
    }
  },
  // 填涂区网格
  getOmrGridLines(ticketNumberLength, isOmrHorizontal = false) {
    let lines = []
    if (isOmrHorizontal) {
      ;[this.omrWidth, this.omrWidth * 2 + this.borderWidth].forEach((left, idx) => {
        lines.push({
          id: `col-${idx + 1}`,
          style: {
            position: 'absolute',
            left: `${left}px`,
            top: `${-this.borderWidth}px`,
            width: `${this.borderWidth / 2}px`,
            bottom: `${-this.borderWidth}px`,
            backgroundColor: 'var(--base-border-color)',
          },
        })
      })
      for (let i = 1; i < ticketNumberLength; i++) {
        lines.push({
          id: `row-${i}`,
          style: {
            position: 'absolute',
            right: `${-this.borderWidth}px`,
            top: `${(this.omrHeight + this.borderWidth) * i - this.borderWidth}px`,
            width: `${this.omrWidth + this.omrWidth * 10 + this.borderWidth * 3}px`,
            height: `${this.borderWidth / 2}px`,
            backgroundColor: 'var(--base-border-color)',
          },
        })
      }
    } else {
      ;[this.omrWidth, this.omrWidth * 2 + this.borderWidth].forEach((top, idx) => {
        lines.push({
          id: `row-${idx + 1}`,
          style: {
            position: 'absolute',
            left: `${-this.borderWidth}px`,
            top: `${top}px`,
            right: `${-this.borderWidth}px`,
            height: `${this.borderWidth / 2}px`,
            backgroundColor: 'var(--base-border-color)',
          },
        })
      })
      for (let j = 1; j < ticketNumberLength; j++) {
        lines.push({
          id: `col-${j}`,
          style: {
            position: 'absolute',
            left: `${(this.omrWidth + this.borderWidth) * j - this.borderWidth}px`,
            bottom: `${-this.borderWidth}px`,
            width: `${this.borderWidth / 2}px`,
            height: `${this.omrWidth + this.omrHeight * 10 + this.borderWidth * 3}px`,
            backgroundColor: 'var(--base-border-color)',
          },
        })
      }
    }
    return lines
  },
  // 填涂区标签样式
  getOmrLabelStyle(isOmrHorizontal = false) {
    if (isOmrHorizontal) {
      return {
        position: 'absolute',
        left: 0,
        top: '50%',
        width: `${this.omrWidth}px`,
        textAlign: 'center',
        transform: 'translateY(-50%)',
        padding: '4px',
      }
    } else {
      return {
        position: 'absolute',
        left: 0,
        top: 0,
        width: '100%',
        textAlign: 'center',
        height: `${this.omrWidth}px`,
        lineHeight: `${this.omrWidth}px`,
      }
    }
  },
  // 填涂区选项
  getOmrCells(ticketNumberLength, isOmrHorizontal = false) {
    let cellPaddingLeftRight = (this.omrWidth - this.cellWidth) / 2
    let cellPaddingTopBottom = (this.omrHeight - this.cellHeight) / 2
    let cells = []
    if (isOmrHorizontal) {
      let labelAndInputWidth = (this.omrWidth + this.borderWidth) * 2
      for (let i = 0; i < ticketNumberLength; i++) {
        for (let j = 0; j < 10; j++) {
          let left = j * this.omrWidth + cellPaddingLeftRight + labelAndInputWidth
          let top = i * this.omrHeight + i * this.borderWidth + cellPaddingTopBottom
          cells.push({
            id: `cell-${i + 1}-${j + 1}`,
            value: j,
            left,
            top,
            width: this.cellWidth,
            height: this.cellHeight,
            style: {
              position: 'absolute',
              left: left + 'px',
              top: top + 'px',
              width: this.cellWidth + 'px',
              height: this.cellHeight + 'px',
            },
          })
        }
      }
    } else {
      let labelAndInputHeight = (this.omrWidth + this.borderWidth) * 2
      for (let j = 0; j < ticketNumberLength; j++) {
        for (let i = 0; i < 10; i++) {
          let left = j * this.omrWidth + j * this.borderWidth + cellPaddingLeftRight
          let top = i * this.omrHeight + cellPaddingTopBottom + labelAndInputHeight
          cells.push({
            id: `cell-${j + 1}-${i + 1}`,
            value: i,
            left,
            top,
            width: this.cellWidth,
            height: this.cellHeight,
            style: {
              position: 'absolute',
              left: left + 'px',
              top: top + 'px',
              width: this.cellWidth + 'px',
              height: this.cellHeight + 'px',
            },
          })
        }
      }
    }
    return cells
  },

  // 条码大小
  getBarcodeSize() {
    return {
      width: this.barWidth,
      height: this.barHeight,
    }
  },
  // 条码样式
  getBarcodeStyle() {
    return {
      border: `${this.borderWidth}px solid var(--base-border-color)`,
      lineHeight: `${this.barHeight - this.borderWidth * 2}px`,
      fontSize: '20px',
      textAlign: 'center',
    }
  },

  // 手写数字大小
  getHandwritingSize(ticketNumberLength, labelLength) {
    return {
      width:
        this.handwritingLabelFontSize * (labelLength + 1.5) +
        this.handwrittingBlankWidth * ticketNumberLength +
        this.borderWidth * (ticketNumberLength + 1),
      height: this.handwrittingBlankWidth + this.borderWidth * 2,
    }
  },
  // 手写数字标签样式
  getHandwritingLabelStyle(labelLength) {
    return {
      position: 'absolute',
      left: '0',
      top: '0',
      width: `${this.handwritingLabelFontSize * (labelLength + 1.5)}px`,
      lineHeight: `${this.handwrittingBlankWidth + this.borderWidth * 2}px`,
      whiteSpace: 'nowrap',
    }
  },
  // 手写数字空格样式
  getHandwritingBlanksStyle(ticketNumberLength) {
    return {
      position: 'absolute',
      right: '0',
      top: '0',
      width: `${this.handwrittingBlankWidth * ticketNumberLength + this.borderWidth * (ticketNumberLength + 1)}px`,
      height: `${this.handwrittingBlankWidth + this.borderWidth * 2}px`,
      border: `${this.borderWidth}px solid var(--base-border-color)`,
    }
  },
  // 手写数字网格线
  getHandwritingGridLines(ticketNumberLength) {
    let lines = []
    for (let i = 1; i < ticketNumberLength; i++) {
      lines.push({
        id: `column-${i}`,
        style: {
          position: 'absolute',
          left: `${(this.handwrittingBlankWidth + this.borderWidth) * i - this.borderWidth}px`,
          top: '0',
          height: `${this.handwrittingBlankWidth}px`,
          borderLeft: `${this.borderWidth / 2}px solid var(--base-border-color)`,
        },
      })
    }
    return lines
  },
  // 手写数字格子
  getHandwritingCells(ticketNumberLength, labelLength) {
    let cells = []
    for (let i = 0; i < ticketNumberLength; i++) {
      cells.push({
        left:
          this.handwritingLabelFontSize * (labelLength + 1.5) + (this.handwrittingBlankWidth + this.borderWidth) * i,
        top: 0,
        width: this.handwrittingBlankWidth + this.borderWidth * 2,
        height: this.handwrittingBlankWidth + this.borderWidth * 2,
      })
    }
    return cells
  },
  // 手写数字外框样式
  getHandwritingRect(ticketNumberLength, labelLength) {
    return {
      left: this.handwritingLabelFontSize * (labelLength + 1.5),
      top: 0,
      width: this.handwrittingBlankWidth * ticketNumberLength + this.borderWidth * (ticketNumberLength + 1),
      height: this.handwrittingBlankWidth + this.borderWidth * 2,
    }
  },
  getHandwritingRectStyle(ticketNumberLength, labelLength) {
    let rect = this.getHandwritingRect(ticketNumberLength, labelLength)
    return {
      position: 'absolute',
      left: rect.left + 'px',
      top: rect.top + 'px',
      width: rect.width + 'px',
      height: rect.height + 'px',
      border: `${this.borderWidth}px solid var(--base-border-color)`,
    }
  },
}

/**
 * 注意事项
 */
export const Cautions = {
  minWidth: 96,
  // 标签“注意事项”高度
  labelHeight: 24,
  labelPadding: 4,
  // 内容样式
  contentStyle: {
    fontSize: '12px',
    whiteSpace: 'pre-line',
    minHeight: '21px',
    lineHeight: 'inherit',
  },
  // 指定内容和宽度下的高度
  getHeight(width, content, baseStyle) {
    if (width < this.minWidth) {
      return Number.MAX_SAFE_INTEGER
    }
    let style = {
      ...baseStyle,
      ...this.contentStyle,
      width: `${width}px`,
    }
    let height = getHTMLHeight(style, content)
    return height + this.labelHeight
  },
  // 标签样式
  getLabelStyle() {
    return {
      height: `${this.labelHeight}px`,
      lineHeight: `${this.labelHeight - this.labelPadding}px`,
      paddingTop: `${this.labelPadding}px`,
    }
  },
  // 内容样式
  getContentStyle() {
    return this.contentStyle
  },
}

/**
 * 缺考标记
 */
export const MissingMark = {
  // 一行排的高度
  height: 20,
  // 标签、填涂和说明的横向间距
  gap: 16,
  // 标签宽度：4个字
  labelWidth: 14 * 4,
  // 填涂宽高和边框
  omrWidth: 24,
  omrHeight: 12,
  omrBorder: 1,
  // 说明部分宽度
  tipWidth: 12 * 8,

  // 能容纳缺考标记的最小宽度
  getMinWidth() {
    return Math.max(this.labelWidth + this.omrWidth + this.gap, this.tipWidth)
  },
  // 指定宽度下是否分两行排
  getIsTwoRow(width) {
    return width < this.labelWidth + this.omrWidth + this.tipWidth + this.gap * 2
  },
  // 指定宽度下的缺考标记高度
  getHeight(width) {
    if (width < this.getMinWidth()) {
      return Number.MAX_SAFE_INTEGER
    }
    return this.getIsTwoRow(width) ? this.height * 2 : this.height
  },
  // 标签样式
  getLabelStyle() {
    return {
      position: 'absolute',
      left: '0',
      top: '0',
      width: `${this.labelWidth}px`,
      height: `${this.height}px`,
      lineHeight: `${this.height}px`,
      whiteSpace: 'nowrap',
    }
  },
  // 填涂位置
  getOmrPosition() {
    return {
      left: this.labelWidth + this.gap,
      top: (this.height - this.omrHeight) / 2,
      width: this.omrWidth,
      height: this.omrHeight,
    }
  },
  // 填涂样式
  getOmrStyle() {
    let { left, top, width, height } = this.getOmrPosition()
    return {
      position: 'absolute',
      left: left + 'px',
      top: top + 'px',
      width: width + 'px',
      height: height + 'px',
      border: `${this.omrBorder}px solid var(--base-border-color)`,
    }
  },
  // 说明样式，高度不够一行排就换行
  getTipStyle(width) {
    let style = {
      position: 'absolute',
      width: `${this.tipWidth}px`,
      height: `${this.height}px`,
      lineHeight: `${this.height}px`,
      whiteSpace: 'nowrap',
    }
    if (this.getIsTwoRow(width)) {
      style.top = `${this.height}px`
      style.left = `${Math.floor((this.getMinWidth() - this.tipWidth) / 2)}px`
    } else {
      style.top = '0'
      style.left = `${this.labelWidth + this.omrWidth + this.gap * 2}px`
    }
    return style
  },
}

/**
 * 操作说明
 */
export const Instruction = {
  width: 92,
  height: 80,
  labelWidth: 12,
}
