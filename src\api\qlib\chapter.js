import ajax from '@/api/ajax'

export function apiGetChapters(bookId) {
  return ajax
    .get({
      url: `ques/chapter/byBookId`,
      params: {
        bid: bookId,
      },
      requestName: '获取章节',
    })
    .then(data => {
      return !data
        ? []
        : data.map(function _transformNode(node) {
            return {
              id: Number(node.id) || 0,
              name: (node.bookId !== undefined ? node.chapterName : node.name) || '',
              isKnowledge: Boolean(node.bookId === undefined),
              children: (node.children || []).map(_transformNode),
            }
          })
    })
}

export function apiExportChapterExcel({ stageId, subjectId, pressId, bookId }) {
  return ajax.download({
    url: 'ques/chapter/export',
    params: {
      gradeLevel: stageId,
      subjectId,
      categoryId: pressId,
      bookId,
    },
    requestName: '下载章节',
  })
}

/**
 * 导入章节检查
 */
export function apiImportChapterCheck({ stageId, subjectId, file }) {
  return ajax.upload({
    url: 'ques/chapter/importCheck',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    data: {
      file,
    },
    responseBlob: true,
  })
}

/**
 * 导入章节
 */
export function apiImportChapter({ stageId, subjectId, file }) {
  return ajax.upload({
    url: 'ques/chapter/import',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    data: {
      file,
    },
    responseBlob: true,
  })
}

/**
 * 导入复制章节题目检查
 */
export function apiImportChapterQuestionCheck({ stageId, subjectId, file }) {
  return ajax.upload({
    url: 'ques/chapter/importCopyChapterQuestionCheck',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    data: {
      file,
    },
  })
}

/**
 * 导入复制章节题目
 */
export function apiImportChapterQuestion({ stageId, subjectId, file }) {
  return ajax.upload({
    url: 'ques/chapter/importCopyChapterQuestion',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    data: {
      file,
    },
  })
}

/**
 * 是否有导入权限
 */
export function apiGetImportChapterAllowed() {
  return ajax.get({
    url: 'ques/chapter/allowImport',
  })
}

export function apiDeleteChapterCache({ stageId, subjectId }) {
  return ajax.delete({
    url: 'ques/chapter/deleteCache',
    params: {
      stageId,
      subjectId,
    },
    requestName: '清除章节缓存',
  })
}
