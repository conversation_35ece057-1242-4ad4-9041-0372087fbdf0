import {
  apiGetClassCompare,
  apiGetSchoolCompare,
  apiGetClassCompareOfSeniorHigh,
  apiGetSchoolCompareOfSeniorHigh,
} from '@/api/report'
import { generateExcelBlob } from '@/utils/excel_export'
import { getInstitutionName } from '../../tools/tools'

import Store from '@/store/index'

import { UUID_ZERO } from '@/const/string'

// 学校 / 班级对比
export function generateExcelReportClassesCompareBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let fileName = Store.getters['report/examName'] + '_'
  let requestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    reportName: 'sch_clsCompare',
    excelShowRank: excelShowRank,
  }

  if (category) {
    const CombinationName = (combination && combination.subjectName) || ''
    requestParams.subjectCategory = category.categoryId
    requestParams.subjectCode = combination.subjectCode

    if (isMultipleSchoolLevel) {
      if (school) {
        requestParams.schoolId = school.schoolId

        fileName = `学校报表/${school.schoolName}/${category.categoryName}/${
          CombinationName ? CombinationName + '/' : ''
        }${fileName}班级对比_${school.schoolName}_${category.categoryName}${
          CombinationName ? '_' + CombinationName : ''
        }`
      } else {
        requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']
        requestParams.reportName = 'union_schCompare'

        fileName = `${category.categoryName}/${CombinationName ? CombinationName + '/' : ''}${fileName}学校对比_联考_${
          category.categoryName
        }${CombinationName ? '_' + CombinationName : ''}`
      }
    } else {
      requestParams.schoolId = Store.getters['report/currentSchoolId']

      fileName = `${category.categoryName}/${CombinationName ? CombinationName + '/' : ''}${fileName}班级对比_${
        Store.getters['report/currentSchoolName']
      }_${category.categoryName}${CombinationName ? '_' + CombinationName : ''}`
    }
  } else {
    if (isMultipleSchoolLevel) {
      if (isNextLevel) {
        if (school) {
          fileName = subject
            ? `学校报表/${school.schoolName}/${subject.subjectName}/${fileName}班级对比_${school.schoolName}_${subject.subjectName}`
            : `学校报表/${school.schoolName}/${fileName}班级对比_${school.schoolName}`
          requestParams.schoolId = school.schoolId
        } else {
          fileName = subject
            ? `下级机构或学校报表/(机构)${institution.name}/${subject.subjectName}/${fileName}学校对比_${institution.name}_${subject.subjectName}`
            : `下级机构或学校报表/(机构)${institution.name}/${fileName}学校对比_${institution.name}`
          requestParams.organizationId = institution.id
          requestParams.reportName = 'union_schCompare'
        }
      } else {
        if (school) {
          fileName = subject
            ? `学校报表/${school.schoolName}/${subject.subjectName}/${fileName}班级对比_${school.schoolName}_${subject.subjectName}`
            : `学校报表/${school.schoolName}/${fileName}班级对比_${school.schoolName}`
          requestParams.schoolId = school.schoolId
        } else {
          fileName = subject
            ? `${subject.subjectName}/${fileName}学校对比_联考_${subject.subjectName}`
            : `${fileName}学校对比_联考`
          requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']
          requestParams.reportName = 'union_schCompare'
        }
      }
    } else {
      fileName = subject
        ? `${subject.subjectName}/${fileName}班级对比_${Store.getters['report/currentSchoolName']}_${subject.subjectName}`
        : `${fileName}班级对比_${Store.getters['report/currentSchoolName']}`
      requestParams.schoolId = Store.getters['report/currentSchoolId']
    }
    if (subject) {
      requestParams.subjectId = subject.subjectId
      requestParams.examSubjectId = subject.examSubjectId
    }
  }
  fileName += '.xlsx'

  return fetchSheets(requestParams)
    .then(sheets => generateExcelBlob(sheets))
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}

async function fetchSheets(requestParams) {
  let tableColumns = [
    {
      title: '实考人数',
      key: 'present',
    },
    {
      title: '缺考人数',
      key: 'absent',
    },
    {
      title: '满分',
      key: 'totalFullScore',
    },
    {
      title: '最高分',
      key: row => (isNaN(row.maxScore) ? '-' : row.maxScore),
      cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
    },
    {
      title: '最低分',
      key: row => (isNaN(row.minScore) ? '-' : row.minScore),
      cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
    },
    {
      title: '平均分',
      key: row => (isNaN(row.avgScore) ? '-' : row.avgScore),
      cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
    },
  ]

  let requestAPI
  if ('subjectCategory' in requestParams) {
    requestAPI = requestParams.organizationId ? apiGetSchoolCompareOfSeniorHigh : apiGetClassCompareOfSeniorHigh
  } else {
    requestAPI = requestParams.organizationId ? apiGetSchoolCompare : apiGetClassCompare
  }

  let tableData = await requestAPI(requestParams)
  if (tableData && tableData.length) {
    if (requestParams.organizationId) {
      tableColumns.unshift({
        title: '学校',
        key: 'institutionName',
        width: 'auto',
      })

      tableData.forEach(td => {
        td.institutionName = getInstitutionName(td.schoolId, td.classId, td.schoolName, td.className)
        td.regionRankRaise = td.schoolId === UUID_ZERO || td.regionRankRaise === 0 ? '-' : td.regionRankRaise
      })
    } else {
      tableColumns.unshift(
        {
          title: '班级',
          key: 'className',
          width: 'auto',
        },
        {
          title: requestParams.examSubjectId ? '科任老师' : '班主任',
          key: row => (row.teachers || []).map(t => t.name).join('、') || '-',
          width: 'auto',
        }
      )
    }

    tableColumns = tableColumns.concat(
      tableData[0].rates.map(rate => {
        return {
          title: rate.id,
          children: [
            {
              title: '值',
              width: 12,
              key: row => {
                const TargetValue = (row.rates || []).find(r => r.id === rate.id)
                return +((TargetValue && TargetValue.name) || 0)
              },
              cellNumberFormat: value =>
                isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%',
            },
            {
              title: '人数',
              width: 12,
              key: row => {
                const TargetValue = (row.rates || []).find(r => r.id === rate.id)
                return +((TargetValue && TargetValue.count) || 0)
              },
            },
            {
              title: '平均分',
              width: 12,
              key: row => {
                const TargetValue = (row.rates || []).find(r => r.id === rate.id)
                return +((TargetValue && TargetValue.avg) || 0)
              },
              cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
            },
          ],
        }
      })
    )
  }

  let isMultipleSchool = Store.getters['report/isMultipleSchool']
  let isAdministrator = Store.getters['report/isAdministrator']
  let regionRankPositions = null
  let temp = Store.getters['report/reportsByLevel'].find(x => x.level === 'school')
  if (temp) {
    temp = (temp.reports || []).find(x => x.text === '班级对比')
    if (temp) {
      regionRankPositions = temp.rolePositions
    }
  }
  if (requestParams.excelShowRank) {
    if (isMultipleSchool) {
      if (
        isAdministrator ||
        (regionRankPositions && regionRankPositions.some(x => x.detail && x.detail.showClassRegionRank))
      ) {
        tableColumns.push({
          title: '联考排名',
          key: row => row.regionRank || '-',
        })
      }
      if (isAdministrator && tableData.some(x => x.regionRankRaise && x.schoolId !== UUID_ZERO)) {
        tableColumns.push({
          title: '联考排名变化',
          key: row => row.regionRankRaise,
        })
      }
    }
    if (
      !requestParams.organizationId &&
      (isAdministrator ||
        (regionRankPositions && regionRankPositions.some(r => r.detail && r.detail.showClassSchoolRank)))
    ) {
      tableColumns.push({
        title: '校排名',
        key: row => row.schoolRank || '-',
      })
    }
  }

  return [
    {
      sheetName: requestParams.organizationId ? '学校对比' : '班级对比',
      rows: tableData,
      columns: tableColumns.map(x => {
        x.width = x.width || 12
        return x
      }),
    },
  ]
}
