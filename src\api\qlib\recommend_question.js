import ajax from '@/api/ajax'

// 试卷题目推题
export function apiGetPaperExamListByPaperId(paperId) {
  return ajax.get({
    url: 'ques/pushQues/listExamSubjectByPaperId',
    params: {
      paperId: paperId,
    },
    requestName: '根据paperId获取试卷相关应用考试列表',
  })
}
export function apiCheckPaperHadRecommendQuestions(requestParams) {
  return ajax.get({
    url: 'ques/pushQues/existsPushQues',
    params: {
      paperId: requestParams.paperId,
      examSubjectId: requestParams.examSubjectId,
    },
    requestName: '查看卷是否已有推题',
  })
}

export function apiGetRecommendQuestionListOfPaperQuestion(requestParams) {
  return ajax.get({
    url: 'ques/pushQues/listPushQuesByPaperIdAndQuestionId',
    params: {
      paperId: requestParams.paperId,
      examSubjectId: requestParams.examSubjectId,
      questionId: requestParams.questionId,
    },
    requestName: '获取试卷题目推题列表',
  })
}

export function apiGeneratePaperRecommendQuestions(requestParams) {
  return ajax.post({
    url: 'ques/pushQues/generateAndSavePaperPushQues',
    data: {
      paperId: requestParams.paperId, // String
      pushNum: requestParams.pushNum || 6, // Number 每道题推题数目
      questionId: requestParams.questionId, // String
    },
    requestName: '生成试卷变式题',
  })
}

export function apiDeleteRecommendedQuestionsByQuestion(requestParams) {
  return ajax.delete({
    url: 'ques/pushQues/deletePushQues',
    params: {
      paperId: requestParams.paperId, // String
      quesId: requestParams.questionId, // String
    },
    data: requestParams.deleteQuestionIds, // Array[String],
    requestName: '删除单道题的变式题',
  })
}

export function apiResetPaperRecommendedQuestions(paperId) {
  return ajax.delete({
    url: 'ques/pushQues/deletePaperPushQues',
    params: {
      paperId: paperId, // String
    },
    requestName: '清空原卷变式题',
  })
}

export function apiAddRecommendQuestionsIntoPaper(requestParams) {
  return ajax.post({
    url: 'ques/pushQues/addPushQuesToPaper',
    params: {
      paperId: requestParams.paperId, // String
      quesId: requestParams.questionId, // String
    },
    data: requestParams.addQuestionIds, // Array[String]
    requestName: '添加变式题到原卷',
  })
}

export function apiSyncOriginalPaperRecommendedQuestions(requestParams) {
  return ajax.post({
    url: 'ques/pushQues/syncPaperPushQuesToExamSubject',
    params: {
      paperId: requestParams.paperId, // String
      examSubjectId: requestParams.examSubjectId, // String
    },
    requestName: '同步原卷变式题到考试应用卷',
  })
}

export function apiGetPaperRecommendQuestionsInfo(requestParams) {
  return ajax.get({
    url: 'ques/pushQues/getPaperPushInfo',
    params: {
      paperId: requestParams.paperId,
    },
    requestName: '获取试卷推题情况',
  })
}

export function apiUpdatePaperQuestionStandardStatus(requestParams) {
  return ajax.post({
    url: 'ques/pushQues/updateIsStandardPushQues',
    params: {
      paperId: requestParams.paperId,
      isStandardPushQues: requestParams.isStandardPushQues,
    },
    requestName: '更新试卷标准变式题状态',
  })
}
