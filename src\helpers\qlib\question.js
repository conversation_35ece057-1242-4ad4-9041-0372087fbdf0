import { deepCopy } from '@/utils/object'
import { randomId } from '@/utils/string'
import { normalizeNumber, sumByDefaultZero } from '@/utils/math'
import { groupArray, isUniq } from '@/utils/array'
import {
  getOptionLabelByIndex,
  getOptionIndexByLabel,
  getQuestionTypeValidBranchTypeIds,
} from '@/helpers/qlib/miscellaneous'
import { PaperInfo } from './paper_info'
import { newId } from './id'

import STORE from '@/store/index'

import { fileToBase64 } from '@/utils/file'
import BranchTypeEnum from '@/enum/qlib/branch_type'
import ListeningVoiceGenderEnum from '@/enum/qlib/listening_voice_gender'
import ListeningAudioSourceEnum from '@/enum/qlib/listening_audio_source'
export const MAX_OPTION_LENGTH = 10
export const MAX_BLANK_LENGTH = 20

export class Question {
  constructor() {
    // ID
    this.id = ''
    // 题号
    this.code = 1
    // 别名
    this.alias = ''
    // 学段
    this.stage = {}
    // 学科
    this.subject = {}
    // 题型
    this.questionType = {}
    // 分数
    this.score = 0
    // 组卷次数
    this.citeTimes = 0
    // 作答次数
    this.answerTimes = 0
    // 得分率
    this.scoringRate = 0
    // 难度
    this.difficulty = {}
    // 年份
    this.year = new Date().getFullYear()
    // 关联试卷
    this.referPapers = []
    // 材料
    this.trunk = ''
    // 章节
    this.chapters = []
    // 各小题
    this.branches = []
    // 划题原始内容
    this.rawContent = ''
    // 听力
    this.listening = null
    // 是否已组卷
    this.composed = false
    // 是否收藏
    this.favorite = false
    // 是否报错
    this.ifCorrect = false
    // 是否已保存，修改题目时用
    this.saved = true
  }

  /**
   * 复制 -------------------------------------------------------------------------------------------
   */
  static copy(q) {
    let question = new Question()
    Object.assign(question, deepCopy(q))
    return question
  }

  /**
   * 复制并替换Id
   */
  static async copyAndReplaceId(q) {
    let question = Question.copy(q)
    question.id = await newId()
    if (question.listening) {
      question.listening.questionId = question.id
    }
    for (let branch of question.branches) {
      branch.id = await newId()
      if (branch.listening) {
        branch.listening.questionId = question.id
        branch.listening.branchId = branch.id
      }
    }
    return question
  }

  /**
   * 导入 -------------------------------------------------------------------------------------------
   */
  static import(q) {
    let question = new Question()
    // 题目信息
    let stages = STORE.state.qlib.stages || []
    let difficulties = STORE.state.qlib.difficulties || []

    let stageId = Number(q.stage)
    let subjectId = Number(q.subjectId || q.subject)
    let questionTypeId = Number(q.type || q.quesType)
    let difficultyId = Number(q.difficulty)

    let stage = stages.find(x => x.id === stageId) || {}
    let subject = (stage.subjects || []).find(x => x.id === subjectId) || {}
    let questionTypes = subject.questionTypes || []
    let questionType = questionTypes.find(x => x.id === questionTypeId) || {}
    let difficulty = difficulties.find(x => x.id === difficultyId) || {}

    question.id = q.id || ''
    question.code = Number(q.code) || 10000 // 如果题号不存在，则使用一个很大的数，这样划题时添加题目会加至试卷末尾
    question.score = Number(q.score) || 0
    question.citeTimes = Number(q.useCount) || 0
    question.answerTimes = Number(q.answerCount) || 0
    question.scoringRate = Number(q.scoreRate) || 0
    question.stage = {
      id: stageId,
      name: stage.name || '',
    }
    question.subject = {
      id: subjectId,
      name: subject.name || '',
    }
    question.questionType = questionType
    question.difficulty = difficulty
    question.year = Number(q.year) || new Date().getFullYear()
    question.favorite = Boolean(q.ifFavorite)
    question.ifCorrect = Boolean(q.ifCorrect)
    question.saved = true

    // 划题内容
    question.rawContent = q.divide || ''

    // 试卷信息
    let referPapers = q.referPapers || q.referPaper
    if (Array.isArray(referPapers)) {
      question.referPapers = referPapers.map(PaperInfo.import)
    }

    // 是否被组卷
    question.composed = Boolean(q.used || q.composed)

    // 材料
    question.trunk = q.trunk || ''

    // 章节，要传入题目对应科目的教材
    question.chapters = importChapters(subject.books || [], q.chapters || [])

    // 小题
    let branches = (q.branches || []).map(importBranch).sort((a, b) => a.code - b.code)
    branches.forEach((b, idx) => {
      b.code = idx + 1
    })
    question.branches = branches

    // 听力
    question.importListening(q.listening)

    return question

    function importChapters(books, chapters) {
      return groupArray(chapters, x => x.bookId).map(b => {
        let book = books.find(y => y.id === b.group[0].bookId)
        return {
          bookId: b.group[0].bookId,
          bookName: (book && book.name) || '',
          pressId: (book && book.pressId) || 0,
          pressName: (book && book.pressName) || '',
          chapters: b.group.map(z => ({
            id: z.id,
            name: z.name,
          })),
        }
      })
    }

    function importBranch(branch) {
      // 小题题型
      let branchTypeId = Number(branch.type || branch.questionTypeId)
      if (!BranchTypeEnum.hasId(branchTypeId)) {
        branchTypeId = BranchTypeEnum.Essay.id
      }
      let branchType = BranchTypeEnum.getEntryById(branchTypeId)

      // 处理答案
      branch.solution = String(branch.solution) || ''
      let answer = {
        singleChoice: '',
        multipleChoice: [],
        trueOrFalse: '',
        fillBlank: [],
        essay: '',
      }
      if (branchTypeId === BranchTypeEnum.SingleChoice.id) {
        answer.singleChoice = branch.solution
      } else if (branchTypeId === BranchTypeEnum.MultipleChoice.id) {
        answer.multipleChoice = branch.solution.split('')
      } else if (branchTypeId === BranchTypeEnum.TrueOrFalse.id) {
        answer.trueOrFalse = branch.solution
      } else if (branchTypeId === BranchTypeEnum.FillBlank.id) {
        try {
          let arr = JSON.parse(branch.solution)
          if (arr instanceof Array && arr.every(item => item == null || typeof item === 'string')) {
            arr.forEach(item => {
              answer.fillBlank.push(item || '')
            })
          } else {
            throw new TypeError('填空题答案非数组')
          }
        } catch {
          answer.fillBlank = [branch.solution]
        }
      } else {
        answer.essay = branch.solution
      }

      let r = {
        id: branch.id || '',
        code: Number(branch.code),
        alias: '',
        score: Number(branch.score) || 0,
        branchType,
        stem: branch.stem || '',
        options: branch.options || [],
        answer,
        explanation: branch.explanation || '',
        knowledges: (branch.knowledges || []).map(k => ({
          id: Number(k.id) || 0,
          name: k.name || '',
        })),
        listening: null,
        solution: branch.solution || '',
      }
      return r
    }
  }

  /**
   * 导出
   */
  async export() {
    let exportQuestion = {
      id: this.id,
      stage: this.stage.id,
      subjectId: this.subject.id,
      code: this.code,
      type: this.questionType.id,
      score: this.score,
      difficulty: this.difficulty.id,
      chapters: exportChapters(this.chapters),
      divide: this.rawContent,
      trunk: this.branches.length > 1 ? this.trunk : '',
      branches: this.branches.map(exportBranch),
      year: this.year,
    }

    if (this.subject && this.subject.name === '英语') {
      exportQuestion.listening = await this.exportListening()
    }

    return exportQuestion

    function exportChapters(cpts) {
      let chapters = []
      cpts.forEach(b => {
        chapters.push(
          ...b.chapters.map(c => ({
            id: c.id,
            name: c.name,
            bookId: b.bookId,
          }))
        )
      })
      return chapters
    }

    function exportBranch(branch, idx) {
      let options = branch.branchType.isChoice ? branch.options || [] : null

      let solution
      let branchTypeId = branch.branchType.id
      if (branchTypeId === BranchTypeEnum.SingleChoice.id) {
        solution = branch.answer.singleChoice
      } else if (branchTypeId === BranchTypeEnum.MultipleChoice.id) {
        solution = branch.answer.multipleChoice.sort().join('')
      } else if (branchTypeId === BranchTypeEnum.TrueOrFalse.id) {
        solution = branch.answer.trueOrFalse
      } else if (branchTypeId === BranchTypeEnum.FillBlank.id) {
        solution = JSON.stringify(branch.answer.fillBlank)
      } else {
        solution = branch.answer.essay
      }

      let knowledges = branch.knowledges.map(x => ({
        id: x.id,
        name: x.name,
      }))

      return {
        id: branch.id,
        code: idx + 1,
        score: branch.score,
        type: branchTypeId,
        stem: branch.stem,
        options,
        solution,
        explanation: branch.explanation,
        knowledges,
      }
    }
  }

  /**
   * 创建新题
   */
  static async createNewQuestion() {
    let q = new Question()
    q.id = await newId()
    // 难度默认中等
    q.difficulty = STORE.state.qlib.difficulties.find(x => x.id == 3)
    return q
  }

  /**
   * 题目属性 -----------------------------------------------------------------------------------------
   */
  // 小题是否展开
  getIsBranchesExpanded() {
    return this.code == 0
  }
  // 是否无内容
  getIsEmpty() {
    return !this.trunk && this.branches.every(b => !b.stem)
  }
  // 题号显示文本
  getQuestionCodeString(showAlias) {
    if (this.getIsBranchesExpanded()) {
      let firstBranch = this.branches[0]
      let lastBranch = this.branches[this.branches.length - 1]
      let firstName = showAlias && firstBranch.alias ? firstBranch.alias : firstBranch.code
      let lastName = showAlias && lastBranch.alias ? lastBranch.alias : lastBranch.code
      return `${firstName} - ${lastName}`
    } else {
      return showAlias && this.alias ? this.alias : String(this.code)
    }
  }
  // 小题号显示文本
  getBranchCodeString(branch, showAlias) {
    if (showAlias && branch.alias) {
      // 如果所有小题名均以题名开头，则不显示这个前缀
      let prefix = this.getQuestionCodeString(showAlias) + '.'
      if (this.branches.every(b => b.alias && b.alias.startsWith(prefix))) {
        return branch.alias.substring(prefix.length)
      } else {
        return branch.alias
      }
    } else {
      return String(branch.code)
    }
  }
  // 材料显示内容
  getTrunkContent(showAlias) {
    // 英语中无小题题干的题目，其题号可能隐含在材料中
    // 录题时这些题号均从1开始，显示时需根据题目在试卷中的题号更新
    if (this.subject.name == '英语' && this.code == 0 && this.branches.every(branch => !branch.stem)) {
      let code = 1
      return this.trunk.replace(/[(（]\s*(\d+)\s*[)）]/g, (match, p1) => {
        if (p1 == code && code <= this.branches.length) {
          let branch = this.branches[code - 1]
          let codeStr = this.getBranchCodeString(branch, showAlias)
          code++
          return ` ${codeStr} `
        } else {
          code = null
          return match
        }
      })
    }
    return this.trunk
  }
  /**
   * 由考试科目题目更新题号别名分数，考试科目题目需先排序
   * @param {*} q 题库题目
   * @param {*} subjectQuestions 对应考试科目题目
   */
  updateQuestionCodeAliasScore(q, subjectQuestions) {
    let branchesExpanded = subjectQuestions.length > 1 && subjectQuestions.every(b => b.branchCode == 0)
    let first = subjectQuestions[0]
    let last = subjectQuestions[subjectQuestions.length - 1]
    q.code = branchesExpanded ? 0 : first.questionCode
    if (branchesExpanded) {
      q.alias = `${first.questionName || first.questionCode} - ${last.questionName || last.questionCode}`
    } else {
      q.alias = first.questionName || String(first.questionCode)
    }
    q.branches.forEach(b => {
      let branch = subjectQuestions.find(x => x.branchId == b.id)
      if (branch) {
        b.code = branchesExpanded ? branch.questionCode : branch.branchCode
        b.alias = branchesExpanded ? branch.questionName : branch.branchName
        b.score = branch.fullScore || 0
      } else {
        b.alias = ''
        b.score = 0
      }
    })
    q.score = q.branches.reduce((a, c) => a + c.score, 0)
  }

  /**
   * 拆分与合并 ----------------------------------------------------------------------------------------
   */
  // 每个小题拆分成独立一题
  static async split(q) {
    let newQuestions = []
    let idx = 0
    for (let branch of q.branches) {
      let newBranch = deepCopy(branch)
      newBranch.id = await newId()
      let newQuestion = await Question.createNewQuestion()
      newQuestion.code = q.code + idx++
      newQuestion.stage = deepCopy(q.stage)
      newQuestion.subject = deepCopy(q.subject)
      newQuestion.questionType = deepCopy(q.questionType)
      newQuestion.score = branch.score
      newQuestion.difficulty = deepCopy(q.difficulty)
      newQuestion.year = q.year
      newQuestion.chapters = deepCopy(q.chapters)
      newQuestion.branches = [newBranch]
      newQuestions.push(newQuestion)
    }
    return newQuestions
  }
  // 小学数学填空题解答题一个小题拆分为多个小题
  async divideBranches() {
    // 拆出来的小题只有题干，没有答案和解析
    let title = '' // 标题
    let knowledges = this.branches[0].knowledges || []
    let branchExplanation = this.branches[0].explanation
    let newBranchesStems = this.branches[0].stem
    let isTableStructure = /<td.*?\/td>/.test(newBranchesStems)

    if (isTableStructure) {
      title = newBranchesStems.slice(0, newBranchesStems.indexOf('<table')) || ''
      newBranchesStems = (newBranchesStems.match(/<td.+?\/td>/g) || []).map(stem =>
        stem
          .replace(/<td.*?>/, '')
          .replace(/<\/td>/, '')
          .replace(/（\d）/, '')
      )
    } else {
      newBranchesStems = newBranchesStems
        .replace(/&nbsp;|<p>|<\/p>/g, ' ')
        .split(' ')
        .filter(String)

      title = newBranchesStems.filter(x => !(x && (x.includes('＝') || x.includes('='))))[0] || ''
      title = (title && title + '<br>') || ''

      newBranchesStems = newBranchesStems.filter(x => x && (x.includes('＝') || x.includes('=')))
    }

    if (newBranchesStems && newBranchesStems.length) {
      let branchScore =
        (this.branches[0].score && this.branches[0].score / newBranchesStems.length) ||
        (this.score && this.score / newBranchesStems.length) ||
        0

      let newBranches = []
      for (let sdx = 0; sdx < newBranchesStems.length; sdx++) {
        let stem = newBranchesStems[sdx]
        let newBranch = {
          id: await newId(),
          code: sdx + 1,
          branchType: deepCopy(this.branches[0].branchType),
          score: branchScore,
          stem: sdx ? stem : title + stem,
          options: [],
          answer: !sdx
            ? this.branches[0].answer
            : {
                singleChoice: '',
                multipleChoice: [],
                trueOrFalse: '',
                fillBlank: [''],
                essay: '',
              },
          explanation: !sdx ? branchExplanation : '',
          knowledges: knowledges,
          listening: null,
        }
        newBranches.push(newBranch)
      }

      this.branches = newBranches
      this.updateQuestionScore()
      this.saved = false
    } else {
      throw '题目结构不支持拆分'
    }
  }
  // 填空题、解答题多个小题合并为一个小题
  async mergeBranches() {
    // 所有小题均为填空题，则合并为填空题，否则合并为解答题
    let branchType
    if (this.branches.every(b => b.branchType.id == BranchTypeEnum.FillBlank.id)) {
      branchType = deepCopy(BranchTypeEnum.FillBlank)
    } else {
      branchType = deepCopy(BranchTypeEnum.Essay)
    }

    // 拼接各小题内容函数
    const mergeContents = contents => {
      let notEmptyContents = contents
        .map((content, idx) => ({
          code: idx + 1,
          content,
        }))
        .filter(item => item.content)
      if (notEmptyContents.length == 0) {
        return ''
      } else if (notEmptyContents.length == 1) {
        return notEmptyContents[0].content
      }

      let text = ''
      notEmptyContents.forEach(item => {
        if (text && !text.endsWith('</p>') && !item.content.startsWith('<p>')) {
          text += '<br>'
        }
        let codeText = `（${item.code}）`
        if (item.content.startsWith('<p>')) {
          text += `<p>${codeText}${item.content.substring(3)}`
        } else {
          text += `${codeText}${item.content}`
        }
      })

      return text
    }

    // 合并题干
    let mergedStem = mergeContents(this.branches.map(b => b.stem))
    if (this.trunk) {
      // 材料添加到小题前
      mergedStem = `${this.trunk}${this.trunk.endsWith('</p>') ? '' : '<br>'}${mergedStem}`
      this.trunk = ''
    }

    // 合并答案
    let mergedAnswer = {
      singleChoice: '',
      multipleChoice: [],
      trueOrFalse: '',
      fillBlank: [],
      essay: '',
    }
    if (branchType.id == BranchTypeEnum.FillBlank.id) {
      mergedAnswer.fillBlank = this.branches.map(b => b.answer.fillBlank).flat()
    } else {
      let answerContents = this.branches.map(b => {
        if (b.branchType.id == BranchTypeEnum.FillBlank.id) {
          return b.answer.fillBlank.join('&nbsp;&nbsp;&nbsp;&nbsp;')
        } else {
          return b.answer.essay
        }
      })
      mergedAnswer.essay = mergeContents(answerContents)
    }

    // 合并解析
    let mergedExplanation = mergeContents(this.branches.map(b => b.explanation))

    // 合并知识点
    let mergedKnowledges = []
    this.branches.forEach(b =>
      b.knowledges.forEach(bk => {
        if (!mergedKnowledges.some(mk => mk.id == bk.id)) {
          mergedKnowledges.push(bk)
        }
      })
    )

    // 合并分数
    let mergedScore = sumByDefaultZero(this.branches, b => b.score)

    this.branches = [
      {
        id: await newId(),
        code: 1,
        branchType,
        score: mergedScore,
        options: [],
        stem: mergedStem,
        answer: mergedAnswer,
        explanation: mergedExplanation,
        knowledges: mergedKnowledges,
        listening: null,
      },
    ]

    this.saved = false
  }

  /**
   * 检查 -------------------------------------------------------------------------------------------
   */
  // 检查题目是否完整
  checkIntegrity(config) {
    if (!this.branches.length) {
      return '没有题目'
    }

    for (let i = 0, len = this.branches.length; i < len; i++) {
      let checkBranchResult = this.checkBranch(
        this.branches[i],
        this.branches.length === 1 ? '' : `第${this.branches[i].code}小题`,
        config
      )
      if (checkBranchResult) {
        return checkBranchResult
      }
    }

    if (!(this.questionType.id > 0) || this.questionType.name === '未知') {
      return '未选择题型'
    }

    if (!this.trunk && this.branches.every(branch => !branch.stem)) {
      return '没有材料或题干'
    }

    if (!(config && config.noDifficulty) && !(this.difficulty.id > 0)) {
      return '未选择难度'
    }

    // 检查章节重复
    let chapterIds = []
    for (let book of this.chapters) {
      for (let item of book.chapters) {
        if (chapterIds.includes(item.id)) {
          return '章节重复'
        }
        chapterIds.push(item.id)
      }
    }

    return this.checkListening(this.listening)
  }
  // 检查小题
  checkBranch(branch, prefix, config) {
    let { id: branchTypeId, isChoice } = branch.branchType
    let { branchHasStem, branchHasOptions } = this.questionType
    if (!(branchTypeId > 0)) {
      return `${prefix}未选择题型`
    }
    if (branchHasStem && !branch.stem) {
      return `${prefix}没有题干`
    }
    if (isChoice && branch.options.length === 0) {
      return `${prefix}没有选项`
    }
    if (branchHasOptions && branch.options.some(x => !x.content)) {
      return `${prefix}部分选项为空`
    }

    let noAnswer = false
    let invalidAnswer = false
    let { singleChoice, multipleChoice, trueOrFalse, fillBlank, essay } = branch.answer
    if (branchTypeId == BranchTypeEnum.SingleChoice.id) {
      if (!singleChoice) {
        noAnswer = true
      } else if (!this.checkAnswer(branch, singleChoice)) {
        invalidAnswer = true
      }
    } else if (branchTypeId == BranchTypeEnum.MultipleChoice.id) {
      if (multipleChoice.length == 0 || multipleChoice.some(choice => !choice)) {
        noAnswer = true
      } else if (!this.checkAnswer(branch, multipleChoice)) {
        invalidAnswer = true
      }
    } else if (branchTypeId == BranchTypeEnum.TrueOrFalse.id) {
      if (!trueOrFalse) {
        noAnswer = true
      } else if (!this.checkAnswer(branch, trueOrFalse)) {
        invalidAnswer = true
      }
    } else if (branchTypeId == BranchTypeEnum.FillBlank.id) {
      if (fillBlank.length === 0 || fillBlank.some(blank => !blank)) {
        noAnswer = true
      } else if (!this.checkAnswer(branch, fillBlank)) {
        invalidAnswer = true
      }
    } else {
      if (!essay) {
        noAnswer = true
      } else if (!this.checkAnswer(branch, essay)) {
        invalidAnswer = true
      }
    }
    if (noAnswer) {
      return `${prefix}没有答案`
    } else if (invalidAnswer) {
      return `${prefix}答案格式错误`
    }

    if (!(config && config.noKnowledge) && branch.knowledges.length === 0) {
      return `${prefix}未选择知识点`
    }

    return this.checkListening(branch.listening, prefix)
  }
  // 检查答案是否有效
  checkAnswer(branch, answer) {
    if (!answer) {
      return false
    }
    let branchTypeId = branch.branchType.id
    if (branchTypeId === BranchTypeEnum.SingleChoice.id) {
      if (typeof answer !== 'string') {
        return false
      }
      return isChoiceAnswerValid([answer], branch.options.length)
    } else if (branchTypeId === BranchTypeEnum.MultipleChoice.id) {
      if (!(answer instanceof Array) || answer.some(char => typeof char !== 'string')) {
        return false
      }
      return isChoiceAnswerValid(answer, branch.options.length)
    } else if (branchTypeId === BranchTypeEnum.TrueOrFalse.id) {
      return answer === 'T' || answer === 'F'
    } else if (branchTypeId === BranchTypeEnum.FillBlank.id) {
      if (!(answer instanceof Array) || answer.length === 0 || answer.length > MAX_BLANK_LENGTH) {
        return false
      }
      return answer.every(char => typeof char === 'string' && char !== 'null' && char !== 'undefined')
    } else {
      return typeof answer === 'string' && answer !== 'null' && answer !== 'undefined'
    }

    function isChoiceAnswerValid(answerChars, optionCount) {
      let maxAnswerChar = String.fromCharCode(65 + optionCount)
      return isUniq(answerChars) && answerChars.every(char => char.length == 1 && char >= 'A' && char < maxAnswerChar)
    }
  }

  /**
   * 修改题目 -----------------------------------------------------------------------------------------
   */
  // 改材料
  changeTrunk(trunk) {
    this.trunk = trunk
    this.saved = false
  }
  // 改难度
  changeDifficulty(id) {
    let difficulties = STORE.state.qlib.difficulties || []
    let finding = difficulties.find(x => x.id === id)
    if (finding) {
      this.difficulty = {
        id: finding.id,
        name: finding.name,
      }
      this.saved = false
      return true
    } else {
      return false
    }
  }
  // 改分数
  changeScore(score) {
    score = normalizeNumber(score, 0, 99.9, 1)
    this.score = score
    if (this.branches.length === 1) {
      this.branches[0].score = score
    }
    this.saved = false
  }
  // 更新分数
  updateQuestionScore() {
    let score = this.branches.reduce((a, c) => a + (c.score || 0), 0)
    if (this.score !== score) {
      this.score = score
      this.saved = false
    }
  }
  // 添加章节
  addChapter({ bookId, bookName, pressId, pressName, id, name }) {
    if (!(bookId && bookName && pressId && pressName && id && name)) {
      return false
    }

    let book = this.chapters.find(x => x.bookId === bookId)
    if (book) {
      if (book.chapters.every(x => x.id != id)) {
        book.chapters.push({
          id,
          name,
        })
        this.saved = false
      }
    } else {
      this.chapters.push({
        bookId,
        bookName,
        pressId,
        pressName,
        chapters: [
          {
            id,
            name,
          },
        ],
      })
      this.saved = false
    }
    return true
  }
  // 删除章节
  removeChapter({ bookId, id }) {
    let finding = this.chapters.find(x => x.bookId === bookId)
    let bookChapters = (finding && finding.chapters) || []
    let chapterIndex = bookChapters.findIndex(x => x.id === id)
    if (chapterIndex >= 0) {
      bookChapters.splice(chapterIndex, 1)
      if (bookChapters.length === 0) {
        let bookIndex = this.chapters.indexOf(finding)
        this.chapters.splice(bookIndex, 1)
      }
      this.saved = false
      return true
    } else {
      return false
    }
  }
  // 获取小题
  getBranch(branch) {
    let code = typeof branch === 'object' ? branch.code : branch
    return this.branches.find(x => x.code === code)
  }
  // 添加小题
  async addBranch(lastBranch) {
    if (!lastBranch) {
      lastBranch = this.branches[this.branches.length - 1]
    }
    let insertIndex = this.branches.indexOf(lastBranch) + 1

    let code = (lastBranch && lastBranch.code + 1) || 1

    let branchType
    if (lastBranch) {
      branchType = deepCopy(lastBranch.branchType)
    } else {
      branchType = BranchTypeEnum.getEntryById(this.questionType.branchTypeId)
    }

    let score = (lastBranch && lastBranch.score) || 0

    let optionLength
    if (branchType.isChoice) {
      optionLength = (lastBranch && lastBranch.branchType.isChoice && lastBranch.options.length) || 4
    } else {
      optionLength = 0
    }
    let options = []
    for (let i = 0; i < optionLength; i++) {
      options.push({
        label: getOptionLabelByIndex(i),
        content: '',
      })
    }

    let answer = {
      singleChoice: '',
      multipleChoice: [],
      trueOrFalse: '',
      fillBlank: [],
      essay: '',
    }
    if (branchType.id === BranchTypeEnum.FillBlank.id) {
      let blanksCount = (lastBranch && lastBranch.answer.fillBlank.length) || 1
      for (let i = 0; i < blanksCount; i++) {
        answer.fillBlank.push('')
      }
    }

    let newBranch = {
      id: await newId(),
      code,
      branchType,
      score,
      stem: '',
      options,
      answer,
      explanation: '',
      knowledges: [],
      listening: null,
    }
    this.branches.splice(insertIndex, 0, newBranch)

    for (let i = insertIndex + 1; i < this.branches.length; i++) {
      this.branches[i].code++
    }

    this.updateQuestionScore()
    this.saved = false
  }
  // 删除小题
  removeBranch(branch) {
    let branchCode = typeof branch === 'object' ? branch.code : branch
    let branchIndex = this.branches.findIndex(b => b.code === branchCode)
    if (branchIndex >= 0) {
      this.branches.splice(branchIndex, 1)
      for (let i = branchIndex; i < this.branches.length; i++) {
        this.branches[i].code--
      }
      // 删除到最后一小题时复制材料到题干
      if (this.branches.length == 1 && this.trunk) {
        this.branches[0].stem = this.trunk + this.branches[0].stem
        this.trunk = ''
      }
      this.updateQuestionScore()
      this.saved = false
    }
  }
  // 改小题号
  changeBranchCodes(changes) {
    if (!changes && changes.length === 0) {
      return
    }

    let changeBranches = changes
      .map(({ oldCode, newCode }) => {
        return {
          oldCode,
          newCode,
          branch: this.branches.find(x => x.code === oldCode),
        }
      })
      .filter(x => x.branch)
    changeBranches.forEach(item => {
      item.branch.code = item.newCode
    })

    this.branches.sort((a, b) => a.code - b.code)
    this.saved = false
  }
  // 改题干
  changeBranchStem(branch, value) {
    branch = this.getBranch(branch)
    if (!branch) {
      return
    }

    branch.stem = value
    this.saved = false
  }
  // 改选项
  changeBranchOption(branch, optionLabel, value) {
    branch = this.getBranch(branch)
    if (!branch) {
      return
    }

    if (optionLabel && typeof optionLabel === 'object') {
      optionLabel = optionLabel.label
    }
    let option = branch.options.find(x => x.label === optionLabel)
    if (!option) {
      return
    }

    option.content = value
    this.saved = false
  }
  // 改选项长度
  changeBranchOptionLength(branch, optionLength) {
    branch = this.getBranch(branch)
    if (!branch) {
      return
    }

    optionLength = normalizeNumber(optionLength, 2, MAX_OPTION_LENGTH, 0)

    if (branch.options.length < optionLength) {
      // 此处选项要重新生成，部分题目选项错乱，如只有B、C，若直接添加选项则导致错误
      let newOptions = []
      for (let i = 0; i < optionLength; i++) {
        let label = getOptionLabelByIndex(i)
        let oldOption = branch.options.find(opt => opt.label === label)
        let content = oldOption ? oldOption.content : ''

        newOptions.push({
          label,
          content,
        })
      }
      branch.options = newOptions
    } else if (branch.options.length > optionLength) {
      branch.options = branch.options.slice(0, optionLength)
      if (branch.branchType.id === BranchTypeEnum.SingleChoice.id) {
        if (getOptionIndexByLabel(branch.answer.singleChoice) >= optionLength) {
          branch.answer.singleChoice = ''
        }
      } else {
        branch.answer.multipleChoice = branch.answer.multipleChoice.filter(c => getOptionIndexByLabel(c) < optionLength)
      }
    }
    this.saved = false
  }
  // 改答案
  changeBranchAnswer(branch, answer, order) {
    branch = this.getBranch(branch)
    if (!branch || !answer) {
      return
    }
    // 若传入不合规的答案，则将答案清空，这样用户可以重新修改答案
    let branchTypeId = branch.branchType.id
    if (branchTypeId === BranchTypeEnum.SingleChoice.id) {
      let answerSingleChoice = String(answer).toUpperCase()
      if (!this.checkAnswer(branch, answerSingleChoice)) {
        answerSingleChoice = ''
      }
      branch.answer.singleChoice = answerSingleChoice
    } else if (branchTypeId === BranchTypeEnum.MultipleChoice.id) {
      let answerMultipleChoice = answer instanceof Array ? answer : String(answer).split('')
      answerMultipleChoice = answerMultipleChoice.map(x => String(x).toUpperCase())
      if (!this.checkAnswer(branch, answerMultipleChoice)) {
        answerMultipleChoice = []
      }
      branch.answer.multipleChoice = answerMultipleChoice
    } else if (branchTypeId === BranchTypeEnum.TrueOrFalse.id) {
      let answerTrueOrFalse = String(answer).toUpperCase()
      if (!this.checkAnswer(branch, answerTrueOrFalse)) {
        answerTrueOrFalse = ''
      }
      branch.answer.trueOrFalse = answerTrueOrFalse
    } else if (branchTypeId === BranchTypeEnum.FillBlank.id) {
      if (!(order >= 0 && order < branch.answer.fillBlank.length)) {
        return
      }
      let answerFillBlanks = branch.answer.fillBlank.slice()
      answerFillBlanks[order] = String(answer).replace('\\n', '').replace('<p>', '').replace('</p>', '').trim()
      if (!this.checkAnswer(branch, answerFillBlanks)) {
        answerFillBlanks[order] = ''
      }
      branch.answer.fillBlank = answerFillBlanks
    } else {
      let answerEssay = String(answer)
      if (!this.checkAnswer(branch, answerEssay)) {
        answerEssay = ''
      }
      branch.answer.essay = answerEssay
    }
    this.saved = false
  }
  // 改填空个数
  changeBranchBlankLength(branch, blankLength) {
    branch = this.getBranch(branch)
    if (!branch) {
      return
    }

    blankLength = normalizeNumber(blankLength, 1, MAX_BLANK_LENGTH, 0)

    if (branch.answer.fillBlank.length < blankLength) {
      for (let i = branch.answer.fillBlank.length; i < blankLength; i++) {
        branch.answer.fillBlank.push('')
      }
    } else if (branch.answer.fillBlank.length > blankLength) {
      branch.answer.fillBlank = branch.answer.fillBlank.slice(0, blankLength)
    }
    this.saved = false
  }
  // 改分数
  changeBranchScore(branch, num) {
    branch = this.getBranch(branch)
    if (!branch) {
      return
    }

    branch.score = normalizeNumber(num, 0, 99.9, 1)
    this.updateQuestionScore()
    this.saved = false
  }
  // 改解析
  changeBranchExplanation(branch, value) {
    branch = this.getBranch(branch)
    if (!branch) {
      return
    }

    branch.explanation = value
    this.saved = false
  }
  // 改知识点
  changeBranchKnowledges(branch, value) {
    branch = this.getBranch(branch)
    if (!branch || !value || !(value instanceof Array)) {
      return
    }

    branch.knowledges = value.map(x => ({
      id: x.id,
      name: x.name,
    }))
    this.saved = false
  }
  // 删除知识点
  removeBranchKnowledge(branch, value) {
    branch = this.getBranch(branch)
    if (!branch || !value) {
      return
    }

    let knowledgeId = typeof value === 'object' ? Number(value.id) : Number(value)
    let index = branch.knowledges.findIndex(x => x.id === knowledgeId)
    if (index >= 0) {
      branch.knowledges.splice(index, 1)
    }
    this.saved = false
  }
  // 改小题题型
  changeBranchType(branch, branchTypeId) {
    branch = this.getBranch(branch)
    if (!branch || !branchTypeId) {
      return
    }
    let oldTypeId = branch.branchType.id
    let newTypeId = branchTypeId
    if (oldTypeId == newTypeId) {
      return
    }
    if (!getQuestionTypeValidBranchTypeIds(this.questionType).includes(newTypeId)) {
      return
    }
    let newType = BranchTypeEnum.getEntryById(newTypeId)
    let oldType = branch.branchType

    branch.branchType = newType
    this.saved = false

    // 处理选项
    if (oldType.isChoice !== newType.isChoice) {
      if (!newType.isChoice) {
        branch.options.forEach(item => {
          branch.stem += `<p>${item.label}.${item.content}</p>`
        })
        branch.options = []
      } else {
        let branchIndex = this.branches.indexOf(branch)
        let previousBranch = this.branches[branchIndex - 1]
        let optionsLength = (previousBranch && previousBranch.options.length) || 4
        branch.options = []
        for (let i = 0; i < optionsLength; i++) {
          branch.options.push({
            label: getOptionLabelByIndex(i),
            content: '',
          })
        }
      }
    }

    // 处理答案
    if (
      [oldTypeId, newTypeId].every(id =>
        [BranchTypeEnum.Essay.id, BranchTypeEnum.ChineseWriting.id, BranchTypeEnum.EnglishWriting.id].includes(id)
      )
    ) {
      return
    }
    let oldAnswer = branch.answer
    let newAnswer = {
      singleChoice: '',
      multipleChoice: [],
      trueOrFalse: '',
      fillBlank: [],
      essay: '',
    }
    if (newTypeId === BranchTypeEnum.SingleChoice.id) {
      let char = ''
      if (oldTypeId === BranchTypeEnum.MultipleChoice.id) {
        char = oldAnswer.multipleChoice[0]
      } else if (oldTypeId === BranchTypeEnum.TrueOrFalse.id) {
        char = oldAnswer.trueOrFalse
      } else if (oldTypeId === BranchTypeEnum.FillBlank.id) {
        char = oldAnswer.fillBlank[0]
      } else {
        char = oldAnswer.essay
      }
      if (this.checkAnswer(branch, char)) {
        newAnswer.singleChoice = char
      }
    } else if (newTypeId === BranchTypeEnum.MultipleChoice.id) {
      let chars = []
      if (oldTypeId === BranchTypeEnum.SingleChoice.id) {
        chars = [oldAnswer.singleChoice]
      } else if (oldTypeId === BranchTypeEnum.TrueOrFalse.id) {
        chars = [oldAnswer.trueOrFalse]
      } else if (oldTypeId === BranchTypeEnum.FillBlank.id) {
        chars = oldAnswer.fillBlank
      } else {
        chars = oldAnswer.essay && oldAnswer.essay.length <= MAX_OPTION_LENGTH ? oldAnswer.essay.split('') : []
      }
      if (this.checkAnswer(branch, chars)) {
        newAnswer.multipleChoice = chars
      }
    } else if (newTypeId === BranchTypeEnum.TrueOrFalse.id) {
      let char = ''
      if (oldTypeId === BranchTypeEnum.SingleChoice.id) {
        char = oldAnswer.singleChoice
      } else if (oldTypeId === BranchTypeEnum.MultipleChoice.id) {
        char = oldAnswer.multipleChoice[0]
      } else if (oldTypeId === BranchTypeEnum.FillBlank.id) {
        char = oldAnswer.fillBlank[0]
      } else {
        char = oldAnswer.essay
      }
      if (this.checkAnswer(branch, char)) {
        newAnswer.trueOrFalse = char
      }
    } else if (newTypeId === BranchTypeEnum.FillBlank.id) {
      let blanks = []
      if (oldTypeId === BranchTypeEnum.SingleChoice.id) {
        blanks = [oldAnswer.singleChoice]
      } else if (oldTypeId === BranchTypeEnum.MultipleChoice.id) {
        blanks = oldAnswer.multipleChoice
      } else if (oldTypeId === BranchTypeEnum.TrueOrFalse.id) {
        blanks = [oldAnswer.trueOrFalse]
      } else {
        let essay = (oldAnswer.essay || '')
          .replace(/^<p[^>]*>(.*)<\/p>$/, '$1')
          .replace(/(&nbsp;){4,}/, '&nbsp;&nbsp;&nbsp;&nbsp;')
        blanks = essay.split('&nbsp;&nbsp;&nbsp;&nbsp;').filter(Boolean)
      }
      if (this.checkAnswer(branch, blanks)) {
        newAnswer.fillBlank = blanks
      }
    } else {
      let essay = ''
      if (oldTypeId === BranchTypeEnum.SingleChoice.id) {
        essay = oldAnswer.singleChoice
      } else if (oldTypeId === BranchTypeEnum.MultipleChoice.id) {
        essay = oldAnswer.multipleChoice.join('')
      } else if (oldTypeId === BranchTypeEnum.TrueOrFalse.id) {
        essay = oldAnswer.trueOrFalse
      } else if (oldTypeId === BranchTypeEnum.FillBlank.id) {
        essay = oldAnswer.fillBlank.join('&nbsp;&nbsp;&nbsp;&nbsp;')
      }
      if (this.checkAnswer(branch, essay)) {
        newAnswer.essay = essay
      }
    }
    branch.answer = newAnswer
  }
  // 改题型能否不改变小题题型
  canChangeQuestionTypeSeamlessly(qTypeId) {
    let questionTypes = STORE.getters['qlib/questionTypesByStageSubject'](this.stage.id, this.subject.id)
    let toType = questionTypes.find(x => x.id === Number(qTypeId))
    if (!toType) {
      return false
    }
    let validBranchTypeIds = getQuestionTypeValidBranchTypeIds(toType)
    return this.branches.every(branch => validBranchTypeIds.includes(branch.branchType.id))
  }
  // 改题型
  changeQuestionType(qTypeId) {
    let questionTypes = STORE.getters['qlib/questionTypesByStageSubject'](this.stage.id, this.subject.id)
    let toType = questionTypes.find(x => x.id === Number(qTypeId))
    if (toType) {
      toType = deepCopy(toType)
    } else {
      return
    }

    this.questionType = toType
    let validBranchTypeIds = getQuestionTypeValidBranchTypeIds(this.questionType)
    this.branches.forEach(branch => {
      if (!validBranchTypeIds.includes(branch.branchType.id)) {
        this.changeBranchType(branch, validBranchTypeIds[0])
      }
    })

    this.saved = false
  }

  /**
   * 听力 -------------------------------------------------------------------------------------------
   */
  findListeningBranch(branchId) {
    if (branchId == '0') {
      return this
    } else {
      return this.branches.find(x => x.id == branchId)
    }
  }
  findListening(branchId) {
    let branch = this.findListeningBranch(branchId)
    return branch && branch.listening
  }
  // 新增
  addEmptyListening(branchId) {
    let branch = this.findListeningBranch(branchId)
    if (!branch || branch.listening) {
      return
    }
    branch.listening = {
      id: randomId(),
      questionId: this.id,
      branchId,
      material: '',
      audioUrl: '',
      repeatCount: 1,
      audioSource: ListeningAudioSourceEnum.No.id,
      speechRate: this.stage.id == 1 ? 100 : this.stage.id == 2 ? 120 : 140,
      voiceGender: ListeningVoiceGenderEnum.Man.id,
      file: null,
      backup: null,
      enableEdit: true,
      deleted: false,
    }
    this.saved = false
  }
  //修改
  enableEditListening(branchId) {
    let listening = this.findListening(branchId)
    if (!listening) {
      return
    }
    listening.enableEdit = true
    this.saved = false
  }
  // 删除
  deleteListening(branchId) {
    let branch = this.findListeningBranch(branchId)
    if (!branch || !branch.listening) {
      return
    }
    // 已保存的标记删除
    if (branch.listening.backup) {
      branch.listening.deleted = true
      this.saved = false
    }
    // 新增的直接删除
    else {
      branch.listening = null
    }
  }
  // 恢复
  recoverListening(branchId) {
    let listening = this.findListening(branchId)
    if (!listening || !listening.backup) {
      return
    }
    Object.assign(listening, listening.backup)
    listening.enableEdit = false
    listening.deleted = false
  }
  // 修改听力属性
  changeListeningProperty(branchId, key, value) {
    let listening = this.findListening(branchId)
    if (!listening || !listening.enableEdit) {
      return
    }
    listening[key] = value
  }
  changeListeningMaterial(branchId, material) {
    this.changeListeningProperty(branchId, 'material', material)
  }
  changeListeningRepeatCount(branchId, repeatCount) {
    this.changeListeningProperty(branchId, 'repeatCount', repeatCount)
  }
  changeListeningAudioSource(branchId, audioSource) {
    this.changeListeningProperty(branchId, 'audioSource', audioSource)
  }
  changeListeningSpeechRate(branchId, speechRate) {
    this.changeListeningProperty(branchId, 'speechRate', speechRate)
  }
  changeListeningVoiceGender(branchId, voiceGender) {
    this.changeListeningProperty(branchId, 'voiceGender', voiceGender)
  }
  changeListeningFile(branchId, file) {
    this.changeListeningProperty(branchId, 'file', file)
  }
  // 检查听力属性是否完整
  checkListening(listening, prefix = '') {
    if (!listening) {
      return ''
    }
    if (listening.backup) {
      if (listening.deleted || !listening.enableEdit) {
        return ''
      }
    }
    if (!listening.material) {
      return `${prefix}请输入听力材料`
    }
    if (!(listening.repeatCount >= 1)) {
      return `${prefix}请输入重播次数`
    }
    if (listening.audioSource == ListeningAudioSourceEnum.Upload.id && !listening.file) {
      return `${prefix}请选择听力音频文件`
    }
    if (listening.audioSource == ListeningAudioSourceEnum.TTS.id) {
      if (!(listening.speechRate >= 100 && listening.speechRate <= 300)) {
        return '语速应在每分钟100词至300词之间'
      }
      if (!ListeningVoiceGenderEnum.hasId(listening.voiceGender)) {
        return '声音性别错误'
      }
    }
  }
  // 导入
  importListening(listening) {
    if (!listening) {
      listening = []
    }
    listening.forEach(x => {
      x.branchId = x.smallQuestionId
      delete x.smallQuestionId
      x.file = null

      // 备份，以便修改后恢复
      x.backup = Object.assign({}, x)
      // 是否开启修改
      x.enableEdit = false
      // 是否已删除
      x.deleted = false
    })
    this.listening = listening.find(x => x.branchId == '0') || null
    this.branches.forEach(b => {
      b.listening = listening.find(x => x.branchId == b.id) || null
    })
  }
  async exportListening() {
    let allListening = []
    if (this.branches.length > 1) {
      allListening.push(this.listening)
    }
    this.branches.forEach(b => {
      allListening.push(b.listening)
    })
    let saveListening = allListening.filter(listening => listening && !listening.deleted)
    let result = []
    for (let x of saveListening) {
      let item = {
        questionId: x.questionId,
        smallQuestionId: x.branchId,
        material: x.material,
        audioUrl: '',
        repeatCount: x.repeatCount,
        audioSource: x.audioSource,
        speechRate: x.audioSource == ListeningAudioSourceEnum.Upload.id ? '' : x.speechRate,
        voiceGender: x.audioSource == ListeningAudioSourceEnum.Upload.id ? '' : x.voiceGender,
      }
      if (x.audioSource == ListeningAudioSourceEnum.Upload.id && x.file) {
        item.audioFileBase64 = await fileToBase64(x.file)
      }
      result.push(item)
    }
    return result
  }

  /**
   * 收藏 -------------------------------------------------------------------------------------------
   */
  favor() {
    this.favorite = true
  }
  unFavor() {
    this.favorite = false
  }

  /**
   * 报错 -------------------------------------------------------------------------------------------
   */
  changeCorrect(status) {
    this.ifCorrect = Boolean(status)
  }
}
