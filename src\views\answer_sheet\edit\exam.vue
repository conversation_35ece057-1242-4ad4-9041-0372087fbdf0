<template>
  <SheetEdit
    :answer-sheet="answerSheet"
    :source-type="sourceType"
    :source-id="examSubjectId"
    @save-sheet="saveSheet"
    @finish-sheet="finishSheet"
    @page-back="backToExamPaper"
  ></SheetEdit>

  <ModalChangeContent
    v-model="showModalChangeContent"
    :check-result="checkResult"
    title="完成制作将引起以下更改，请您确认"
    @cancel="handleModalChangeContentCancel"
    @ok="handleModalChangeContentOK"
  ></ModalChangeContent>
</template>

<script>
  import SheetEdit from './edit'
  import ModalChangeContent from '@/views/scan_template/components/modal_change_content.vue'

  import {
    apiGetExamById,
    apiGetObjectiveQuestions,
    apiGetSubjectiveQuestions,
    apiGetBindPaperContent,
    apiGetBlocks,
    apiGetSubjectScanTemplates,
    apiGetScanTemplateForEdit,
    apiSaveScanTemplate,
    apiCheckScanTemplateFinish,
    apiFinishScanTemplate,
  } from '@/api/emarking'

  import {
    parseQuestionsAndBlocks,
    isSameAnswerSheetPaperStructure,
    isSameAnswerSheetBlocks,
    updateQuestionNameScoreAnswerBlockName,
    reOrderBlocksJson,
  } from '@/helpers/answer_sheet/util'
  import { mapMutations } from 'vuex'
  import { sleep } from '@/utils/promise'

  import ModeEnum from '@/enum/answer_sheet/mode'
  import SourceTypeEnum from '@/enum/answer_sheet/source_type'
  import SubjectAnswerSheetTypeEnum from '@/enum/emarking/subject_answer_sheet_type'
  import { SheetVersion } from '@/const/answer_sheet'

  export default {
    components: {
      SheetEdit,
      ModalChangeContent,
    },
    data() {
      return {
        // 考试信息
        exam: null,
        // 科目客观题
        objectiveDefines: [],
        // 科目主观题
        subjectiveDefines: [],
        // 科目题块
        blockDefines: [],
        // 科目绑定试卷
        paper: null,
        // 科目答题卡列表
        subjectScanTemplates: [],
        // 当前答题卡
        answerSheet: null,
        // 应用答题卡检查
        showModalChangeContent: false,
        checkResult: null,
      }
    },
    computed: {
      examId() {
        return this.$route.params.examId
      },
      examSubjectId() {
        return this.$route.params.examSubjectId
      },
      answerSheetId() {
        let id = this.$route.params.answerSheetId
        if (id == 'new') {
          id = ''
        }
        return id
      },
      isNewAnswerSheet() {
        return !this.answerSheetId
      },
      examSubject() {
        if (!this.exam) {
          return null
        }
        return this.exam.subjects.find(s => s.examSubjectId === this.examSubjectId)
      },
      examSubjectName() {
        if (!this.examSubject) {
          return ''
        } else {
          return this.exam.examName + this.examSubject.subjectName
        }
      },
      sourceType() {
        return SourceTypeEnum.Exam.id
      },
      enableEditQuestion() {
        return this.$store.getters['answerSheet/enableEditQuestion']
      },
    },
    created() {
      this.resetSheet()
      this.init()
    },
    methods: {
      ...mapMutations('answerSheet', [
        'resetSheet',
        'createFromPaperStructure',
        'generatePages',
        'changeEnableEditQuestion',
      ]),
      init() {
        this.showLoadingSpin()
        return Promise.all([this.loadSubjectData(), this.loadSubjectScanTemplates(), this.loadAnswerSheet()])
          .then(() => {
            return this.checkAndImportSheet()
          })
          .finally(() => {
            this.$Spin.hide()
          })
      },
      showLoadingSpin() {
        this.$Spin.show({
          render: h =>
            h(
              'div',
              {
                style: {
                  fontSize: '24px',
                },
              },
              '正在加载答题卡，请稍候'
            ),
        })
      },

      /**
       * 加载数据
       */
      async loadSubjectData() {
        let params = { examId: this.examId, examSubjectId: this.examSubjectId }
        let [exam, objectives, subjectives, paper, blocks] = await Promise.all([
          apiGetExamById({ examId: this.examId }),
          apiGetObjectiveQuestions(params),
          apiGetSubjectiveQuestions(params),
          apiGetBindPaperContent(params),
          apiGetBlocks(params),
        ])
        this.exam = exam
        this.objectiveDefines = objectives
        this.subjectiveDefines = subjectives
        this.blockDefines = blocks
        this.paper = paper
      },
      loadSubjectScanTemplates() {
        return apiGetSubjectScanTemplates(this.examSubjectId).then(res => {
          this.subjectScanTemplates = res
        })
      },
      loadAnswerSheet() {
        if (this.isNewAnswerSheet) {
          return Promise.resolve()
        }
        return apiGetScanTemplateForEdit({
          examSubjectId: this.examSubjectId,
          scanTemplateId: this.answerSheetId,
        }).then(res => {
          // 答题卡完成制作后blocksJson对象属性顺序、分值字符串会变，导致用户未作修改页面仍提示保存
          res.blocksJson = reOrderBlocksJson(res.blocksJson)
          this.answerSheet = res
        })
      },

      /**
       * 导入与生成
       */
      async checkAndImportSheet() {
        // 情况一：新答题卡或由扫描模板切换为使用同级生答题卡，则新建
        if (
          this.isNewAnswerSheet ||
          (this.answerSheet && this.answerSheet.answerSheetType != SubjectAnswerSheetTypeEnum.AnswerSheet.id)
        ) {
          this.generateSheetAndPage()
          return
        }

        // 情况二：已有答题卡
        let sheet
        try {
          sheet = JSON.parse(this.answerSheet.sheetJson)
        } catch (err) {
          this.handleSheetError('答题卡结构损坏', '抱歉！由于未知原因，答题卡结构已被损坏')
          return
        }

        if (sheet.version !== SheetVersion) {
          // 上一个版本可选导入，更旧版本不可导入
          if (sheet.version + 1 == SheetVersion) {
            let back = await this.handlePreviousVersion()
            if (back) {
              this.backToExamPaper()
              return
            }
          } else {
            this.handleSheetError('答题卡版本不一致', '本答题卡不是由当前版本的答题卡制作工具制作的，无法直接编辑。')
            return
          }
        }

        let { objectiveQuestions, subjectiveQuestions, blocks } = parseQuestionsAndBlocks({
          objectivesJson: this.answerSheet.objectivesJson,
          blocksJson: this.answerSheet.blocksJson,
          sheet,
        })
        let isPostScan = sheet.mode == ModeEnum.PostScan.id

        // 可更改题目：保留答题卡中题目，但需更新满分、题名、题块名、答案
        if (this.answerSheet.enableEditQuestion) {
          updateQuestionNameScoreAnswerBlockName({
            objectiveQuestions,
            subjectiveQuestions,
            blocks,
            objectiveDefines: this.objectiveDefines,
            subjectiveDefines: this.subjectiveDefines,
            blockDefines: this.blockDefines,
            isPostScan,
          })
        }
        // 不可更改题目：检查题目题块是否有变化
        else {
          let isSamePaperStructure = isSameAnswerSheetPaperStructure(
            this.objectiveDefines,
            this.subjectiveDefines,
            objectiveQuestions,
            subjectiveQuestions,
            isPostScan
          )
          let isSameBlocks = isPostScan ? true : isSameAnswerSheetBlocks(this.blockDefines, blocks)
          if (!isSamePaperStructure || !isSameBlocks) {
            this.handleSheetError('试卷结构已改变', '科目题目定义或题块定义与答题卡不一致，无法直接编辑。')
            return
          }
        }

        try {
          let enableEditQuestion = this.answerSheet.enableEditQuestion
          await this.$store.dispatch('answerSheet/importSheet', {
            sheet,
            enableEditQuestion,
            objectiveQuestions: enableEditQuestion ? objectiveQuestions : this.objectiveDefines,
            subjectiveQuestions: enableEditQuestion ? subjectiveQuestions : this.subjectiveDefines,
            blocks: enableEditQuestion ? blocks : this.blockDefines,
            paper: this.paper,
            from: SourceTypeEnum.Exam.id,
          })
        } catch (err) {
          this.$Message.error({
            content: '导入答题卡失败',
          })
          throw err
        }
        this.generatePages()
      },
      generateSheetAndPage() {
        try {
          this.createFromPaperStructure({
            examName: this.exam.examName,
            gradeId: this.exam.grade.id,
            examSubjectId: this.examSubjectId,
            subjectId: this.examSubject.subjectId,
            // 选考科目带有(选)字样，须去除
            subjectName: this.examSubject.subjectName.replace(/[(（].*[）)]/, ''),
            mode: ModeEnum.Online.id,
            objectiveQuestions: this.objectiveDefines,
            subjectiveQuestions: this.subjectiveDefines,
            paper: this.paper,
            blocks: this.blockDefines,
          })
          // 创建答题卡时若已定义题目则不能再增删题目，同扫描模板
          if (this.isNewAnswerSheet) {
            this.changeEnableEditQuestion(this.objectiveDefines.length == 0 && this.subjectiveDefines.length == 0)
          }
        } catch (err) {
          // 延后弹出，避免上一个对话框关闭时将此对话框一起关闭了
          setTimeout(() => {
            this.$Modal.error({
              title: '生成答题卡失败',
              content: err,
            })
          }, 1000)
          return
        }

        this.generatePages()
      },
      handleSheetError(title, content) {
        this.$Modal.confirm({
          title: `${title}`,
          content: `${content}
          <br><br>您可以：
          <br>1. 退出编辑，或：
          <br>2. 重新生成答题卡，<strong>原答题卡将被覆盖</strong>`,
          okText: '退出编辑',
          cancelText: '重新生成',
          onOk: () => {
            this.backToExamPaper()
          },
          onCancel: () => {
            this.generateSheetAndPage()
          },
        })
      },
      handlePreviousVersion() {
        this.$Spin.hide()
        return new Promise(resolve => {
          this.$Modal.confirm({
            title: '答题卡版本不一致',
            width: 430,
            content: `本答题卡不是由当前版本的答题卡制作工具制作的。
            <br><br>您可以：
            <br>1. 退出编辑，或：
            <br>2. 继续编辑答题卡，<strong>原答题卡版式将发生变化</strong>，需要您检查调整`,
            okText: '退出编辑',
            cancelText: '继续编辑',
            onOk: () => {
              resolve(true)
            },
            onCancel: () => {
              this.showLoadingSpin()
              resolve(false)
            },
          })
        })
      },

      /**
       * 按钮事件
       */
      async saveSheet(data) {
        data.examSubjectId = this.examSubjectId
        data.scanTemplateId = this.isNewAnswerSheet ? '' : this.answerSheetId
        data.answerSheetType = SubjectAnswerSheetTypeEnum.AnswerSheet.id
        data.enableEditQuestion = this.enableEditQuestion
        // 接口中的answerSheetId是指该答题卡从哪份答题卡复制而来，而data中的answerSheetId是指本答题卡的Id，意义不一致
        delete data.answerSheetId

        try {
          let newAnswerSheetId = await apiSaveScanTemplate(data)
          this.$Message.success({
            content: '保存答题卡成功',
          })

          // 新答题卡需更改路由
          if (this.isNewAnswerSheet) {
            await this.$router.replace({
              name: 'answerSheet-editExam',
              params: {
                examId: this.examId,
                examSubjectId: this.examSubjectId,
                answerSheetId: newAnswerSheetId,
              },
            })
            // 重新加载答题卡
            await this.loadAnswerSheet()
          } else {
            this.answerSheet.sheetJson = data.sheetJson
            this.answerSheet.objectivesJson = data.objectivesJson
            this.answerSheet.blocksJson = data.blocksJson
          }
        } finally {
          this.$Spin.hide()
        }
      },
      async finishSheet() {
        await this.loadSubjectScanTemplates()
        let scanTemplate = this.subjectScanTemplates.find(x => x.id == this.answerSheetId)
        if (!scanTemplate) {
          return
        }
        if (scanTemplate.isFinish && !scanTemplate.copy) {
          this.$Message.info({
            content: '本答题卡已完成制作，无需再次完成',
          })
          return
        }
        this.$TransparentSpin.show()
        try {
          this.checkResult = await apiCheckScanTemplateFinish({
            examSubjectId: this.examSubjectId,
            scanTemplateId: this.answerSheetId,
          })
          this.showModalChangeContent = true
        } catch (ex) {
          this.$Modal.error({
            title: '无法完成制作',
            content: (ex && ex.msg) || '',
          })
          return
        } finally {
          this.$TransparentSpin.hide()
        }
      },
      handleModalChangeContentCancel() {
        this.showModalChangeContent = false
        this.checkResult = null
      },
      async handleModalChangeContentOK({
        redoRecognizeUnit,
        redoRecognizeObjective,
        redoClipImage,
        redoRecognizeSelect,
      }) {
        this.handleModalChangeContentCancel()
        this.$TransparentSpin.show()
        try {
          await apiFinishScanTemplate({
            examSubjectId: this.examSubjectId,
            scanTemplateId: this.answerSheetId,
            redoRecognizeUnit,
            redoRecognizeObjective,
            redoClipImage,
            redoRecognizeSelect,
          })
          this.$Message.success({
            content: '完成制作成功',
            duration: 5,
            closable: true,
          })
          this.backToExamPaper()
        } catch (ex) {
          // 等待上个弹窗关闭
          await sleep(500)
          this.$Modal.error({
            title: '完成制作失败',
            content: (ex && ex.msg) || '',
          })
        } finally {
          this.$TransparentSpin.hide()
        }
      },
      backToExamPaper() {
        // this.$router.push({
        //   name: 'emarking-exam-paper',
        //   params: {
        //     examId: this.examId,
        //   },
        // })
        this.$router.back()
      },
    },
  }
</script>
