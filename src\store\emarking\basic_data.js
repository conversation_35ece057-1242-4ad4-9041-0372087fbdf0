import { apiGetEMarkingBasicData } from '@/api/emarking'
import {
  apiGetSystemSemesters,
  apiGetMySchoolGradeSubjects,
  apiGetSchoolSemesters,
  apiGetSchoolTerms,
  apiGetSystemEduSchools,
  apiGetSystemSchools,
} from '@/api/user'

import SchoolTypeEnum from '@/enum/school/school_type'

export default {
  state: {
    examScopes: [],
    examModes: [],
    examTypes: [],
    questionTypes: [],
    examRoles: [],
    multipleSchoolMarkingModes: [],
    files: [],
    scanModes: [],
    uploadModes: [],
    stages: [],
    markModes: [],
    assignModes: [],
    examSubjectStatus: [],
    semesters: [],
    terms: [],
    schoolGradeSubjects: [],
    monitorRouteExamSubjectId: '',
    systemAllInstitutions: [],
  },
  getters: {
    examScopes(state) {
      return () =>
        state.examScopes.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },
    examModes(state) {
      return () =>
        state.examModes.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },
    examTypes(state) {
      return () =>
        state.examTypes.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },
    questionTypes(state) {
      return () =>
        state.questionTypes.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },
    examRoles(state) {
      return () =>
        state.examRoles.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },
    markModes(state) {
      return () =>
        state.markModes.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },
    assignModes(state) {
      return () =>
        state.assignModes.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },
    examSubjectStatus(state) {
      return () =>
        state.examSubjectStatus.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },
    multipleSchoolMarkingModes(state) {
      return () =>
        state.multipleSchoolMarkingModes.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },
    files(state) {
      return () =>
        state.files.map(x => ({
          id: x.id,
          name: x.name,
          url: x.url,
        }))
    },
    fileUrlByName(state) {
      return name => {
        let file = state.files.find(x => x.name === name)
        return (file && file.url) || undefined
      }
    },
    scanModes(state) {
      return () =>
        state.scanModes.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },
    uploadModes(state) {
      return () =>
        state.uploadModes.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },
    stages(state) {
      return () =>
        state.stages.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },
    stageGrades(state) {
      return () =>
        state.stages.map(s => ({
          id: s.id,
          name: s.name,
          grades: s.grades.map(g => ({
            id: g.id,
            name: g.name,
          })),
        }))
    },
    gradesByStageId(state) {
      return stageId => {
        let stage = state.stages.find(x => x.id === stageId)
        return ((stage && stage.grades) || []).map(g => ({
          id: g.id,
          name: g.name,
        }))
      }
    },
    grades(state) {
      return () => {
        let list = []
        state.stages.forEach(stage => {
          stage.grades.forEach(g => {
            if (state.schoolGradeSubjects.some(x => x.gradeId === g.id)) {
              list.push({
                id: g.id,
                name: g.name,
              })
            }
          })
        })
        return list
      }
    },
    gradeSubjects(state) {
      return () => {
        let list = []
        state.stages.forEach(stage => {
          stage.grades.forEach(g => {
            list.push({
              id: g.id,
              name: g.name,
              subjects: g.subjects.map(s => ({
                id: s.id,
                name: s.name,
              })),
            })
          })
        })
        return list
      }
    },

    subjects(state) {
      return () => {
        let subjects = []
        state.stages.forEach(stage => {
          stage.grades.forEach(g => {
            g.subjects.forEach(s => {
              if (subjects.every(x => x.id !== s.id)) {
                subjects.push({
                  id: s.id,
                  name: s.name,
                })
              }
            })
          })
        })

        return subjects.sort((a, b) => a.id - b.id)
      }
    },
    subjectsByGradeId(state) {
      return gradeId => {
        let stage = state.stages.find(stage => stage.grades.some(g => g.id === gradeId))
        if (!stage) {
          return []
        }

        let grade = stage.grades.find(g => g.id === gradeId)
        return grade.subjects.map(s => ({
          id: s.id,
          name: s.name,
        }))
      }
    },
    semesters(state) {
      return () => state.semesters.map(s => ({ ...s }))
    },
    terms(state) {
      return () => state.terms.map(t => ({ ...t }))
    },
    monitorRouteExamSubjectId(state) {
      return state.monitorRouteExamSubjectId
    },
    systemAllInstitutions(state) {
      return state.systemAllInstitutions
    },
  },
  mutations: {
    updateBasicData(state, data) {
      Object.keys(data).forEach(key => {
        if (state[key]) {
          Object.freeze(data[key])
          state[key] = data[key]
        }
      })
    },
    setMonitorRouteExamSubjectId(state, examSubjectId) {
      state.monitorRouteExamSubjectId = examSubjectId || ''
    },
    updateSystemAllInstitutions(state, res = []) {
      if (res.length) {
        let institutions = []
        res.forEach(item => {
          if (item.records && item.records.length) {
            institutions = institutions.concat(
              item.records.map(record => ({
                name: record.schoolName,
                id: record.schoolId,
              }))
            )
          }
        })
        state.systemAllInstitutions = institutions
      } else {
        state.systemAllInstitutions = res
      }
    },
  },
  actions: {
    getBasicData(context) {
      let requests = [apiGetEMarkingBasicData(), apiGetMySchoolGradeSubjects()]
      if (context.rootGetters['user/info'].schoolType != SchoolTypeEnum.Bureau.id) {
        requests.push(apiGetSchoolSemesters())
      } else {
        requests.push(apiGetSystemSemesters())
      }
      requests.push(apiGetSchoolTerms())
      return Promise.all(requests).then(([data, schoolGradeSubjects, semesters, terms]) => {
        data.schoolGradeSubjects = schoolGradeSubjects || []
        data.semesters = semesters || []
        data.terms = terms || []
        context.commit('updateBasicData', data)
      })
    },
    refreshBasicData(context) {
      if (window.sessionStorage) {
        window.sessionStorage.removeItem('emarkingBasicData')
      }
      return context.dispatch('getBasicData')
    },
    fetchSystemAllInstitutions(context) {
      const ReqeustParams = {
        currentPage: 1,
        pageSize: 5000,
      }
      return Promise.all([apiGetSystemEduSchools(ReqeustParams), apiGetSystemSchools(ReqeustParams)]).then(responses =>
        context.commit('updateSystemAllInstitutions', responses)
      )
    },
  },
}
