<template>
  <div class="com-loading">
    <Icon class="loading-img" type="ios-loading" :size="size" :color="color"></Icon>
    <p class="loading-text">{{ text }}</p>
  </div>
</template>

<script>
  export default {
    props: {
      size: {
        type: Number,
        default: 24,
      },
      color: {
        type: String,
        default: 'auto',
      },
      text: {
        type: String,
        default: '正在加载',
      },
    },
    data() {
      return {}
    },
  }
</script>

<style lang="scss" scoped>
  .com-loading {
    padding: 20px;
    color: $color-primary;
    text-align: center;
  }

  .loading-img {
    margin-bottom: 10px;
    animation: keyframes-loading 1s linear infinite;
  }

  @keyframes keyframes-loading {
    from {
      transform: rotate(0deg);
    }

    50% {
      transform: rotate(180deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
</style>
