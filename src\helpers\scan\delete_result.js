import { Message } from 'view-ui-plus'
import { createModalInstance } from '@/utils/iview'
import ModalDeleteResult from '@/views/scan/answer_sheet/exception/components/modal_delete_result.vue'

/**
 * 显示删除结果，如果阅卷服务的扫描数据未能删除，即markUnDeletedStudents.length > 0，则弹窗提示标记缺考
 * @param {*} deleteResult 删除结果
 * @param {*} messageBuilder 提示消息创建函数，参数为已删除扫描单元数量
 */
export function showDeleteResult(deleteResult, messageBuilder, { examSubjectId, isInSchoolExam }) {
  if (deleteResult == null) {
    return
  }
  let { deletedScanUnitCount, markUnDeletedStudents, message } = deleteResult
  try {
    if (markUnDeletedStudents.length == 0) {
      Message.success({
        content: message || messageBuilder(deletedScanUnitCount),
      })
    } else {
      createModalInstance(ModalDeleteResult, {
        examSubjectId,
        isInSchoolExam,
        deleteResult,
      })
    }
  } catch {
    //
  }
}
