<script>
  /**
   * 像 breadcrumb 的 tabs 切换
   */
  export default {
    props: {
      navs: {
        type: Array,
        default: () => [],
      },
    },

    computed: {
      activeNavName() {
        let activeNav = this.navs.find(nav => (this.$route.matched || []).map(m => m.name).includes(nav.name))
        return (activeNav && activeNav.name) || ''
      },
    },

    methods: {
      handleChangeActiveNav(nav) {
        if (nav && nav.name !== this.activeNavName) {
          this.$router.replace(nav)
        }
      },
    },
  }
</script>

<template>
  <div class="component-bread-crumb-tabs">
    <div v-for="(nav, index) of navs" :key="nav.name" class="tab">
      <span v-show="index !== 0">|</span>
      <div
        class="tab-text"
        :class="[nav.name === activeNavName ? 'active-tab' : 'inactive-tab']"
        @click="handleChangeActiveNav(nav)"
      >
        {{ nav.menu }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .component-bread-crumb-tabs {
    @include flex(row, flex-start, center);
    user-select: none;

    .tab {
      @include flex(row, flex-start, center);

      .tab-text {
        padding: 0 1em;

        &:hover {
          cursor: pointer;
        }
      }

      .active-tab {
        color: $color-primary;
        font-weight: bold;
      }

      .inactive-tab {
        color: $color-content;
      }
    }
  }
</style>
