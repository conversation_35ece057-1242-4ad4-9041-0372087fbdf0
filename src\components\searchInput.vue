<template>
  <div class="search-input-container">
    <Input
      class="input-search-text"
      :model-value="modelValue"
      type="text"
      :size="size"
      :maxlength="maxlength"
      :placeholder="placeholder"
      :clearable="clearable"
      :prefix="prefix"
      :suffix="suffix"
      @on-change="changeKeyword"
    />
  </div>
</template>

<script>
  import { debounce } from '@/utils/function'

  export default {
    name: 'SearchInput',
    props: {
      placeholder: {
        type: String,
        default: '请输入关键词',
      },
      modelValue: {
        type: String,
        default: '',
      },
      clearable: {
        type: Boolean,
        default: false,
      },
      maxlength: {
        type: Number,
        default: 20,
      },
      size: {
        type: String,
        default: 'default',
      },
      prefix: {
        type: String,
        default: '',
      },
      suffix: {
        type: String,
        default: '',
      },
      delay: {
        type: Number,
        default: 500,
      },
    },
    emits: ['update:modelValue', 'on-change'],
    data() {
      return {
        emitEvent: null,
      }
    },
    created() {
      this.emitEvent = debounce(val => {
        if (this.trim) {
          val = val.trim()
        }
        this.$emit('update:modelValue', val)
        this.$emit('on-change', val)
      }, this.delay)
    },
    methods: {
      changeKeyword(e) {
        this.emitEvent(e.target.value)
      },
    },
  }
</script>
