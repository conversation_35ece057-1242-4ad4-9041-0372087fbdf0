import { apiDownloadClassesRankIntervalExcel } from '@/api/report'

import Store from '@/store/index'

export function downloadClsRankIntervalExcel(templateId) {
  let ranges = []
  const StoreTemplateSettingIntervals = Store.getters['report/templateSettingRankIntervals']
  const AllSubjectSettingIntervals =
    StoreTemplateSettingIntervals && StoreTemplateSettingIntervals.find(x => x.subjectId === 0)
  const AllSubjectIntervals = ((AllSubjectSettingIntervals && AllSubjectSettingIntervals.intervals) || []).map(
    (x, idx) => ({
      name: x.range,
      range: x.range,
      sortCode: idx + 1,
    })
  )

  if (AllSubjectIntervals.length) {
    ranges = AllSubjectIntervals
  } else {
    const ExamAttributes = Store.getters['report/examAttributes']
    const StudentNumStr = ExamAttributes.studentNum + ''
    const OperateStr = StudentNumStr.slice(0, 2)
    const CulInterval = Math.round(+OperateStr / 10) * Math.pow(10, StudentNumStr.length - 2)
    const Interval = CulInterval > 2 ? CulInterval : 2

    let name = ''
    let range = ''
    let i = 0
    let j = 1
    for (i = Interval; i < ExamAttributes.studentNum; i += Interval, j++) {
      name = `1~${i}`
      range = '[1, ' + i + ']'
      ranges.push({
        sortCode: j,
        name: range,
        range,
      })
    }
    name = `1~${ExamAttributes.studentNum}`
    range = '[1, ' + ExamAttributes.studentNum + ']'
    ranges.push({
      sortCode: j,
      name,
      range,
    })
  }

  return apiDownloadClassesRankIntervalExcel({
    templateId: templateId,
    ranges: ranges,
  })
}
