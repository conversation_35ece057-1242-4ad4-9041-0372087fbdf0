<template>
  <div class="container-student">
    <div class="action-box">
      <div class="action-box-left">
        <div class="search-box">
          <SearchInput v-model="keyword" placeholder="姓名/手机号码" @update:model-value="changeKeyword" />
        </div>
      </div>
      <div v-if="isSysAdmin" class="action-box-right">
        <Button type="primary" @click="handleTeacherImport">导入本校教师</Button>
        <Button v-if="currentSelectedSchool.schoolType === 1" type="primary" @click="handleMultipleSchoolTeacherImport"
          >导入下属学校教师</Button
        >
        <Button type="primary" @click="handleTeacherPwdImport">导入教师密码</Button>
        <Button type="default" @click="handleBtnExportStudentsClick">导出教师</Button>
        <Button
          v-if="isSystem && currentSelectedSchoolIsInstitution"
          type="default"
          @click="handleInstitutionTeacherExport"
          >导出下属学校教师</Button
        >
      </div>
    </div>
    <div class="block-content">
      <div class="table-box">
        <Table :columns="tableColumns" :data="teacherList"></Table>
      </div>
      <div class="pager">
        <Page
          :total="pageTotal"
          :page-size="pageSize"
          :model-value="currentPage"
          show-elevator
          show-sizer
          show-total
          :page-size-opts="[10, 20, 50, 100]"
          @on-change="changePage"
          @on-page-size-change="changePageSize"
        ></Page>
      </div>
    </div>
    <ModalTeacherImport
      v-model="modalTeacherImportVisible"
      :is-multiple-school="isMultipleSchool"
      :school-id="currentSelectedSchool.value"
      @on-refresh="onRefresh"
    />
    <ModalTeacherPasswordImport v-model="modalTeacherPwdImportVisible" @on-refresh="onRefresh" />
    <modal-export-institution-schools-teachers
      v-model="modalExportInstitutionTeacherVisible"
      :school="currentSelectedSchool"
    ></modal-export-institution-schools-teachers>
  </div>
</template>

<script>
  import SearchInput from '@/components/searchInput.vue'
  import ModalTeacherImport from './modal_teacher_import.vue'
  import ModalTeacherPasswordImport from './modal_teacher_password_import.vue'
  import ModalExportInstitutionSchoolsTeachers from './modal_export_institution_schools_teachers.vue'

  import { apiGetTeacherList, apiExportTeachersBySchool } from '@/api/user/school'

  import { groupArray } from '@/utils/array'
  import { downloadBlob } from '@/utils/download'

  import { SchoolPositions } from '@/enum/user/role.js'

  export default {
    components: {
      SearchInput,
      ModalTeacherImport,
      ModalTeacherPasswordImport,
      'modal-export-institution-schools-teachers': ModalExportInstitutionSchoolsTeachers,
    },
    props: {
      currentSelectedSchool: {
        type: Object,
        default: () => {},
      },
      isSysAdmin: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        currentPage: 1,
        pageSize: 10,
        keyword: '',
        teacherList: [],
        pageTotal: 0,
        modalTeacherImportVisible: false,
        isMultipleSchool: false,
        modalStudentDeleteVisible: false,
        modalExportInstitutionTeacherVisible: false,
        modalTeacherPwdImportVisible: false,
      }
    },
    computed: {
      isSystem() {
        return this.$store.getters['user/isSystem'] || false
      },
      currentSelectedSchoolIsInstitution() {
        return (
          (this.currentSelectedSchool &&
            this.currentSelectedSchool.schoolType &&
            Boolean(this.currentSelectedSchool.schoolType)) ||
          false
        )
      },
      schoolGradeClassSubjects() {
        return this.$store.getters['school/schoolGradeClassSubjects']
      },
      schoolStageGradeClassSubjects() {
        return this.$store.state.school.schoolStageGradeClassSubjects
      },
      schoolSubjectGradeClasses() {
        return this.$store.getters['school/schoolSubjectGradeClasses']
      },
      tableColumns() {
        let columns = [
          {
            title: '姓名',
            key: 'realName',
            minWidth: 90,
            align: 'center',
          },
          {
            title: '账号',
            align: 'center',
            minWidth: 100,
            key: 'userName',
          },
          {
            title: '性别',
            width: 60,
            align: 'center',
            key: 'sex',
          },
          {
            title: '手机号码',
            minWidth: 120,
            align: 'center',
            render: (h, params) => {
              if (params.row.mobile && params.row.mobile.length > 0) {
                return h('span', {}, params.row.mobile.slice(0, 3) + '****' + params.row.mobile.slice(7))
              } else {
                return ''
              }
            },
          },
          {
            title: '任教科目',
            minWidth: 200,
            render: (h, params) => {
              let strs = groupArray(params.row.teachings, x => x.subjectId).map(x => {
                let subjectName = x.group[0].subjectName
                let classNames = x.group.map(c => c.className).join('，')
                return `${subjectName}（${classNames}）`
              })

              return h(
                'div',
                {
                  style: {
                    marginTop: '10px',
                    marginBottom: '10px',
                  },
                },
                strs.map(str => h('p', {}, str))
              )
            },
          },
          {
            title: '担任职务',
            minWidth: 160,
            render: (h, params) => {
              let strs = []
              let roles = groupArray(params.row.roles, r => r.roleId)
              roles.forEach(role => {
                if (role.key === SchoolPositions.SchoolLeader.id) {
                  strs.push(`${SchoolPositions.SchoolLeader.name}`)
                } else if (role.key === SchoolPositions.GradeLeader.id) {
                  strs.push(`${SchoolPositions.GradeLeader.name}（${role.group.map(x => x.gradeName).join('，')}）`)
                } else if (role.key === SchoolPositions.SubjectLeader.id) {
                  strs.push(
                    `${SchoolPositions.SubjectLeader.name}（${role.group
                      .map(x => x.stageName + x.subjectName)
                      .join('，')}）`
                  )
                } else if (role.key === SchoolPositions.GradeSubjectLeader.id) {
                  strs.push(
                    `${SchoolPositions.GradeSubjectLeader.name}（${role.group
                      .map(x => x.gradeName + x.subjectName)
                      .join('，')}）`
                  )
                } else if (role.key === SchoolPositions.ClassLeader.id) {
                  strs.push(`${SchoolPositions.ClassLeader.name}（${role.group.map(x => x.className).join('，')}）`)
                }
              })

              return h(
                'div',
                {
                  style: {
                    marginTop: '10px',
                    marginBottom: '10px',
                  },
                },
                strs.map(str => h('p', {}, str))
              )
            },
          },
        ]

        return columns
      },
    },
    watch: {
      currentSelectedSchool: {
        handler: async function (val) {
          if (val && val.value) {
            this.resetData()
            this.getTeacherList()
          }
        },
        immediate: true,
        deep: true,
      },
    },
    created() {},
    methods: {
      resetData() {
        this.currentPage = 1
        this.teacherList = []
        this.keyword = ''
      },
      onRefresh() {
        this.resetData()
        this.getTeacherList()
      },
      changeKeyword(keyword) {
        this.keyword = keyword
        this.currentPage = 1

        this.getTeacherList()
      },
      changePage(page) {
        this.currentPage = page
        this.getTeacherList()
      },
      changePageSize(size) {
        this.pageSize = size
        this.currentPage = 1
        this.getTeacherList()
      },
      getTeacherList() {
        const { currentPage, pageSize, keyword, currentSelectedSchool } = this
        apiGetTeacherList({
          schoolId: currentSelectedSchool.value,
          key: keyword,
          current: currentPage,
          size: pageSize,
        })
          .then(data => {
            data.teachers.forEach(t => {
              t.teachings.forEach(x => {
                let subject = this.schoolSubjectGradeClasses.find(s => s.subjectId === x.subjectId)
                let grade = ((subject && subject.grades) || []).find(g => g.gradeId === x.gradeId)
                // let cls = ((grade && grade.classes) || []).find(c => c.classId === x.classId)
                x.subjectName = (subject && subject.subjectName) || ''
                x.gradeName = (grade && grade.gradeName) || ''
                // x.className = (cls && cls.className) || ''
              })

              t.roles.forEach(x => {
                if (x.stageId) {
                  x.stageName = (
                    this.schoolStageGradeClassSubjects.find(s => s.stageId === x.stageId) || {
                      stageName: '',
                    }
                  ).stageName
                }

                if (x.gradeId) {
                  x.gradeName = (
                    this.schoolGradeClassSubjects.find(g => g.gradeId === x.gradeId) || {
                      gradeName: '',
                    }
                  ).gradeName
                }

                if (x.subjectId) {
                  x.subjectName = (
                    this.schoolSubjectGradeClasses.find(s => s.subjectId === x.subjectId) || {
                      subjectName: '',
                    }
                  ).subjectName
                }
              })
            })

            this.pageTotal = data.total
            this.teacherList = data.teachers
          })
          .catch(() => {
            this.teacherList = []
            this.pageTotal = 0
          })
      },
      handleTeacherImport() {
        this.modalTeacherImportVisible = true
        this.isMultipleSchool = false
      },
      handleTeacherPwdImport() {
        this.modalTeacherPwdImportVisible = true
      },
      handleMultipleSchoolTeacherImport() {
        this.modalTeacherImportVisible = true
        this.isMultipleSchool = true
      },
      handleBtnExportStudentsClick() {
        apiExportTeachersBySchool({
          schoolId: this.currentSelectedSchool.value,
        }).then(blob => {
          downloadBlob(blob, `${this.currentSelectedSchool.title}教师名单.xlsx`)
        })
      },
      handleInstitutionTeacherExport() {
        if (this.isSystem) {
          this.modalExportInstitutionTeacherVisible = true
        }
      },
    },
  }
</script>

<style scoped lang="scss">
  .container-student {
    .action-box {
      @include flex(row, space-between, center);
      margin-bottom: 20px;

      .action-box-left {
        @include flex(row, flex-start, center);
      }
    }

    .search-box {
      margin-right: 15px;
    }

    .screen-box {
      @include flex(row, flex-start, flex-start);
      margin-right: 15px;

      .screen-grade-box {
        margin-right: 10px;
      }

      .label {
        margin-right: 10px;
      }
    }

    .pager {
      @include flex(row, flex-end, center);
      margin-top: 30px;
    }
  }
</style>
