/**
 * 页面块（跨页时一个题组有多块）
 */

import { randomId } from '@/utils/string'
import PageBlockTypeEnum from '@/enum/answer_sheet/page_block_type'
export default class PageBlock {
  constructor(b = {}) {
    // 块Id
    this.id = b.id || ''
    // 类型
    this.type = b.type || ''
    // 在答题卡第几页
    this.pageIndex = b.pageIndex || 0
    // 高度
    this.height = b.height || 0
    // 距顶部的高度
    this.top = b.top || 0
    // vue组件实例
    this.instance = null
    // 对应的大题
    this.topic = null
    // 对应的题组
    this.questionGroup = null
    // 在题组中的序号
    this.part = b.part || 0
    // 内容
    this.content = b.content || ''
    // 内容高度
    this.contentHeight = b.contentHeight || 0
    // 客观题块本块包含的题目
    this.questions = []
    // 语文作文块本块的字数
    this.words = 0
    // 语文作文块本块开始的字数
    this.wordsStart = 0
  }

  static createExamInfoBlock(height) {
    let block = new PageBlock()
    block.id = 'examInfo'
    block.type = PageBlockTypeEnum.ExamInfo.id
    block.height = height
    return block
  }

  static createDescriptionBlock(topic) {
    let block = new PageBlock()
    block.id = `description_topicCode-${topic.code}-${randomId()}`
    block.type = PageBlockTypeEnum.Description.id
    block.height = topic.descriptionHeight
    block.topic = topic
    return block
  }

  static createQuestionBlock(topic, questionGroup) {
    let block = new PageBlock()
    block.id = questionGroup.id
    block.type = questionGroup.type
    block.height = questionGroup.height
    block.topic = topic
    block.questionGroup = questionGroup
    block.content = questionGroup.content
    block.contentHeight = questionGroup.contentHeight

    if (block.type == PageBlockTypeEnum.Objective.id) {
      block.questions = questionGroup.questions.slice()
    } else if (block.type == PageBlockTypeEnum.ChineseWriting.id) {
      block.words = questionGroup.words
    }

    questionGroup.newBlocks.push(block)
    return block
  }
}
