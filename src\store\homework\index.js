import { apiGetTheTeachingInfo, apiGetStudentInfo, apiGetHomeworkList } from '@/api/homework/index'

export default {
  namespaced: true,
  state: {
    teachingInfo: [],
    currentTeachingInfo: {},
    studentsInfo: {},
    homeworkList: [],
  },
  mutations: {},
  actions: {
    getTheTeachingInfo(context, params) {
      return apiGetTheTeachingInfo(params).then(data => {
        context.state.teachingInfo = data
        return data
      })
    },
    initCurrentTeachingInfo(context) {
      context.state.currentTeachingInfo = context.state.teachingInfo[0]
    },
    setCurrentTeachingInfo(context, params) {
      context.state.currentTeachingInfo = params
    },
    getStudentInfo(context, params) {
      return apiGetStudentInfo(params).then(data => {
        context.state.studentsInfo = data
        return data
      })
    },
    getHomeworkList(context, params) {
      const homeworkTypes = {
        1: '学习任务',
        2: '自由出题',
        3: '题库出题',
      }
      let openAnswerWayText = ''
      return apiGetHomeworkList(params).then(data => {
        context.state.homeworkList = data.records.map(item => {
          const datetime = new Date(item.createTime)
          const createDateText = `${datetime.getFullYear()}-${datetime.getMonth() + 1}-${datetime.getDate()}`
          const createTimeText = `${datetime.getHours()}:${datetime.getMinutes()}`
          if (item.openAnswerWay === 1) {
            openAnswerWayText = '学生提交后'
          } else if (item.openAnswerWay === 0) {
            openAnswerWayText = '练习截止后'
          } else if (item.openAnswerWay === 3) {
            openAnswerWayText = item.openAnswerTime
          }
          return {
            ...item,
            openAnswerWayText: openAnswerWayText,
            isAllowMakeupText: item.isAllowMakeup == 1 ? '允许' : '不允许',
            homeworkTypeText: homeworkTypes[item.homeworkType],
            createDateText,
            createTimeText,
          }
        })
        return data
      })
    },
  },
}
