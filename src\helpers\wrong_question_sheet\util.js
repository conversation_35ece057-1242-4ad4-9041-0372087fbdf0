import { sortQuestionFunction } from '../emarking/miscellaneous'
import { groupArray } from '@/utils/array'
import Block from '@/helpers/emarking/block'
import ObjectiveQuestion from '@/helpers/emarking/objective_question'
import SubjectiveQuestion from '@/helpers/emarking/subjective_question'
import { randomId } from '@/utils/string'

/**
 * 比较反馈卡题目与试卷题目是否一致
 */
export function isSameWrongQuestionSheetPaperStructure(objectiveQuestions, subjectiveQuestions, paper) {
  let newPaperStructure = paper.extractEMarkingQuestions()
  let objectiveQuestionsNew = getSortedQuestions(newPaperStructure.objectiveQuestions)
  let objectiveQuestionsOld = getSortedQuestions(objectiveQuestions)
  let subjectiveQuestionsNew = getSortedQuestions(newPaperStructure.subjectiveQuestions)
  let subjectiveQuestionsOld = getSortedQuestions(subjectiveQuestions)
  let objectiveCompareKeys = [
    'topicCode',
    'questionCode',
    'branchTypeId',
    'optionCount',
    'originalQuestionId',
    'originalBranchId',
  ]
  let subjectiveCompareKeys = ['topicCode', 'questionCode', 'branchCode', 'originalQuestionId', 'originalBranchId']
  return (
    isSameArrayCompareByKeys(objectiveQuestionsOld, objectiveQuestionsNew, objectiveCompareKeys) &&
    isSameArrayCompareByKeys(subjectiveQuestionsOld, subjectiveQuestionsNew, subjectiveCompareKeys)
  )
}

function getSortedQuestions(questions) {
  return questions.slice().sort(sortQuestionFunction)
}

function isSameArrayCompareByKeys(arr1, arr2, keys) {
  return isSameArray(arr1, arr2, (a, b) => keys.every(key => a[key] == b[key]))
}

function isSameArray(arr1, arr2, compareFunction) {
  if (arr1.length != arr2.length) {
    return false
  }
  return arr1.every((element1, idx) => compareFunction(element1, arr2[idx]))
}

/**
 * 从试卷提取题目结构
 */
export function extractPaperStructure(paper) {
  let { objectiveQuestions, subjectiveQuestions } = paper.extractEMarkingQuestions()
  let blocks = buildBlocks(subjectiveQuestions)
  return {
    objectiveQuestions,
    subjectiveQuestions,
    blocks,
  }
}

/**
 * 分题块，题内各小题不拆开，每个题块题数不超过限制
 */
export function buildBlocks(subjectiveQuestions) {
  if (subjectiveQuestions.length == 0) {
    return []
  }
  let blocks = []
  let blockMaxQuestionCount = 50
  let block = null
  let createEmptyBlock = () => {
    block = new Block()
    block.blockId = randomId()
    block.blockName = ''
    block.questions = []
    blocks.push(block)
  }
  createEmptyBlock()

  groupArray(subjectiveQuestions, q => q.questionCode).forEach(g => {
    let questions = g.group
    if (questions.length + block.questions.length > blockMaxQuestionCount) {
      createEmptyBlock()
    }
    block.questions.push(...questions)
  })
  blocks.forEach(block => {
    let firstQuestionCode = block.questions[0].questionCode
    let lastQuestionCode = block.questions[block.questions.length - 1].questionCode
    if (firstQuestionCode == lastQuestionCode) {
      block.blockName = `题${firstQuestionCode}`
    } else {
      block.blockName = `题${firstQuestionCode}-${lastQuestionCode}`
    }
  })
  return blocks
}

/**
 * 获取无分数的题目
 */
export function getPaperScoreInvalidQuestionNames(paper) {
  let scoreInvalidQuestions = []
  let { objectiveQuestions, subjectiveQuestions } = paper.extractEMarkingQuestions()
  ;[...objectiveQuestions, ...subjectiveQuestions].forEach(q => {
    if (!(q.fullScore > 0)) {
      scoreInvalidQuestions.push(q)
    }
  })
  scoreInvalidQuestions.sort(sortQuestionFunction)
  return scoreInvalidQuestions
    .map(q => (q.branchCode > 0 ? `${q.questionCode}.${q.branchCode}` : `${q.questionCode}`))
    .join('、')
}

/**
 * 反馈卡导入时解析题目题块
 */
export function parseQuestionsAndBlocks(sheet) {
  let { objectiveQuestions, subjectiveQuestions } = sheet.paperStructure
  let blocks = sheet.blocks

  objectiveQuestions.forEach(obj => Object.setPrototypeOf(obj, ObjectiveQuestion.prototype))
  subjectiveQuestions.forEach(subj => Object.setPrototypeOf(subj, SubjectiveQuestion.prototype))
  blocks.forEach(block => Object.setPrototypeOf(block, Block.prototype))

  objectiveQuestions.sort(sortQuestionFunction)
  subjectiveQuestions.sort(sortQuestionFunction)
  blocks.forEach(block => block.questions.sort(sortQuestionFunction))

  return { objectiveQuestions, subjectiveQuestions, blocks }
}

/**
 * 反馈卡题目题块与科目试卷结构对比，更新题名分数题块名
 */
export function updateQuestionNameScoreAnswerBlockName({
  objectiveQuestions,
  subjectiveQuestions,
  blocks,
  objectiveDefines,
  subjectiveDefines,
  blockDefines,
}) {
  // 客观题更新题名分数
  objectiveQuestions.forEach(sheetObj => {
    let objDefine = objectiveDefines.find(q => q.questionCode == sheetObj.questionCode)
    if (objDefine) {
      sheetObj.questionName = objDefine.questionName
      sheetObj.branchName = objDefine.branchName
      sheetObj.fullScore = objDefine.fullScore
    }
  })
  // 主观题更新题名分数
  subjectiveQuestions.forEach(sheetSubj => {
    let subjDefine = subjectiveDefines.find(
      q => q.questionCode == sheetSubj.questionCode && q.branchCode == sheetSubj.branchCode
    )
    if (subjDefine) {
      sheetSubj.questionName = subjDefine.questionName
      sheetSubj.branchName = subjDefine.branchName
      sheetSubj.fullScore = subjDefine.fullScore
    }
  })
  // 题块按照包含题名与科目题块对应，更新题块名称
  let blockDefineQuestionCodesMap = new Map()
  blockDefines.forEach(block => {
    let questionCodes = block.questions.map(q => `${q.questionCode}.${q.branchCode}`).join(',')
    blockDefineQuestionCodesMap.set(questionCodes, block)
  })
  blocks.forEach(sheetBlock => {
    let questionCodes = sheetBlock.questions.map(q => `${q.questionCode}.${q.branchCode}`).join(',')
    let blockDefine = blockDefineQuestionCodesMap.get(questionCodes)
    if (blockDefine) {
      sheetBlock.blockName = blockDefine.blockName
    }
    sheetBlock.questions.forEach(sheetSubj => {
      let subjDefine = subjectiveDefines.find(
        q => q.questionCode == sheetSubj.questionCode && q.branchCode == sheetSubj.branchCode
      )
      if (subjDefine) {
        sheetSubj.questionName = subjDefine.questionName
        sheetSubj.branchName = subjDefine.branchName
      }
    })
  })
}
