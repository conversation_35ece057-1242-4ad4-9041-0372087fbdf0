import Role from '@/enum/user/role'

export default {
  name: 'report',
  path: 'report',
  meta: {
    title: '学情分析',
    menu: '分析',
    name: '学情分析',
    roles: [
      Role.SchoolAdministrator.id,
      Role.SchoolLeader.id,
      Role.GradeLeader.id,
      Role.SubjectLeader.id,
      Role.GradeSubjectLeader.id,
      Role.ClassLeader.id,
      Role.Teacher.id,
      Role.ResearcherLeader.id,
      Role.Researcher.id,
      Role.SystemAdministrator.id,
    ],
  },
  component: () => import('@/views/report/index.vue'),
  children: [
    {
      name: 'report-home',
      path: 'home',
      component: () => import('@/views/report/home/<USER>'),
    },
    {
      name: 'report-exam',
      path: 'exam/:examId/:templateId',
      meta: {
        showHeader: false,
        pageWidth: 'no-max',
      },
      component: () => import('@/views/report/exam/index.vue'),
      children: [
        {
          name: 'report-multipleSchool-general',
          path: 'mgeneral',
          component: () => import('@/views/report/exam/multiple_school/general/index.vue'),
        },
        {
          name: 'report-multipleSchool-score',
          path: 'mscore',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/multiple_school/score/index.vue'),
        },
        {
          name: 'report-multipleSchool-school',
          path: 'mschool',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/multiple_school/school/index.vue'),
        },
        {
          name: 'report-multipleSchool-scoreInterval',
          path: 'mscoreinterval',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/multiple_school/score_interval/index.vue'),
        },
        {
          name: 'report-multipleSchool-rankInterval',
          path: 'mrankinterval',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/multiple_school/rank_interval/index.vue'),
        },
        {
          name: 'report-multipleSchool-passingAnalysis',
          path: 'mpassinganalysis',
          component: () => import('@/views/report/exam/multiple_school/passing_analysis/index.vue'),
        },
        // {
        //   name: 'report-multipleSchool-critical-student',
        //   path: 'mcriticalstudent',
        //   component: () => import('@/views/report/exam/')
        // },
        {
          name: 'report-multipleSchool-scoreGrades',
          path: 'mscoregrades',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/multiple_school/score_grades/index.vue'),
        },
        {
          name: 'report-multipleSchool-paper',
          path: 'mpaper',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/multiple_school/paper/index.vue'),
        },
        {
          name: 'report-multipleSchool-question',
          path: 'mquestion',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/multiple_school/question/index.vue'),
        },
        {
          name: 'report-multipleSchool-knowledge',
          path: 'mknowledge',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/multiple_school/knowledge/index.vue'),
        },
        {
          name: 'report-multipleSchool-answer',
          path: 'manswer',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/multiple_school/answer/index.vue'),
        },
        {
          name: 'report-multipleSchool-topic',
          path: 'mtopic',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/multiple_school/topic/index.vue'),
        },
        {
          name: 'report-multipleSchool-newScore',
          path: 'mnewScore',
          meta: {
            showSubject: false,
          },
          component: () => import('@/views/report/exam/multiple_school/new_entrance/index.vue'),
        },
        {
          name: 'report-school-general',
          path: 'sgeneral',
          component: () => import('@/views/report/exam/school/general/index.vue'),
        },
        {
          name: 'report-school-score',
          path: 'sscore',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/school/score/index.vue'),
        },
        {
          name: 'report-school-class',
          path: 'sclass',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/school/class/index.vue'),
        },
        {
          name: 'report-school-score-rank-interval',
          path: 'sscorerankinterval',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/school/score_rank_interval/index.vue'),
        },
        {
          name: 'report-school-scoreInterval',
          path: 'sscoreinterval',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/school/score_interval/index.vue'),
        },
        {
          name: 'report-school-rankInterval',
          path: 'srankinterval',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/school/rank_interval/index.vue'),
        },
        // {
        //   name: 'report-school-score-level-distribution',
        //   path: 'sscoreintervaldistribution',
        //   component: () => import('@/views/report/exam/school/score_level_distribution/index.vue')
        // },
        {
          name: 'report-school-passingAnalysis',
          path: 'spassinganalysis',
          component: () => import('@/views/report/exam/school/passing_analysis/index.vue'),
        },
        {
          name: 'report-school-criticalStudent',
          path: 'scriticalstudent',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/school/critical_student/index.vue'),
        },
        {
          name: 'report-school-scoreGrades',
          path: 'sscoregrades',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/school/score_grades/index.vue'),
        },
        {
          name: 'report-school-subject-compare',
          path: 'ssubjectcompare',
          component: () => import('@/views/report/exam/school/subject_compare/index.vue'),
        },
        {
          name: 'report-school-paper',
          path: 'spaper',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/school/paper/index.vue'),
        },
        {
          name: 'report-school-question',
          path: 'squestion',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/school/question/index.vue'),
        },
        {
          name: 'report-school-knowledge',
          path: 'sknowledge',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/school/knowledge/index.vue'),
        },
        {
          name: 'report-school-answer',
          path: 'sanswer',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/school/answer/index.vue'),
        },
        {
          name: 'report-school-topic',
          path: 'stopic',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/school/topic/index.vue'),
        },
        {
          name: 'report-school-newScore',
          path: 'snewScore',
          meta: {
            showSubject: false,
          },
          component: () => import('@/views/report/exam/school/new_entrance/index.vue'),
        },
        {
          name: 'report-class-general',
          path: 'cgeneral',
          component: () => import('@/views/report/exam/class/general/index.vue'),
        },
        {
          name: 'report-class-score',
          path: 'cscore',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/class/score/index.vue'),
        },
        {
          name: 'report-class-scoreInterval',
          path: 'cscoreinterval',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/class/score_interval/index.vue'),
        },
        {
          name: 'report-class-rankInterval',
          path: 'crankinterval',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/class/rank_interval/index.vue'),
        },
        // {
        //   name: 'report-class-passing-analysis',
        //   path: 'cpassinganalysis',
        //   component: () => import('@/views/report/exam/class/passing_analysis/index.vue'),
        // },
        {
          name: 'report-class-criticalStudent',
          path: 'ccriticalstudent',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/class/critical_student/index.vue'),
        },
        {
          name: 'report-class-scoreGrades',
          path: 'cscoregrades',
          meta: {
            showSubject: true,
            showSubjectAll: true,
          },
          component: () => import('@/views/report/exam/class/score_grades/index.vue'),
        },
        // {
        //   name: 'report-class-paper',
        //   path: 'cpaper',
        //   meta: {
        //     showSubject: true,
        //   },
        //   component: () => import('@/views/report/exam/class/paper/index.vue')
        // },
        {
          name: 'report-class-question',
          path: 'cquestion',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/class/question/index.vue'),
        },
        {
          name: 'report-class-knowledge',
          path: 'cknowledge',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/class/knowledge/index.vue'),
        },
        {
          name: 'report-class-answer',
          path: 'canswer',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/class/answer/index.vue'),
        },
        {
          name: 'report-class-topic',
          path: 'ctopic',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/class/topic/index.vue'),
        },
        {
          name: 'report-class-lecture',
          path: 'clecture',
          meta: {
            showSubject: true,
          },
          component: () => import('@/views/report/exam/class/lecture/index.vue'),
        },
        {
          name: 'report-class-newScore',
          path: 'cnewScore',
          meta: {
            showSubject: false,
          },
          component: () => import('@/views/report/exam/class/new_entrance/index.vue'),
        },
      ],
    },
    {
      name: 'report-config',
      path: 'config/:examId/:templateId',
      meta: {
        showHeader: false,
      },
      component: () => import('@/views/report/config/index.vue'),
    },
    {
      name: 'report-objective-comment',
      path: 'objectivecomment/:examId?',
      meta: {
        showHeader: false,
        showSubject: true,
      },
      component: () => import('@/views/report/exam/class/objective_comment/index.vue'),
    },
    {
      name: 'report-tools',
      path: 'tools',
      component: () => import('@/views/report/tools/index.vue'),
    },
    {
      name: 'report-tool-merge-exam',
      path: 'tools/merge',
      component: () => import('@/views/report/tools/merge_exams/index.vue'),
    },
    {
      name: 'report-tool-create-merge-exam',
      path: 'tools/merge/create',
      component: () => import('@/views/report/tools/merge_exams/create_merge_exams.vue'),
    },
    {
      name: 'report-tool-edit-merge-exam',
      path: 'tools/merge/edit/:id',
      component: () => import('@/views/report/tools/merge_exams/create_merge_exams.vue'),
    },
  ],
}
