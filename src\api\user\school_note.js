import ajax from '@/api/ajax'

export function apiGetSchoolNoteCategoryList() {
  return ajax.get({
    url: 'user/schoolNote/getCategoryList',
    requestName: '获取学校注意事项分类',
  })
}

export function apiGetSchoolNoteList(params) {
  return ajax.get({
    url: 'user/schoolNote/getNoteList',
    params: {
      groupId: params.groupId,
      // categoryId: params.categoryId || undefined,
    },
    requestName: '获取学校注意事项',
  })
}

export function apiAddSchoolNote(params) {
  return ajax.post({
    url: 'user/schoolNote/add',
    data: params,
    requestName: '新增学校注意事项',
  })
}

export function apiEditSchoolNote(params) {
  return ajax.put({
    url: 'user/schoolNote/edit',
    data: params,
    requestName: '编辑学校注意事项',
  })
}

export function apiDeleteSchoolNote(params) {
  return ajax.delete({
    url: 'user/schoolNote/delete',
    params: {
      id: params.noteId,
    },
    requestName: '删除学校注意事项',
  })
}

export function apiUpdateNoteSortCode(params) {
  return ajax.post({
    url: 'user/schoolNote/updateSortCode',
    data: params,
    requestName: '学校注意事项排序',
  })
}

export function apiGetSchoolNoteGroup() {
  return ajax.get({
    url: 'user/schoolNoteGroup/list',
    requestName: '获取学校注意事项分组',
  })
}

export function apiAddSchoolNoteGroup(params) {
  return ajax.post({
    url: 'user/schoolNoteGroup/add',
    params: {
      name: params.name,
    },
    requestName: '新增注意事项分组',
  })
}

export function apiUpdateSchoolNoteGroup(params) {
  return ajax.put({
    url: 'user/schoolNoteGroup/edit',
    params: {
      id: params.id,
      name: params.name,
    },
    requestName: '编辑注意事项分组',
  })
}

export function apiDeleteSchoolNoteGroup(params) {
  return ajax.delete({
    url: 'user/schoolNoteGroup/delete',
    params: {
      id: params.id,
    },
    requestName: '删除学校注意事项分组',
  })
}

export function apiUpdateNoteGroupSortCode(params) {
  return ajax.post({
    url: 'user/schoolNoteGroup/updateSortCode',
    data: params,
    requestName: '学校注意事项分组排序',
  })
}
