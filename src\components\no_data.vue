<template>
  <div class="no-data" :style="noDataStyle">
    <img v-if="showImage" class="no-data-image" :src="imageSrc" :style="imageStyle" />
    <div class="no-data-slot">
      <slot>暂无数据</slot>
    </div>
  </div>
</template>

<script>
  import NoDataDefaultImage from '@/assets/images/common/no_data.svg'

  export default {
    props: {
      showImage: {
        type: Boolean,
        default: true,
      },
      imageSrc: {
        type: String,
        default: NoDataDefaultImage,
      },
      imageWidth: {
        type: Number,
        default: 300,
      },
      visible: {
        type: Boolean,
        default: true,
      },
    },
    computed: {
      noDataStyle() {
        if (!this.visible) {
          return {
            visibility: 'hidden',
          }
        }
        return null
      },
      imageStyle() {
        return {
          width: this.imageWidth + 'px',
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .no-data {
    @include flex(column, center, center);
    padding-top: 50px;
    padding-bottom: 50px;
  }

  .no-data-image {
    margin-bottom: 20px;
  }

  .no-data-slot {
    color: $color-icon;
    text-align: center;
  }
</style>
