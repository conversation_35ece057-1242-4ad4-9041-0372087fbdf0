<!doctype html>
<html lang="zh-CN">
  <head>
    <title></title>
    <meta charset="UTF-8" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link rel="icon" href="/images/icon.png" />
    <script>
      ;(function mathjaxConfig() {
        window.MathJax = {
          skipStartupTypeset: true,
          showProcessingMessages: false,
          messageStyle: 'none',
          showMathMenu: false,
          showMathMenuMSIE: false,
          webFont: 'STIX-Web',
          availableFonts: [],
          preferredFont: null,
          styles: {
            '.MathJax_Display': {
              'overflow-x': 'auto',
              'overflow-y': 'hidden',
            },
            '.MathJax_CHTML': {
              outline: 'none',
            },
          },
          tex2jax: {
            inlineMath: [
              ['$', '$'],
              ['\\(', '\\)'],
            ],
            displayMath: [
              ['$$', '$$'],
              ['\\[', '\\]'],
            ],
          },
          TeX: { extensions: ['extpfeil.js'] },
        }
      })()
    </script>
    <script src="/lib/MathJax-2.7.8/MathJax.js?config=TeX-MML-AM_CHTML"></script>
    <script src="/lib/ckeditor4/ckeditor.js"></script>
    <script>
      ;(function ckeditorConfig() {
        window.CKEDITOR.disableAutoInline = true
        window.CKEDITOR.config.enterMode = CKEDITOR.ENTER_BR
        window.CKEDITOR.config.shiftEnterMode = CKEDITOR.ENTER_BR
        // window.CKEDITOR.config.filebrowserImageUploadUrl =
        //   'https://ckeditor.com/apps/ckfinder/3.4.5/core/connector/php/connector.php?command=QuickUpload&type=Images'
        // window.CKEDITOR.config.pasteFilter = `a abbr address area article aside audio b base bdi bdo blockquote body br button canvas caption cite code col colgroup command datalist dd del details dfn dl dt em embed fieldset figcaption figure footer form h1 h2 h3 h4 h5 h6 head header hgroup hr html i iframe img input ins kbd keygen label legend li link main map mark menu meta meter nav noscript object ol optgroup option output p param pre progress q rp rt ruby s samp script section select small source strong style sub summary sup table tbody td textarea tfoot th thead time title tr track u ul var video wbr acronym applet basefont big center dialog dir font isindex noframes strike tt[*]; span(!math-formula)`
        window.CKEDITOR.config.pasteFilter = null
        window.CKEDITOR.config.title = false
        window.CKEDITOR.config.allowedContent = true
        window.CKEDITOR.plugins.addExternal('mathjax', '/scripts/ckeditor/plugins/mathjax/', 'plugin.js')
        window.CKEDITOR.plugins.addExternal(
          'answer-sheet-mathjax',
          '/scripts/ckeditor/plugins/answer-sheet-mathjax/',
          'plugin.js'
        )
        window.CKEDITOR.plugins.addExternal('lineheight', '/scripts/ckeditor/plugins/lineheight/', 'plugin.js')
        window.CKEDITOR.plugins.addExternal('image2', '/scripts/ckeditor/plugins/image2/', 'plugin.js')
        window.CKEDITOR.plugins.addExternal('newimage', '/scripts/ckeditor/plugins/newimage/', 'plugin.js')
        window.CKEDITOR.plugins.addExternal('text-emphasis', '/scripts/ckeditor/plugins/text-emphasis/', 'plugin.js')
        window.CKEDITOR.plugins.addExternal('wavy-underline', '/scripts/ckeditor/plugins/wavy-underline/', 'plugin.js')
        window.CKEDITOR.config.removePlugins = 'image, uploadimage'
        window.CKEDITOR.config.extraPlugins = 'mathjax, lineheight, image2, newimage'
        window.CKEDITOR.config.extraAllowedContent = 'br'

        // window.CKEDITOR.plugins.addExternal('ckeditor_wiris', 'https://ckeditor.com/docs/ckeditor4/4.12.1/examples/assets/plugins/ckeditor_wiris/', 'plugin.js')
        // window.CKEDITOR.config.extraPlugins = 'ckeditor_wiris'
        // window.CKEDITOR.config.removePlugins = 'image,uploadimage,uploadwidget,uploadfile,filetools,filebrowser'
        var mathElements = [
          'math',
          'maction',
          'maligngroup',
          'malignmark',
          'menclose',
          'merror',
          'mfenced',
          'mfrac',
          'mglyph',
          'mi',
          'mlabeledtr',
          'mlongdiv',
          'mmultiscripts',
          'mn',
          'mo',
          'mover',
          'mpadded',
          'mphantom',
          'mroot',
          'mrow',
          'ms',
          'mscarries',
          'mscarry',
          'msgroup',
          'msline',
          'mspace',
          'msqrt',
          'msrow',
          'mstack',
          'mstyle',
          'msub',
          'msup',
          'msubsup',
          'mtable',
          'mtd',
          'mtext',
          'mtr',
          'munder',
          'munderover',
          'semantics',
          'annotation',
          'annotation-xml',
        ]
        window.CKEDITOR.config.extraAllowedContent = mathElements.join(' ') + '(*)[*]{*}'
      })()
    </script>
  </head>

  <body>
    <div id="app"></div>
    <script>
      ;(function compatibilityCheck() {
        var browserVersion = getBrowserVersion()
        if (isBrowserVersionNeedUpdate(browserVersion)) {
          showUpdateMessage()
        }

        function getBrowserVersion() {
          var browser, version, matched
          var UA = window.navigator.userAgent.toLowerCase()
          if (!UA) {
            return {
              browser: null,
              version: null,
            }
          }

          if (/msie|trident/.test(UA)) {
            browser = 'ie'
            version = null
          } else if ((matched = UA.match(/edge\/(\d+)/))) {
            browser = 'edge'
            version = matched[1]
          } else if ((matched = UA.match(/chrome\/(\d+)/))) {
            /*新版edge及opera当作chrome*/
            browser = 'chrome'
            version = matched[1]
          } else if ((matched = UA.match(/firefox\/(\d+)/))) {
            browser = 'firefox'
            version = matched[1]
          } else if ((matched = UA.match(/version\/(\d+).+safari/))) {
            browser = 'safari'
            version = matched[1]
          }

          return {
            browser: browser || null,
            version: parseFloat(version) || null,
          }
        }

        function isBrowserVersionNeedUpdate(browserVersion) {
          var browser = browserVersion.browser,
            version = browserVersion.version
          if (browser == null) {
            return false
          }
          if (browser === 'chrome' && version >= 69) {
            return false
          } else if (browser === 'firefox' && version >= 60) {
            return false
          } else if (browser === 'safari' && version >= 13) {
            return false
          }

          return true
        }

        function showUpdateMessage() {
          var divCompatibilityCheckMessage = document.createElement('div')
          divCompatibilityCheckMessage.id = 'sureCompatibilityCheckMessage'
          divCompatibilityCheckMessage.style.position = 'fixed'
          divCompatibilityCheckMessage.style.zIndex = '99999999'
          divCompatibilityCheckMessage.style.left = '0px'
          divCompatibilityCheckMessage.style.top = '0px'
          divCompatibilityCheckMessage.style.width = '100%'
          divCompatibilityCheckMessage.style.height = '100%'
          divCompatibilityCheckMessage.style.backgroundColor = 'transparent'
          divCompatibilityCheckMessage.innerHTML =
            '<p style="font-size: 16px; padding: 50px 4%; background-color: #fff9e6;">您的浏览器版本较低，部分页面无法正常显示。请您使用最新版的<a href="http://browser.360.cn/" target="_blank">360浏览器</a>、<a href="https://browser.qq.com/" target="_blank">qq浏览器</a>或<a href="https://www.google.cn/chrome/" target="_blank">谷歌浏览器</a>。</p>'
          document.body.appendChild(divCompatibilityCheckMessage)
        }
      })()
    </script>
  </body>
</html>
