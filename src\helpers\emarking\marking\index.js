import { round } from 'lodash'
import { splitQuestionScore } from '@/helpers/emarking'

export function getImageBoxStyle({ width, rotate, naturalWidth, naturalHeight }) {
  let style = {
    width: width + 'px',
    transformOrigin: 'left top',
  }

  if (!(rotate > 0 && naturalWidth > 0 && naturalHeight > 0)) {
    return style
  }

  let translateX = 0
  let translateY = 0
  let height = (naturalHeight / naturalWidth) * width
  if (rotate === 90) {
    translateX = height
    translateY = 0
  } else if (rotate === 180) {
    translateX = width
    translateY = height
  } else if (rotate === 270) {
    translateX = 0
    translateY = width
  }
  style.transform = `translate(${translateX}px, ${translateY}px) rotate(${rotate}deg)`

  return style
}

export function checkQuestionScore(questions, isStrictMark) {
  for (let q of questions) {
    if (q.score == null || q.score === '') {
      return `${q.questionName}题尚未给分`
    }

    if (!(q.score >= q.minScore && q.score <= q.fullScore)) {
      return `${q.questionName}题给分错误`
    }

    // 严格给分（根据给分间隔给分）检查
    if (isStrictMark) {
      if (!q.keys.includes(q.score)) {
        return `${q.questionName}题未按给分间隔给分`
      }
    }
  }
}

export function getKeys(fullScore, minScore, scoreInterval) {
  let step = 1
  if (scoreInterval > 0 && scoreInterval <= fullScore - minScore) {
    step = scoreInterval
  }

  let keys = []
  for (let i = minScore; i < fullScore; i = round(i + step, 2)) {
    keys.push(i)
  }
  keys.push(fullScore)

  return keys
}

export function getBlockQuestionsDefineAndKeys(questions, customScoreIntervalStr) {
  let scoreIntervals = splitQuestionScore(customScoreIntervalStr)
  return questions.map(q => {
    // 优先使用自定义给分间隔
    let nameScore = scoreIntervals.find(x => x.name === q.questionCode)
    let scoreInterval = (nameScore && nameScore.score) || q.scoreInterval || null
    let minScore = q.minScore >= 0 && q.minScore <= q.fullScore ? q.minScore : 0
    let keys = getKeys(q.fullScore, minScore, scoreInterval)

    return {
      questionCode: q.questionCode,
      questionName: q.questionName,
      fullScore: q.fullScore,
      minScore,
      scoreInterval,
      keys,
    }
  })
}

export function replaceSubScoreName(subScore, questions) {
  return splitQuestionScore(subScore)
    .map(item => {
      let q = (questions || []).find(x => x.questionCode === item.name)
      let name = q ? q.questionName : item.name
      return `${name}=${item.score}`
    })
    .join(';')
}
