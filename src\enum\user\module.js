/**
 * 功能模块
 */

import Enum from '@/enum/enum'

export default new Enum({
  Question: {
    id: 'tk',
    name: '组卷服务',
  },
  InSchoolExam: {
    id: 'inschoolexam',
    name: '校内考试服务',
  },
  ClassPractise: {
    id: 'classpractice',
    name: '班级测练服务',
  },
  Homework: {
    id: 'homework',
    name: '作业服务',
  },
  QuestionFeedBack: {
    id: 'feedback',
    name: '错题反馈服务',
  },
  TeacherRegister: {
    id: 'teacherregister',
    name: '教师注册和设置任教',
  },
  AIScore: {
    id: 'aiscore',
    name: '智能阅卷服务',
  },
  CoachHomework: {
    id: 'coachhomework',
    name: '数智教辅服务',
  },
})
