import ScanTemplate from '@/helpers/scan_template/scan_template'
import SubjectiveArea from '@/helpers/scan_template/subjective_area'
import { groupArray } from '@/utils/array'

export default {
  namespaced: true,
  state: {
    scanTemplate: new ScanTemplate(),
    currentPageIndex: 0,
    // 准考号选项样板
    admissionOptionSample: null,
    // 客观题选项样板
    objectiveOptionSample: null,
  },
  getters: {
    /**
     * 可否更改题目
     */
    enableEditQuestion(state) {
      return state.scanTemplate.enableEditQuestion
    },
    onlyEditBlock(state) {
      return state.scanTemplate.onlyEditBlock
    },

    /**
     * 页面
     */
    pages(state) {
      return state.scanTemplate.pages
    },
    isDoubleSide(state) {
      return state.scanTemplate.isDoubleSide
    },
    currentPageIndex(state) {
      return state.currentPageIndex
    },
    currentPage(state) {
      return state.scanTemplate.pages[state.currentPageIndex]
    },
    currentPageImage(state, getters) {
      let currentPage = getters['currentPage']
      return currentPage && currentPage.image
    },

    /**
     * 科目题目题块
     */
    subjectObjectives(state) {
      return state.scanTemplate.subjectObjectives
    },
    subjectSubjectives(state) {
      return state.scanTemplate.subjectSubjectives
    },
    subjectBlocks(state) {
      return state.scanTemplate.subjectBlocks
    },
    // 选做题组
    selectGroups(state) {
      let selectBlocks = state.scanTemplate.subjectBlocks.filter(block => block.selectGroupName)
      return groupArray(selectBlocks, block => block.selectGroupName).map(g => ({
        selectGroupName: g.group[0].selectGroupName,
        selectCount: g.group[0].selectCount,
        blocks: g.group.sort((a, b) => a.questions[0].questionCode - b.questions[0].questionCode),
      }))
    },
    // 各题块区域相同的选做题组
    blocksSameAreaSelectGroups(state, getters) {
      let selectGroups = getters['selectGroups']
      let pages = getters['pages']
      if (selectGroups.length == 0) {
        return []
      }
      let blockIdAreasMap = new Map()
      pages.forEach(page => {
        page.subjectiveAreas.forEach(area => {
          let areas = blockIdAreasMap.get(area.blockId)
          if (areas) {
            areas.push(area)
          } else {
            blockIdAreasMap.set(area.blockId, [area])
          }
        })
      })
      // 判断每个题块均包含相同个数的区域，且各区域位置相同
      return selectGroups.filter(({ blocks }) => {
        let blockAreasList = blocks.map(block => blockIdAreasMap.get(block.blockId))
        if (blockAreasList.some(areas => !areas)) {
          return false
        }
        let areaCount = blockAreasList[0].length
        if (blockAreasList.some(areas => areas.length != areaCount)) {
          return false
        }
        // 同一题块的多个区域排序
        if (areaCount > 1) {
          blockAreasList.forEach(areas => {
            areas.sort((a, b) => {
              if (a.pageIndex == b.pageIndex) {
                if (a.rect.y == b.rect.y) {
                  return a.rect.x - b.rect.x
                } else {
                  return a.rect.y - b.rect.y
                }
              } else {
                return a.pageIndex - b.pageIndex
              }
            })
          })
        }
        for (let areaIdx = 0; areaIdx < areaCount; areaIdx++) {
          let firstBlockArea = blockAreasList[0][areaIdx]
          for (let blockIdx = 1; blockIdx < blockAreasList.length; blockIdx++) {
            if (!SubjectiveArea.isSameRect(firstBlockArea, blockAreasList[blockIdx][areaIdx])) {
              return false
            }
          }
        }
        return true
      })
    },

    /**
     * 模板题目题块
     */
    templateObjectives(state) {
      let list = []
      state.scanTemplate.questions.objectives.forEach(area => {
        area.questions.forEach(q => {
          let obj = state.scanTemplate.subjectObjectives.find(x => x.questionCode == q.questionCode)
          if (obj) {
            list.push(obj)
          }
        })
      })
      return list
    },
    templateBlocks(state) {
      return state.scanTemplate.questions.blocks
        .map(block => state.scanTemplate.subjectBlocks.find(x => x.blockId == block.subjectiveId))
        .filter(Boolean)
    },
    templateSubjectives(state, getters) {
      let list = []
      getters['templateBlocks'].forEach(block => {
        list.push(...block.questions)
      })
      return list
    },

    /**
     * 制作状态
     */
    locateStatus(state) {
      return state.scanTemplate.getLocateStatus()
    },
    titleStatus(state) {
      return state.scanTemplate.getTitleStatus()
    },
    admissionNumStatus(state) {
      return state.scanTemplate.getAdmissionNumStatus()
    },
    objectiveStatus(state) {
      return state.scanTemplate.getObjectiveStatus()
    },
    subjectiveStatus(state) {
      return state.scanTemplate.getSubjectiveStatus()
    },
    pageMarkStatus(state) {
      return state.scanTemplate.getPageMarkStatus()
    },
    missingMarkStatus(state) {
      return state.scanTemplate.getMissingMarkStatus()
    },
    subjectMarkStatus(state) {
      return state.scanTemplate.getSubjectMarkStatus()
    },
    selectMarkStatus(state) {
      return state.scanTemplate.getSelectMarkStatus()
    },
    scoreAreaStatus(state) {
      return state.scanTemplate.getScoreAreaStatus()
    },

    /**
     * 选项样板
     */
    admissionOptionSample(state) {
      return state.admissionOptionSample
    },
    objectiveOptionSample(state) {
      return state.objectiveOptionSample
    },
  },
  mutations: {
    reset(state) {
      state.scanTemplate = new ScanTemplate()
      state.currentPageIndex = 0
      state.admissionOptionSample = null
      state.objectiveOptionSample = null
    },
    clear(state) {
      state.scanTemplate.clear()
      state.currentPageIndex = 0
    },
    changeCurrentPageIndex(state, pageIndex) {
      if (pageIndex >= 0 && pageIndex < state.scanTemplate.pages.length) {
        state.currentPageIndex = pageIndex
      }
    },
    changeEnableEditQuestion(state, value) {
      state.scanTemplate.changeEnableEditQuestion(value)
    },

    /**
     * 定位
     */
    changeLocateMode(state, mode) {
      state.scanTemplate.changeLocateMode(mode)
    },
    addAnchor(state, data) {
      return state.scanTemplate.addAnchor(data)
    },
    deleteAnchor(state, data) {
      return state.scanTemplate.deleteAnchor(data)
    },
    addLine(state, data) {
      return state.scanTemplate.addLine(data)
    },
    deleteLine(state, data) {
      return state.scanTemplate.deleteLine(data)
    },
    addText(state, data) {
      return state.scanTemplate.addText(data)
    },
    deleteText(state, data) {
      return state.scanTemplate.deleteText(data)
    },

    /**
     * 标题
     */
    addTitle(state, data) {
      return state.scanTemplate.addTitle(data)
    },
    deleteTitle(state, data) {
      return state.scanTemplate.deleteTitle(data)
    },

    /**
     * 准考号
     */
    changeAdmissionNumType(state, data) {
      return state.scanTemplate.changeAdmissionNumType(data)
    },
    changeAdmissionNumLength(state, data) {
      return state.scanTemplate.changeAdmissionNumLength(data)
    },
    addAdmissionNumBarcode(state, data) {
      return state.scanTemplate.addAdmissionNumBarcode(data)
    },
    deleteAdmissionNumBarcode(state, data) {
      return state.scanTemplate.deleteAdmissionNumBarcode(data)
    },
    addAdmissionNumHandwriting(state, data) {
      return state.scanTemplate.addAdmissionNumHandwriting(data)
    },
    deleteAdmissionNumHandwriting(state, data) {
      return state.scanTemplate.deleteAdmissionNumHandwriting(data)
    },
    addAdmissionNumOmr(state, data) {
      return state.scanTemplate.addAdmissionNumOmr(data)
    },
    replaceAdmissionNumOmr(state, data) {
      return state.scanTemplate.replaceAdmissionNumOmr(data)
    },
    deleteAdmissionNumOmr(state, data) {
      return state.scanTemplate.deleteAdmissionNumOmr(data)
    },

    /**
     * 客观题
     */
    addObjectiveArea(state, data) {
      return state.scanTemplate.addObjectiveArea(data)
    },
    replaceObjectiveArea(state, data) {
      return state.scanTemplate.replaceObjectiveArea(data)
    },
    deleteObjectiveArea(state, data) {
      return state.scanTemplate.deleteObjectiveArea(data)
    },
    deleteObjectiveAreaBatch(state, data) {
      return state.scanTemplate.deleteObjectiveAreaBatch(data)
    },

    /**
     * 主观题
     */
    addSubjectiveAreaBatch(state, data) {
      return state.scanTemplate.addSubjectiveAreaBatch(data)
    },
    changeSubjectiveAreas(state, data) {
      return state.scanTemplate.changeSubjectiveAreas(data)
    },
    deleteSubjectiveAreaBatch(state, data) {
      return state.scanTemplate.deleteSubjectiveAreaBatch(data)
    },

    /**
     * 页码标记
     */
    addPageMark(state, data) {
      return state.scanTemplate.addPageMark(data)
    },
    deletePageMark(state, data) {
      return state.scanTemplate.deletePageMark(data)
    },
    changeIsPageMarkText(state, data) {
      return state.scanTemplate.changeIsPageMarkText(data)
    },

    /**
     * 选做标记
     */
    addSelectGroup(state, data) {
      return state.scanTemplate.addSelectGroup(data)
    },
    deleteSelectGroups(state, data) {
      return state.scanTemplate.deleteSelectGroups(data)
    },
    addSelectMarkArea(state, data) {
      return state.scanTemplate.addSelectMarkArea(data)
    },
    deleteSelectMarkArea(state, data) {
      return state.scanTemplate.deleteSelectMarkArea(data)
    },
    deleteNoBlockSelectMarkAreas(state) {
      return state.scanTemplate.deleteNoBlockSelectMarkAreas()
    },

    /**
     * 缺考标记
     */
    addMissingMark(state, data) {
      return state.scanTemplate.addMissingMark(data)
    },
    deleteMissingMark(state, data) {
      return state.scanTemplate.deleteMissingMark(data)
    },

    /**
     * 科目标记
     */
    addSubjectMark(state, data) {
      return state.scanTemplate.addSubjectMark(data)
    },
    deleteSubjectMark(state, data) {
      return state.scanTemplate.deleteSubjectMark(data)
    },

    /**
     * 遮挡检测
     */
    addBlockDetectSkipArea(state, data) {
      return state.scanTemplate.addBlockDetectSkipArea(data)
    },
    deleteBlockDetectSkipArea(state, data) {
      return state.scanTemplate.deleteBlockDetectSkipArea(data)
    },

    /**
     * 学生信息区域
     */
    addStudentInfoArea(state, data) {
      return state.scanTemplate.addStudentInfoArea(data)
    },
    deleteStudentInfoArea(state, data) {
      return state.scanTemplate.deleteStudentInfoArea(data)
    },

    /**
     * 打分区域
     */
    changeTrueFalseScoreAreaRect(state, data) {
      return state.scanTemplate.changeTrueFalseScoreAreaRect(data)
    },

    /**
     * 修改分数
     */
    changeQuestionScores(state, data) {
      return state.scanTemplate.changeQuestionScores(data)
    },

    /**
     * 选项样板
     */
    addAdmissionOptionSample(state, data) {
      state.admissionOptionSample = data
    },
    deleteAdmissionOptionSample(state) {
      state.admissionOptionSample = null
    },
    addObjectiveOptionSample(state, data) {
      state.objectiveOptionSample = data
    },
    deleteObjectiveOptionSample(state) {
      state.objectiveOptionSample = null
    },
  },
  actions: {
    import(context, { templateJson, imageUrls, subjectObjectives, subjectBlocks, enableEditQuestion, onlyEditBlock }) {
      return ScanTemplate.import({
        templateJson,
        imageUrls,
        subjectObjectives,
        subjectBlocks,
        enableEditQuestion,
        onlyEditBlock,
      }).then(scanTemplate => {
        context.state.scanTemplate = scanTemplate
      })
    },
    export(context) {
      let templateJSON = ''
      try {
        templateJSON = context.state.scanTemplate.export()
      } catch (err) {
        return Promise.reject(err)
      }
      return Promise.resolve(templateJSON)
    },
    exportBlockSubImages(context) {
      return context.state.scanTemplate.exportBlockSubImagesAsync()
    },
  },
}
