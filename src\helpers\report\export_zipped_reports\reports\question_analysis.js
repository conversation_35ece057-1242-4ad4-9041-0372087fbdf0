import { apiGetExpandQuestionAnalyse, apiGetUnionQuestionAnalyse } from '@/api/report'

import { generateExcelBlob } from '@/utils/excel_export'
import { retainTwoDecimalPlaces } from '@/utils/math'
import { getInstitutionName } from '../../tools/tools'

import { UUID_ZERO } from '@/const/string'

import Store from '@/store/index'

// 小题分析
export function generateExcelReportQuestionAnalysisBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let fileName = `${subject.subjectName}/${Store.getters['report/examName']}_小题分析_`
  let requestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    examSubjectId: subject.examSubjectId,
    reportName: 'sch_quesAnalyse',
  }

  if (category && category.categoryName) {
    fileName = category.categoryName + '/' + fileName
  }
  if (isMultipleSchoolLevel) {
    if (isNextLevel) {
      if (school) {
        fileName = `下级机构或学校报表/(学校)${school.schoolName}/${fileName}_${school.schoolName}`
        requestParams.schoolId = school.schoolId
      } else {
        fileName = `下级机构或学校报表/(机构)${institution.name}/${fileName}_${institution.name}`
        requestParams.organizationId = institution.id
        requestParams.reportName = undefined
      }
    } else {
      if (school) {
        fileName = `学校报表/${school.schoolName}/${fileName}_${school.schoolName}`
        requestParams.schoolId = school.schoolId
      } else {
        fileName = `${fileName}_联考`
        requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']
        requestParams.reportName = undefined
      }
    }
  } else {
    fileName = `${fileName}${Store.getters['report/currentSchoolName']}`
    requestParams.schoolId = Store.getters['report/currentSchoolId']
  }
  fileName = `${fileName}_${subject.subjectName}.xlsx`

  return fetchSheets(requestParams)
    .then(sheets => generateExcelBlob(sheets))
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}

async function fetchSheets(requestParams) {
  let tableColumns = [
    {
      title: '小题',
      key: 'newBranchName',
      group: true,
      width: 'auto',
    },
    {
      title: '满分',
      key: 'fullScore',
      group: true,
    },
    {
      title: '统计人数',
      key: 'countNumber',
    },
    {
      title: '最高分',
      key: 'maxScore',
      cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
    },
    {
      title: '最低分',
      key: 'minScore',
      cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
    },
    {
      title: '平均分',
      key: 'avgScore',
      cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
    },
    {
      title: '得分率',
      key: 'scoreRate',
      cellNumberFormat: value => (isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%'),
    },
    {
      title: '满分率',
      key: 'fullScoreRate',
      cellNumberFormat: value => (isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%'),
    },
    {
      title: '标准差',
      key: 'standardDeviation',
      cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
    },
    {
      title: '难度',
      key: 'difficult',
      cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
    },
    {
      title: '差异系数',
      key: 'differentCoefficient',
      cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
    },
  ]

  tableColumns.forEach(column => (column.width = column.width || 12))

  let tableData = []
  let rawData
  if (requestParams.organizationId) {
    rawData = await apiGetUnionQuestionAnalyse(requestParams)
    tableData = unExpandTransferData(rawData)
  } else {
    rawData = await apiGetExpandQuestionAnalyse(requestParams)
    tableData = expandTransferData(rawData.quesScoreVos || [])
  }

  if (!requestParams.organizationId) {
    tableColumns.splice(2, 0, {
      title: '单位名称',
      key: 'institutionName',
      width: 'auto',
    })
  } else if (tableData && tableData.length && tableData.some(x => x.knowledgesText)) {
    tableColumns.splice(2, 0, {
      title: '知识点',
      key: row => row.knowledgesText || '-',
      width: 'auto',
    })
  }

  if (tableData.length) {
    return [
      {
        sheetName: '小题分析',
        rows: tableData,
        columns: tableColumns,
      },
    ]
  } else {
    throw {
      code: 9998,
      msg: '暂无数据',
    }
  }
}

function unExpandTransferData(data) {
  let transferedData = []

  if (data && data.length) {
    transferedData = data
      .sort((a, b) => a.quesCode - b.quesCode)
      .map(question => {
        return {
          newBranchName:
            question.branchName ||
            question.questionName ||
            (question.branchCode && question.branchCode > 0
              ? `${question.quesCode}.${question.branchCode}`
              : question.quesCode + ''),
          questionName: question.questionName,
          branchName: question.branchName,
          fullScore: question.totalFullScore || 0,
          countNumber: question.countNum || '-',
          maxScore: (question.max && Number(question.max)) || 0,
          minScore: (question.min && Number(question.min)) || 0,
          avgScore: (question.avg && Number(question.avg)) || 0,
          scoreRate: (question.scoreRate && Number(question.scoreRate.slice(0, -1)) / 100) || 0,
          fullScoreRate: (question.fullRate && Number(question.fullRate.slice(0, -1)) / 100) || 0,
          standardDeviation: (question.sqrt && Number(question.sqrt)) || 0,
          difficult: (question.diff && Number(question.diff)) || 0,
          differentCoefficient: (question.cv && Number(question.cv)) || 0,
          knowledgesText: (question.knowledgeList || []).join('、'),
        }
      })
  }

  return transferedData
}

function expandTransferData(data) {
  let transferedData = []

  if (data && data.length) {
    data
      .sort((a, b) => a.quesCode - b.quesCode)
      .forEach(question => {
        if (!Store.getters['report/isMultipleSchool']) {
          question.classScores = question.classScores.filter(x => x.schoolId !== UUID_ZERO)
        }
        question.classScores
          .sort((a, b) => a.classSortCode - b.classSortCode)
          .forEach(institution => {
            transferedData.push({
              newBranchName:
                question.branchName ||
                question.questionName ||
                (question.quesNo ? retainTwoDecimalPlaces(question.quesNo) || '-' : '-'),
              fullScore: institution.totalFullScore || 0,
              countNumber: institution.countNum || '-',
              maxScore: (institution.max && Number(institution.max)) || 0,
              minScore: (institution.min && Number(institution.min)) || 0,
              avgScore: (institution.avg && Number(institution.avg)) || 0,
              scoreRate: (institution.scoreRate && Number(institution.scoreRate.slice(0, -1)) / 100) || 0,
              fullScoreRate: (institution.fullRate && Number(institution.fullRate.slice(0, -1)) / 100) || 0,
              standardDeviation: (institution.sqrt && Number(institution.sqrt)) || 0,
              difficult: (institution.diff && Number(institution.diff)) || 0,
              differentCoefficient: (institution.cv && Number(institution.cv)) || 0,
              institutionName: getInstitutionName(
                institution.schoolId,
                institution.classId,
                institution.schoolName,
                institution.className
              ),
            })
          })
      })
  }

  return transferedData
}
