/**
 * 学生填写信息
 */

import Enum from '@/enum/enum'

export default new Enum({
  TicketNumber: {
    id: '准考号',
    name: '准考号',
    required: true,
  },
  StudentName: {
    id: '姓名',
    name: '姓名',
    required: true,
  },
  SchoolName: {
    id: '学校',
    name: '学校',
    required: false,
  },
  ClassName: {
    id: '班级',
    name: '班级',
    required: false,
  },
  RoomNo: {
    id: '考场号',
    name: '考场号',
    required: false,
  },
  SeatNum: {
    id: '座位号',
    name: '座位号',
    required: false,
  },
})
