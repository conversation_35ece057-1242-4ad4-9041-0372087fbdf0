import Sheet from '@/helpers/answer_sheet/sheet'
import { preProcessPaper } from '@/helpers/answer_sheet/util'
import SourceTypeEnum from '@/enum/answer_sheet/source_type'
import ModeEnum from '@/enum/answer_sheet/mode'

export default {
  namespaced: true,
  state: {
    sheet: new Sheet(),
    coachBookInfo: null,
  },

  getters: {
    // 答题卡Id
    sheetId(state) {
      return state.sheet.id
    },
    // 答题卡名称
    sheetName(state) {
      return state.sheet.name
    },
    // 是否教辅
    isCoachBook(state) {
      return state.coachBookInfo != null
    },
    // 教辅合作商
    isCoachBookIgrade(state) {
      return state.coachBookInfo?.partner == '同级生'
    },
    isCoachBookYSH(state) {
      return state.coachBookInfo?.partner == '阅山海'
    },
    // 教辅书名
    coachBookInfoBookName(state) {
      return (state.coachBookInfo && state.coachBookInfo.bookName) || ''
    },
    // 教辅章节名
    coachBookInfoChapterName(state) {
      return (state.coachBookInfo && state.coachBookInfo.chapterName) || ''
    },
    // 教辅起始页码
    coachBookInfoStartPage(state) {
      return (state.coachBookInfo && state.coachBookInfo.startPage) || 1
    },
    // 制作来源
    from(state) {
      return state.sheet.from
    },
    // 是否来自自主创建
    isFromManual(state) {
      return state.sheet.from == SourceTypeEnum.Manual.id
    },
    // 是否来自题库试卷
    isFromPaper(state) {
      return state.sheet.from == SourceTypeEnum.Paper.id
    },
    // 是否来自考试科目
    isFromExam(state) {
      return state.sheet.from == SourceTypeEnum.Exam.id
    },
    // 学段
    stage(state) {
      return state.sheet.stage
    },
    // 学科
    subject(state) {
      return state.sheet.subject
    },
    // 关联的试卷ID
    paperId(state) {
      return state.sheet.paperId
    },
    // 关联的考试科目Id
    examSubjectId(state) {
      return state.sheet.examSubjectId
    },
    // 客观题是否分大题排
    objectivesSeparate(state) {
      return state.sheet.objectivesSeparate
    },
    // 当前模式
    mode(state) {
      return state.sheet.mode
    },
    // 是否先阅后扫
    isPostScan(state) {
      return state.sheet.mode == ModeEnum.PostScan.id
    },
    // 当前尺寸名称
    pageSizeId(state) {
      return state.sheet.size.id
    },
    // 当前尺寸页面内容宽度
    pageContentWidth(state) {
      return state.sheet.getPageContentWidth()
    },
    // 函数，返回当前尺寸下指定页（栏）的像素大小
    pageSize(state) {
      return pageIndex => state.sheet.getPageSize(pageIndex)
    },
    // 答题卡基础样式
    baseStyle(state) {
      return state.sheet.baseStyle
    },
    // 显示页眉内容
    showPageHeader(state) {
      return state.sheet.header.showPageHeader
    },
    // 页眉内容
    pageHeaderContent(state) {
      return state.sheet.header.pageHeaderContent
    },
    // 页脚内容
    pageFooterContent(state) {
      return state.sheet.pageFooterContent
    },
    // 答题卡页眉样式
    pageHeaderLayout(state) {
      return state.sheet.header.layoutPageHeader()
    },
    // 答题卡头部标题样式
    headerTitleLayout(state, getters) {
      return state.sheet.header.layoutTitle(getters.pageContentWidth)
    },
    // 答题卡头部其他样式
    headerGroupLayout(state, getters) {
      return state.sheet.header.layoutExceptTitle(getters.pageContentWidth, getters.baseStyle)
    },
    // 答题卡头部高度
    headerHeight(state, getters) {
      let group = getters.headerGroupLayout
      return group.top + group.height + group.marginBottom
    },
    // 答题卡标题
    title(state) {
      return state.sheet.header.title
    },
    // 显示操作说明二维码
    showInstruction(state) {
      return state.sheet.header.showInstruction
    },
    // 当前准考号类型
    ticketNumberType(state) {
      return state.sheet.header.ticketNumberType
    },
    // 准考号长度
    ticketNumberLength(state) {
      return state.sheet.header.ticketNumberLength
    },
    // 准考号omr是否横排
    ticketNumberOmrHorizontal(state) {
      return state.sheet.header.ticketNumberOmrHorizontal
    },
    // 学生填写信息
    stuInfoLabels(state) {
      return state.sheet.header.stuInfoLabels
    },
    // 学生填写信息别名
    stuInfoLabelAlias(state) {
      return state.sheet.header.stuInfoLabelAlias
    },
    // 二维码
    showQrcode(state) {
      return state.sheet.header.showQrcode
    },
    // 二维码文本
    qrcodeText(state) {
      return state.sheet.header.qrcodeText
    },
    qrcodeImageSrc(state) {
      return state.sheet.header.qrcodeImageSrc
    },
    // 注意事项
    cautions(state) {
      return state.sheet.header.cautions
    },
    showCautions(state) {
      return state.sheet.header.showCautions
    },
    // 缺考标记
    showMissingMark(state) {
      return state.sheet.header.showMissingMark
    },
    // 显示题目内容
    showQuestionContent(state) {
      return state.sheet.showQuestionContent
    },
    // 在先阅后扫模式下具体的打分方式
    scoreType(state) {
      return state.sheet.scoreType
    },
    // 对错框留空默认值
    scoreTypeTrueFalseEmptyDefault(state) {
      return state.sheet.scoreTypeTrueFalseEmptyDefault
    },
    // 答题卡大题数组
    topics(state) {
      return state.sheet.topics
    },
    // 答题卡页面数组
    pages(state) {
      return state.sheet.pages
    },
    // 答题卡页面总数
    totalPages(state) {
      return state.sheet.pages.length
    },
    // 函数，返回添加空行后的文本
    lineContent(state) {
      return str => state.sheet.getLineContent(str)
    },
    // 函数，返回填空题下划线文本
    blankContent(state) {
      return (blanks, questionCodeStr = '', spaceCharCount = 4) =>
        state.sheet.getBlankContent(blanks, questionCodeStr, spaceCharCount)
    },
    // 客观题
    objectiveQuestions(state) {
      return state.sheet.paperStructure.objectiveQuestions
    },
    // 主观题
    subjectiveQuestions(state) {
      return state.sheet.paperStructure.subjectiveQuestions
    },
    // 题块
    blocks(state) {
      return state.sheet.paperStructure.blocks
    },
    // 是否可增删题目
    enableEditQuestion(state) {
      return state.sheet.enableEditQuestion
    },
    // 题号列表
    questionCodes(state) {
      return state.sheet.paperStructure.getQuestionCodes()
    },
    // 最大题号
    maxQuestionCode(state) {
      return state.sheet.paperStructure.getMaxQuestionCode()
    },
    // 最大大题号
    maxTopicCode(state) {
      return state.sheet.paperStructure.getMaxTopicCode()
    },
    // 已存在的大题号、大题名
    topicCodeNames(state) {
      return state.sheet.paperStructure.getTopicCodeNames()
    },
    // 函数，返回题目所属题块的名称
    getBlockName(state) {
      return questionId => {
        let block = state.sheet.paperStructure.getBlockByQuestionId(questionId)
        return (block && block.blockName) || ''
      }
    },
    // 函数，返回页面块边框显隐
    getShowBoxContentBorder(state) {
      return pageBlock => state.sheet.getShowBoxContentBorder(pageBlock)
    },
    // 函数，返回某题组是否可与上一题组合并
    getCanMergeQuestionGroup(state) {
      return questionGroupId => state.sheet.getCanMergeQuestionGroup(questionGroupId)
    },
    // 函数，返回某题组是否可拆分
    getCanSplitQuestionGroup(state) {
      return questionGroupId => state.sheet.getCanSplitQuestionGroup(questionGroupId)
    },
    // 函数，返回某题组是否可设置为选做
    getCanSetSelect(state) {
      return questionGroupId => state.sheet.getCanSetSelect(questionGroupId)
    },
    // 函数，返回某题组是否可设置智能评分
    getCanSetAiScore(state) {
      return questionGroupId => state.sheet.getCanSetAiScore(questionGroupId)
    },
    // 是否有智能评分题目
    hasAiScoreQuestion(state) {
      return state.sheet.hasAiScoreQuestion()
    },
    // 客观题大题说明是否显示大题名称
    objectiveTopicDescriptionShowTopicName(state) {
      return state.sheet.objectiveTopicDescriptionShowTopicName()
    },
    // 主观题大题说明显示对错打分说明
    showScoreTypeTrueFalseTip(state) {
      return topicCode => {
        return state.sheet.showScoreTypeTrueFalseTip(topicCode)
      }
    },
    // 函数，生成页码
    generatePageNumber(state) {
      return (sideIndex, sideCount) => state.sheet.generatePageNumber(sideIndex, sideCount)
    },
  },

  mutations: {
    // 重置
    resetSheet(state) {
      state.sheet = new Sheet()
      state.coachBookInfo = null
    },
    // 由组卷试卷创建
    createFromPaper(state, { newId, paper, title }) {
      state.sheet = Sheet.createFromPaper({ newId, paper, title })
    },
    // 从试卷结构创建
    createFromPaperStructure(
      state,
      {
        newId,
        examName,
        gradeId,
        examSubjectId,
        subjectId,
        subjectName,
        mode,
        objectiveQuestions,
        subjectiveQuestions,
        blocks,
        paper,
      }
    ) {
      state.sheet = Sheet.createFromPaperStructure({
        newId,
        examName,
        gradeId,
        examSubjectId,
        subjectId,
        subjectName,
        mode,
        objectiveQuestions,
        subjectiveQuestions,
        blocks,
        paper,
      })
    },
    // 手动创建
    createFromManual(state, { newId }) {
      state.sheet = Sheet.createFromManual({ newId })
    },
    // 设置教辅
    setCoachBookInfo(state, coachBookInfo) {
      state.coachBookInfo = coachBookInfo
    },
    // 初始化教辅样式
    initCoachBookStyle(state) {
      let partner = state.coachBookInfo?.partner
      if (partner == '阅山海') {
        state.sheet.initCoachBookStyle(state.coachBookInfo)
        state.sheet.header.changePageHeaderContentCoachBook(
          state.coachBookInfo.bookName,
          state.coachBookInfo.chapterName
        )
        state.sheet.changePageFooterContentCoachBook(state.coachBookInfo.startPage, state.coachBookInfo.chapterName)
      } else if (partner == '同级生') {
        state.sheet.initCoachBookStyleIgrade(state.coachBookInfo)
      }
    },
    // 从答题卡提取教辅信息
    extractCoachBookInfo(state) {
      if (state.coachBookInfo?.partner != '阅山海') {
        return
      }
      let { bookName, chapterName } = state.sheet.header.extractCoachBookInfoFromPageHeaderContent()
      let startPage = state.sheet.extractStartPageFromPageFooter()
      state.coachBookInfo.bookName = bookName
      state.coachBookInfo.chapterName = chapterName
      state.coachBookInfo.startPage = startPage
    },
    // 生成页面
    generatePages(state) {
      state.sheet.generatePages()
    },
    // 改教辅书名
    changeCoachBookInfoBookName(state, bookName) {
      if (state.coachBookInfo) {
        state.coachBookInfo.bookName = bookName || ''
        state.sheet.header.changePageHeaderContentCoachBook(
          state.coachBookInfo.bookName,
          state.coachBookInfo.chapterName
        )
      }
    },
    // 改教辅章节名
    changeCoachBookInfoChapterName(state, chapterName) {
      if (state.coachBookInfo) {
        state.coachBookInfo.chapterName = chapterName || ''
        state.sheet.header.changePageHeaderContentCoachBook(
          state.coachBookInfo.bookName,
          state.coachBookInfo.chapterName
        )
        state.sheet.changePageFooterContentCoachBook(state.coachBookInfo.startPage, state.coachBookInfo.chapterName)
      }
    },
    // 改教辅起始页码
    changeCoachBookInfoStartPage(state, startPage) {
      if (state.coachBookInfo) {
        state.coachBookInfo.startPage = startPage || 1
        state.sheet.changePageFooterContentCoachBook(state.coachBookInfo.startPage, state.coachBookInfo.chapterName)
      }
    },
    // 改学段
    changeStage(state, stage) {
      state.sheet.changeStage(stage)
    },
    // 改学科
    changeSubject(state, subject) {
      state.sheet.changeSubject(subject)
    },
    // 改可否增删题目
    changeEnableEditQuestion(state, value) {
      state.sheet.changeEnableEditQuestion(value)
    },
    // 改页面尺寸
    changeSize(state, sizeId) {
      state.sheet.changeSize(sizeId)
    },
    // 改基础样式
    changeBaseStyle(state, style) {
      state.sheet.changeBaseStyle(style)
    },
    // 切换显示页眉内容
    changeShowPageHeader(state, value) {
      state.sheet.header.changeShowPageHeader(value)
    },
    // 改页眉内容
    changePageHeaderContent(state, value) {
      state.sheet.header.changePageHeaderContent(value)
    },
    // 改页脚内容
    changePageFooterContent(state, value) {
      state.sheet.changePageFooterContent(value)
    },
    // 改准考号类型
    changeTicketNumberType(state, type) {
      state.sheet.header.changeTicketNumberType(type)
    },
    // 改准考号长度
    changeTicketNumberLength(state, len) {
      state.sheet.header.changeTicketNumberLength(len)
    },
    // 改准考号omr是否横排
    changeTicketNumberOmrHorizontal(state, value) {
      state.sheet.header.changeTicketNumberOmrHorizontal(value)
    },
    // 改考生填写信息
    changeStuInfoLabels(state, labels) {
      state.sheet.header.changeStuInfoLabels(labels)
    },
    // 改标题
    changeTitle(state, title) {
      state.sheet.changeTitle(title)
    },
    // 改二维码
    changeShowQrcode(state, showQrcode) {
      state.sheet.header.changeShowQrcode(showQrcode)
    },
    // 改注意事项
    changeShowCautions(state, showCautions) {
      state.sheet.header.changeShowCautions(showCautions)
    },
    changeCautions(state, cautions) {
      state.sheet.header.changeCautions(cautions)
    },
    // 缺考标记
    changeShowMissingMark(state, showMissingMark) {
      state.sheet.header.changeShowMissingMark(showMissingMark)
    },
    // 显示题目内容
    displayQuestionContent(state) {
      state.sheet.displayQuestionContent()
    },
    // 隐藏题目内容
    hideQuestionContent(state) {
      state.sheet.hideQuestionContent()
    },
    // 切换显示材料题组
    toggleShowTrunkQuestionGroup(state, topicCode) {
      state.sheet.toggleShowTrunkQuestionGroup(topicCode)
    },
    // 改为线上阅卷模式
    changeModeToOnline(state) {
      state.sheet.changeModeToOnline()
    },
    // 改为先阅后扫模式
    changeModeToPostScan(state) {
      state.sheet.changeModeToPostScan()
    },
    // 改所有题组的打分方式
    changeAllScoreType(state, scoreTypeId) {
      state.sheet.changeAllScoreType(scoreTypeId)
    },
    // 添加客观题
    addObjectiveQuestions(state, { topicCode, topicName, objectiveQuestions, trueOrFalseType }) {
      state.sheet.addObjectiveQuestions({ topicCode, topicName, objectiveQuestions, trueOrFalseType })
    },
    // 添加填空题
    addFillBlankQuestions(state, { topicCode, topicName, blanksPerRow, subjectiveQuestions }) {
      state.sheet.addFillBlankQuestions({ topicCode, topicName, blanksPerRow, subjectiveQuestions })
    },
    // 添加解答题
    addEssayQuestions(state, { topicCode, topicName, subjectiveQuestions, rows, showLine }) {
      state.sheet.addEssayQuestions({ topicCode, topicName, subjectiveQuestions, rows, showLine })
    },
    // 添加语文作文题
    addChineseWritingQuestion(state, { topicCode, topicName, subjectiveQuestion, words }) {
      state.sheet.addChineseWritingQuestion({ topicCode, topicName, subjectiveQuestion, words })
    },
    // 添加英语作文题
    addEnglishWritingQuestion(state, { topicCode, topicName, subjectiveQuestion, rows }) {
      state.sheet.addEnglishWritingQuestion({ topicCode, topicName, subjectiveQuestion, rows })
    },
    // 删除客观题题组
    deleteObjectiveQuestionGroup(state, { questionGroupId, questionCodes }) {
      state.sheet.deleteQuestionGroup(questionGroupId, questionCodes)
    },
    // 删除主观题题组
    deleteSubjectiveQuestionGroup(state, questionGroupId) {
      state.sheet.deleteQuestionGroup(questionGroupId)
    },
    // 改题目分数
    changeQuestionScores(state, { objectives, subjectives }) {
      state.sheet.changeQuestionScores(objectives, subjectives)
    },
    // 客观题分大题排
    splitObjectiveTopics(state, { groupLength, trueOrFalseType }) {
      state.sheet.splitObjectiveTopics({ groupLength, trueOrFalseType })
    },
    // 客观题合并排
    mergeObjectiveTopics(state, { groupLength, trueOrFalseType }) {
      state.sheet.mergeObjectiveTopics({ groupLength, trueOrFalseType })
    },
    // 改题组类型
    changeQuestionGroupType(
      state,
      { questionGroupId, type, groupLength, trueOrFalseType, words, rows, showLine, branches, blanksPerRow }
    ) {
      state.sheet.changeQuestionGroupType({
        questionGroupId,
        type,
        groupLength,
        trueOrFalseType,
        words,
        rows,
        showLine,
        branches,
        blanksPerRow,
      })
    },
    // 改题组是否支持0.5分或半对错
    changeQuestionGroupScoreTypeHalfScore(state, { questionGroupId, halfScore }) {
      state.sheet.changeQuestionGroupScoreTypeHalfScore(questionGroupId, halfScore)
    },
    // 重设填空题组对错框
    setFillBlankQuestionGroupScoreBox(state, questionGroupId) {
      state.sheet.setFillBlankQuestionGroupScoreBox({ questionGroupId, showScoreBox: true })
    },
    // 设置页头实例
    changePageHeaderInstance(state, { pageIndex, instance }) {
      state.sheet.changePageHeaderInstance(pageIndex, instance)
    },
    // 设置页脚实例
    changePageFooterInstance(state, { pageIndex, instance }) {
      state.sheet.changePageFooterInstance(pageIndex, instance)
    },
    // 设置页面块实例
    changePageBlockInstance(state, { blockId, instance }) {
      state.sheet.changePageBlockInstance(blockId, instance)
    },
    // 设置页面块内容
    changePageBlockContent(state, { blockId, content }) {
      state.sheet.changePageBlockContent(blockId, content)
    },
    // 设置页面块选项内容
    changePageBlockOptionContent(state, { blockId, label, content }) {
      state.sheet.changePageBlockOptionContent(blockId, label, content)
    },
    // 设置页面块高度
    changePageBlockHeight(state, { blockId, height, contentHeight }) {
      state.sheet.changePageBlockHeight(blockId, height, contentHeight)
    },
    // 合并题组
    mergeQuestionGroup(state, questionGroupId) {
      state.sheet.mergeQuestionGroup(questionGroupId)
    },
    // 拆分题组
    splitQuestionGroup(state, questionGroupId) {
      state.sheet.splitQuestionGroup(questionGroupId)
    },
    // 设为选做
    setQuestionGroupSelect(state, { questionGroupId, selectCount }) {
      state.sheet.setQuestionGroupSelect(questionGroupId, selectCount)
    },
    // 改选做说明
    changeSelectDescription(state, { questionGroupId, selectDescription }) {
      state.sheet.changeSelectDescription(questionGroupId, selectDescription)
    },
    // 改智能评分
    changeAiScoreType(state, { questionGroupId, aiScoreTypeId }) {
      state.sheet.changeAiScoreType(questionGroupId, aiScoreTypeId)
    },
    // 设置对错打分说明
    changeShowScoreTypeTrueFalseTip(state, { topicCode, show, emptyDefault }) {
      state.sheet.changeShowScoreTypeTrueFalseTip({ topicCode, show, emptyDefault })
    },
    // 设置客观题大题说明
    changeObjectiveTopicDescriptionShowTopicName(state, { topicCode, showTopicName }) {
      state.sheet.changeObjectiveTopicDescriptionShowTopicName({ topicCode, showTopicName })
    },
    // 添加选择题括号中三角形
    addContentObjectiveTriangle(state) {
      state.sheet.addOrRemoveContentObjectiveTriangle('add')
    },
    // 删除选择题括号中三角形
    removeContentObjectiveTriangle(state) {
      state.sheet.addOrRemoveContentObjectiveTriangle('remove')
    },
  },
  actions: {
    // 导入
    importSheet(context, data) {
      return Sheet.import(data).then(sheet => {
        context.state.sheet = sheet
      })
    },
    // 导出答题卡
    exportSheet(context) {
      return context.state.sheet.exportSheet()
    },
    // 导出扫描模板
    exportScanTemplate(context) {
      return context.state.sheet.exportScanTemplate()
    },
    // 生成二维码图片
    generateQrcodeImage(context) {
      return context.state.sheet.header.generateQrcodeImage()
    },
    // 预处理试卷（渲染公式、设置图片大小、设置表格样式）
    preProcessPaper(context) {
      let { paper, baseStyle } = context.state.sheet
      if (!paper) {
        return Promise.resolve()
      }
      return preProcessPaper(paper, baseStyle)
    },
  },
}
