import ajax from '@/api/ajax'

export function apiCreateActivity(data) {
  return ajax.post({
    url: 'review/activity/create',
    data,
    requestName: '创建评审活动',
  })
}

export function apiGetReviewActivityList({ status, name, currentPage, pageSize }) {
  return ajax.get({
    url: 'review/activity/list',
    params: {
      status, // Number 不传为 '全部'
      name, // String 筛选，搜索
      pageNum: currentPage || 1, // Number
      pageSize: pageSize || 10, // Number
    },
    requestName: '获取评审活动列表',
  })
}

export function apiGetReviewActivityDetail(params) {
  return ajax.get({
    url: 'review/activity/detail',
    params: {
      id: params.activityId,
    },
    requestName: '获取评审活动详情',
  })
}

export function apiUpdateActivityBaseInfo(data) {
  return ajax.post({
    url: 'review/activity/update',
    data,
    requestName: '更新评审活动基本信息设置',
  })
}

export function apiUpdateActivityCategory(data) {
  return ajax.post({
    url: 'review/activity/editCategory',
    data,
    requestName: '更新评审活动类别',
  })
}

export function apiAddActivityCategory(data) {
  return ajax.post({
    url: 'review/activity/createCategory',
    data,
    requestName: '新增评审活动类别',
  })
}

export function apiDeleteActivityCategory(categoryId) {
  return ajax.delete({
    url: 'review/activity/deleteCategory',
    params: {
      categoryId,
    },
    requestName: '删除评审活动类别',
  })
}

export function apiAddActivitySchools({ activityId, schoolIds }) {
  return ajax.post({
    url: 'review/activity/addSchools',
    params: {
      activityId,
    },
    data: schoolIds,
    requestName: '新增活动参与学校',
  })
}

export function apiDeleteActivitySchools({ activityId, schoolIds }) {
  return ajax.delete({
    url: 'review/activity/deleteSchools',
    params: {
      activityId,
    },
    data: schoolIds,
    requestName: '删除活动参与学校',
  })
}

export function apiAddFormField(data) {
  return ajax.post({
    url: 'review/activity/addFormField',
    data,
    requestName: '添加分类的表单项',
  })
}

export function apiEditFormField(data) {
  return ajax.post({
    url: 'review/activity/editFormFields',
    data,
    requestName: '编辑分类的表单项',
  })
}

export function apiDeleteFormField(id) {
  return ajax.delete({
    url: 'review/activity/deleteFormField',
    params: {
      id,
    },
    requestName: '删除分类的表单项',
  })
}

export function apiSaveFormFieldOrder(data) {
  return ajax.put({
    url: 'review/activity/updateFormFieldOrder',
    data,
    requestName: '更新自定义表单项排序',
  })
}

export function apiDeleteActivityProject(id) {
  return ajax.delete({
    url: 'review/activity/delete',
    params: { id },
    requestName: '删除活动',
  })
}

export function apiChangeActivityStatus(params) {
  return ajax.post({
    url: 'review/activity/updateStatus',
    data: params,
    /**
     * params: {
        "activityId": 0,
        "currentStatus": 0,
        "newStatus": 0,
        "registerEndTime": "string",
        "registerStartTime": "string",
        "submitEndTime": "string"
     * }
     */
    requestName: '修改评审活动项目状态',
  })
}

/**
 * 添加评审活动人员
 */
export function addActivityUsers({ activityId, users }) {
  return ajax.post({
    url: 'review/activity/addUsers',
    params: {
      activityId,
    },
    data: users,
    requestName: '添加评审活动人员',
  })
}

/**
 * 删除评审活动人员
 */
export function deleteActivityUsers({ activityId, ids }) {
  return ajax.delete({
    url: 'review/activity/deleteUsers',
    params: {
      activityId,
    },
    data: ids,
    requestName: '删除评审活动人员',
  })
}

export function apiSaveAwardConfig(data) {
  return ajax.post({
    url: 'review/activity/saveOrUpdateAwardConfig',
    data,
    requestName: '保存奖项设置',
  })
}

export function apiGetAwardList(params) {
  return ajax.get({
    url: 'review/activity/reviewResultList',
    params,
    requestName: '获取获奖列表',
  })
}

export function apiGetAwardConfig(params) {
  return ajax.get({
    url: 'review/activity/awardConfig',
    params,
    requestName: '获取奖项设置',
  })
}

export function apiUpdateAwardStatus(params) {
  return ajax.post({
    url: 'review/activity/updateAwardStatus',
    data: params,
    requestName: '修改评审活动评奖状态',
  })
}

export function apiGetAwardSchoolStatistics(params) {
  return ajax.get({
    url: 'review/activity/schoolStatistics',
    params,
    requestName: '获取学校获奖统计',
  })
}

export function apiExportAwardList(params) {
  return ajax.download({
    url: 'review/activity/exportReviewResult',
    params,
    requestName: '导出评审结果',
  })
}

export function apiExportAwardSchoolStatistics(params) {
  return ajax.download({
    url: 'review/activity/exportSchoolStatistics',
    params,
    requestName: '导出学校获奖统计',
  })
}
