import ajax from '@/api/ajax'

export function apiGetTemplateContent(examSubjectId) {
  return ajax.get({
    url: 'scan/template/content',
    params: {
      examSubjectId,
    },
    requestName: '获取扫描模板',
  })
}

export function apiValidateTemplate(scanTemplateId) {
  return ajax.get({
    url: 'scan/template/validate',
    params: {
      scanTemplateId,
    },
    requestName: '检查模板制作是否规范',
  })
}
