/**
 * 树结构算法测试文件
 * Tree algorithms test file
 */

import {
  traverse,
  traverseBFS,
  findNode,
  findAllNodes,
  getNodePath,
  getMaxDepth,
  getNodeDepth,
  filterTree,
  mapTree,
  flattenTree,
  buildTreeFromFlat,
  getLeafNodes,
  getParentNode,
  getSiblingNodes,
  sortTree,
  cloneTree,
  validateTree,
  countNodes,
  getAllPaths,
  getNodeByPath,
  isTreeEqual
} from './tree.js'

// 测试数据
const sampleTree = [
  {
    id: 1,
    name: 'Root 1',
    children: [
      {
        id: 2,
        name: 'Child 1.1',
        children: [
          { id: 4, name: 'Grandchild 1.1.1', children: [] },
          { id: 5, name: 'Grandchild 1.1.2', children: [] }
        ]
      },
      {
        id: 3,
        name: 'Child 1.2',
        children: []
      }
    ]
  },
  {
    id: 6,
    name: 'Root 2',
    children: [
      { id: 7, name: 'Child 2.1', children: [] }
    ]
  }
]

const flatArray = [
  { id: 1, name: 'Root 1', parentId: null },
  { id: 2, name: 'Child 1.1', parentId: 1 },
  { id: 3, name: 'Child 1.2', parentId: 1 },
  { id: 4, name: 'Grandchild 1.1.1', parentId: 2 },
  { id: 5, name: 'Grandchild 1.1.2', parentId: 2 },
  { id: 6, name: 'Root 2', parentId: null },
  { id: 7, name: 'Child 2.1', parentId: 6 }
]

// 测试函数
function runTests() {
  console.log('=== 树结构算法测试 ===\n')

  // 1. 深度优先遍历测试
  console.log('1. 深度优先遍历 (DFS):')
  const dfsResult = []
  traverse(sampleTree, node => dfsResult.push(node.name))
  console.log('遍历结果:', dfsResult.join(' -> '))
  console.log()

  // 2. 广度优先遍历测试
  console.log('2. 广度优先遍历 (BFS):')
  const bfsResult = []
  traverseBFS(sampleTree, node => bfsResult.push(node.name))
  console.log('遍历结果:', bfsResult.join(' -> '))
  console.log()

  // 3. 查找节点测试
  console.log('3. 查找节点:')
  const foundNode = findNode(sampleTree, node => node.id === 4)
  console.log('查找ID为4的节点:', foundNode ? foundNode.name : '未找到')
  console.log()

  // 4. 查找所有匹配节点
  console.log('4. 查找所有包含"Child"的节点:')
  const allChildNodes = findAllNodes(sampleTree, node => node.name.includes('Child'))
  console.log('找到的节点:', allChildNodes.map(n => n.name).join(', '))
  console.log()

  // 5. 获取节点路径
  console.log('5. 获取节点路径:')
  const path = getNodePath(sampleTree, node => node.id === 5)
  console.log('ID为5的节点路径:', path ? path.map(n => n.name).join(' -> ') : '未找到')
  console.log()

  // 6. 获取最大深度
  console.log('6. 树的最大深度:')
  const maxDepth = getMaxDepth(sampleTree)
  console.log('最大深度:', maxDepth)
  console.log()

  // 7. 获取节点深度
  console.log('7. 节点深度:')
  const nodeDepth = getNodeDepth(sampleTree, node => node.id === 4)
  console.log('ID为4的节点深度:', nodeDepth)
  console.log()

  // 8. 过滤树
  console.log('8. 过滤树（只保留包含"1"的节点及其路径）:')
  const filteredTree = filterTree(sampleTree, node => node.name.includes('1'))
  console.log('过滤后的树:', JSON.stringify(filteredTree, null, 2))
  console.log()

  // 9. 映射树
  console.log('9. 映射树（添加level属性）:')
  let currentLevel = 0
  const mappedTree = mapTree(sampleTree, node => {
    const level = getNodeDepth(sampleTree, n => n.id === node.id)
    return { ...node, level }
  })
  console.log('映射后的树（部分）:', mappedTree[0].name, 'level:', mappedTree[0].level)
  console.log()

  // 10. 扁平化树
  console.log('10. 扁平化树:')
  const flattenedTree = flattenTree(sampleTree)
  console.log('扁平化结果:', flattenedTree.map(n => n.name).join(', '))
  console.log()

  // 11. 从扁平数组构建树
  console.log('11. 从扁平数组构建树:')
  const builtTree = buildTreeFromFlat(flatArray)
  console.log('构建的树根节点:', builtTree.map(n => n.name).join(', '))
  console.log()

  // 12. 获取叶子节点
  console.log('12. 获取叶子节点:')
  const leafNodes = getLeafNodes(sampleTree)
  console.log('叶子节点:', leafNodes.map(n => n.name).join(', '))
  console.log()

  // 13. 获取父节点
  console.log('13. 获取父节点:')
  const parentNode = getParentNode(sampleTree, node => node.id === 4)
  console.log('ID为4的节点的父节点:', parentNode ? parentNode.name : '未找到')
  console.log()

  // 14. 获取兄弟节点
  console.log('14. 获取兄弟节点:')
  const siblingNodes = getSiblingNodes(sampleTree, node => node.id === 4)
  console.log('ID为4的节点的兄弟节点:', siblingNodes.map(n => n.name).join(', '))
  console.log()

  // 15. 排序树
  console.log('15. 按名称排序树:')
  const sortedTree = sortTree(sampleTree, (a, b) => a.name.localeCompare(b.name))
  console.log('排序后的根节点:', sortedTree.map(n => n.name).join(', '))
  console.log()

  // 16. 克隆树
  console.log('16. 克隆树:')
  const clonedTree = cloneTree(sampleTree)
  console.log('克隆成功:', clonedTree[0].name === sampleTree[0].name && clonedTree !== sampleTree)
  console.log()

  // 17. 验证树结构
  console.log('17. 验证树结构:')
  const isValid = validateTree(sampleTree)
  console.log('树结构有效:', isValid)
  console.log()

  // 18. 计算节点总数
  console.log('18. 计算节点总数:')
  const totalNodes = countNodes(sampleTree)
  console.log('节点总数:', totalNodes)
  console.log()

  // 19. 获取所有路径
  console.log('19. 获取所有路径:')
  const allPaths = getAllPaths(sampleTree)
  console.log('路径总数:', allPaths.length)
  console.log('第一条路径:', allPaths[0].map(n => n.name).join(' -> '))
  console.log()

  // 20. 根据路径获取节点
  console.log('20. 根据路径获取节点:')
  const nodeByPath = getNodeByPath(sampleTree, [1, 2, 4])
  console.log('路径[1,2,4]对应的节点:', nodeByPath ? nodeByPath.name : '未找到')
  console.log()

  // 21. 比较树是否相等
  console.log('21. 比较树是否相等:')
  const clonedTree2 = cloneTree(sampleTree)
  const isEqual = isTreeEqual(sampleTree, clonedTree2)
  console.log('原树与克隆树相等:', isEqual)
  console.log()

  console.log('=== 所有测试完成 ===')
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.runTreeTests = runTests
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runTests, sampleTree, flatArray }
}

// 自动运行测试（如果直接执行此文件）
if (typeof window === 'undefined' && typeof module !== 'undefined' && require.main === module) {
  runTests()
}
