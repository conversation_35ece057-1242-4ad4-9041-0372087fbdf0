import ajax from '@/api/ajax'
import UserTypeEnum from '@/enum/user/user_type'

export function apiGetStageGradeSubjects() {
  return ajax
    .get({
      url: 'user/user/gradeLevel',
      requestName: '获取学段年级科目定义',
    })
    .then(data =>
      (data || []).map(stage => ({
        id: Number(stage.id),
        name: stage.name,
        grades: stage.grades.map(g => ({
          id: Number(g.id),
          name: g.name,
          subjects: g.subjects.map(s => ({
            id: Number(s.id),
            name: s.name,
          })),
        })),
      }))
    )
}

export function apiLogin(loginInfo) {
  let loginData = {
    name: loginInfo.userName,
    pwd: loginInfo.password,
    key: UserTypeEnum.Teacher.id,
    checkSchoolLogin: true,
  }
  return ajax
    .post({
      url: `user/user/login`,
      data: loginData,
      params: {
        from: 'pc',
      },
    })
    .then(data => {
      return {
        token: data.token,
        info: {
          userId: data.userInfo.userId || '',
          userName: data.userInfo.userName || '',
          realName: data.userInfo.realName || '',
          identity: data.userInfo.identity || '',
          sex: data.userInfo.sex || '',
          mobile: data.userInfo.mobile || '',
          email: data.userInfo.email || '',
          avatarUrl: data.userInfo.avatar || '',
          schoolId: data.userInfo.schoolId || '',
          schoolName: data.userInfo.schoolName || '',
          schoolType: data.userInfo.schoolType || 0,
          roles: data.userInfo.roles.map(id => Number(id)),
          teachings: data.userInfo.teachings || [],
          schoolPositions: data.userInfo.schoolPositions || [],
          isSystem: data.userInfo.isSys || false,
          needChangePassword: data.userInfo.needChangePwd || false,
          needUpdatePassword: data.userInfo.isExpiredPwd || false,
          userType: data.userInfo.userType,
        },
      }
    })
}

export function apiGetUserInfo() {
  return ajax
    .get({
      url: `user/user/Info`,
      requestName: '获取用户信息',
    })
    .then(data => {
      return {
        userId: data.userId || '',
        userName: data.userName || '',
        realName: data.realName || '',
        identity: data.identity || '',
        sex: data.sex || '',
        mobile: data.mobile || '',
        email: data.email || '',
        avatarUrl: data.avatar || '',
        schoolId: data.schoolId || '',
        schoolName: data.schoolName || '',
        schoolType: data.schoolType || 0,
        roles: data.roles.map(id => Number(id)),
        teachings: data.teachings || [],
        schoolPositions: data.schoolPositions || [],
        isSystem: data.isSys || false,
        needChangePassword: data.needChangePwd || false,
        needUpdatePassword: data.isExpiredPwd || false,
        userType: data.userType,
      }
    })
}

export function apiChangeUserInfo(data) {
  return ajax.put({
    url: 'user/user/changeMsg',
    data: {
      realName: data.realName,
      sex: data.sex,
      key: data.identity,
    },
    requestName: '修改个人信息',
  })
}

export function apiChangePassword(data) {
  return ajax.put({
    url: 'user/user/changePwd',
    data: {
      oldPwd: data.oldPassword,
      newPwd: data.newPassword,
      key: data.identity,
    },
    requestName: '修改密码',
  })
}

export function apiLogout() {
  return ajax.delete({
    url: 'user/user/logout',
  })
}

// 设置默认学校
export function apiSetDefaultSchool(schoolId) {
  return ajax.put({
    url: 'user/user/mySchool',
    params: {
      schoolId,
    },
    requestName: '设置默认学校',
  })
}

// 获取用户所在学校列表
export function apiGetUserSchools() {
  return ajax.get({
    url: 'user/user/mySchools',
    params: {
      checkSchoolLogin: true,
    },
    requestName: '获取学校列表',
  })
}

// 切换用户学校
export function apiSwitchSchool(schoolId) {
  return ajax.post({
    url: 'user/user/switchSchool',
    params: {
      schoolId,
      from: 'pc',
      checkSchoolLogin: true,
    },
    requestName: '切换学校',
  })
}

/**
 * 编辑员
 */
// 获取学科编辑人员
export function apiGetEditorUsers(params) {
  let sendParams = {
    gradeLevel: params.stageId,
    subjectId: params.subjectId,
  }

  return ajax
    .get({
      url: 'user/user/editList',
      params: sendParams,
      requestName: '获取编辑人员名单',
    })
    .then(data => {
      return data.map(editor => ({
        userId: editor.id,
        realName: editor.realName || editor.userName || '',
      }))
    })
}

// 获取编辑人员信息（含角色）
export function apiGetEditorsInfo({ stageId, subjectId }) {
  return ajax
    .get({
      url: 'user/user/editorsInfo',
      params: {
        gradeLevel: stageId,
        subjectId,
      },
      requestName: '获取编辑人员信息',
    })
    .then(data => {
      data.forEach(editor => {
        editor.roles.forEach(role => {
          role.stageId = role.gradeLevel
        })
        editor.roles.sort((a, b) => {
          if (a.stageId == b.stageId) {
            return a.subjectId - b.subjectId
          }
          return a.stageId - b.stageId
        })
      })
      return data
    })
}

// 删除编辑账号
export function apiDeleteEditorUser(userId) {
  return ajax.put({
    url: 'user/user/delEditor',
    params: {
      id: userId,
    },
    requestName: '删除编辑员',
  })
}

// 创建编辑账号
export function apiCreateEditorUser(info) {
  return ajax.post({
    url: 'user/user/createEditor',
    data: {
      userName: info.name,
      password: info.password,
      gradeLevel: info.stageId,
      subjectId: info.subjectId,
    },
    requestName: '新增编辑员',
  })
}

/**
 * 学校服务
 */
export function apiGetUserModules() {
  return ajax.get({
    url: 'user/user/schoolModules',
    requestName: '获取已开通服务',
  })
}

export function apiGetUserSchoolList(params) {
  return ajax.get({
    url: 'user/school/getUserSchoolList',
    params: params,
    requestName: '查询教师手机号',
  })
}

export function apiGetBindingPlatform(params) {
  return ajax.get({
    url: 'user/oauth/platforms',
    params: params,
  })
}

export function apiUpdateAvatar(file) {
  return ajax.upload({
    url: 'user/user/updateAvatar',
    data: {
      file: file,
    },
    requestName: '更新头像',
  })
}

export function apiGetUserQuestionLibraryRoles(requestParams) {
  return ajax.get({
    url: 'user/user/myEditorRoles',
    params: {
      gradeLevel: requestParams.gradeLevel, // Number
      subjectId: requestParams.subjectId, // Number
    },
    // requestName: '获取用户在指定学段学科下拥有的角色权限',
  })
}

export function apiUpdateInstitutionFinancialStaffInfo(info) {
  return ajax.post({
    url: 'user/user/savaOrUpdateSchoolFinancialStaffInfo',
    data: info, // Object* { email(String), mobile(String), name(String)， invoiceFullName(String), examId(String), taxNo(String) }
    requestName: '新增或编辑机构财务信息',
  })
}

export function apiDeleteInstitutionFinancialStaffInfo() {
  return ajax.delete({
    url: 'user/user/deleteSchoolFinancialStaffInfo',
    requestName: '删除机构财务信息',
  })
}

export function apiGetInstitutionFinancialStaffInfo() {
  return ajax.get({
    url: 'user/user/schoolFinancialStaffInfo',
    requestName: '获取机构财务信息',
  })
}

export function apiDownloadExamSchoolFinancialStaffInfos(examId) {
  return ajax.download({
    url: 'user/user/exportUnionExamSchoolFinancialStaffInfos',
    params: {
      examId: examId, // String*
    },
    requestName: '导出联考学校财务信息表',
  })
}

export function apiAdminLoginUserAccount({ teacherId }) {
  return ajax.get({
    url: 'user/user/sysAdminLoginUserAccount',
    params: {
      teacherId,
    },
    requestName: '在线协助登录',
  })
}

export function apiGetUnionExamSchoolFinancialStaffInfos(examId) {
  return ajax.get({
    url: 'user/user/unionExamSchoolFinancialStaffInfos',
    params: {
      examId: examId, // * String
    },
    requestName: '查询联考学校财务信息表',
  })
}

export function apiSaveExamSchoolFinancialStaffInfo(requestParams) {
  return ajax.post({
    url: 'user/user/saveExamSchoolFinancialStaffInfo',
    data: {
      email: requestParams.email, // String
      examId: requestParams.examId, // String
      mobile: requestParams.mobile, // String
      name: requestParams.name, // String
      invoiceFullName: requestParams.invoiceFullName, // String
      taxNo: requestParams.taxNo, // String
    },
    requestName: '联考项目提交本校财务信息',
  })
}

export function apiDeleteExamSchoolFinancialStaffInfo(examId) {
  return ajax.delete({
    url: 'user/user/deleteExamSchoolFinancialStaffInfo',
    params: {
      examId: examId, // * String
    },
    requestName: '联考项目删除本校财务信息',
  })
}
