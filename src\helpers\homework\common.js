// id
export function uid(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'

  let str = ''
  for (let i = 0; i < length; i++) {
    str += chars[Math.floor(Math.random() * chars.length)]
  }

  return str
}

/**
 * 分组
 * @param {Array} arr 待分组的数组
 * @param {Function} groupByFunc 分组依据函数，必须返回 Number, String 或 Boolean 值
 * @param {Boolean} keepOrder 指示是否保持原数组顺序，默认保持；不保持顺序性能更优 ps: 实测差别不大
 */
export function groupArray(arr, groupByFunc, keepOrder = true) {
  let arrWithKey = arr.map(x => ({
    key: groupByFunc(x),
    value: x,
  }))

  let result = []

  if (keepOrder) {
    arrWithKey.forEach(item => {
      let existing = result.find(x => x.key === item.key)
      if (existing) {
        existing.group.push(item.value)
      } else {
        result.push({
          key: item.key,
          group: [item.value],
        })
      }
    })
  } else {
    arrWithKey.sort((a, b) => (a.key < b.key ? -1 : 1))

    let lastGroupItem = null
    arrWithKey.forEach(item => {
      if (!lastGroupItem || lastGroupItem.key !== item.key) {
        lastGroupItem = {
          key: item.key,
          group: [item.value],
        }
        result.push(lastGroupItem)
      } else {
        lastGroupItem.group.push(item.value)
      }
    })
  }

  return result
}

/**
 * 相邻分组
 * @param {*} arr
 * @param {*} groupByFunc
 */
export function groupArrayAdjacent(arr, groupByFunc) {
  let arrWithKey = arr.map(x => ({
    key: groupByFunc(x),
    value: x,
  }))

  let result = []
  let lastGroup = {
    key: undefined,
    group: [],
  }

  arrWithKey.forEach(item => {
    if (item.key === lastGroup.key && lastGroup.key) {
      lastGroup.group.push(item.value)
    } else {
      if (lastGroup.group.length) {
        result.push(lastGroup)
      }
      lastGroup = {
        key: item.key,
        group: [item.value],
      }
    }
  })
  if (lastGroup.group.length) {
    result.push(lastGroup)
  }

  return result
}

/**
 *
 * @param {Array} arr 待拆分的已分组的数组
 * @param {Function} unGroupByFunc 拆分每组时调用的函数，返回数组
 */
export function unGroupArray(arr, unGroupByFunc) {
  return arr.reduce((a, c) => a.concat(unGroupByFunc(c)), [])
}
