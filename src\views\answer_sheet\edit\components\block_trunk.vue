<template>
  <div class="block-trunk">
    <com-box-content
      ref="contentBox"
      :content="block.content"
      :width="pageBodyWidth"
      :height="block.contentHeight"
      :padding-top="2"
      :border="contentBorderStyle"
      :resizable="true"
      @height-change="handleContentHeightChange"
      @resize="handleResize"
      @resize-end="handleResizeEnd"
      @content-change="changeContent"
    ></com-box-content>
  </div>
</template>

<script>
  import comBoxContent from './box_content.vue'

  import { mapMutations } from 'vuex'

  export default {
    components: {
      comBoxContent,
    },
    props: {
      block: {
        type: Object,
        required: true,
      },
    },
    emits: ['height-change'],
    computed: {
      /**
       * 样式
       */
      pageBodyWidth() {
        let pageSize = this.$store.getters['answerSheet/pageSize'](this.block.pageIndex)
        return pageSize.width - pageSize.left - pageSize.right
      },
      contentBorderStyle() {
        return this.$store.getters['answerSheet/getShowBoxContentBorder'](this.block)
      },
    },
    created() {
      this.changeInstance()
    },
    updated() {
      this.changeInstance()
    },
    methods: {
      ...mapMutations('answerSheet', ['changePageBlockInstance', 'changePageBlockContent', 'changePageBlockHeight']),

      /**
       * 实例、内容、高度
       */
      changeInstance() {
        this.changePageBlockInstance({
          blockId: this.block.id,
          instance: this,
        })
      },
      changeContent(content) {
        this.changePageBlockContent({
          blockId: this.block.id,
          content: content,
        })
      },
      changeHeight(contentHeight = this.block.contentHeight, emitEvent) {
        let height = contentHeight
        let heightChanged = height != this.block.height
        this.changePageBlockHeight({
          blockId: this.block.id,
          height,
          contentHeight,
        })
        if (emitEvent || (emitEvent == null && heightChanged)) {
          this.$emit('height-change', height)
        }
      },
      handleContentHeightChange(contentHeight) {
        this.changeHeight(contentHeight)
      },
      handleResize(contentHeight) {
        this.changeHeight(contentHeight, false)
      },
      handleResizeEnd() {
        this.changeHeight(this.contentHeight, true)
      },

      /**
       * 拆分
       */
      split(getNextPageAvailableHeightFunc) {
        let blocks
        try {
          blocks = this.$refs['contentBox'].split(getNextPageAvailableHeightFunc, this.block.questionGroup)
        } catch {
          blocks = []
        }
        return blocks
      },
    },
  }
</script>
