import { deepCopy } from '@/utils/object'

import Roles, { ReportRoles } from '@/enum/user/role'

const Reports = [
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_overview',
    reportText: '考情概览',
    detail: null,
  },
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_stuScore',
    reportText: '考生成绩',
    detail: [
      {
        name: 'showStudentRegionRank',
        text: '显示联考排名',
      },
      {
        name: 'showStudentInstitutionRank',
        text: '显示机构排名',
      },
      {
        name: 'showStudentSchoolRank',
        text: '显示校排名',
      },
      {
        name: 'showStudentClassRank',
        text: '显示班排名',
      },
      {
        name: 'showStudentScore',
        text: '显示科目成绩',
      },
      {
        name: 'showStudentScoreLevel',
        text: '显示等级分',
      },
      {
        name: 'showStudentPaper',
        text: '显示考生原卷',
      },
      {
        name: 'showStudentPaperBlockScore',
        text: '考生原卷显示题块分数',
      },
      {
        name: 'showStudentPaperMarker',
        text: '考生原卷显示评卷员',
      },
      // {
      //   name: 'canExportTopic',
      //   text: '可导出学生大题得分明细表'
      // },
      // {
      //   name: 'canExportDetail',
      //   text: '可导出得分明细表'
      // }
    ],
  },
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_newScore',
    reportText: '新高考报告',
    detail: null,
  },
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_schCompare',
    reportText: '学校对比',
    detail: [
      {
        name: 'showSchoolRegionRank',
        text: '显示联考排名',
      },
    ],
  },
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_scoreInterval',
    reportText: '分数段分析',
    detail: null,
  },
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_rankInterval',
    reportText: '名次段分析',
    detail: null,
  },
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_scoreLevel',
    reportText: '成绩分档分析',
    detail: null,
  },
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_scoreLine',
    reportText: '上线分析',
    detail: null,
  },
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_answer',
    reportText: '作答统计',
    detail: null,
  },
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_quesAnalyse',
    reportText: '小题分析',
    detail: null,
  },
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_topicAnalyse',
    reportText: '大题分析',
    detail: null,
  },
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_knowAnalyse',
    reportText: '知识点分析',
    detail: null,
  },
  {
    levelName: 'union',
    levelText: '联考报告',
    reportName: 'union_paper',
    reportText: '试卷分析',
    detail: null,
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_overview',
    reportText: '考情概览',
    detail: [
      {
        name: 'showSchoolRegionRank',
        text: '显示联考排名',
        levelName: 'union',
      },
      {
        name: 'showExportSchoolPDFBtn',
        text: '校级PDF',
      },
    ],
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_stuScore',
    reportText: '考生成绩',
    detail: [
      {
        name: 'showStudentRegionRank',
        text: '显示联考排名',
        levelName: 'union',
      },
      {
        name: 'showStudentSchoolRank',
        text: '显示校排名',
      },
      {
        name: 'showStudentClassRank',
        text: '显示班排名',
      },
      {
        name: 'showStudentScore',
        text: '显示科目成绩',
      },
      {
        name: 'showStudentScoreLevel',
        text: '显示等级分',
      },
      {
        name: 'showStudentPaper',
        text: '显示考生原卷',
      },
      {
        name: 'showStudentPaperBlockScore',
        text: '考生原卷显示题块分数',
      },
      {
        name: 'showStudentPaperMarker',
        text: '考生原卷显示评卷员',
      },
      {
        name: 'canExportTopic',
        text: '可导出学生大题得分明细表',
      },
      {
        name: 'canExportDetail',
        text: '可导出得分明细表',
      },
    ],
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_newScore',
    reportText: '新高考报告',
    detail: null,
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_clsCompare',
    reportText: '班级对比',
    detail: [
      {
        name: 'showClassRegionRank',
        text: '显示联考排名',
        levelName: 'union',
      },
      {
        name: 'showClassSchoolRank',
        text: '显示校排名',
      },
    ],
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_scoreInterval',
    reportText: '分数段分析',
    detail: null,
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_rankInterval',
    reportText: '名次段分析',
    detail: null,
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_critical',
    reportText: '临界生统计',
    detail: null,
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_scoreLevel',
    reportText: '成绩分档分析',
    detail: null,
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_scoreLine',
    reportText: '上线分析',
    detail: null,
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_answer',
    reportText: '作答统计',
    detail: null,
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_quesAnalyse',
    reportText: '小题分析',
    detail: null,
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_topicAnalyse',
    reportText: '大题分析',
    detail: null,
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_knowAnalyse',
    reportText: '知识点分析',
    detail: null,
  },
  {
    levelName: 'sch',
    levelText: '校级报告',
    reportName: 'sch_paper',
    reportText: '试卷分析',
    detail: null,
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_overview',
    reportText: '考情概览',
    detail: [
      {
        name: 'showClassRegionRank',
        text: '显示联考排名',
        levelName: 'union',
      },
      {
        name: 'showClassSchoolRank',
        text: '显示校排名',
      },
    ],
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_stuScore',
    reportText: '考生成绩',
    detail: [
      {
        name: 'showStudentRegionRank',
        text: '显示联考排名',
        levelName: 'union',
      },
      {
        name: 'showStudentSchoolRank',
        text: '显示校排名',
      },
      {
        name: 'showStudentClassRank',
        text: '显示班排名',
      },
      {
        name: 'showStudentScore',
        text: '显示科目成绩',
      },
      {
        name: 'showStudentScoreLevel',
        text: '显示等级分',
      },
      {
        name: 'showStudentPaper',
        text: '显示考生原卷',
      },
      {
        name: 'showStudentPaperBlockScore',
        text: '考生原卷显示题块分数',
      },
      {
        name: 'showStudentPaperMarker',
        text: '考生原卷显示评卷员',
      },
      {
        name: 'canExportTopic',
        text: '可导出学生大题得分明细表',
      },
      {
        name: 'canExportDetail',
        text: '可导出得分明细表',
      },
    ],
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_newScore',
    reportText: '新高考报告',
    detail: null,
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_scoreInterval',
    reportText: '分数段分析',
    detail: null,
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_rankInterval',
    reportText: '名次段分析',
    detail: null,
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_critical',
    reportText: '临界生统计',
    detail: null,
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_scoreLevel',
    reportText: '成绩分档分析',
    detail: null,
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_scoreLine',
    reportText: '上线分析',
    detail: null,
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_answer',
    reportText: '作答统计',
    detail: null,
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_quesAnalyse',
    reportText: '小题分析',
    detail: null,
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_topicAnalyse',
    reportText: '大题分析',
    detail: null,
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_knowAnalyse',
    reportText: '知识点分析',
    detail: null,
  },
  {
    levelName: 'cls',
    levelText: '班级报告',
    reportName: 'cls_paperComment',
    reportText: '试卷讲评',
    detail: null,
  },
]

export class ReportPermissionConfig {
  constructor() {
    this.roleReports = []
  }

  static copyPermission(permission) {
    let config = new ReportPermissionConfig()
    config.roleReports = deepCopy(permission.roleReports)
    return config
  }

  static import(exam, templateId, list = []) {
    const ListItemHasAttrubuteEduSchoolLevel = list.some(item => item.eduSchoolLevel)
    let rolesAll = deepCopy(ReportRoles).filter(
      role => role.default || list.some(permission => permission.eduSchoolLevel === role.eduSchoolLevel)
    )
    let reportsAll = deepCopy(Reports)
    let isMultipleSchool = exam.examScope.id > 1

    if (isMultipleSchool) {
      // 联考没有校级试卷分析
      reportsAll = reportsAll.filter(x => !(x.levelName == 'sch' && x.reportName == 'sch_paper'))
    } else {
      rolesAll = rolesAll.filter(x => x.levelName !== 'union')
      reportsAll = reportsAll.filter(x => x.levelName !== 'union')
    }

    let isSeniorHighSchoolSubjectCombinationSelectableProject = false
    const IsSeniorHighSchoolProject = ((exam && exam.grade && exam.grade.id) || 0) > 9
    if (IsSeniorHighSchoolProject) {
      const SubjectTypeSelected = ((exam && exam.subjects) || []).filter(s => s.subjectType === 1)
      isSeniorHighSchoolSubjectCombinationSelectableProject = [5, 7].every(item =>
        SubjectTypeSelected.some(s => s.subjectId === item)
      )
    }
    if (isSeniorHighSchoolSubjectCombinationSelectableProject) {
      // 高中分科学生成绩只能选择等级分原始分
      reportsAll.forEach(r => {
        if (r.reportText === '考生成绩') {
          r.detail = (r.detail || []).filter(d => d.name !== 'showStudentScore')
        }
      })
    }

    // 用于校级PDF选项角色筛选
    let schoolPDFRoles = [Roles.SchoolAdministrator, Roles.SchoolLeader, Roles.GradeLeader, Roles.ResearcherLeader]

    rolesAll.forEach(role => {
      let reports = []
      if (role.levelName === 'union') {
        if (role.eduSchoolLevel > 0) {
          reports = reportsAll.filter(r => r.reportName != 'union_paper')
        } else {
          reports = reportsAll
        }
      } else if (role.levelName === 'sch') {
        reports = reportsAll.filter(r => r.levelName === 'sch' || r.levelName === 'cls')
        // 特殊处理 校级PDF选项
        if (!schoolPDFRoles.some(sr => sr.id === role.id)) {
          reports.forEach(r => {
            if (r.detail && r.detail.length) {
              r.detail = r.detail.filter(d => d.name !== 'showExportSchoolPDFBtn')
            }
          })
        }
      } else if (role.levelName === 'cls') {
        reports = reportsAll.filter(r => r.levelName === 'cls')
      }

      role.reports = reports.map(r => {
        let target = list.find(
          x =>
            x.roleId === role.id &&
            (ListItemHasAttrubuteEduSchoolLevel && role.eduSchoolLevel
              ? x.eduSchoolLevel === role.eduSchoolLevel
              : true) &&
            x.reportName === r.reportName
        )
        let visible = target ? (target.visible === false ? false : true) : false
        let detail = null
        if (r.detail) {
          let targetDetail = null
          if (target && target.detail) {
            try {
              targetDetail = JSON.parse(target.detail)
            } catch (err) {
              // 啥也不做
            }
          }
          detail = r.detail
            .filter(item => isMultipleSchool || item.levelName !== 'union')
            .map(item => {
              return {
                name: item.name,
                text: item.text,
                value: (targetDetail && targetDetail[item.name]) || false,
              }
            })
        }

        return {
          levelName: r.levelName,
          levelText: r.levelText,
          reportName: r.reportName,
          reportText: r.reportText,
          roleId: role.id,
          examId: exam.examId,
          visible: visible,
          detail: detail,
          templateId: templateId,
        }
      })
    })

    let config = new ReportPermissionConfig()
    config.roleReports = rolesAll
    return config
  }

  export() {
    let list = []
    this.roleReports.forEach(role => {
      role.reports.forEach(report => {
        if (report.visible || (report.levelName === 'union' && role.eduSchoolLevel)) {
          let detail = ''
          if (report.detail) {
            let detailObj = {}
            report.detail.forEach(item => {
              detailObj[item.name] = item.value
            })

            detail = JSON.stringify(detailObj)
          }

          list.push({
            visible: report.visible || false,
            examId: report.examId,
            templateId: report.templateId,
            reportName: report.reportName,
            roleId: report.roleId,
            detail: detail,
            default: role.default,
            eduSchoolLevel: role.eduSchoolLevel,
          })
        }
      })
    })

    return list
  }

  importByJSON(importedPermissions, functionUpdateMenuBar = null) {
    /* permissions */
    if (
      importedPermissions &&
      importedPermissions.roleReports &&
      importedPermissions.roleReports.length &&
      this.roleReports &&
      this.roleReports.length
    ) {
      const MaxRoleReportsEduSchoolLevel = this.roleReports.reduce(
        (acc, cur) => (acc < cur.eduSchoolLevel ? cur.eduSchoolLevel : acc),
        -1
      )
      if (MaxRoleReportsEduSchoolLevel > -1) {
        const MaxImportedPermissionsEduSchoolLevel = importedPermissions.roleReports.reduce(
          (acc, cur) => (acc < cur.eduSchoolLevel ? cur.eduSchoolLevel : acc),
          -1
        )
        if (
          MaxRoleReportsEduSchoolLevel !== MaxImportedPermissionsEduSchoolLevel &&
          MaxImportedPermissionsEduSchoolLevel > -1
        ) {
          if (MaxRoleReportsEduSchoolLevel > MaxImportedPermissionsEduSchoolLevel) {
            this.roleReports = this.roleReports.filter(
              item => item.eduSchoolLevel <= MaxImportedPermissionsEduSchoolLevel
            )
            if (functionUpdateMenuBar) {
              functionUpdateMenuBar()
            }
          } else {
            const RoleReportsFirstNotInstitutionRoleIndex = this.roleReports.findIndex(
              item => item.levelName && item.levelName !== 'union'
            )
            let roles = []
            for (let i = MaxRoleReportsEduSchoolLevel; i < MaxImportedPermissionsEduSchoolLevel; i++) {
              roles.push(
                ...deepCopy(
                  this.roleReports.filter(
                    item => item.levelName && item.levelName === 'union' && item.eduSchoolLevel === 0
                  )
                ).map(item => {
                  item.eduSchoolLevel = i + 1
                  return item
                })
              )
            }
            if (roles.length) {
              this.roleReports.splice(RoleReportsFirstNotInstitutionRoleIndex, 0, ...roles)
              if (functionUpdateMenuBar) {
                functionUpdateMenuBar()
              }
            }
          }
        }
      }

      this.roleReports.forEach(originRole => {
        let targetRole = importedPermissions.roleReports.find(
          importedRole =>
            importedRole.id === originRole.id &&
            (importedRole.eduSchoolLevel ? importedRole.eduSchoolLevel === originRole.eduSchoolLevel : true)
        )
        if (targetRole) {
          originRole.reports.forEach(originReport => {
            let targetReport = targetRole.reports.find(
              importedReport => importedReport.reportName === originReport.reportName
            )
            if (targetReport) {
              originReport.visible = targetReport.visible || false
              if (
                originReport.detail &&
                originReport.detail.length &&
                targetReport.detail &&
                targetReport.detail.length
              ) {
                originReport.detail.forEach(originDetail => {
                  let targetDetail = targetReport.detail.find(
                    importedDetail => importedDetail.name === originDetail.name
                  )
                  if (targetDetail && typeof targetDetail.value === 'boolean') {
                    originDetail.value = targetDetail.value
                  }
                })
              }
            }
          })
        }
      })
    }
  }

  exportByJSON() {
    return JSON.stringify(this.export())
  }

  addMultipleLevelInstitutionRolesSetting(functionUpdateMenuBar = null) {
    const RoleReportsFirstNotInstitutionRoleIndex = this.roleReports.findIndex(
      item => item.levelName && item.levelName !== 'union'
    )
    const eduSchoolLevelForAdding =
      this.roleReports
        .filter(item => item.levelName && item.levelName === 'union')
        .reduce((acc, cur) => (acc < (cur.eduSchoolLevel || 0) ? cur.eduSchoolLevel : acc), 0) + 1

    const Roles = deepCopy(
      this.roleReports.filter(item => item.levelName && item.levelName === 'union' && item.eduSchoolLevel === 0)
    ).map(item => {
      item.eduSchoolLevel = eduSchoolLevelForAdding
      // 下级机构无试卷分析
      item.reports = item.reports.filter(x => x.reportName != 'union_paper')
      let unionLevel = item.reportsByLevel.find(x => x.levelName == 'union')
      if (unionLevel) {
        unionLevel.reports = unionLevel.reports.filter(x => x.reportName != 'union_paper')
      }
      return item
    })

    if (Roles && Roles.length) {
      this.roleReports.splice(RoleReportsFirstNotInstitutionRoleIndex, 0, ...Roles)
      if (functionUpdateMenuBar) {
        functionUpdateMenuBar()
      }
    }
  }

  removeMultipleLevelInstitutionRolesSetting(functionUpdateMenuBar = null) {
    const eduSchoolLevelForRemoving = this.roleReports
      .filter(item => item.levelName && item.levelName === 'union')
      .reduce((acc, cur) => (acc < (cur.eduSchoolLevel || 0) ? cur.eduSchoolLevel : acc), 0)
    if (eduSchoolLevelForRemoving && eduSchoolLevelForRemoving !== 0) {
      this.roleReports = this.roleReports.filter(
        item => item.levelName !== 'union' || item.eduSchoolLevel !== eduSchoolLevelForRemoving
      )
      if (functionUpdateMenuBar) {
        functionUpdateMenuBar()
      }
    }
  }
}
