<template>
  <div v-if="isOmr" class="ticket-number ticket-number-omr" :style="omrStyle">
    <div v-for="l in omrGridLines" :key="l.id" class="grid-line" :style="l.style"></div>
    <div class="label" :style="omrLabelStyle">{{ ticketNumberText }}</div>
    <img
      v-for="cell in omrCells"
      :key="cell.id"
      class="cell-omr"
      :src="getOmrCellImgSrc(cell.value)"
      :style="cell.style"
    />
    <div v-if="showBarcode2" :style="barcode2Style">条形码粘贴处</div>
  </div>
  <div v-else-if="isBarcode" class="ticket-number ticket-number-barcode" :style="barcodeStyle">条形码粘贴处</div>
  <div v-else-if="isHandwriting" class="ticket-number ticket-number-handwriting">
    <div class="label" :style="handwritingLabelStyle">{{ ticketNumberText }}：</div>
    <!-- <div class="rect" :style="handwritingRectStyle"></div> -->
    <div class="blanks" :style="handwritingBlanksStyle">
      <div v-for="l in handwritingGridLines" :key="l.id" class="grid-line-column" :style="l.style"></div>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { TicketNumber } from '@/helpers/answer_sheet/header'
  import TicketNumberTypeEnum from '@/enum/answer_sheet/ticket_number_type'
  import { OmrSVG } from '@/const/answer_sheet'

  export default {
    props: {
      size: {
        type: Object,
        required: true,
      },
    },
    computed: {
      ...mapGetters('answerSheet', [
        'ticketNumberType',
        'ticketNumberLength',
        'ticketNumberOmrHorizontal',
        'stuInfoLabelAlias',
        'headerGroupLayout',
      ]),
      isOmr() {
        return this.ticketNumberType == TicketNumberTypeEnum.Omr.id
      },
      showBarcode2() {
        // 特定学校
        let schoolId = this.$store.getters['user/info'].schoolId
        return this.isOmr && schoolId == '2hn3h6i3joxs'
      },
      isBarcode() {
        return this.ticketNumberType == TicketNumberTypeEnum.Barcode.id
      },
      isHandwriting() {
        return this.ticketNumberType == TicketNumberTypeEnum.Handwriting.id
      },
      ticketNumberText() {
        return (this.stuInfoLabelAlias && this.stuInfoLabelAlias['准考号']) || '准考号'
      },
      // 填涂区域样式
      omrStyle() {
        return TicketNumber.getOmrStyle()
      },
      // 填涂区域网格
      omrGridLines() {
        return TicketNumber.getOmrGridLines(this.ticketNumberLength, this.ticketNumberOmrHorizontal)
      },
      // 填涂标签样式
      omrLabelStyle() {
        return TicketNumber.getOmrLabelStyle(this.ticketNumberOmrHorizontal)
      },
      // 各选项样式
      omrCells() {
        return TicketNumber.getOmrCells(this.ticketNumberLength, this.ticketNumberOmrHorizontal)
      },
      // 条码样式
      barcodeStyle() {
        return TicketNumber.getBarcodeStyle()
      },
      barcode2Style() {
        return {
          position: 'absolute',
          fontSize: '20px',
          lineHeight: '100px',
          textAlign: 'center',
          bottom: '-1px',
          left: '-204px',
          width: '204px',
          height: '102px',
          borderTop: '1px solid',
          borderLeft: '1px solid',
        }
      },
      // 手写数字标签样式
      handwritingLabelStyle() {
        return TicketNumber.getHandwritingLabelStyle(this.ticketNumberText.length)
      },
      // 手写数字空格样式
      handwritingBlanksStyle() {
        return TicketNumber.getHandwritingBlanksStyle(this.ticketNumberLength)
      },
      // 手写数字网格
      handwritingGridLines() {
        return TicketNumber.getHandwritingGridLines(this.ticketNumberLength)
      },
      // 手写数字矩形框样式
      handwritingRectStyle() {
        return TicketNumber.getHandwritingRectStyle(this.ticketNumberLength, this.ticketNumberText.length)
      },
    },
    methods: {
      getOmrCellImgSrc(value) {
        return OmrSVG['cell_' + value]
      },
      getTemplateElements() {
        if (this.isBarcode) {
          return {
            length: this.ticketNumberLength,
            barcode: Object.assign({}, this.size),
          }
        }
        if (this.isHandwriting) {
          // let cells = TicketNumber.getHandwritingCells(this.ticketNumberLength, this.ticketNumberText.length)
          // cells.forEach(cell => {
          //   ;(cell.left += this.size.left), (cell.top += this.size.top)
          // })
          // return {
          //   length: this.ticketNumberLength,
          //   handwriting: {
          //     cells,
          //   },
          // }
          let rect = TicketNumber.getHandwritingRect(this.ticketNumberLength, this.ticketNumberText.length)
          rect.left += this.size.left
          rect.top += this.size.top
          return {
            length: this.ticketNumberLength,
            handwriting: {
              cells: [rect],
            },
          }
        } else {
          let rect = Object.assign({}, this.size)
          let direction = this.ticketNumberOmrHorizontal ? 'H' : 'V'
          let options = this.omrCells.map(cell => ({
            left: cell.left + this.size.left + TicketNumber.borderWidth + this.headerGroupLayout.borderWidth,
            top: cell.top + this.size.top + TicketNumber.borderWidth + this.headerGroupLayout.borderWidth,
            width: cell.width,
            height: cell.height,
          }))
          return {
            length: this.ticketNumberLength,
            omr: {
              id: 'admissionNumber',
              rect,
              direction,
              optionCount: 10,
              options,
            },
          }
        }
      },
    },
  }
</script>
