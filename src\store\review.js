import { apiGetReviewActivityDetail, apiGetReviewConfig } from '@/api/review'

import ActivityUserRoleEnum from '../enum/review/activity_user_role'
import ActivityScopeEnum from '../enum/review/activity_scope'

function flattenSchools(arr) {
  return arr.reduce((acc, val) => {
    acc.push(val)
    return Array.isArray(val.schools) ? acc.concat(flattenSchools(val.schools)) : acc.concat([val])
  }, [])
}

export default {
  namespaced: true,

  state: {
    currentActivity: null,
    baseFormInfo: {},
    checkedSchools: [],
    allCategoryComponents: {},
    activityConfigs: [],
    flattenSchoolList: [],
    awardConfig: {},
  },

  getters: {
    currentActivity(state) {
      return state.currentActivity
    },
    activityId(state) {
      return state?.currentActivity?.id || ''
    },
    activityName(state) {
      return state?.currentActivity?.name || ''
    },

    baseFormInfo(state) {
      return state.baseFormInfo
    },

    checkedSchools(state) {
      return state.checkedSchools
    },

    allCategoryComponents(state) {
      return state.allCategoryComponents
    },

    categories(state) {
      return state?.currentActivity?.categories || []
    },

    schoolList(state) {
      return state?.currentActivity?.schoolList || []
    },

    participantIdentity(state) {
      return state?.currentActivity?.participantIdentity || 'student'
    },

    /**
     * 是否单校
     */
    isSingleSchool(state) {
      return state?.currentActivity?.scope == ActivityScopeEnum.Single.id
    },

    /**
     * 是否多校
     */
    isMultipleSchool(state) {
      return state?.currentActivity?.scope == ActivityScopeEnum.Multiple.id
    },

    /**
     * 评审活动人员列表
     */
    userList(state) {
      return state?.currentActivity?.userList || []
    },

    /**
     * 是否评审活动创建者
     */
    isCreator(state, getters, rootState, rootGetters) {
      let userId = rootGetters['user/info'].userId
      return state.currentActivity?.createBy == userId
    },

    /**
     * 是否评审活动管理员（含创建者、单校学校管理员）
     */
    isAdmin(state, getters, rootState, rootGetters) {
      let userId = rootGetters['user/info'].userId
      // 配置的管理员
      let isConfigAdmin = state.currentActivity?.userList.some(
        x => x.userId == userId && [ActivityUserRoleEnum.Creator.id, ActivityUserRoleEnum.Admin.id].includes(x.role)
      )
      if (isConfigAdmin) {
        return true
      }
      // 单校评审学校管理员
      if (getters.isSingleSchool) {
        return getters.isSchoolAdmin
      }
      return false
    },

    /**
     * 是否评审活动学校管理员
     */
    isSchoolAdmin(state, getters, rootState, rootGetters) {
      let schoolId = rootGetters['user/info'].schoolId
      if (getters.schoolList.every(x => x.id != schoolId)) {
        return false
      }
      return rootGetters['user/isSchoolAdministrator']
    },

    /**
     * 是否评审活动学校负责人
     */
    isSchoolPersonInCharge(state, getters, rootState, rootGetters) {
      let schoolId = rootGetters['user/info'].schoolId
      if (getters.schoolList.every(x => x.id != schoolId)) {
        return false
      }
      let userId = rootGetters['user/info'].userId
      return state.currentActivity?.userList.some(
        x => x.userId == userId && x.role == ActivityUserRoleEnum.SchoolPersonInCharge.id
      )
    },

    /**
     * 是否评审活动报名审核员
     */
    isRegisterAuditor(state, getters, rootState, rootGetters) {
      let userId = rootGetters['user/info'].userId
      return state.currentActivity?.userList.some(
        x => x.userId == userId && x.role == ActivityUserRoleEnum.RegisterAuditor.id
      )
    },

    /**
     * 是否需要审核
     */
    needAudit(state) {
      return state.currentActivity?.needAudit
    },

    /**
     * 是否允许学校审核本校报名
     */
    enableSchoolAudit(state) {
      return state.currentActivity?.enableSchoolAudit
    },

    activityConfigs(state) {
      return state?.activityConfigs
    },
    flattenSchoolList(state) {
      return state?.flattenSchoolList
    },
    awardConfig(state) {
      return state?.awardConfig
    },
  },

  mutations: {
    clear(state) {
      state.currentActivity = null
    },
    setCurrentActivity(state, activity) {
      state.currentActivity = activity
    },
  },

  actions: {
    fetchActivityDetail(context, activityId) {
      if (!activityId) {
        return Promise.resolve()
      }

      return apiGetReviewActivityDetail({ activityId }).then(response =>
        context.commit('setCurrentActivity', {
          ...response,
          categories: response.categoryList,
        })
      )
    },
    /**
     * 刷新评审活动详情
     */
    refreshActivityDetail(context) {
      if (!context.state.currentActivity?.id) {
        return Promise.resolve()
      }
      return context.dispatch('fetchActivityDetail', context.state.currentActivity.id)
    },
    setBaseFormInfo(context, params) {
      context.state.baseFormInfo = params
    },
    setCheckedSchools(context, params) {
      context.state.checkedSchools = params
    },
    setAllCategoryComponents(context, params) {
      context.state.allCategoryComponents = params
    },
    clearStore(context) {
      context.state.baseFormInfo = {}
      context.state.checkedSchools = []
      context.state.allCategoryComponents = []
    },
    fetchReviewConfig(context) {
      apiGetReviewConfig({
        activityId: context.state?.currentActivity?.id,
      }).then(res => {
        context.state.activityConfigs = context.getters.categories.map(item => {
          const theConfig = (res || []).find(c => c.categoryId === item.id)

          return theConfig
            ? theConfig
            : {
                id: '',
                activityId: context.state?.currentActivity?.id,
                categoryId: item.id,
                categoryName: item.name,
                reviewCount: null,
                scoreMethod: null,
                judgeMaxTask: null,
                scoreMode: null,
                fullScore: null,
                participantCount: null,
              }
        })
      })
    },
    setFlattenSchoolList(context, params) {
      context.state.flattenSchoolList = flattenSchools(params)
    },
    setAwardConfig(context, params) {
      context.state.awardConfig = params
    },
    clearAwardConfig(context) {
      context.state.awardConfig = []
    },
  },
}
