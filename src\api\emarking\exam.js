import ajax from '@/api/ajax'
import store from '@/store/index'
import { unGroupArray } from '@/utils/array'
import { formatDate } from '@/utils/date'
import { addImageUrlParam } from '@/utils/url'
import ExamScopeEnum from '@/enum/emarking/exam_scope'
import ExamTypeEnum from '@/enum/emarking/exam_type'
import ExamUserRoleEnum from '@/enum/emarking/exam_user_role'
import UploadModeEnum from '@/enum/emarking/upload_mode'
import MarkingWayEnum from '@/enum/emarking/marking_way'

export function transformExam(responseExam) {
  let gradeSubjects = store.getters['emarking/gradeSubjects']()
  let stages = store.getters['emarking/stages']()
  let examScope = ExamScopeEnum.getEntryById(responseExam.examScope) || {}
  let examType = ExamTypeEnum.getEntryById(responseExam.examType) || {}
  let stage = stages.find(x => x.id == responseExam.gradeLevel) || {}
  let grade = gradeSubjects.find(x => x.id === responseExam.gradeId) || { subjects: [] }
  let schools = (responseExam.schools || []).map(x => ({
    schoolId: x.id || x.schoolId,
    schoolName: x.name || x.schoolName || x.value,
  }))
  let schoolTree = responseExam.schoolTree ? transformSchoolTreeNode(responseExam.schoolTree) : null

  let creator = {
    ...transformUser(responseExam.creator || {}),
    examRole: ExamUserRoleEnum.ExamCreator,
  }
  let administrators = (responseExam.users || responseExam.managers || []).map(x => ({
    ...transformUser(x),
    examRole: ExamUserRoleEnum.ExamAdmin,
  }))
  let subjects = (responseExam.subjects || responseExam.examSubjects || []).map(s =>
    transformSubject(s, grade.subjects)
  )

  return {
    examId: responseExam.examId,
    examName: responseExam.examName,
    examScope,
    examType,
    apiExamTypeId: responseExam.examType,
    startDate: new Date(responseExam.beginTime),
    endDate: new Date(responseExam.endTime),
    createTime: new Date(responseExam.createTime),
    schools,
    schoolTree,
    creator,
    administrators,
    stage,
    grade,
    subjects,
    semesterId: responseExam.semesterId,
    term: responseExam.term,
    educationBureaus: responseExam.eduSchools,
    paperSubscribe: responseExam.paperSubscribe,
    isFinish: responseExam.isFinish,
    enableVenue: responseExam.enableVenue,
    enableAdmissionTicket: responseExam.enableAdmissionTicket,
    isAdmissionTicketShowPhoto: responseExam.isAdmissionTicketShowPhoto,
    isFeedbackScanPriority: responseExam.isFeedbackScanPriority || false,
    schoolImportScoreEndTime: responseExam.schoolImportScoreEndTime,
    enableRecommendBlocLeader: responseExam.enableRecommendBlocLeader,
    isShow: responseExam.isShow,
    showPaperObjectiveComment: responseExam.showPaperObjectiveComment,
    enableFinancial: responseExam.enableFinancial,
    enableGenRoomSeat: responseExam.enableGenRoomSeat,
    enableSetRoomSeat: responseExam.enableSetRoomSeat,
    enableAppMark: responseExam.enableAppMark,
  }
}

function transformSubject(s, examGradeSubjects) {
  let subject = examGradeSubjects.find(x => x.id === s.subjectId)
  let subjectName = (subject && subject.name) || ''
  // 选考科目加字样"(选)"
  if (s.subjectType == 1) {
    subjectName += '(选)'
  }
  // 外语选课加字样"(外)"
  if (s.foreignSubjectBranch) {
    subjectName += '(外)'
  }
  let administrators = (s.users || []).map(x => ({
    ...transformUser(x),
    examRole: ExamUserRoleEnum.SubjectAdmin,
  }))
  let monitors = (s.monitors || []).map(x => ({
    ...transformUser(x),
    examRole: ExamUserRoleEnum.SubjectMonitor,
  }))
  let spoters = (s.spoters || []).map(x => ({
    ...transformUser(x),
    examRole: ExamUserRoleEnum.SubjectInspector,
  }))
  let markingWay = MarkingWayEnum.getEntryById(s.markingWay) || { id: s.markingWay }

  return {
    examSubjectId: s.examSubjectId,
    subjectId: s.subjectId,
    studentNum: s.studentNum,
    isPaperStructureReady: s.isPaperStruct,
    isScanTemplateUploaded: s.isTemplateUpload,
    existsLocalTemplate: s.existsLocalTemplate || false,
    isMarkingTaskAssigned: s.isTask,
    subjectName,
    subjectType: 'subjectType' in s ? s.subjectType : undefined,
    administrators,
    status: s.status,
    monitors: monitors,
    spoters: spoters,
    uploadMode: UploadModeEnum.getEntryById(s.uploadMode) || {},
    beginTime: s.beginTime ? new Date(s.beginTime) : null,
    endTime: s.endTime ? new Date(s.endTime) : null,
    timeSchedule: s.timeSchedule,
    markingWay,
    enableIncreaseMarkTaskNum: s.enableIncreaseMarkTaskNum || false,
    showPaperAnswer: s.showPaperAnswer || false,
    enableScan: s.enableScan,
    foreignSubjectBranch: s.foreignSubjectBranch || false,
  }
}

function transformTeacher(responseTeacher) {
  return {
    teacherId: responseTeacher.teacherId,
    teacherName: responseTeacher.realName || '',
    mobile: responseTeacher.mobile || '',
    teacherCode: responseTeacher.teacherCode || '',
    admissionNumber: responseTeacher.admission || '',
    schoolId: responseTeacher.schoolId,
    schoolName: responseTeacher.schoolName || '',
    classId: responseTeacher.classId,
    className: responseTeacher.className || '',
    roomNo: responseTeacher.room || '',
    seatNo: responseTeacher.seat || '',
    subjectIds: responseTeacher.subjectIds || [],
    isRecommendAsLeader: responseTeacher.isRecommendAsLeader || false,
    recommendAsLeaderSubjectIds: responseTeacher.recommendAsLeaderSubjectIds || [],
  }
}

export function transformUser(responseUser) {
  return {
    userId: responseUser.userId || responseUser.userID || responseUser.id,
    userName: responseUser.userName || responseUser.name || '',
    realName: responseUser.realName || responseUser.name || responseUser.chineseName || '',
    mobile: responseUser.mobile || responseUser.phone || '',
    schoolId: responseUser.schoolId || responseUser.schoolID,
    schoolName: responseUser.schoolName || '',
  }
}

function transformSchoolTreeNode(node) {
  if (!node) {
    return node
  }
  if (Array.isArray(node)) {
    node = node[0]
  }
  return {
    schoolId: node.schoolId,
    schoolName: node.schoolName,
    schoolTypeId: node.schoolType,
    levelName: node.levelName,
    children: (node.children || []).map(transformSchoolTreeNode),
  }
}

/**
 * 学校数据
 */
export function apiGetSchools(params) {
  return ajax.get({
    url: 'mark/exam/schools',
    params: params,
    requestName: '获取学校列表',
  })
}

export function apiGetBureaus() {
  return ajax.get({
    url: 'mark/exam/educations',
    requestName: '获取教育局列表',
  })
}

export function apiGetSchoolGradesAndSubjects(schoolId) {
  return ajax
    .get({
      url: 'mark/exam/gradelevel',
      params: {
        schoolId: schoolId,
      },
      requestName: '获取学校年级科目',
    })
    .then(stages => {
      return unGroupArray(stages, stage => {
        stage.grades.forEach(g => {
          g.stageId = stage.id
          g.stageName = stage.name
        })
        return stage.grades
      }).map(g => ({
        id: Number(g.id),
        name: g.name,
        stageId: g.stageId,
        stageName: g.stageName,
        subjects: g.subjects.map(s => ({
          id: Number(s.id),
          name: s.name,
        })),
      }))
    })
}

export function apiGetSchoolTeachers(params) {
  return ajax
    .get({
      url: 'mark/exam/teachers',
      params: {
        schoolId: params.schoolId,
        gradeId: params.gradeId || null,
        subjectId: params.subjectId || null,
        key: params.keyword || '',
      },
      requestName: '获取学校教师',
    })
    .then(teachers =>
      (teachers || []).map(t => ({
        ...transformUser(t),
        isSystem: t.isSys,
      }))
    )
}

export function apiGetExamSchoolTree({ examId, includeNoExamSchool = false }) {
  return ajax
    .get({
      url: 'mark/exam/schoolTree',
      params: {
        examId,
        includeNoExamSchool,
      },
      requestName: '获取考试学校',
    })
    .then(nodes => nodes.map(transformSchoolTreeNode))
}

export function apiGetExamSchoolTree2({ examId, includeNoExamSchool = false }) {
  return ajax
    .get({
      url: 'mark/exam/schoolTree',
      params: {
        examId,
        includeNoExamSchool,
      },
      requestName: '获取考试学校',
    })
    .then(response => {
      if (!response) {
        return response
      }
      if (Array.isArray(response)) {
        return response.map(item => transformSchoolTreeNode(item))
      } else {
        return transformSchoolTreeNode(response)
      }
    })
}

/**
 * 考试增删查改
 */
export function apiCreateExamSingleSchool(data) {
  return ajax.post({
    url: 'mark/exam/create',
    data: {
      examName: data.examName,
      examMode: data.examModeId,
      examType: data.examTypeId,
      beginTime: data.startDate && formatDate(data.startDate) + ' 00:00:00',
      endTime: data.endDate && formatDate(data.endDate) + ' 23:59:59',
      gradeId: data.examGradeId,
      subjects: data.subjects,
      initStudent: data.initStudent,
      initAdmin: data.initAdmin,
      initSubjectAdmin: data.initPrincipal,
      classIds: data.classIds,
      foreignSubjectBranch: data.isForeignLangeuageSubject,
    },
    requestName: '新建考试',
  })
}

export function apiCreateExamMultipleSchool(data) {
  let sendData = {
    examName: data.examName,
    examMode: data.examModeId,
    examType: data.examTypeId,
    beginTime: data.startDate && formatDate(data.startDate) + ' 00:00:00',
    endTime: data.endDate && formatDate(data.endDate) + ' 23:59:59',
    gradeId: data.examGradeId,
    subjects: data.subjects,
    schools: data.schoolIds,
    upStuBeginTime: data.updateStudentBeginTime && formatDate(data.updateStudentBeginTime) + ' 00:00:00',
    upStuEndTime: data.updateStudentEndTime && formatDate(data.updateStudentEndTime) + ' 23:59:59',
    upTeacherBeginTime: data.upTeacherBeginTime && formatDate(data.upTeacherBeginTime) + ' 00:00:00',
    upTeacherEndTime: data.upTeacherEndTime && formatDate(data.upTeacherEndTime) + ' 23:59:59',
    initStudent: data.initStudent,
    foreignSubjectBranch: data.isForeignLangeuageSubject,
  }

  return ajax.post({
    url: 'mark/exam/create2',
    data: sendData,
    requestName: '新建联考',
  })
}

export function apiEditExamSingleSchool(data) {
  let sendData = {
    examId: data.examId,
    examName: data.examName,
    examMode: data.examModeId,
    examType: data.examTypeId,
    beginTime: data.startDate && formatDate(data.startDate),
    endTime: data.endDate && formatDate(data.endDate),
    gradeId: data.examGradeId,
    subjects: data.subjectIds,
    schools: data.schoolIds,
  }

  return ajax.put({
    url: 'mark/exam/update',
    data: sendData,
    requestName: '修改考试定义',
  })
}

export function apiEditExamMultipleSchool(data) {
  let sendData = {
    examId: data.examId,
    examName: data.examName,
    examMode: data.examModeId,
    examType: data.examTypeId,
    beginTime: data.startDate && formatDate(data.startDate),
    endTime: data.endDate && formatDate(data.endDate),
    gradeId: data.examGradeId,
    subjects: data.subjectIds,
    schools: data.schoolIds,
  }

  return ajax.put({
    url: 'mark/exam/update2',
    data: sendData,
    requestName: '修改考试定义',
  })
}

export function apiDeleteExam(examId) {
  return ajax.delete({
    url: 'mark/exam/delete',
    params: {
      id: examId,
    },
    requestName: '删除考试',
  })
}

export function apiGetExamsAdmin({
  semesterId,
  term,
  gradeId,
  examType,
  examScope,
  beginTime,
  endTime,
  keyword,
  pageSize,
  currentPage,
  schoolId,
}) {
  return ajax
    .get({
      url: 'mark/exam/exams',
      params: {
        semesterId,
        term,
        gradeId,
        examType,
        examScope,
        beginTime,
        endTime,
        key: keyword,
        size: pageSize,
        current: currentPage,
        schoolId: schoolId,
      },
      requestName: '获取考试列表',
    })
    .then(data => {
      return {
        total: data.total,
        exams: data.records.map(x => transformExam(x)),
      }
    })
}

export function apiGetExamById(requestParams) {
  return ajax
    .get({
      url: 'mark/exam/detail',
      params: {
        id: requestParams.examId,
        examSubjectId: requestParams.examSubjectId || undefined,
      },
      requestName: '获取考试信息',
    })
    .then(responseExam => transformExam(responseExam))
}

export function apiGetExamEnableGenRoomSeatStatus(examId) {
  return ajax.get({
    url: 'mark/exam/studentSetting/enableGenRoomSeat',
    params: {
      examId: examId,
    },
    requestName: '查询联考是否允许学校使用生成考场座位号功能',
  })
}

export function apiChangeExamEnableGenRoomSeatStatus(requestParams) {
  return ajax.put({
    url: 'mark/exam/studentSetting/enableGenRoomSeat',
    params: {
      examId: requestParams.examId, // *String
      enabled: requestParams.enabled, // *
    },
    requestName: '配置考试是否允许学校使用生成考场座位号功能',
  })
}

export function apiGetExamEnableSetRoomSeatStatus(examId) {
  return ajax.get({
    url: 'mark/exam/studentSetting/enableSetRoomSeat',
    params: {
      examId: examId,
    },
    requestName: '查询联考是否允许学校导入考场座位',
  })
}

export function apiChangeExamEnableSetRoomSeatStatus({ examId, enabled }) {
  return ajax.put({
    url: 'mark/exam/studentSetting/enableSetRoomSeat',
    params: {
      examId,
      enabled,
    },
    requestName: '配置联考是否允许学校导入考场座位',
  })
}

export function apiGetExamSubjectInfoById(examSubjectId, gradeId) {
  return ajax
    .get({
      url: 'mark/exam/subjectDetail',
      params: {
        examSubjectId,
      },
      requestName: '获取考试科目信息',
    })
    .then(s => {
      let gradeSubjects = store.getters['emarking/gradeSubjects']()
      let grade = gradeSubjects.find(x => x.id == gradeId) || { subjects: [] }
      return transformSubject(s, grade.subjects)
    })
}

/**
 * 考试设置
 */
export function apiSetBelongedEducationBureau(data) {
  return ajax.put({
    url: 'mark/exam/edu',
    params: {
      examId: data.examId,
    },
    data: data.educationBureauIds,
    requestName: '设置考试项目所属教育局',
  })
}

export function apiGetExamSchoolCodes(examId) {
  return ajax.get({
    url: 'mark/exam/schoolSetting/examSchoolCode',
    params: {
      examId,
    },
    requestName: '获取学校考试编码',
  })
}

export function apiChangeExamSchoolCodes(examId, schools) {
  return ajax.put({
    url: 'mark/exam/schoolSetting/examSchoolCode',
    params: {
      examId,
    },
    data: schools.map(s => ({
      schoolId: s.schoolId,
      examSchoolCode: s.examSchoolCode,
    })),
    requestName: '修改学校考试编码',
  })
}

export function apiGetExamSchoolSettingConfigs(examId) {
  return ajax.get({
    url: 'mark/exam/schoolSetting/configs',
    params: {
      examId: examId, // String*
    },
    requestName: '获取联考学校配置',
  })
}
export function apiSetRequireSubmitMarkersSchool(requestParams) {
  return ajax.put({
    url: 'mark/exam/schoolSetting/requiresUpTeacher',
    params: {
      examId: requestParams.examId, // String*
      schoolId: requestParams.schoolId, // String*
    },
    // requestName: '联考设置学校需要上报评卷员',
  })
}
export function apiSetNeedNotSubmitMarkersSchool(requestParams) {
  return ajax.put({
    url: 'mark/exam/schoolSetting/notRequiresUpTeacher',
    params: {
      examId: requestParams.examId, // String*
      schoolId: requestParams.schoolId, // String*
    },
    // requestName: '联考设置学校不需上报评卷员',
  })
}
export function apiSetRequireSubmitPapersSubscribeSchool(requestParams) {
  return ajax.put({
    url: 'mark/exam/schoolSetting/requiresSubscribePaper',
    params: {
      examId: requestParams.examId, // String
      schoolId: requestParams.schoolId, // SchoolId
    },
    // requestName: '联考设置学校需要上报试卷订阅信息',
  })
}
export function apiSetNeedNotSubmitPapersSubscribeSchool(requestParams) {
  return ajax.put({
    url: 'mark/exam/schoolSetting/notRequiresSubscribePaper',
    params: {
      examId: requestParams.examId, // String*
      schoolId: requestParams.schoolId, // String*
    },
    // requestName: '联考设置学校不需上报试卷订阅信息',
  })
}

/**
 * 考试人员安排
 */
export function apiAddExamAdministrator(params) {
  if (params.examRole.id === ExamUserRoleEnum.ExamAdmin.id) {
    return ajax.post({
      url: 'mark/exam/examAdmin',
      params: {
        examId: params.examId,
        userId: params.userId,
      },
      requestName: '添加考试管理员',
    })
  } else if (params.examRole.id === ExamUserRoleEnum.SubjectAdmin.id) {
    return ajax.post({
      url: 'mark/exam/subjectAdmin',
      data: [params.examSubjectId],
      params: {
        examId: params.examId,
        userId: params.userId,
      },
      requestName: '添加学科负责人',
    })
  } else if (params.examRole.id === ExamUserRoleEnum.SubjectMonitor.id) {
    return ajax.post({
      url: 'mark/exam/subjectMonitor',
      // data: [params.examSubjectId],
      params: {
        examSubjectId: params.examSubjectId,
        userId: params.userId,
      },
      requestName: '添加学科监控员',
    })
  } else if (params.examRole.id === ExamUserRoleEnum.SubjectInspector.id) {
    return ajax.post({
      url: 'mark/exam/subjectSpoter',
      // data: [params.examSubjectId],
      params: {
        examSubjectId: params.examSubjectId,
        userId: params.userId,
      },
      requestName: '添加学科抽查员',
    })
  } else {
    return Promise.reject({
      code: 1,
      msg: '角色有误',
    })
  }
}

export function apiRemoveExamAdministrator(params) {
  if (params.examRole.name === ExamUserRoleEnum.ExamAdmin.name) {
    return ajax.delete({
      url: 'mark/exam/examAdmin',
      params: {
        examId: params.examId,
        userId: params.userId,
      },
      requestName: '删除考试管理员',
    })
  } else if (params.examRole.name === ExamUserRoleEnum.SubjectAdmin.name) {
    return ajax.delete({
      url: 'mark/exam/subjectAdmin',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
        userId: params.userId,
      },
      requestName: '删除学科负责人',
    })
  } else if (params.examRole.name === ExamUserRoleEnum.SubjectMonitor.name) {
    return ajax.delete({
      url: 'mark/exam/subjectMonitor',
      params: {
        examSubjectId: params.examSubjectId,
        userId: params.userId,
      },
      requestName: '删除学科监控员',
    })
  } else if (params.examRole.name === ExamUserRoleEnum.SubjectInspector.name) {
    return ajax.delete({
      url: 'mark/exam/subjectSpoter',
      params: {
        examSubjectId: params.examSubjectId,
        userId: params.userId,
      },
      requestName: '删除学科抽查员',
    })
  } else {
    return Promise.reject({
      code: 1,
      msg: '角色有误',
    })
  }
}

/**
 * 考生
 */
export function apiGetMultipleSchoolExamStudentSetting(examId) {
  return ajax
    .get({
      url: 'mark/exam/studentSetting',
      params: {
        examId,
      },
      requestName: '获取联考考生设置',
    })
    .then(data => {
      return {
        submitBeginTime: (data.upStuBeginTime && new Date(data.upStuBeginTime)) || null,
        submitEndTime: (data.upStuEndTime && new Date(data.upStuEndTime)) || null,
        generateRoomNoWay: data.genRoomNoWay,
        roomNoLength: data.roomNoLength,
        seatNoLength: data.seatNoLength,
        admissionNumLength: data.admissionNumLength,
      }
    })
}

export function apiEditSubmitStudentTimeRange(params) {
  return ajax.put({
    url: 'mark/exam/studentSetting/upStuTime',
    params: {
      examId: params.examId,
      beginTime: params.submitBeginTime,
      endTime: params.submitEndTime,
    },
    requestName: '修改考生上报截止时间',
  })
}

export function apiEditRoomSeatRule(params) {
  return ajax.put({
    url: 'mark/exam/studentSetting/genRoomNo',
    params: {
      examId: params.examId,
      genRoomNoWay: params.generateRoomNoWay,
      roomNoLength: params.roomNoLength,
      seatNoLength: params.seatNoLength,
      admissionNumLength: params.admissionNumLength,
    },
    requestName: '修改考场座位编排规则',
  })
}

export function apiGenerateRoomSeat(data) {
  return ajax.put({
    url: 'mark/exam/student/genStuRoom2',
    params: {
      examId: data.examId,
      schoolId: data.schoolId,
    },
    data: data.groups,
    requestName: '生成考场号座位号',
  })
}

export function apiGetReferenceAvailableExamList(requestParams) {
  return ajax.get({
    url: 'mark/exam/listAvailableExams',
    params: {
      examId: requestParams.examId, // * [string]
      schoolId: requestParams.schoolId, // * [string]
      semesterId: requestParams.semesterId, // * [string]
      examScope: requestParams.examScope || undefined, // [integer]
    },
    requestName: '获取生成考场号座位号可参考过往考试列表',
  })
}

export function apiGenerateRoomSeat2(requestParams) {
  return ajax.put({
    url: 'mark/exam/student/genStuRoom2',
    params: {
      examId: requestParams.examId, // [string]
      schoolId: requestParams.schoolId, // [string]
    },
    data: requestParams.groups,
    // groups: {
    //   examSubjectId: 'string',
    //   groups: [
    //     {
    //       allotWay: 0,
    //       classIds: ['string'],
    //       examId: 'string',
    //       genAdmission: true,
    //       prefix: 'string',
    //       rooms: [
    //         {
    //           roomNo: 'string',
    //           stuNum: 0,
    //         },
    //       ],
    //     },
    //   ],
    // },
    requestName: '生成考场号座位号',
  })
}

export function apiGetSchoolStudentSubmitStatus(examId) {
  return ajax
    .get({
      url: 'mark/exam/student/upStuStatus',
      params: {
        examId,
      },
      requestName: '获取学校考生上报状态',
    })
    .then(data => {
      return (data || []).map(s => ({
        schoolId: s.schoolId,
        status: s.status == 1 ? 1 : 0,
        enableDelayUp: s.enableDelayUp,
        lastUpdateTime: s.lastUpTime,
        num: s.num || 0,
      }))
    })
}

// 上报考生
export function apiSubmitSchoolStudent(params) {
  return ajax.put({
    url: 'mark/exam/student/submit',
    params: {
      examId: params.examId,
      schoolId: params.schoolId,
      checkPrefixExamNum: params.checkPrefixExamNum,
    },
  })
}

// 管理员批量上报考生
export function apiSubmitSchoolStudentBatch(data) {
  return ajax.put({
    url: 'mark/exam/student/submitBatch',
    params: {
      examId: data.examId,
      checkPrefixExamNum: data.checkPrefixExamNum,
    },
    data: data.schoolIds,
  })
}

export function apiUndoSubmitSchoolStudent(params) {
  return ajax.put({
    url: 'mark/exam/student/undoSubmit',
    params: {
      examId: params.examId,
      schoolId: params.schoolId,
    },
    requestName: '撤回上报考生',
  })
}

export function apiUndoSubmitSchoolStudentBySchools(requestParams) {
  return ajax.put({
    url: 'mark/exam/student/undoSubmitBatch',
    params: {
      examId: requestParams.examId,
    },
    data: requestParams.schoolIds, // ['schoolId1', 'schoolId2', ...],
    // requestName: '批量撤回上报考生',
  })
}

export function apiSyncStudentInfo(params) {
  return ajax.put({
    url: 'mark/exam/syncBaseMsg',
    params: {
      examId: params.examId,
      schoolId: params.schoolId,
    },
    requestName: '同步学生信息到考试项目',
  })
}

/**
 * 扫描
 */
export function apiAddScanStation(data) {
  return ajax.post({
    url: 'mark/exam/scan/station',
    data: {
      examId: data.examId,
      stationName: data.stationName,
      schoolIds: data.schoolIds,
    },
    requestName: '新增扫描点',
  })
}

export function apiEditScanStation(data) {
  return ajax.put({
    url: 'mark/exam/scan/station',
    data: {
      examId: data.examId,
      stationId: data.stationId,
      stationName: data.stationName,
      schoolIds: data.schoolIds,
    },
    requestName: '修改扫描点',
  })
}

export function apiGetScanStation(examId) {
  return ajax.get({
    url: 'mark/exam/scan/station',
    params: {
      examId: examId,
    },
    requestName: '获取扫描点',
  })
}

export function apiDeleteScanStation(params) {
  return ajax.delete({
    url: 'mark/exam/scan/station',
    params: {
      examId: params.examId,
      stationId: params.stationId,
    },
    requestName: '删除扫描点',
  })
}

export function apiGetScanStatistics(params) {
  return ajax.get({
    url: 'mark/exam/scan/monitor/total',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
    },
    requestName: '获取扫描统计',
  })
}

export function apiGetScanStudents(data) {
  return ajax.put({
    url: 'mark/exam/scan/monitor/studentList',
    params: {
      examId: data.examId,
      examSubjectId: data.examSubjectId,
      key: (data.key && data.key.trim()) || null,
      schoolId: data.schoolId || null,
      roomNo: data.roomNo || null,
      scanStationId: data.stationId || null,
      page: data.currentPage,
      size: data.pageSize,
    },
    data: data.status,
    requestName: '获取扫描考生',
  })
}

export function apiGetStudentPaper(params) {
  return ajax
    .get({
      url: 'mark/exam/scan/monitor/studentPage',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
        studentId: params.studentId,
      },
      requestName: '获取答卷',
    })
    .then(data => {
      if (data && data.imgUrls && data.imgUrls.length > 0) {
        data.imgUrls = data.imgUrls.map(imgUrl => addImageUrlParam(imgUrl))
      }

      return data
    })
}

export function apiGetExamUnScanStudentExcel(requestParams) {
  return ajax.download({
    url: 'mark/exam/scan/monitor/examNoScanStus',
    params: {
      examId: requestParams.examId,
    },
    requestName: '下载未扫学生名单',
  })
}

export function apiGetExamAbsentStudentExcel(params) {
  return ajax.download({
    url: 'mark/exam/scan/monitor/examAbsentStus',
    params: {
      examId: params.examId,
    },
    requestName: '下载缺考学生名单',
  })
}

/**
 * 评卷员
 */
export function apiGetMultipleSchoolExamTeacherSetting(examId) {
  return ajax
    .get({
      url: 'mark/exam/teacher/upTime',
      params: {
        examId,
      },
      requestName: '获取联考评卷员设置',
    })
    .then(data => {
      return {
        submitBeginTime: (data.beginTime && new Date(data.beginTime)) || null,
        submitEndTime: (data.endTime && new Date(data.endTime)) || null,
      }
    })
}

export function apiEditSubmitTeacherTimeRange(params) {
  return ajax.put({
    url: 'mark/exam/teacher/upTime',
    params: {
      examId: params.examId,
      beginTime: params.submitBeginTime,
      endTime: params.submitEndTime,
    },
    requestName: '修改评卷员上报截止时间',
  })
}

export function apiGetExamTeachers(params) {
  let sendParams = {
    examId: params.examId,
    examSubjectId: params.examSubjectId,
    schoolId: params.schoolId || null,
    key: params.keyword || null,
    size: params.pageSize,
    page: params.currentPage,
  }

  if (sendParams.examSubjectId)
    return ajax
      .get({
        url: 'mark/exam/teacher/examSubjectTeachers',
        params: sendParams,
        requestName: '获取科目评卷员',
      })
      .then(data => {
        return {
          total: data.total,
          teachers: data.records.map(s => ({
            ...transformTeacher(s),
            teachingRoles: s.teachingRoles,
          })),
        }
      })
  else {
    return ajax
      .get({
        url: 'mark/exam/teacher/examTeachers',
        params: sendParams,
        requestName: '获取全部科目评卷员',
      })
      .then(data => {
        return {
          total: data.total,
          teachers: data.records.map(s => ({
            ...transformTeacher(s),
            teachingRoles: s.teachingRoles,
          })),
        }
      })
  }
}

export function apiAddGradeTeachersIntoExamTeachers(data) {
  if (!data.examSubjectId) {
    return ajax.put({
      url: 'mark/exam/teacher/initExamTeacher',
      params: {
        examId: data.examId,
      },
      data: data.schoolIds,
      requestName: '添加本年级本科目所有任课老师为评卷员',
    })
  } else {
    return ajax.put({
      url: 'mark/exam/teacher/initExamSubjectTeacher',
      params: {
        examSubjectId: data.examSubjectId,
      },
      data: data.schoolIds,
      requestName: '添加本年级所有任课老师为评卷员',
    })
  }
}

export function apiSetEnableDelayUpTeacher(requestParams) {
  return ajax.put({
    url: 'mark/exam/teacher/setEnableDelayUpTeacher',
    params: {
      examId: requestParams.examId, // String
      schoolId: requestParams.schoolId, // String
      enable: requestParams.enable, // Boolean
    },
    requestName: '设置学校是否可以延迟上报评卷员',
  })
}

export function apiGetSchoolTeacherSubmitStatus(examId, examSubjectId) {
  let url = examSubjectId ? 'mark/exam/teacher/examSubjectUpStatus' : 'mark/exam/teacher/examUpStatus'

  return ajax
    .get({
      url,
      params: {
        examId,
        examSubjectId,
      },
      requestName: '获取学校评卷员上报状态',
    })
    .then(data => {
      return (data || []).map(s => ({
        schoolId: s.schoolId,
        num: s.num,
        status: s.status == 1 ? 1 : 0,
        enableDelayUp: s.enableDelayUp,
        lastUpdateTime: s.lastUpTime,
      }))
    })
}

export function apiSubmitSchoolTeacher(params) {
  return ajax.put({
    url: 'mark/exam/teacher/submit',
    params: {
      examId: params.examId,
      schoolId: params.schoolId,
      check: Boolean(params.check),
    },
  })
}

export function apiUndoSubmitSchoolTeacher(params) {
  return ajax.put({
    url: 'mark/exam/teacher/undoSubmit',
    params: {
      examId: params.examId,
      schoolId: params.schoolId,
    },
    requestName: '撤回上报评卷员',
  })
}

export function apiRemoveExamTeachers(data) {
  if (data.examSubjectId)
    return ajax.delete({
      url: 'mark/exam/teacher/examSubjectTeacher',
      params: {
        examSubjectId: data.examSubjectId,
      },
      data: data.teacherIds,
      requestName: '删除指定评卷员',
    })
  else
    return ajax.delete({
      url: 'mark/exam/teacher/examTeacher',
      params: {
        examId: data.examId,
      },
      data: data.teacherIds,
      requestName: '删除指定评卷员',
    })
}

export function apiRemoveAllExamTeachers(data) {
  if (data.examSubjectId)
    return ajax.delete({
      url: 'mark/exam/teacher/allExamSubjectTeacher',
      params: {
        examSubjectId: data.examSubjectId,
        schoolId: data.schoolId,
      },
      requestName: '批量删除评卷员',
    })
  else
    return ajax.delete({
      url: 'mark/exam/teacher/allExamTeacher',
      params: {
        examId: data.examId,
        schoolId: data.schoolId,
      },
      requestName: '批量删除评卷员',
    })
}

export function apiGetExamTeachersExportExcel(params) {
  return ajax.download({
    url: params.examSubjectId ? 'mark/exam/teacher/downExamSubjectTeachers' : 'mark/exam/teacher/downExamTeachers',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      schoolId: params.schoolId || '',
    },
    requestName: '导出评卷员列表',
  })
}

export function apiGetSchoolTeachersShortList(data) {
  return ajax
    .get({
      url: 'mark/exam/teacher/shortlist',
      params: {
        schoolId: data.schoolId,
        gradeId: data.gradeId || null,
        subjectId: data.subjectId || null,
        key: data.key || '',
        examSubjectId: data.examSubjectId || '',
      },
      requestName: '获取学校评卷员候选列表',
    })
    .then(teachers => (teachers || []).map(transformUser))
}

export function apiAddMarkingTeachers(data) {
  return ajax
    .post({
      url: 'mark/exam/teacher/examSubjectTeacher',
      params: {
        examSubjectId: data.examSubjectId || '',
      },
      data: data.teacherIds || null,
      requestName: '添加评卷员',
    })
    .then(teachers => (teachers || []).map(transformUser))
}

export function apiGetSchoolAllTeachers(data) {
  return ajax
    .get({
      url: 'mark/exam/teacher/examSubjectTeacherAll',
      params: {
        schoolId: data.schoolId,
        examSubjectId: data.examSubjectId || '',
      },
      requestName: '获取学校科目下全部评卷员',
    })
    .then(data => {
      return data
    })
}

export function apiDeleteSubjectUnMarkMakers(requestParams) {
  return ajax.delete({
    url: 'mark/exam/teacher/examSubjectBlockTeacher',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      subjectiveId: requestParams.subjectiveId || undefined, // 题块Id
    },
    requestName: '删除未评卷评卷员',
  })
}

export function apiDeleteSubjectUnMarkLeaders(params) {
  return ajax.delete({
    url: '/mark/mark/task/delExamSubjectBlockLeader',
    params: {
      examSubjectId: params.examSubjectId,
    },
    requestName: '删除未处理任务题组长',
  })
}

/**
 * 题块给分间隔
 */
export function apiSetScoreInterval(data) {
  return ajax.put({
    url: 'mark/exam/block/scoreInterval',
    data: {
      examSubjectId: data.examSubjectId,
      blockId: data.blockId,
      questions: data.questions.map(q => ({
        questionId: q.questionId,
        topicCode: q.topicCode,
        questionCode: q.questionCode,
        branchCode: q.branchCode,
        scoreInterval: q.scoreInterval,
      })),
    },
    requestName: '修改给分间隔',
  })
}

export function apiGetExcelSchoolIds(data) {
  return ajax.upload({
    url: 'mark/exam/examSchoolCode/getSchId',
    data,
    requestName: '获取Excel导入学校的schoolId',
  })
}

export function apiCopyScanConfig(params) {
  return ajax.put({
    url: 'mark/exam/scan/station/copyScanConfig',
    params: {
      sourceExamId: params.sourceExamId,
      destExamId: params.destExamId,
    },
    requestName: '复制扫描配置',
  })
}

export function apiGetTeachingActionInfo(params) {
  return ajax
    .get({
      url: 'mark/exam/teachingActivities',
      params: params,
      requestName: '获取教学活动',
    })
    .then(data => {
      return data
    })
}

export function apiGetTeachingActionInfoByGrade(params) {
  return ajax
    .get({
      url: 'mark/exam/gradeExaminationDistribution',
      params: params,
      requestName: '获取各年级考试概况',
    })
    .then(data => {
      return data
    })
}

export function apiGetTeachingActionInfoByMonth(params) {
  return ajax
    .get({
      url: 'mark/exam/monthExaminationDistribution',
      params: params,
      requestName: '获取各月份考试概况',
    })
    .then(data => {
      return data
    })
}

export function apiExamAddSubject(requestParams) {
  return ajax.post({
    url: 'mark/exam/addSubject',
    params: {
      examId: requestParams.examId,
      subjectId: requestParams.subjectId,
      subjectType: requestParams.subjectType,
    },
    requestName: '考试项目添加考试科目',
  })
}

export function apiExamDeleteSubject(requestParams) {
  return ajax.delete({
    url: 'mark/exam/delSubject',
    params: {
      examSubjectId: requestParams.examSubjectId,
    },
    requestName: '考试项目删除考试科目',
  })
}

export function apiAddExamSchool(params) {
  return ajax.post({
    url: 'mark/exam/school/add',
    params: {
      examId: params.examId,
      schoolId: params.schoolId,
      examSchoolCode: params.examSchoolCode || '',
    },
    requestName: '添加学校',
  })
}

export function apiDeleteExamSchool(params) {
  return ajax.delete({
    url: 'mark/exam/school/delete',
    params: {
      examId: params.examId,
      schoolId: params.schoolId,
    },
    requestName: '删除学校',
  })
}

export function apiImportExamSchools(requestParams) {
  return ajax.upload({
    url: 'mark/exam/school/import',
    data: {
      examId: requestParams.examId, // * String
      file: requestParams.file, // *Stream
    },
    requestName: '导入联考学校',
  })
}

export function apiEditExamNameAndDate(params) {
  return ajax.post({
    url: 'mark/exam/updateExamInfo',
    params: {
      examId: params.examId,
      examName: params.examName,
      beginTime: params.startDate && formatDate(params.startDate),
      endTime: params.endDate && formatDate(params.endDate),
      paperSubscribe: params.paperSubscribe || false,
      enableVenue: params.enableVenue,
      enableAdmissionTicket: params.enableAdmissionTicket,
      isAdmissionTicketShowPhoto: params.enableAdmissionTicket && params.isAdmissionTicketShowPhoto,
      isShow: params.isShow,
      enableFinancial: params.enableFinancial || false,
      enableAppMark: 'enableAppMark' in params ? params.enableAppMark : true,
    },
    requestName: '修改考试名称与时间',
  })
}

// export function apiSaveAnswerSheet(answerSheetData, onProgress) {
//   return ajax.upload({
//     url: `mark/exam/answerSheet`,
//     data: answerSheetData,
//     onProgress,
//     requestName: '保存答题卡',
//   })
// }

// export function apiCopyAnswerSheet(requestParams) {
//   return ajax.put({
//     url: 'mark/exam/copyAnswerSheet',
//     params: {
//       answerSheetId: requestParams.answerSheetId,
//       name: requestParams.name,
//     },
//     requestName: '复制答题卡',
//   })
// }

export function apiGetAnswerSheet(answerSheetId) {
  return ajax.get({
    url: `mark/exam/getSheetJson`,
    params: {
      answerSheetId,
    },
    requestName: '加载答题卡',
  })
}

// 由试卷Id获取答题卡
export function apiGetAnswerSheetByPaperId(paperId) {
  return ajax.get({
    url: `mark/exam/getSheetJsonByPaperId`,
    params: {
      paperId,
    },
    requestName: '加载答题卡',
  })
}

// 由考试科目Id获取答题卡
export function apiGetAnswerSheetByExamSubjectId(examSubjectId) {
  return ajax.get({
    url: `mark/exam/getSheetJsonByExamSubjectId`,
    params: {
      examSubjectId,
    },
    requestName: '加载答题卡',
  })
}

export function apiGetAnswerSheetPDFUrl(answerSheetId) {
  return ajax.get({
    url: `mark/exam/getAnswerSheetPdfUrl`,
    params: {
      answerSheetId,
    },
    requestName: '下载答题卡',
  })
}

// 由试卷Id获取PDFUrl
export function apiGetAnswerSheetPDFUrlByPaperId(paperId) {
  return ajax.get({
    url: `mark/exam/getAnswerSheetPdfUrlByPaperId`,
    params: {
      paperId,
    },
    requestName: '下载答题卡',
  })
}

// 由考试科目Id获取PDFUrl
export function apiGetAnswerSheetPDFUrlByExamSubjectId(examSubjectId) {
  return ajax.get({
    url: `mark/exam/getAnswerSheetPdfUrlByExamSubjectId`,
    params: {
      examSubjectId,
    },
    requestName: '下载答题卡',
  })
}

// 由试卷Id获取答题卡Id
export function apiGetAnswerSheetIdByPaperId(paperId) {
  return ajax.get({
    url: `mark/exam/getAnswerSheetIdByPaperId`,
    params: {
      paperId,
    },
    requestName: '获取答题卡信息',
  })
}

// 由考试科目Id获取答题卡Id
export function apiGetAnswerSheetIdByExamSubjectId(examSubjectId) {
  return ajax.get({
    url: `mark/exam/getAnswerSheetIdByExamSubjectId`,
    params: {
      examSubjectId,
    },
    requestName: '获取答题卡信息',
  })
}

// 先阅后扫答题卡信息，故只传examId
export function apiGetAnswerSheetInfo(examId) {
  return ajax.get({
    url: 'mark/exam/getAnswerSheetInfo',
    params: {
      examId,
    },
    requestName: '获取答题卡信息',
  })
}

export function apiUpdateExamSubjectTime(data) {
  return ajax.post({
    url: 'mark/exam/updateExamSubjectTime',
    params: {
      examId: data.examId,
    },
    data: data.subjects,
    requestName: '修改科目考试时间',
  })
}

export function apiGetManagerRoles(params) {
  return ajax.get({
    url: 'mark/exam/managerRoles',
    params: {
      examId: params.examId,
    },
    requestName: '获取项目管理人员角色',
  })
}

export function apiCopyExamAdmin(data) {
  return ajax.put({
    url: 'mark/exam/copyExamManagers',
    params: {
      examId: data.examId,
      sourceExamId: data.sourceExamId,
      examRoleIds: data.examRoleIds,
    },
    requestName: '复制考试管理人员',
  })
}

export function apiExamSubjectAllowMarkerMarkMoreTask(requestParams) {
  return ajax.put({
    url: 'mark/exam/enableIncreaseMarkTaskNum',
    params: {
      enable: requestParams.enable, // *Boolean
      examSubjectId: requestParams.examSubjectId, // *String
    },
    requestName: '考试科目设置是否允许评卷员评比任务量更多的试卷',
  })
}

export function apiGetExportSchoolAdmissionTicketPDF(requestParams) {
  return ajax.download({
    url: 'mark/exam/exportAdmissionTicket',
    params: {
      examId: requestParams.examId,
      schoolId: requestParams.schoolId,
    },
    requestName: '获取导出学校考生准考证链接',
  })
}

export function apiGetExportExamAdmissionTicketUrl(examId) {
  return ajax.get({
    url: 'mark/exam/exportAllAdmissionTicket',
    params: {
      examId: examId,
    },
    requestName: '获取导出考试项目考生准考证链接',
  })
}

export function apiGetExportExamAdmissionTicketStatus(examId) {
  return ajax.get({
    url: 'mark/exam/exportAllAdmissionTicketStatus',
    params: {
      examId: examId,
    },
    requestName: '获取导出考试项目考生准考证状态',
  })
}

export function apiGetReGenerateExportExamAdmissionTicketUrl(examId) {
  return ajax.get({
    url: 'mark/exam/reExportAllAdmissionTicket',
    params: {
      examId: examId,
    },
    requestName: '重新生成并获取导出考试项目考生准考证链接',
  })
}

export function apiUpdateIsRecommendAsLeader(data) {
  return ajax.put({
    url: 'mark/exam/teacher/updateIsRecommendAsLeader',
    params: {
      isRecommendAsLeader: data.isRecommendAsLeader,
      examSubjectId: data.examSubjectId,
      teacherId: data.teacherId,
    },
    requestName: '设置推荐题组长',
  })
}

export function apiUpdateRecommendAsLeaderSetting(data) {
  return ajax.put({
    url: 'mark/exam/updateEnableRecommendBlocLeader',
    params: {
      examId: data.examId,
      enable: data.enable,
    },
    requestName: '设置是否允许推荐题组长',
  })
}

export function apiChangeExamSubjectShowPaperAnswer(requestParams) {
  return ajax.put({
    url: 'mark/exam/updateShowPaperAnswer',
    params: {
      examSubjectId: requestParams.examSubjectId, // *String
      showPaperAnswer: requestParams.showPaperAnswer, // *Boolean
    },
    requestName: '更新评卷题块是否显示绑定试卷的答案',
  })
}

export function apiMergeSchoolExams(requestParams) {
  return ajax.post({
    url: 'mark/exam/merge',
    data: {
      examName: requestParams.examName, // *String
      examSubjectIds: requestParams.examSubjectIds, // *Array[*String]
    },
    requestName: '合并校内考试',
  })
}

export function apiExamCreatorEditAdmissionNumCount(requestParams) {
  return ajax.put({
    url: 'mark/exam/examCreatorUpdateAdmissionNumLength',
    params: {
      examId: requestParams.examId, // *String
      length: requestParams.length, // *String
    },
    requestName: '考试创建者修改准考号长度',
  })
}
