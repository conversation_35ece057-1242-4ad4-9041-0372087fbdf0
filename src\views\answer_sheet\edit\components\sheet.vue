<template>
  <div class="sheet">
    <com-sheet-page v-for="page in pages" :key="page.pageIndex" class="sheet-page" :page="page" @height-change="layout">
    </com-sheet-page>
  </div>
</template>

<script>
  import comSheetPage from './sheet_page.vue'

  import { mapGetters, mapMutations } from 'vuex'

  export default {
    components: {
      comSheetPage,
    },
    data() {
      return {
        needLayout: false,
      }
    },
    computed: {
      ...mapGetters('answerSheet', ['pages', 'pageSizeId']),
    },
    watch: {
      // 切换尺寸需重新生成页面
      // 如内容只有1页的A4，转为A3两栏时，若不重新生成，页面仍只有1页，而两栏排版的页数应该是2的倍数
      pageSizeId() {
        this.layout()
      },
    },
    methods: {
      ...mapMutations('answerSheet', ['generatePages']),
      layout() {
        // 多个页面多次发出高度变化事件，在下个宏任务统一处理
        if (this.needLayout) {
          return
        }
        this.needLayout = true
        setTimeout(() => {
          this.needLayout = false
          this.generatePages()
        }, 20)
      },
    },
  }
</script>
