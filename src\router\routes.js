/**
 * 1. 静态路由一直存在，动态路由根据登录用户角色添加
 * 2. 页面菜单项名称从路由中提取（meta.menu），需：
 *  2.1 所有路由项都必须有name，且唯一
 *  2.2 每个组件只能对应一个路由项
 */

import home from './home'
import qlib from './qlib'
import emarking from './emarking'
import report from './report'
import homework from './homework'
import teaching from './teaching'
import school from './school'
import eduSchool from './eduSchool'
import account from './account'
import research from './research'
import answerSheet from './answer_sheet'
import scanTemplate from './scan_template'
import teacherClasses from './teacher_classes'
import wrongQuestionSheet from './wrong_question_sheet'
import scan from './scan'
import stageReport from './staged_report'
import recommendQuestions from './recommend_questions'
import schoolResources from './school_resources'
import remarks from './remarks'
import practise from './practise'
import schoolNote from './school_note'
import supplementaryExercise from './supplementary_exercise'
import reivew from './review'
import ai from './ai'

export const staticRoutes = [
  {
    name: 'login',
    path: '/login',
    component: () => import('@/views/login/index.vue'),
  },
  {
    name: 'forget-password',
    path: '/forgetpassword',
    component: () => import('@/views/forget_password/index.vue'),
  },
  {
    name: 'oauth',
    path: '/oauth/callback',
    component: () => import('@/views/oauth/index.vue'),
  },
  {
    // 路由未匹配，显示404
    path: '/:catchAll(.*)',
    component: () => import('@/views/notfound/index.vue'),
  },
]

export const dynamicRoutes = [
  {
    name: 'index',
    path: '/',
    component: () => import('@/views/index/index.vue'),
    children: [
      home,
      ai,
      qlib,
      emarking,
      report,
      teaching,
      research,
      homework,
      stageReport,
      school,
      reivew,
      eduSchool,
      account,
      answerSheet,
      scanTemplate,
      teacherClasses,
      wrongQuestionSheet,
      scan,
      recommendQuestions,
      schoolResources,
      remarks,
      practise,
      schoolNote,
      supplementaryExercise,
    ],
  },
]

export const referenceBookScannerRoutes = [
  {
    name: 'index',
    path: '/',
    component: () => import('@/views/index/index.vue'),
    children: [
      {
        name: 'home',
        path: 'home',
        meta: {
          menu: '教辅扫描员任务',
        },
        component: () => import('@/views/reference_book_scanner_tasks/index.vue'),
        children: [
          {
            name: 'tasks',
            path: 'tasks',
            meta: {
              menu: '教辅扫描员任务列表',
            },
            component: () => import('@/views/reference_book_scanner_tasks/tasks.vue'),
          },
        ],
      },
      {
        name: 'scan-supplementary-exercise',
        path: 'supplementaryexercise/:examId/:examSubjectId',
        meta: {
          showHeader: false,
          showFooter: false,
          pageWidth: 'full',
        },
        component: () => import('@/views/scan/supplementary_exercise/scan/index.vue'),
      },
      {
        name: 'scan-answerSheet-exception',
        path: 'answersheet/exception/:examId/:examSubjectId',
        meta: {
          showHeader: false,
          showFooter: false,
          pageWidth: 'full',
        },
        component: () => import('@/views/scan/answer_sheet/exception/index.vue'),
      },
    ],
  },
]
