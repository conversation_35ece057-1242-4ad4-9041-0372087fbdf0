<template>
  <div class="block-english-writing">
    <ScorePanel v-if="showScorePanel" ref="scorePanel" :block="block" :move-top="!contentBorderStyle.top"></ScorePanel>
    <com-box-content
      ref="contentBox"
      :content="block.content"
      :width="pageBodyWidth"
      :height="block.contentHeight"
      :border="contentBorderStyle"
      :padding-top="contentPaddingTop"
      :resizable="true"
      @height-change="handleContentHeightChange"
      @resize="handleResize"
      @resize-end="handleResizeEnd"
      @content-change="changeContent"
    >
    </com-box-content>

    <div class="page-block-toolbar no-print">
      <div class="btn btn-primary" title="设置" @click="showModalChangeType = true">
        <Icon class="btn-icon" type="ios-settings"></Icon>
      </div>
      <div v-if="enableEditQuestion" class="btn btn-error" title="删除" @click="handleDelete">
        <Icon class="btn-icon" type="md-close"></Icon>
      </div>
    </div>

    <ModalChangeType v-model="showModalChangeType" :question-group="block.questionGroup"></ModalChangeType>
  </div>
</template>
<script>
  import comBoxContent from './box_content.vue'
  import ModalChangeType from './modal_change_type'
  import ScorePanel from './score_panel'

  import { mapMutations } from 'vuex'
  import ScoreTypeEnum from '@/enum/answer_sheet/score_type'

  export default {
    components: {
      comBoxContent,
      ModalChangeType,
      ScorePanel,
    },
    props: {
      block: {
        type: Object,
        required: true,
      },
    },
    emits: ['height-change'],
    data() {
      return {
        showModalChangeType: false,
        contentBasePaddingTop: 14,
      }
    },
    computed: {
      enableEditQuestion() {
        return this.$store.getters['answerSheet/enableEditQuestion']
      },

      /**
       * 打分栏相关
       */
      showScorePanel() {
        let isPostScan = this.$store.getters['answerSheet/isPostScan']
        return isPostScan && this.block.part == 0
      },
      showScorePanelBar() {
        return this.showScorePanel && this.block.questionGroup.scoreType.name == ScoreTypeEnum.Bar.id
      },

      pageBodyWidth() {
        let pageSize = this.$store.getters['answerSheet/pageSize'](this.block.pageIndex)
        return pageSize.width - pageSize.left - pageSize.right
      },
      contentBorderStyle() {
        return this.$store.getters['answerSheet/getShowBoxContentBorder'](this.block)
      },
      contentPaddingTop() {
        let paddingTop = this.contentBasePaddingTop
        if (this.showScorePanelBar) {
          paddingTop += this.block.questionGroup.scoreType.height
        }
        return paddingTop
      },
    },
    created() {
      this.changeInstance()
    },
    updated() {
      this.changeInstance()
    },
    methods: {
      ...mapMutations('answerSheet', ['changePageBlockInstance', 'changePageBlockContent', 'changePageBlockHeight']),

      /**
       * 实例、内容、高度
       */
      changeInstance() {
        this.changePageBlockInstance({
          blockId: this.block.id,
          instance: this,
        })
      },
      changeContent(content) {
        this.changePageBlockContent({
          blockId: this.block.id,
          content: content,
        })
      },
      changeHeight(contentHeight = this.block.contentHeight, emitEvent) {
        let height = contentHeight
        let heightChanged = height != this.block.height
        this.changePageBlockHeight({
          blockId: this.block.id,
          height,
          contentHeight,
        })
        if (emitEvent || (emitEvent == null && heightChanged)) {
          this.$emit('height-change', height)
        }
      },
      handleContentHeightChange(contentHeight) {
        this.changeHeight(contentHeight)
      },
      handleResize(contentHeight) {
        this.changeHeight(contentHeight, false)
      },
      handleResizeEnd() {
        this.changeHeight(this.block.contentHeight, true)
      },

      /**
       * 操作
       */
      handleDelete() {
        let questionCodes = Array.from(new Set(this.block.questionGroup.questions.map(q => q.questionCode)))
        this.$Modal.confirm({
          title: '确认删除',
          content: `您将删除第${questionCodes.join('、')}题`,
          onOk: () => {
            this.$store.commit('answerSheet/deleteSubjectiveQuestionGroup', this.block.questionGroup.id)
            this.$store.commit('answerSheet/generatePages')
          },
        })
      },

      /**
       * 拆分
       */
      split(getNextPageAvailableHeightFunc) {
        let blocks
        try {
          blocks = this.$refs['contentBox'].split(getNextPageAvailableHeightFunc, this.block.questionGroup, {
            notFirstPaddingTop: this.contentBasePaddingTop,
          })
        } catch {
          blocks = []
        }
        return blocks
      },

      /**
       * 扫描模板
       */
      getScoreAreas(areaLeft, areaTop) {
        if (this.showScorePanel) {
          return this.$refs['scorePanel'].getScoreAreas(areaLeft, areaTop)
        } else {
          return []
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-block-toolbar {
    .tbtn {
      display: block;
    }
  }
</style>
