CKEDITOR.plugins.add('text-emphasis', {
  icons: 'text-emphasis',
  init: function (editor) {
    editor.addCommand('text-emphasis', {
      exec: function (editor) {
        const className = 'char-emphasis'

        let selection = editor.getSelection()
        let ranges = selection.getRanges()
        ranges.forEach(range => {
          // 扩展选中区域
          let startContainer = range.startContainer
          let startParent = startContainer.getParent()
          if (startContainer.type == 3 && startParent.hasClass(className) && range.startOffset == 0) {
            range.setStartBefore(startParent)
          }
          let endContainer = range.endContainer
          let endParent = endContainer.getParent()
          if (
            endContainer.type == 3 &&
            endParent.hasClass(className) &&
            endParent.getText().length == range.endOffset
          ) {
            range.setEndAfter(endParent)
          }
          // 已有着重号则清除样式
          let cloned = range.cloneContents()
          let hasEmphasis = cloned.find('.' + className).count() > 0
          if (hasEmphasis) {
            editor.removeStyle(new CKEDITOR.style({ element: 'span', attributes: { class: className } }))
            return
          }
          // 否则添加着重号类给每个汉字
          let text = range.extractContents().$.textContent
          let fragment = new CKEDITOR.dom.documentFragment()
          let font = startParent.getStyle('font-family')
          text.split('').forEach(char => {
            const charCode = char.charCodeAt(0)
            if (charCode >= 19968 && charCode <= 40869) {
              const span = new CKEDITOR.dom.element('span')
              span.addClass(className)
              if (font) {
                span.setStyle('font-family', font)
              }
              span.setText(char)
              fragment.append(span)
            } else {
              fragment.append(new CKEDITOR.dom.text(char))
            }
          })
          editor.insertHtml(fragment.getHtml())
        })
      },
    })

    // 添加工具栏按钮
    editor.ui.addButton('text-emphasis', {
      label: '着重号',
      command: 'text-emphasis',
      toolbar: 'basicstyles,10', // 工具栏组和按钮优先级
      icon: this.path + 'icons/text-emphasis.png', // 按钮图标路径
    })
  },
})
