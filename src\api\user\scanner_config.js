import ajax from '@/api/ajax'

export function apiGetScannerConfig() {
  return ajax.get({
    url: 'user/scannerConfig/list',
    requestName: '获取扫描配置',
  })
}

export function apiAddScannerConfig(params) {
  return ajax.post({
    url: 'user/scannerConfig/add',
    params: {
      teacherId: params.teacherId,
      rangeType: params.rangeType,
      gradeId: params.gradeId,
      classId: params.classId,
      subjectId: params.subjectId,
    },
    requestName: '添加扫描配置',
  })
}

export function apiDeleteScannerConfig(ids) {
  return ajax.delete({
    url: 'user/scannerConfig/delete',
    params: {
      ids: ids.join(','),
    },
    requestName: '删除扫描配置',
  })
}
