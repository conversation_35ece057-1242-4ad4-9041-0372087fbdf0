/**
 * 数字转汉字，仅支持绝对值小于一亿的数字
 * @param {Number} n
 * @returns String
 */
export function numberToChinese(n) {
  if (Number.isNaN(n) && !Number.isFinite(n)) {
    return '非数字'
  }
  // 只处理一亿以内数字
  if (n >= 100000000) {
    return '一亿+'
  } else if (n <= -100000000) {
    return '负一亿-'
  }

  // 符号
  let sign = n < 0 ? '负' : ''
  n = n < 0 ? -n : n

  // 分整数部分与小数部分
  const cNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  let integerChinese = getIntegerChinese(Math.floor(n))
  let decimalChinese = getDecimalChinese(String(n).split('.')[1] || '')

  return sign + integerChinese + decimalChinese

  function getIntegerChinese(intNum) {
    // 从万位分成高低两段
    let intNumHigh = Math.floor(intNum / 10000)
    let intNumLow = intNum - intNumHigh * 10000

    let intCStrHigh = intNumHigh ? numToC9999(intNumHigh) : ''
    let intCStrLow = intNumLow ? numToC9999(intNumLow) : '零'

    if (intCStrHigh) {
      intCStrHigh += '万'
      if (intNumLow === 0) {
        intCStrLow = ''
      } else if (intNumLow < 1000) {
        intCStrLow = '零' + intCStrLow
      }
    }

    return intCStrHigh + intCStrLow
  }

  function getDecimalChinese(numStr) {
    return (
      (numStr ? '点' : '') +
      numStr
        .split('')
        .map(x => cNumbers[x])
        .join('')
    )
  }

  function numToC9999(num) {
    const units = ['', '十', '百', '千']
    let c = String(num)
      .split('')
      .reverse()
      .map((n, idx) => `${cNumbers[n]}${n != '0' ? units[idx] : ''}`)
      .reverse()
      .join('')

    // 去掉末尾零
    while (c.endsWith('零')) {
      c = c.substring(0, c.length - 1)
    }

    // 去掉连续零
    c = c.replace('零零', '零')

    // 一十几 -> 十几
    if (c.startsWith('一十')) {
      c = '十' + c.substring(2)
    }
    return c
  }
}

export function isPositiveFinite(v) {
  return typeof v == 'number' && Number.isFinite(v) && v > 0
}

export function isNegativeFinite(v) {
  return typeof v == 'number' && Number.isFinite(v) && v < 0
}

export function isNonNegativeFinite(v) {
  return isPositiveFinite(v) || v === 0
}

export function isNonPositiveFinite(v) {
  return isNegativeFinite(v) || v === 0
}

export function isPositiveInteger(v) {
  return isPositiveFinite(v) && Number.isInteger(v)
}

export function isPrecisionLessThanOrEqual(v, precision) {
  let value = v
  for (let i = 0; i < precision; i++) {
    value *= 10
  }
  return Math.floor(value) === value
}
