import { Alphabet_Number } from '@/const/string'

/**
 * 随机字符串
 * @param {Number} length
 * @returns String
 */
export function randomId(length = 8) {
  let str = ''
  for (let i = 0; i < length; i++) {
    str += Alphabet_Number[Math.floor(Math.random() * Alphabet_Number.length)]
  }

  return str
}

export function randomString(e = 32) {
  let t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
    a = t.length,
    n = ''

  for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
  return n
}
