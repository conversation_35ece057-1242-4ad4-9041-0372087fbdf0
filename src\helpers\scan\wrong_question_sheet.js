import { loadImage } from '@/utils/promise'
import ScanUnitRecognizeStatusEnum from '@/enum/scan/scan_unit_recognize_status'
import PageStatusEnum from '@/enum/scan/page_status'
import ExceptionStatusEnum from '@/enum/scan/exception_status'

export function prefetchScanUnitImages(scanUnit) {
  if (!scanUnit) {
    return Promise.resolve()
  }
  let imageUrls = []
  if (
    [
      ScanUnitRecognizeStatusEnum.MatchSuccess.id,
      ScanUnitRecognizeStatusEnum.AdmissionNumException.id,
      ScanUnitRecognizeStatusEnum.RecognizeSuccess.id,
    ].includes(scanUnit.recogStatus)
  ) {
    imageUrls = scanUnit.adjustImgUrls
  } else {
    imageUrls = scanUnit.imgUrls
  }
  return Promise.all(imageUrls.map(url => loadImage(url, false))).catch(() => {})
}

export function resetScanUnitEditData(scanUnit) {
  scanUnit.newAdmissionNum = scanUnit.admissionNum
  scanUnit.newStudentName = scanUnit.studentName
  scanUnit.objectives.forEach(q => {
    q.newAnswer = q.answer
    q.newStatus = q.status
  })
}

export function getScanUnitStatusText(unit) {
  if (unit.recognizing) {
    return '识别中...'
  } else if (unit.exception) {
    if (
      ![
        ScanUnitRecognizeStatusEnum.Init.id,
        ScanUnitRecognizeStatusEnum.ExamInit.id,
        ScanUnitRecognizeStatusEnum.ImageNormal.id,
        ScanUnitRecognizeStatusEnum.MatchSuccess.id,
        ScanUnitRecognizeStatusEnum.RecognizeSuccess.id,
      ].includes(unit.recogStatus)
    ) {
      return ScanUnitRecognizeStatusEnum.getNameById(unit.recogStatus)
    } else if ([PageStatusEnum.Duplicate.id, PageStatusEnum.Missing.id].includes(unit.pageStatus)) {
      return PageStatusEnum.getNameById(unit.pageStatus)
    } else if (ExceptionStatusEnum.Yes.id == unit.objectiveEx) {
      return '填涂异常'
    } else {
      return '识别异常'
    }
  } else {
    return '识别正常'
  }
}
