import { createApp } from 'vue'

/**
 * Modal隐藏的同时若有大量计算，可能无法正常显示滚动条
 * 此方法强制显示滚动条
 */
export function showScrollbarAfterHideModal() {
  setTimeout(() => {
    document.body.style.paddingRight = ''
    document.body.style.overflow = ''
  }, 0)
}

/**
 * 获取iview Tree组件勾选的结点，勾选父节点的忽略子节点
 * @param {*} treeData
 * @returns
 */
export function getCheckedNodes(treeData) {
  let nodes = []
  treeData.forEach(function _getCheckedNodes(node) {
    if (node.checked) {
      nodes.push(node)
    } else {
      node.children.forEach(_getCheckedNodes)
    }
  })
  return nodes.map(node => ({
    ...node,
    name: node.title || node.name,
  }))
}

/**
 * 获取iview Tree组件勾选的所有结点
 * @param {*} treeData
 * @returns
 */
export function getCheckedNodesStrictly(treeData) {
  let nodes = []
  treeData.forEach(function _getCheckedNodes(node) {
    if (node.checked) {
      nodes.push(node)
    }
    node.children.forEach(_getCheckedNodes)
  })
  return nodes.map(node => ({
    ...node,
    name: node.title || node.name,
  }))
}

/**
 * iview Tree组件显示勾选的结点
 * @param {*} treeData
 * @param {*} ifShowFunc
 */
export function showCheckedNodes(treeData, ifShowFunc) {
  treeData.forEach(function _showNode(node) {
    node.checked = ifShowFunc(node)
    if (node.children && node.children.length) {
      node.children.forEach(_showNode)
    }
    node.expand = node.children.some(child => child.checked || child.expand)
  })
}

/**
 * iview Tree组件显示点选的结点
 * @param {*} treeData
 * @returns
 */
export function getSelectedNodes(treeData) {
  let nodes = []
  treeData.forEach(function _getSelectedNodes(node) {
    if (node.selected) {
      nodes.push(node)
    } else {
      node.children.forEach(_getSelectedNodes)
    }
  })
  return nodes.map(node => ({
    ...node,
    name: node.title,
  }))
}

/**
 * 新建vue实例显示弹窗
 * @param {*} 包含弹窗的组件
 * @param {*} props 传入组件的属性
 */
export function createModalInstance(modalComponent, props) {
  let elContainer = document.createElement('div')
  elContainer.classList.add('custom-modal-instance-container')
  document.body.appendChild(elContainer)

  let instance = null

  let closeMethod = () => {
    if (instance) {
      instance.unmount()
    }
    elContainer.remove()
  }

  instance = createApp(modalComponent, {
    ...props,
    modelValue: true,
    closeMethod,
  })
  instance.mount(elContainer)
}

/**
 * 是否正在显示有遮罩的弹窗
 */
export function getShowModalMask() {
  return Array.from(document.body.querySelectorAll('.ivu-modal-mask')).some(el => el.style.display != 'none')
}
