import { apiGetClassStudentScoreDetail } from '@/api/report'

import { jsPDF } from 'jspdf'
import { applyPlugin } from 'jspdf-autotable'
import { arrayBufferToBase64 } from '@/utils/file'

import { ApiBaseUrl } from '@/config/config'

applyPlugin(jsPDF)

export async function exportSubjectClassStudentsScore(templateId, examSubjectId, classId, fileName) {
  const PDFData =
    (await apiGetClassStudentScoreDetail({ templateId, examSubjectId, classId, reportName: 'cls_stuScoreDetail' })) ||
    []

  let pdfNormalFont = '0123456789，,.( )同学在本次满分的考试中成绩为班级名年第最高平均总题号数'
  let pdfBoldFont = '0123456789_.( )成绩单得分明细：'
  PDFData.forEach(item => {
    pdfNormalFont += item.name
    ;(item.scoreDetails || []).forEach(sd => {
      pdfNormalFont += sd.questionName
    })
    pdfBoldFont += item.name + item.examName + item.subjectName + item.className
  })
  pdfNormalFont = filtedFont(pdfNormalFont)
  pdfBoldFont = filtedFont(pdfBoldFont)

  let msyhBase64 = null
  let msyhbdBase64 = null
  try {
    msyhBase64 = await fetchGeneratePDFFont({
      font: 'msyh',
      text: pdfNormalFont,
    })
    msyhbdBase64 = await fetchGeneratePDFFont({
      font: 'msyhbd',
      text: pdfBoldFont,
    })
  } catch {
    msyhBase64 = null
    msyhbdBase64 = null
  }

  if (!msyhBase64 || !msyhbdBase64) {
    return Promise.reject('字体加载失败')
  }

  const Doc = new jsPDF({
    format: 'a4',
    unit: 'mm',
    orientation: 'portrait',
  })
  Doc.addFileToVFS('msyh.ttf', msyhBase64)
  Doc.addFileToVFS('msyhbd.ttf', msyhbdBase64)
  Doc.addFont('msyh.ttf', 'msyh', 'normal', 'normal')
  Doc.addFont('msyhbd.ttf', 'msyhbd', 'normal', 'bold')

  const PageWidth = Math.round(Doc.internal.pageSize.getWidth())
  const PageHeight = Math.round(Doc.internal.pageSize.getHeight())

  const PageHorizontalMargin = 10
  const PageVerticalMargin = 14.8
  const MaxRowWidth = PageWidth - PageHorizontalMargin * 2
  const PageContentHeight = PageHeight - PageVerticalMargin * 2

  let pagePartCount = 3
  let pagePartHeight = PageContentHeight / pagePartCount

  const TableRowItemMinCount = 7
  const TableRowItemMaxCount = 15
  let tableRowItemCount = 15

  let tableFinalY = 0

  PDFData.forEach((item, index) => {
    const ItemPageOrder = index % pagePartCount
    let operatePointY = PageVerticalMargin + ItemPageOrder * pagePartHeight
    if (index) {
      if (ItemPageOrder && tableFinalY) {
        const DashY = (operatePointY + tableFinalY) / 2
        Doc.setLineDashPattern([3, 1.5])
        Doc.setLineWidth(0.4)
        Doc.setDrawColor(255, 153, 0)
        Doc.line(0, DashY, PageWidth, DashY)
      } else {
        Doc.addPage()
      }
    }

    let text = `${item.name}(${item.admissionNum})_ 成绩单`
    Doc.setFontSize(10)
    Doc.setFont('msyhbd', 'normal', 'bold')
    let textDimension = Doc.getTextDimensions(text)
    let textWidth = textDimension.w
    let textHeight = textDimension.h
    let operatePointX = (PageWidth - textWidth) / 2
    Doc.text(text, operatePointX, operatePointY)

    text = `${item.examName}(${item.subjectName})_${item.className}`
    Doc.setFontSize(9)
    textDimension = Doc.getTextDimensions(text)
    textWidth = textDimension.w
    operatePointX = (PageWidth - textWidth) / 2
    operatePointY += textHeight + 2
    textHeight = textDimension.h
    Doc.text(text, operatePointX, operatePointY)

    text = `${item.name}同学 在本次满分 ${item.subjectFullScore} 分的考试中，成绩为 ${item.score} 分`
    if (item.classRank) {
      text += `，班级名次第 ${item.classRank} 名`
    }
    if (item.gradeRank) {
      text += `，年级名次第 ${item.gradeRank} 名`
    }
    if (item.classHighestScore || item.classHighestScore === 0) {
      text += `，班级最高分 ${item.classHighestScore} 分`
    }
    if (item.gradeHighestScore || item.gradeHighestScore === 0) {
      text += `，年级最高分 ${item.gradeHighestScore} 分`
    }
    if (item.gradeAverageScore || item.gradeAverageScore === 0) {
      text += `，年级平均分 ${item.gradeAverageScore} 分`
    }
    Doc.setFont('msyh', 'normal', 'normal')
    textDimension = Doc.getTextDimensions(text)
    textWidth = textDimension.w
    operatePointX = PageHorizontalMargin
    operatePointY += textHeight + 4
    textHeight = textDimension.h
    if (textWidth > MaxRowWidth) {
      const TextRows = Doc.splitTextToSize(text, MaxRowWidth)
      TextRows.forEach((tr, trdx) => {
        if (trdx) {
          operatePointY += textHeight + 1.5
        }
        Doc.text(tr, operatePointX, operatePointY)
      })
    } else {
      Doc.text(text, operatePointX, operatePointY)
    }

    text = `${item.subjectName}得分明细：`
    Doc.setFont('msyhbd', 'normal', 'bold')
    operatePointY += textHeight + 3
    textHeight = Doc.getTextDimensions(text).h
    Doc.text(text, operatePointX, operatePointY)

    if (item.scoreDetails && item.scoreDetails.length) {
      if (!index) {
        // 第一个计算表格每行单元格合适个数
        const LongestTableCellContent = item.scoreDetails.reduce(
          (acc, cur) => (cur.questionName.length > acc.length ? cur.questionName : acc),
          '题号'
        )
        Doc.setFont('msyh', 'normal', 'normal')
        Doc.setFontSize(7)
        const LongestTableCellContentWidth = Doc.getTextDimensions(LongestTableCellContent).w
        const LongestTableCellContentTableRowCount = Math.ceil(MaxRowWidth / LongestTableCellContentWidth)
        tableRowItemCount = Math.min(
          Math.max(LongestTableCellContentTableRowCount, TableRowItemMinCount),
          TableRowItemMaxCount
        )
      }

      const TableData = []
      const LoopTimes = Math.ceil(item.scoreDetails.length / tableRowItemCount)
      for (let i = 0; i < LoopTimes; i++) {
        const RowData = item.scoreDetails.slice(i * tableRowItemCount, (i + 1) * tableRowItemCount)
        TableData.push(['题号', ...RowData.map(rd => rd.questionName)])
        TableData.push(['分数', ...RowData.map(rd => rd.score)])
      }
      TableData[TableData.length - 2].push('总分')
      TableData[TableData.length - 1].push(item.score)

      operatePointY += textHeight
      Doc.autoTable({
        tableWidth: MaxRowWidth,
        startY: operatePointY,
        styles: {
          font: 'msyh',
          lineColor: [114, 223, 220],
          cellWidth: MaxRowWidth / (tableRowItemCount + 1),
          fontSize: 7,
          valign: 'middle',
          halign: 'center',
          cellPadding: {
            top: 1,
            right: 0,
            bottom: 1,
            left: 0,
          },
        },
        margin: {
          top: 0,
          right: PageHorizontalMargin,
          left: PageHorizontalMargin,
          bottom: 0,
        },
        alternateRowStyles: {
          fillColor: [201, 243, 242],
        },
        body: TableData,
        didParseCell(data) {
          if (data.row.index === TableData.length - 2 && data.column.index + 1 > data.row.raw.length) {
            data.cell.styles.fillColor = [255, 255, 255]
          }
        },
        didDrawCell(data) {
          const { x, y, width, height } = data.cell
          Doc.setLineDash()
          Doc.setLineWidth(0.3)

          if (data.column.index + 1 <= data.row.raw.length) {
            Doc.line(x, y, x + width, y) // 上边框
            Doc.line(x, y + height, x, y) // 左边框

            if (data.row.index === TableData.length - 1) {
              Doc.line(x, y + height, x + width, y + height) // 下边框
            }
          } else if (data.row.index === TableData.length - 2 && data.column.index + 1 > data.row.raw.length) {
            Doc.line(x, y, x + width, y) // 上边框
          }

          if (data.column.index + 1 === data.row.raw.length) {
            Doc.line(x + width, y, x + width, y + height) // 右边框
          }
        },
      })

      tableFinalY = Doc.lastAutoTable.finalY

      if (!index) {
        // 设置一页放多少个学生信息为好 （最多3个 最少1个）
        const OnePartHeight = tableFinalY - PageVerticalMargin
        pagePartCount = Math.min(Math.max(Math.floor(PageContentHeight / OnePartHeight), 1), 3)
        pagePartHeight = PageContentHeight / pagePartCount
      }
    }
  })

  Doc.save(`${fileName || '学生成绩'}.pdf`)

  return Promise.resolve()
}

function filtedFont(font = '') {
  const FontArray = font.split('')
  const FontFiltedArray = []
  FontArray.forEach(item => {
    if (!FontFiltedArray.includes(item)) {
      FontFiltedArray.push(item)
    }
  })
  return FontFiltedArray.join('')
}

/**
 * 用fetch方法获取字体文件ttf
 */
function fetchGeneratePDFFont(requestParams) {
  return fetch(ApiBaseUrl + '/report/fontsubset', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      font: requestParams.font, // *String: 'msyh'(微软雅黑) | 'msyhbd'(微软雅黑粗体)
      text: requestParams.text, // *String
    }),
  })
    .then(response => response.arrayBuffer())
    .then(responseArrayBuffer => arrayBufferToBase64(responseArrayBuffer))
}
