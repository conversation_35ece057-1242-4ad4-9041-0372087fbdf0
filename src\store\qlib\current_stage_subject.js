import { apiGetKnowledges } from '@/api/qlib'
import { apiGetUserQuestionLibraryRoles } from '@/api/user/user'

import Role from '@/enum/user/role'

export default {
  state: {
    currentStageSubject: {
      stageId: 0,
      stageName: '',
      subjectId: 0,
      subjectName: '',
    },

    currentStageSubjectKnowledgesForDevideQuestion: [],

    userCurrentStageSubjectEditorRolePermissions: {
      stageId: 0,
      subjectId: 0,
      roleList: [],
    },
  },

  getters: {
    currentStageSubject(state) {
      return state.currentStageSubject
    },

    currentStageSubjectKnowledgesForDevideQuestion(state) {
      return state.currentStageSubjectKnowledgesForDevideQuestion
    },

    userCurrentStageSubjectEditorRolePermissions(state) {
      return state.userCurrentStageSubjectEditorRolePermissions
    },

    hasEditQlibManagementPermission(state, getters, rootState, rootGetters) {
      return (
        rootGetters['user/isQuestionLibraryAdministrator'] ||
        getters['userCurrentStageSubjectEditorRolePermissions'].roleList.some(
          roleId => roleId == Role.QuestionLibraryEditor.id
        )
      )
    },

    // isHasEditQlibManagementPermissionEffective(state, getters, rootState, rootGetters) {
    //   return (
    //     rootGetters['user/isQuestionLibraryAdministrator'] ||
    //     (getters['userCurrentStageSubjectEditorRolePermissions'].stageId === getters['currentStageSubject'].stageId &&
    //       getters['userCurrentStageSubjectEditorRolePermissions'].subject === getters['currentStageSubject'].subjectId)
    //   )
    // },
  },

  mutations: {
    updateCurrentStageSubjectKnowledgesForDevideQuestion(state, newKnowledges) {
      state.currentStageSubjectKnowledgesForDevideQuestion = newKnowledges || []
    },

    clearCurrentStageSubjectKnowledgesForDevideQuestion(state) {
      state.currentStageSubjectKnowledgesForDevideQuestion = []
    },

    updateUserCurrentStageSubjectEditorRolePermissions(state, newPermissions) {
      state.userCurrentStageSubjectEditorRolePermissions = newPermissions
    },

    clearUserCurrentStageSubjectEditorRolePermissions(state) {
      state.userCurrentStageSubjectEditorRolePermissions = {
        stageId: 0,
        subjectId: 0,
        roleList: [],
      }
    },
  },

  actions: {
    initCurrentStageSubject(context) {
      if (this.currentStageSubject && this.currentStageSubject.stageId && this.currentStageSubject.subjectId) {
        return Promise.resolve()
      }

      let allStageSubjects = []
      context.rootGetters['qlib/stageSubjects']().forEach(stage => {
        stage.subjects.forEach(s => {
          allStageSubjects.push({
            stageId: stage.id,
            stageName: stage.name,
            subjectId: s.id,
            subjectName: s.name,
          })
        })
      })
      let { schoolId, teachings } = context.rootState.user
      let data = null

      // 从localStorage中读取
      let userSubjectStr = localStorage.getItem('userSubject')
      if (userSubjectStr) {
        let userSubject = JSON.parse(userSubjectStr)
        if (userSubject.schoolId == schoolId) {
          data = allStageSubjects.find(s => s.stageId == userSubject.stageId && s.subjectId == userSubject.subjectId)
        }
      }
      // 从任教中取
      if (!data && teachings.length > 0) {
        data = allStageSubjects.find(s => teachings.some(t => t.stageId == s.stageId && t.subjectId == s.subjectId))
      }
      // 默认初中数学
      if (!data) {
        data = allStageSubjects.find(s => s.stageName == '初中' && s.subjectName == '数学')
      }

      context.state.currentStageSubject = data
    },

    updateCurrentStageSubject(context, newStageSubject) {
      context.state.currentStageSubject = newStageSubject
      window.localStorage.setItem(
        'userSubject',
        JSON.stringify({
          ...newStageSubject,
          schoolId: context.rootState.user.schoolId,
        })
      )
    },

    getCurrentStageSubjectKnowledgesForDevideQuestion(context) {
      if (!context.getters['currentStageSubject']) {
        return
      }

      apiGetKnowledges(context.getters['currentStageSubject'].stageId, context.getters['currentStageSubject'].subjectId)
        .then(knowledges => {
          let tiledKnowledges = []

          knowledges.forEach(function _tilKnowledgeNode(node) {
            let sameContentIndex = tiledKnowledges.findIndex(x => x.title === node.name)
            if (sameContentIndex === -1) {
              tiledKnowledges.push({
                id: node.id,
                title: node.name,
              })
            } else {
              tiledKnowledges.splice(sameContentIndex, 1)
            }

            if (node.children && node.children.length) {
              node.children.forEach(nc => _tilKnowledgeNode(nc))
            }
          })

          return tiledKnowledges
        })
        .then(knowledges => context.commit('updateCurrentStageSubjectKnowledgesForDevideQuestion', knowledges))
        .catch(() => context.commit('clearCurrentStageSubjectKnowledgesForDevideQuestion'))
    },

    getUserCurrentStageSubjectEditorRolePermissions(context, targetStageSubject) {
      return apiGetUserQuestionLibraryRoles({
        gradeLevel: targetStageSubject.stageId,
        subjectId: targetStageSubject.subjectId,
      })
        .then(response =>
          context.commit('updateUserCurrentStageSubjectEditorRolePermissions', {
            stageId: targetStageSubject.stageId,
            subjectId: targetStageSubject.subjectId,
            roleList: response,
          })
        )
        .catch(() => context.commit('clearUserCurrentStageSubjectEditorRolePermissions'))
    },
  },
}
