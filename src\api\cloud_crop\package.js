import ajax from '@/api/ajax'

const formatPackageData = data => {
  return data.map(item => ({
    examName: item.examname || '',
    examSubjectid: item.examsubjectid || '',
    failReason: item.failreason || '',
    importer: item.importer || '',
    importTime: item.importtime || '',
    insertTime: item.inserttime || '',
    packageId: item.packageid || '',
    packageUrl: item.packageurl || '',
    roomId: item.roomid || '',
    rowCount: item.rowcount || 0,
    serialNo: item.serialno || '',
    status: item.status || '',
    statusText: item.status ? status[item.status] : '',
    subjectName: item.subjectname || '',
    updateTime: item.updatetime || '',
    uploader: item.uploader || '',
    uploadIp: item.uploadip || '',
    uploadTime: item.uploadtime || '',
  }))
}

const status = {
  S: '导入成功',
  N: '未导入',
  I: '正在导入',
  E: '导入失败',
  F: '上传失败',
}

export function apiGetPackageStatus(data) {
  return ajax
    .get({
      url: '/imgproc/package/stats',
      params: {
        examSubjectId: data.examSubjectId,
      },
      requestName: '获取扫描包状态统计',
    })
    .then(data => {
      return data
    })
}

export function apiGetPackageByStatus(data) {
  return ajax
    .get({
      url: '/imgproc/package/packagesByStatus',
      params: {
        examSubjectId: data.examSubjectId,
        page: data.page,
        size: data.size,
        status: data.status,
      },
      requestName: '获取扫描包各状态统计',
    })
    .then(data => {
      data.records = formatPackageData(data.records)
      return data
    })
}

export function apiGetPackageByDuplicate(data) {
  return ajax
    .get({
      url: '/imgproc/package/duplicatePackages',
      params: {
        examSubjectId: data.examSubjectId,
        page: data.page,
        size: data.size,
      },
      requestName: '获取多次上传扫描包统计',
    })
    .then(data => {
      data.records = data.records.map(item => {
        return {
          packages: formatPackageData(item.packages),
          roomId: item.roomId,
        }
      })
      return data
    })
}

export function apiImportAllPackages(data) {
  return ajax.put({
    url: '/imgproc/package/importAllNormal',
    params: {
      examSubjectId: data.examSubjectId,
    },
    requestName: '导入所有扫描包',
  })
}

export function apiImportThePackage(data) {
  return ajax.put({
    url: '/imgproc/package/import',
    params: {
      examSubjectId: data.examSubjectId,
    },
    data: data.packageSerialNos,
    requestName: '导入扫描包',
  })
}

export function apiImportAllFailedPackages(data) {
  return ajax.put({
    url: '/imgproc/package/importAllException',
    params: {
      examSubjectId: data.examSubjectId,
    },
    requestName: '导入所有导入失败的扫描包',
  })
}

export function apiDeleteThePackage(data) {
  return ajax.delete({
    url: '/imgproc/package/packages',
    params: {
      examSubjectId: data.examSubjectId,
    },
    data: data.packageSerialNos,
    requestName: '删除扫描包',
  })
}

export function apiFixImporting(data) {
  return ajax.put({
    url: '/imgproc/package/fixImporting',
    params: {
      examSubjectId: data.examSubjectId,
    },
    data: data.serialNos,
    requestName: '刷新扫描包导入',
  })
}
