<template>
  <div class="container-sheet-edit">
    <LayoutHeader sticky background class="page-header">
      <Button class="btn-back" type="info" @click="pageBack">
        <Icon type="md-arrow-round-back"></Icon>&nbsp;返回
      </Button>
      <p class="page-title">
        {{ title }}
      </p>
      <div class="actions">
        <Button class="btn-action" shape="circle" :disabled="!hasPage" @click="showModalQuestionList = true">
          <Icon type="md-list"></Icon>题目列表
        </Button>
        <Button class="btn-action" shape="circle" :disabled="!hasPage" @click="previewSheet">
          <Icon type="md-eye"></Icon>&nbsp;&nbsp;预览
        </Button>
        <Button class="btn-action" shape="circle" :disabled="!hasPage" @click="saveSheet">
          <Icon type="md-document"></Icon>&nbsp;&nbsp;保存
        </Button>
        <Button v-if="isFromManual || isFromPaper" class="btn-action" shape="circle" @click="downloadSheet">
          <Icon type="md-download"></Icon>&nbsp;&nbsp;下载
        </Button>
        <Button v-if="isFromExam" class="btn-action" shape="circle" :disabled="!hasPage" @click="finishSheet">
          <Icon type="md-done-all"></Icon>&nbsp;&nbsp;完成制作
        </Button>
      </div>
    </LayoutHeader>

    <div class="page-body">
      <div class="container-sheet">
        <com-sheet ref="sheetContainer"></com-sheet>
      </div>

      <Affix :offset-top="70">
        <com-panel :before-save="beforeSave" @back="$emit('page-back')"></com-panel>
      </Affix>
    </div>

    <ModalPreview v-model="showModalPreview" :sheet="previewAnswerSheet"></ModalPreview>

    <ModalQuestionList v-model="showModalQuestionList"></ModalQuestionList>
  </div>
</template>

<script>
  import LayoutHeader from '@/components/layout_header'
  import comSheet from './components/sheet.vue'
  import comPanel from './components/panel.vue'
  import ModalPreview from './components/modal_preview'
  import ModalQuestionList from './components/modal_question_list'

  import { mapGetters } from 'vuex'
  import html2canvas from 'html2canvas'
  import { sleep } from '@/utils/promise'
  import { TemplateResolutionScale, TrueFalseBoxCssClass } from '@/const/answer_sheet'

  export default {
    components: {
      LayoutHeader,
      'com-sheet': comSheet,
      'com-panel': comPanel,
      ModalPreview,
      ModalQuestionList,
    },
    props: {
      answerSheet: Object,
      sourceType: Number,
      sourceId: String,
    },
    emits: ['save-sheet', 'download-sheet', 'finish-sheet', 'page-back'],
    data() {
      return {
        // 题目列表
        showModalQuestionList: false,

        // 预览答题卡
        showModalPreview: false,
        previewAnswerSheet: null,
      }
    },
    computed: {
      ...mapGetters('answerSheet', [
        'pages',
        'sheetId',
        'sheetName',
        'mode',
        'from',
        'isFromManual',
        'isFromPaper',
        'isFromExam',
        'objectiveQuestions',
        'subjectiveQuestions',
        'stage',
        'subject',
      ]),
      beforeSave() {
        return !this.answerSheet
      },
      sheetJson() {
        return (this.answerSheet && this.answerSheet.sheetJson) || ''
      },
      objectivesJson() {
        return (this.answerSheet && this.answerSheet.objectivesJson) || ''
      },
      blocksJson() {
        return (this.answerSheet && this.answerSheet.blocksJson) || ''
      },
      sheetSize() {
        return this.$store.state.answerSheet.sheet.size
      },
      title() {
        return `答题卡编辑${this.sheetName ? '——' + this.sheetName : ''}`
      },
      hasPage() {
        return this.pages.length > 0
      },
    },
    beforeUnmount() {
      this.revokePreviewImageObjectUrl()
    },
    methods: {
      /**
       * 按钮事件
       */
      async pageBack() {
        if (await this.getIsAnswerSheetUnSave()) {
          this.$Modal.confirm({
            title: '保存答题卡',
            content: '答题卡已修改且尚未保存！',
            okText: '去保存',
            cancelText: '放弃更改',
            onCancel: () => {
              this.$emit('page-back')
            },
          })
        } else {
          this.$emit('page-back')
        }
      },

      async previewSheet() {
        if (!this.hasPage) {
          return
        }
        this.revokePreviewImageObjectUrl()
        this.previewAnswerSheet = null

        this.$Spin.show({
          render: h =>
            h(
              'div',
              {
                style: {
                  fontSize: '24px',
                },
              },
              '请稍候'
            ),
        })

        try {
          // 生成图片
          let sideImages = await this.getSheetSideImages(TemplateResolutionScale)
          let imageUrls = sideImages.map(imgBlob => URL.createObjectURL(imgBlob)).join(',')

          // 生成扫描模板
          let templateJson = await this.$store.dispatch('answerSheet/exportScanTemplate')
          let { objectivesJson, blocksJson } = await this.$store.dispatch('answerSheet/exportSheet')

          this.previewAnswerSheet = {
            imageUrls,
            templateJson,
            objectivesJson,
            blocksJson,
          }
          this.showModalPreview = true
        } catch (err) {
          this.$Message.error({
            content: err,
          })
          throw err
        } finally {
          this.$Spin.hide()
        }
      },
      revokePreviewImageObjectUrl() {
        let imageUrls = this.previewAnswerSheet?.imageUrls
        if (!imageUrls) {
          return
        }
        imageUrls.split(',').forEach(url => {
          URL.revokeObjectURL(url)
        })
      },

      async saveSheet() {
        if (!this.hasPage) {
          return
        }

        if (!this.sheetName) {
          this.$Message.info({
            content: '请输入答题卡名称',
          })
          return
        }

        if (this.objectiveQuestions.length == 0 && this.subjectiveQuestions.length == 0) {
          this.$Message.info({
            content: '请添加题目',
          })
          return
        }

        if (!this.stage.id || !this.subject.id) {
          this.$Message.info({
            content: '未选择学段学科',
          })
          return
        }

        let exportSheetData
        try {
          exportSheetData = await this.$store.dispatch('answerSheet/exportSheet')
        } catch (err) {
          this.$Message.error({
            content: err,
            duration: 5,
            closable: true,
          })
          throw err
        }
        let { sheetJson, objectivesJson, blocksJson } = exportSheetData
        if (sheetJson == this.sheetJson && objectivesJson == this.objectivesJson && blocksJson == this.blocksJson) {
          this.$Message.info({
            content: '未作修改',
          })
          return
        }

        // 此遮罩在保存接口调用完成后关闭
        this.$Spin.show({
          render: h =>
            h(
              'div',
              {
                style: {
                  fontSize: '24px',
                },
              },
              '正在保存，请稍候'
            ),
        })
        await sleep(0)

        let data = {}
        try {
          let sheetData = await this.getSheetData()
          data.isNew = Boolean(!this.answerSheet)
          data.answerSheetId = this.answerSheet ? this.answerSheet.id : this.sheetId
          data.name = this.sheetName
          data.sheetType = 1
          data.mode = this.mode
          data.sourceType = this.sourceType
          data.sourceId = this.sourceId
          data.imageWidth = sheetData.imageWidth
          data.imageHeight = sheetData.imageHeight
          data.sheetJson = sheetData.sheetJson
          data.objectivesJson = sheetData.objectivesJson
          data.blocksJson = sheetData.blocksJson
          data.templateJson = sheetData.templateJson
          data.html = sheetData.html
          data.objectiveCount = this.objectiveQuestions.length
          data.subjectiveCount = this.subjectiveQuestions.length
          data.gradeLevel = this.stage.id
          data.subjectId = this.subject.id

          data.pageCount = 0
          for (let i = 1; i <= 8; i++) {
            let key = `image${i}`
            if (sheetData[key]) {
              data[key] = sheetData[key]
              data.pageCount++
            }
          }
        } catch (err) {
          this.$Message.error({
            content: err,
            duration: 5,
            closable: true,
          })
          this.$Spin.hide()
          throw err
        }

        this.$emit('save-sheet', data)
      },

      async downloadSheet() {
        try {
          await this.checkDownloadAvailable()
        } catch (err) {
          if (err) {
            this.$Message.info({
              content: err,
            })
          }
          return
        }
        this.$emit('download-sheet')
      },
      async checkDownloadAvailable() {
        if (this.hasPage) {
          if (!this.answerSheet) {
            throw '请先保存答题卡'
          }
          if (await this.getIsAnswerSheetUnSave()) {
            throw '答题卡已修改，请先保存！'
          }
        } else if (!this.sheetJson) {
          throw '尚无答题卡PDF可下载'
        }
      },

      async finishSheet() {
        if (await this.getIsAnswerSheetUnSave()) {
          this.$Message.info({
            content: '答题卡已修改，请先保存！',
          })
          return
        }
        this.$emit('finish-sheet')
      },

      /**
       * 内部方法
       */
      // 检查是否未保存
      async getIsAnswerSheetUnSave() {
        let { sheetJson, objectivesJson, blocksJson } = await this.$store.dispatch('answerSheet/exportSheet')
        return this.sheetJson != sheetJson || this.objectivesJson != objectivesJson || this.blocksJson != blocksJson
      },
      // 生成答题卡数据
      async getSheetData() {
        let sheetData = {
          sheetJson: '',
          objectivesJson: '',
          blocksJson: '',
          templateJson: '',
          html: '',
          image1: null,
          image2: null,
          image3: null,
          image4: null,
          image5: null,
          image6: null,
          image7: null,
          image8: null,
          imageWidth: this.sheetSize.width,
          imageHeight: this.sheetSize.height,
        }

        try {
          // 生成图片
          // 用于印刷的答题卡图片分辨率 = 4 * PixelsPerMillimeter * 25.4 = 16 * 25.4 = 406.4
          let images = await this.getSheetSideImages(4)
          images.forEach((img, idx) => {
            sheetData[`image${idx + 1}`] = img
          })

          // 生成前端答题卡数据
          let exportSheetResult = await this.$store.dispatch('answerSheet/exportSheet')
          sheetData.sheetJson = exportSheetResult.sheetJson
          sheetData.objectivesJson = exportSheetResult.objectivesJson
          sheetData.blocksJson = exportSheetResult.blocksJson

          // 生成扫描模板
          sheetData.templateJson = await this.$store.dispatch('answerSheet/exportScanTemplate')

          // // 生成答题卡HTML，原意为答题卡图片由后端根据此html生成，但考虑到浏览器渲染不一致及性能问题，目前调用html2canvas生成
          // sheetData.html = this.getAnswerSheetHTML()
        } catch (err) {
          throw `生成答题卡数据出错：${err}`
        }

        return sheetData
      },
      // 获取答题卡各页（栏）html
      getSheetPages() {
        let elPages = this.$refs['sheetContainer'].$el.children
        let pages = []
        for (let oldNode of elPages) {
          let node = oldNode.cloneNode(true)

          // 剔除不属于答题卡本身的节点
          let noPrintNodes = node.querySelectorAll('.no-print')
          for (let noPrint of noPrintNodes) {
            noPrint.parentNode.removeChild(noPrint)
          }

          // 先阅后扫填空题不显示辅助题号
          node.querySelectorAll('.' + TrueFalseBoxCssClass).forEach(el => {
            el.classList.add('print')
          })

          // <u></u>元素若只有空白内容，则html2canvas不显示。https://github.com/niklasvh/html2canvas/issues/1513
          // 将空白字符替换为下划线。&nbsp的宽度为_的一半
          let underlineNodes = node.querySelectorAll('u')
          for (let el of underlineNodes) {
            if (Array.from(el.childNodes).every(node => node.nodeType == 3)) {
              let innerText = el.innerText
              if (innerText && !innerText.trim()) {
                el.innerText = '_'.repeat(innerText.length / 2)
                el.style.textDecorationLine = 'none'
              }
            }
          }

          // 边框为0的表格不显示辅助边框线
          let noBorderTables = node.querySelectorAll('table.cke_show_border[border="0"]')
          for (let el of noBorderTables) {
            el.classList.remove('cke_show_border')
          }

          // 表格居中样式
          let alignCenterTables = node.querySelectorAll('table[align="center"]')
          for (let el of alignCenterTables) {
            el.style.marginLeft = 'auto'
            el.style.marginRight = 'auto'
          }

          node.style.margin = 0
          pages.push(node)
        }
        return pages
      },
      // 获取答题卡各面html
      getSheetSides() {
        let pages = this.getSheetPages()
        // 将各页（栏）合并为面
        let sides = []
        pages.forEach((pageNode, pageIndex) => {
          let { sideIndex, sideWidth, sideHeight, pageLeft } = this.$store.getters['answerSheet/pageSize'](pageIndex)
          let sideNode = sides[sideIndex]
          if (!sideNode) {
            sideNode = document.createElement('div')
            sideNode.classList.add('sheet-side')
            sideNode.style.position = 'relative'
            sideNode.style.width = sideWidth + 'px'
            sideNode.style.height = sideHeight + 'px'
            sideNode.style.margin = '0'
            sideNode.style.backgroundColor = 'white'
            sides[sideIndex] = sideNode
          }
          pageNode.style.position = 'absolute'
          pageNode.style.top = '0'
          pageNode.style.left = pageLeft + 'px'
          sideNode.appendChild(pageNode)
        })
        // 添加页码
        sides.forEach((sideNode, sideIndex) => {
          let { text, style, position } = this.$store.getters['answerSheet/generatePageNumber'](sideIndex, sides.length)
          let elPageNumber = document.createElement('div')
          elPageNumber.innerText = text
          Object.keys(style).forEach(key => {
            elPageNumber.style[key] = style[key]
          })
          elPageNumber.style.position = 'absolute'
          elPageNumber.style.left = position.left + 'px'
          elPageNumber.style.top = position.top + 'px'
          sideNode.appendChild(elPageNumber)
        })
        return sides
      },
      // 获取答题卡html
      getAnswerSheetHTML() {
        let sides = this.getSheetSides().map((side, idx) => {
          return `<!DOCTYPE html><html><head><meta charset="UTF-8"><title>答题卡第${
            idx + 1
          }面</title><style>* {box-sizing: border-box;} p {margin: 0}</style></head><body style="margin: 0">${
            side.outerHTML
          }</body></html>`
        })
        return JSON.stringify(sides)
      },
      // 获取答题卡各面图片
      async getSheetSideImages(scale) {
        let sides = this.getSheetSides()
        if (sides.length > 8) {
          throw '答题卡不能超过四张纸'
        }

        return this.printHtmlToImages(sides, scale)
      },
      // 将页面转换为图片
      async printHtmlToImages(nodes, scale) {
        let container = document.createElement('div')
        container.style.position = 'fixed'
        container.style.zIndex = '-999'
        container.style.overflow = 'hidden'
        container.style.left = 0
        container.style.top = 0
        container.style.width = '100px'
        container.style.height = '100px'
        window.document.body.appendChild(container)

        let images = []
        try {
          for (let node of nodes) {
            container.innerHTML = ''
            container.appendChild(node)
            await sleep(0)

            let canvasObj = await html2canvas(node, {
              background: null,
              scale,
              scrollX: 0,
              scrollY: 0,
              useCORS: true,
              allowTaint: true,
            })
            await new Promise(resolve => {
              canvasObj.toBlob(
                img => {
                  images.push(img)
                  resolve()
                },
                'image/jpeg',
                0.9
              )
            })
          }
        } catch {
          throw '生成答题卡图像出错'
        } finally {
          window.document.body.removeChild(container)
        }
        return images
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-header {
    @include flex(row, flex-start, center);
    z-index: 10;

    :deep(> .background) {
      background-color: transparent;
    }

    &.pinned {
      :deep(> .background) {
        background-color: white;
        box-shadow: 0 1px 1px #aaa;
      }
    }

    .page-title {
      flex-grow: 1;
      margin-left: 50px;
      color: black;
      font-weight: bold;
      font-size: $font-size-medium-x;
      line-height: 60px;
    }

    .actions {
      @include flex(row, space-between, center);
      flex-grow: 0;
      flex-shrink: 0;
      min-width: 300px;
      margin-left: 20px;

      .btn-action:not(:first-child) {
        margin-left: 20px;
      }
    }
  }

  .page-body {
    @include flex(row, flex-start, flex-start);
    margin-top: 10px;
  }

  .container-sheet {
    flex-grow: 1;
    min-height: 1228px;
    border: 1px solid transparent;
    background-color: $color-icon;

    .sheet-page {
      margin: 20px auto;
      background-color: white;
    }
  }

  .modal-template {
    .page {
      position: relative;

      img {
        display: block;
      }
    }

    .mask {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
    }

    .square {
      position: absolute;
      border: 1px solid red;
    }
  }
</style>
