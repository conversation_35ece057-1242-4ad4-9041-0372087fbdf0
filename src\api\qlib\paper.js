import ajax from '@/api/ajax'
import { PaperInfo } from '@/helpers/qlib/paper_info'
import { Paper } from '@/helpers/qlib/paper'
import { Question } from '@/helpers/qlib/question'

/**
 * 试卷查询
 */
export function apiGetPapers(params) {
  let sendParams = {
    stage: params.stage,
    subject: params.subject,
    page: params.currentPage,
    size: params.pageSize,
    term: params.term || null,
    type: params.paperType || null,
    gradeId: params.grade || null,
    year: params.year || null,
    region: (params.region && params.region[params.region.length - 1]) || null,
    keyword: params.keyword || null,
  }
  return ajax
    .get({
      url: `ques/paper/byCondition`,
      params: sendParams,
      requestName: '获取试卷',
    })
    .then(data => {
      return {
        total: data.total || 0,
        papers: (data.records || []).map(p => PaperInfo.import(p)),
      }
    })
}

export function apiGetFavoritePapers(params) {
  let sendParams = {
    stage: params.stage,
    subject: params.subject,
    grade: params.grade || null,
    term: params.term || null,
    type: params.paperType || null,
    size: params.pageSize,
    page: params.currentPage,
  }
  return ajax
    .get({
      url: `ques/paper/fav`,
      params: sendParams,
      requestName: '获取收藏试卷',
    })
    .then(data => {
      return {
        total: data.total || 0,
        papers: (data.records || []).map(p => PaperInfo.import(p)),
      }
    })
}

export function apiGetPaperInfo(paperId) {
  return ajax
    .get({
      url: `ques/paper/info`,
      params: {
        paperId,
      },
      requestName: '获取试卷信息',
    })
    .then(p => {
      return PaperInfo.import(p)
    })
}

/**
 * 获取试卷内容
 */
export function apiGetPaperContent(paperId) {
  return ajax
    .get({
      url: `ques/paper/content`,
      params: {
        paperId,
      },
      requestName: '获取试卷内容',
    })
    .then(data => {
      return new Paper(
        data.paperInfo,
        data.basketPaperStyle,
        data.paperStructure,
        (data.questions || []).map(q => Question.import(q))
      )
    })
}

/**
 * 普通用户试卷操作
 */
export function apiDeletePaper(paperId) {
  return ajax.delete({
    url: `ques/paper/delete`,
    params: {
      paperId,
    },
    requestName: '删除试卷',
  })
}

export function apiModifyPaperInfo(paperInfo) {
  let region = paperInfo.region[paperInfo.region.length - 1]
  let sendData = {
    id: paperInfo.id,
    paperName: paperInfo.name,
    gradeLevel: paperInfo.stage.id,
    subjectId: paperInfo.subject.id,
    gradeId: paperInfo.grade.id,
    term: paperInfo.term.id || null,
    paperTypeId: paperInfo.paperType.id,
    year: paperInfo.year,
    regionId: region && region.value >= 0 ? region.value : null,
    flagCoachBookId: paperInfo.coachbookId || 0,
    paperGroupId: paperInfo.paperGroupId || 0,
  }

  return ajax.put({
    url: `ques/paper/infoChange`,
    data: sendData,
    requestName: '修改试卷信息',
  })
}

export function apiFavorPaper(pid) {
  return ajax.put({
    url: `ques/paper/changeFav/${pid}`,
    requestName: '收藏试卷',
  })
}

export function apiUnFavorPaper(pid) {
  return ajax.put({
    url: `ques/paper/changeFav/${pid}`,
    requestName: '取消收藏试卷',
  })
}

// 教辅管理根据试卷来修改题名
export function apiEditQuestionNameByPaper(paper) {
  return ajax.request({
    url: 'ques/paper/updateQuestionAlias',
    params: {
      paperId: paper.paperInfo.id,
    },
    data: {
      paperStyleJson: JSON.stringify(paper.paperStructure.export()),
    },
    useFormData: true,
    requestName: '修改试卷题名',
  })
}

// 修改试卷结构
export function apiChangePaperStructure(paper) {
  let paperStructure = JSON.stringify(paper.paperStructure.export())

  return ajax.request({
    method: 'put',
    url: `ques/paper/structure`,
    params: {
      paperId: paper.paperInfo.id,
    },
    data: {
      paperStructure,
    },
    useFormData: true,
    requestName: '更改试卷结构',
  })
}

/**
 * 上传下载
 */

export function apiDownloadPaperWord({ paperId, size, gradeId, paperTypeId, html }) {
  return ajax.download({
    url: 'ques/paper/downloadWord',
    params: {
      paperId,
      size,
      gradeId,
      paperTypeId,
    },
    data: { html },
    isFormData: true,
    requestName: '下载试卷',
  })
}

/**
 * 可用来绑定到某考试科目的试卷
 */
export function apiGetPapersForBind(params) {
  return ajax
    .get({
      url: 'ques/paperBind/availablePapers',
      params: {
        stage: params.stageId,
        subjectId: params.subjectId,
        gradeId: params.gradeId,
        term: params.term,
        type: params.type,
      },
      requestName: '获取可绑定试卷',
    })
    .then(data => (data || []).map(PaperInfo.import))
}

export function apiGetCanBeAddedtoCoachBookComposedPapers(requestParams) {
  return ajax.get({
    url: 'ques/paper/availableCoachBookPapers',
    params: {
      coachBookId: requestParams.coachBookId, // Number*
      stage: requestParams.gradeLevel, // Number
      subjectId: requestParams.subjectId, // Number
      keyword: requestParams.keyword, // String
      paperGroupId: requestParams.paperGroupId, // Number
      page: requestParams.currentPage || 1, // Number
      size: requestParams.pageSize || 10, // Number
      flagCoachBookId: requestParams.flagCoachBookId, // Number
    },
    requestName: '获取用户组卷中可添加入教辅的试卷列表',
  })
}

export function apiGetCanBeAddedtoCoachBookUploadedPapers(requestParams) {
  return ajax.get({
    url: 'ques/paper/availableCoachBookPapersUpload',
    params: {
      coachBookId: requestParams.coachBookId, // Number*
      stage: requestParams.gradeLevel, // Number
      subjectId: requestParams.subjectId, // Number
      keyword: requestParams.keyword, // String
      paperGroupId: requestParams.paperGroupId, // Number
      page: requestParams.currentPage || 1, // Number
      size: requestParams.pageSize || 10, // Number
      flagCoachBookId: requestParams.flagCoachBookId, // Number
    },
    requestName: '获取用户上传卷中可加入教辅的试卷列表',
  })
}

export function apiGetCanBeAddedtoCoachBookSharedPapers(requestParams) {
  return ajax.get({
    url: 'ques/paperShare/availableCoachBookPapersShareToMe',
    params: {
      coachBookId: requestParams.coachBookId, // Number
      type: requestParams.type, // Number
      grade: requestParams.grade, // Number
      year: requestParams.year, // Number
      region: requestParams.region, // String
      subject: requestParams.subjectId, // Number
      stage: requestParams.gradeLevel, // Number
      bank: requestParams.bank, // Number
      keyword: requestParams.keyword, // String
      paperGroupId: requestParams.paperGroupId, // String
      page: requestParams.currentPage, // Number
      size: requestParams.pageSize, // Number
      flagCoachBookId: requestParams.flagCoachBookId, // Number
    },
    requestName: '获取用户被分享卷中可加入教辅的试卷列表',
  })
}
