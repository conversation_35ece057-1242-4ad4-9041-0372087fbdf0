<template>
  <Modal :model-value="modelValue" title="主观题设置" width="600" @on-visible-change="handleModalVisibleChange">
    <Table :data="modalSubjectives" :columns="tableColumns" :span-method="handleSpan" border></Table>
    <template #footer>
      <div class="modal-footer">
        <Button type="text" @click="handleCloseModal">取消</Button>
        <Button type="primary" @click="handleBtnConfirmChangeSubjectives">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { InputNumber } from 'view-ui-plus'

  import { numberToChinese } from '@/utils/number'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },

      subjectives: {
        type: Array,
        default: () => [],
      },

      enableChangeScore: Boolean,
    },

    emits: ['update:modelValue', 'change-subjectives'],

    data() {
      return {
        modalSubjectives: [],
      }
    },

    computed: {
      hasBlanks() {
        return this.subjectives.some(x => x.blankScores && x.blankScores.length > 0)
      },

      tableColumns() {
        let columns = [
          {
            title: '大题',
            align: 'center',
            minWidth: 100,
            render: (h, params) => h('span', {}, numberToChinese(params.row.topicCode)),
          },
          {
            title: '题号',
            align: 'center',
            minWidth: 100,
            render: (h, params) =>
              h(
                'span',
                {},
                params.row.branchCode ? params.row.questionCode + '.' + params.row.branchCode : params.row.questionCode
              ),
          },
        ]

        if (this.hasBlanks) {
          columns.push({
            title: '小题分',
            align: 'center',
            key: 'fullScore',
            minWidth: 80,
          })
        }

        columns.push({
          title: '分数',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            if (params.row.blankScores.length > 1) {
              let blankInputNumbers = params.row.blankScores.map((blank, index) =>
                h(
                  'div',
                  {
                    style: {
                      height: '36px',
                      'line-height': 2,
                    },
                  },
                  [
                    h('span', {}, '空' + (index + 1) + '：'),
                    h(InputNumber, {
                      modelValue: blank,
                      size: 'small',
                      min: 0,
                      step: 0.5,
                      disabled: !this.enableChangeScore,
                      onOnChange: score => {
                        let targetQuestion = this.modalSubjectives.find(
                          x =>
                            x.topicCode === params.row.topicCode &&
                            x.questionCode === params.row.questionCode &&
                            x.branchCode === params.row.branchCode
                        )
                        if (targetQuestion) {
                          targetQuestion.blankScores[index] = score
                          targetQuestion.fullScore = targetQuestion.blankScores.reduce((acc, cur) => acc + cur, 0)
                        }
                      },
                    }),
                  ]
                )
              )
              return h(
                'div',
                {
                  style: {
                    margin: '6px 0 2px 0',
                  },
                },
                blankInputNumbers
              )
            } else {
              return h(InputNumber, {
                modelValue: Number(params.row.fullScore),
                size: 'small',
                min: 0,
                step: 0.5,
                disabled: !this.enableChangeScore,
                onOnChange: score => {
                  let target = this.modalSubjectives.find(
                    x =>
                      x.topicCode === params.row.topicCode &&
                      x.questionCode === params.row.questionCode &&
                      x.branchCode === params.row.branchCode
                  )
                  if (target) {
                    if (target.blankScores.length) {
                      target.blankScores[0] = score
                    }
                    target.fullScore = score
                  }
                },
              })
            }
          },
        })

        return columns
      },
    },

    watch: {
      modelValue() {
        if (this.modelValue) {
          this.modalSubjectives = this.subjectives.map(x => x.clone())
        }
      },
    },

    methods: {
      handleModalVisibleChange(visible) {
        if (!visible) {
          this.handleCloseModal()
        }
      },

      handleCloseModal() {
        this.$emit('update:modelValue', false)
      },

      handleBtnConfirmChangeSubjectives() {
        let noScoreSubjectives = this.modalSubjectives.filter(
          q => !(q.fullScore > 0) || q.blankScores.some(blank => !(blank > 0))
        )
        if (noScoreSubjectives.length > 0) {
          let questionNames = noScoreSubjectives.map(q => q.fullCode).join('、')
          this.$Message.info({
            content: `请输入以下题目的分数：${questionNames}`,
          })
          return
        }
        this.$emit('change-subjectives', this.modalSubjectives)
        this.handleCloseModal()
      },

      handleSpan({ row, rowIndex, columnIndex }) {
        let index = this.modalSubjectives.findIndex(x => x.topicCode === row.topicCode)
        let count = this.modalSubjectives.filter(x => x.topicCode === row.topicCode).length
        if (index === -1) {
          return [1, 1]
        } else {
          if (!columnIndex && rowIndex === index) {
            return [count, 1]
          } else if (!columnIndex && rowIndex > index) {
            return [0, 0]
          }
        }
      },
    },
  }
</script>
