import ajax from '@/api/ajax'
import { getScanUnitStatusText, resetScanUnitEditData } from '@/helpers/scan/wrong_question_sheet'

export function transformScanUnit(scanUnit) {
  if (!scanUnit) {
    return scanUnit
  }
  // 批次信息
  scanUnit.batchName = `${scanUnit.scanClient}-${scanUnit.batchNo}`
  // 图片url
  scanUnit.imgUrls = splitByComma(scanUnit.imgUrls)
  scanUnit.adjustImgUrls = splitByComma(scanUnit.adjustImgUrls)
  // 页面顺序
  scanUnit.pageNos = splitByComma(scanUnit.pageNos).map(Number)
  // 客观题
  scanUnit.objectives = scanUnit.objectives ? JSON.parse(scanUnit.objectives) : []
  // 状态
  scanUnit.statusText = getScanUnitStatusText(scanUnit)
  // 编辑数据
  resetScanUnitEditData(scanUnit)
  // 勾选状态
  scanUnit._checked = false
  return scanUnit
}

function splitByComma(str) {
  if (!str) {
    return []
  }
  return str.split(',').filter(Boolean)
}

export function apiGetScanUnitData(scanUnitId) {
  return ajax
    .get({
      url: 'fbscan/scanUnit/data',
      params: {
        scanUnitId,
      },
      requestName: '获取扫描单元数据',
    })
    .then(transformScanUnit)
}

export function apiGetStudentScanUnits(params) {
  return ajax
    .get({
      url: 'fbscan/scanUnit/student',
      params: {
        examSubjectId: params.examSubjectId,
        studentId: params.studentId,
      },
      requestName: '获取学生扫描单元数据',
    })
    .then(data => data.map(transformScanUnit))
}

export function apiSetScanUnitPaperId(params) {
  return ajax.put({
    url: 'fbscan/scanUnit/paperId',
    params: {
      scanUnitId: params.scanUnitId,
      paperId: params.paperId,
    },
    requestName: '设置章节',
  })
}

export function apiRetryInitExam(scanUnitId) {
  return ajax.put({
    url: 'fbscan/scanUnit/reInitExam',
    params: {
      scanUnitId,
    },
    requestName: '重试创建项目',
  })
}

export function apiSetLocateNormal(data) {
  return ajax.put({
    url: 'fbscan/scanUnit/toAnchorNormal',
    params: {
      scanUnitId: data.scanUnitId,
    },
    data: data.pages,
    requestName: '转为定位正常卷',
  })
}

export function apiRedoRecognize(data) {
  return ajax.put({
    url: 'fbscan/scanUnit/redoRecognize',
    params: {
      scanUnitId: data.scanUnitId,
    },
    data: data.pages.map(page => ({
      rotate: page.rotate,
      anchors: page.anchors.map(anchor => ({
        x: Math.round(anchor.x),
        y: Math.round(anchor.y),
        width: Math.round(anchor.width),
        height: Math.round(anchor.height),
      })),
    })),
    requestName: '重新定位识别',
  })
}

export function apiChangeAdmissionNum(params) {
  return ajax.put({
    url: 'fbscan/scanUnit/admissionNum',
    params: {
      scanUnitId: params.scanUnitId,
      admissionNum: params.admissionNum,
    },
    requestName: '修改准考号',
  })
}

export function apiChangeObjectives(data) {
  return ajax.put({
    url: 'fbscan/scanUnit/objective',
    params: {
      scanUnitId: data.scanUnitId,
    },
    data: data.objectives,
    requestName: '修改题目填涂',
  })
}

export function apiVerifyObjectiveExBatch(scanUnitIds) {
  return ajax.put({
    url: 'fbscan/scanUnit/objectiveEx/verify',
    data: scanUnitIds,
    requestName: '忽略填涂异常',
  })
}

export function apiDeleteScanUnit(scanUnitId) {
  return ajax.delete({
    url: 'fbscan/scanUnit/delete',
    params: {
      scanUnitId,
    },
    requestName: '删除卷',
  })
}

export function apiDeleteScanUnitBatch(scanUnitIds) {
  return ajax.delete({
    url: 'fbscan/scanUnit/deleteBatch',
    data: scanUnitIds,
    requestName: '删除卷',
  })
}

export function apiRecoverScanUnit(scanUnitId) {
  return ajax.put({
    url: 'fbscan/scanUnit/recover',
    params: {
      scanUnitId,
    },
    requestName: '恢复卷',
  })
}
