import SchoolTypeEnum from '@/enum/school/school_type'

export default {
  name: 'supplementaryExercise',
  path: 'supplementaryexercise',
  meta: {
    title: '数智教辅',
    menu: '数智教辅',
    name: '数智教辅',
    roles: user => {
      return user.schoolType == SchoolTypeEnum.School.id
    },
  },
  component: () => import('@/views/supplementary_exercise/index.vue'),
  redirect: {
    name: 'supplementaryExerciseHome',
  },
  children: [
    {
      name: 'supplementaryExerciseHome',
      path: 'home',
      component: () => import('@/views/supplementary_exercise/list/index.vue'),
    },
    {
      name: 'supplementaryExerciseSetting',
      path: 'setting/:examId/:examSubjectId',
      component: () => import('@/views/supplementary_exercise/setting/index.vue'),
    },
    {
      name: 'supplementaryExerciseReport',
      path: 'report/:examId',
      meta: {
        showHeader: false,
      },
      component: () => import('@/views/supplementary_exercise/report/index.vue'),
    },
  ],
}
