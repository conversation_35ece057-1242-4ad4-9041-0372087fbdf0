import ajax from '@/api/ajax'

export function apiGetAiBlockPrompt({ examSubjectId, blockId }) {
  return ajax.get({
    url: 'mark/mark/ai/blockPrompt',
    params: { examSubjectId, subjectiveId: blockId },
    requestName: '获取AI阅卷题块提示词',
  })
}

export function apiGetAllAiBlockPrompt(examSubjectId) {
  return ajax.get({
    url: 'mark/mark/ai/allBlockPrompts',
    params: { examSubjectId },
    requestName: '获取AI阅卷题块提示词',
  })
}

export function apiModifyAiBlockPrompt({ examSubjectId, prompt }) {
  return ajax.put({
    url: 'mark/mark/ai/modifyPrompt',
    params: {
      examSubjectId,
    },
    data: prompt,
    requestName: '修改AI阅卷题块提示词',
  })
}

export function apiGenerateAiBlockPrompt({ examSubjectId, blockId }) {
  return ajax.put({
    url: 'mark/mark/ai/generatePrompt',
    params: {
      examSubjectId,
      subjectiveId: blockId,
    },
    requestName: '生成AI阅卷题块提示词',
  })
}

export function apiGenerateAllAiBlockPrompt(examSubjectId) {
  return ajax.put({
    url: 'mark/mark/ai/generateAllBlockPrompts',
    params: {
      examSubjectId,
    },
    requestName: '生成AI阅卷题块提示词',
  })
}

export function apiChangeToAiScore({ examSubjectId, blockIds }) {
  return ajax.put({
    url: 'mark/mark/ai/changeToAiScore',
    params: {
      examSubjectId,
    },
    data: blockIds,
    requestName: '转为AI阅卷',
  })
}

export function apiChangeToManualScore({ examSubjectId, blockIds }) {
  return ajax.put({
    url: 'mark/mark/ai/changeToManualScore',
    params: {
      examSubjectId,
    },
    data: blockIds,
    requestName: '转为人工阅卷',
  })
}

export function apiChangeAiScoreStatus({ examSubjectId, blockIds, aiScoreStatus }) {
  return ajax.put({
    url: 'mark/mark/ai/changeAiScoreStatus',
    params: {
      examSubjectId,
      aiScoreStatus,
    },
    data: blockIds,
    requestName: '更改AI阅卷状态',
  })
}
