import ajax from '@/api/ajax'

export function apiDownloadAreaPDF(params) {
  return ajax.get({
    url: 'report/export/areaReport',
    params: {
      examId: params.examId,
      templateId: params.templateId,
    },
    requestName: '获取联考PDF报告',
  })
}

export function apiDownloadSchoolPDF(requestParams) {
  return ajax.get({
    url: 'report/export/schoolReport',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      examSubjectId: requestParams.examSubjectId || undefined,
      schoolId: requestParams.schoolId,
    },
    requestName: '获取校级PDF报告',
  })
}

export function apiDownloadTeacherAnalyseExcel(params) {
  return ajax.download({
    url: 'report/export/teacherAnalyse',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      excelShowRank: 'excelShowRank' in params ? params.excelShowRank : true,
      orgSchId: params.orgSchId || undefined,
      isMerged: 'isMerged' in params ? params.isMerged : true,
    },
    requestName: '下载教师教学质量分析报表',
  })
}

export function apiDownloadUnionClassCompareExcel(params) {
  return ajax.download({
    url: 'report/export/unionClsCompare',
    params: {
      examId: params.examId,
      // examSubjectId: params.examSubjectId,   放在考情概览，不提供 examSubjectId
      templateId: params.templateId,
      orgSchId: params.orgSchId || undefined,
      excelShowRank: 'excelShowRank' in params ? params.excelShowRank : true,
    },
    requestName: '下载联考班级对比分析报表',
  })
}

// 下载学生成绩Excel
export function apiGetStudentScoreExcel(params) {
  const ExcelShowRank = 'excelShowRank' in params ? params.excelShowRank : true
  return ajax.download({
    url: 'report/export/students',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId || null,
      templateId: params.templateId,
      schoolId: params.schoolId || null,
      classId: params.classId || null,
      reportName: params.reportName,
      orgSchId: params.organizationId,
      includeTeachingPoint: params.includeTeachingPoint,
      excelShowRank: ExcelShowRank,
      showSubOrgRank: (ExcelShowRank && params.showSubOrgRank) || false,
    },
    requestName: '下载学生成绩',
  })
}
export function apiGetStudentScoreExcelOfSeniorHigh(requestParams) {
  const ExcelShowRank = 'excelShowRank' in requestParams ? requestParams.excelShowRank : true
  return ajax.download({
    url: 'report/export/students/v2',
    params: {
      categoryId: requestParams.categoryId,
      classId: requestParams.classId,
      examId: requestParams.examId,
      foreignSubjectId: requestParams.foreignSubjectId,
      orgSchId: requestParams.organizationId,
      reportName: requestParams.reportName,
      schoolId: requestParams.schoolId,
      subjectCode: requestParams.subjectCode,
      templateId: requestParams.templateId,
      excelShowRank: ExcelShowRank,
      showSubOrgRank: (ExcelShowRank && requestParams.showSubOrgRank) || false,
    },
    requestName: '下载高中学生成绩',
  })
}

// 下载学生成绩明细报表Excel
export function apiDownloadScoreBreakdown(params) {
  return ajax.download({
    url: 'report/export/scoreDetail',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId || null,
      templateId: params.templateId,
      schoolId: params.schoolId || null,
      classId: params.classId || null,
      reportName: params.reportName,
      orgSchId: params.organizationId,
      includeTeachingPoint: params.includeTeachingPoint,
      excelShowRank: 'excelShowRank' in params ? params.excelShowRank : true,
    },
    requestName: '下载学生成绩明细报表',
  })
}

// 下载作答统计Excel表
export function apiDownloadAnswerCal(params) {
  return ajax.download({
    url: 'report/export/answerCal',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      templateId: params.templateId,
      schoolId: params.schoolId || null,
      classId: params.classId || null,
      orgSchId: params.organizationId,
    },
    data: params.customQuestionIntervals || undefined,
    requestName: '导出作答统计表',
  })
}

// 导出校级学生大题得分明细表
export function apiDownloadStudentTopicAnalysis(params) {
  return ajax.download({
    url: 'report/export/stuTopicScore',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      examSubjectId: params.examSubjectId,
      schoolId: params.schoolId,
      classId: params.classId,
      orgSchId: params.organizationId,
      includeTeachingPoint: params.includeTeachingPoint,
      excelShowRank: 'excelShowRank' in params ? params.excelShowRank : true,
    },
    requestName: '导出校级学生大题得分明细表',
  })
}

// 下载大题分析
export function apiDownloadTopicAnalysis(params) {
  return ajax.download({
    url: 'report/export/topicAnalyse',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId || null,
      templateId: params.templateId,
      schoolId: params.schoolId || null,
      classId: params.classId || null,
    },
  })
}

// 导出班级大题得分明细表
export function apiDownloadClassTopicAnalysis(params) {
  return ajax.download({
    url: 'report/export/clsTopicAnalyse',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      examSubjectId: params.examSubjectId,
      schoolId: params.schoolId,
      classId: params.classId,
      orgSchId: params.organizationId,
    },
    requestName: '导出班级大题得分明细表',
  })
}

// 导出考生题块图
export function apiGetBlockFiltedStudentList(requestParams) {
  return ajax.get({
    url: 'report/export/listBlockStudent',
    params: {
      blockId: requestParams.blockId, // String
      maxScore: requestParams.maxScore, // String
      minScore: requestParams.minScore, // String
      page: requestParams.currentPage, // String
      size: requestParams.pageSize, // String
    },
    requestName: '查询题块对应分数区间学生列表',
  })
}
export function apiDownloadStudentBlockImages(requestParams) {
  return ajax.download({
    url: 'report/export/exportBlockZip',
    params: {
      blockId: requestParams.blockId, // String*
      isDisplayScore: requestParams.isDisplayScore, // Boolean
      isFilenameIncludeStuInfo: requestParams.isFilenameIncludeStuInfo, // Boolean
      maxScore: requestParams.maxScore, // String*
      minScore: requestParams.minScore, // String*
    },
    data: requestParams.studentIds || [], // Array[String]：空值则为查出来的所有学生
    requestName: '导出符合条件考生题块图',
  })
}

export function apiDownloadExamSubjectStudentScorePaperExcel(requestParams) {
  return ajax.download({
    url: 'report/export/studentScore',
    params: {
      categoryId: requestParams.categoryId,
      entityId: requestParams.entityId,
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      includeTeachingPoint: requestParams.includeTeachingPoint,
      reportName: requestParams.reportName,
    },
    requestName: '导出学生成绩单',
  })
}

export function apiDownloadMultiTemplateSchoolCompareExcel(templateIds) {
  return ajax.download({
    url: 'report/export/multiReportSummaries',
    data: templateIds,
    requestName: '导出多模板【学校对比】合并表',
  })
}

export function apiDownloadObjectiveStatistics(requestParams) {
  return ajax.download({
    url: 'report/export/exportObjectiveScore',
    data: {
      examSubjectId: requestParams.examSubjectId,
      classIds: requestParams.classIds,
    },
    requestName: '导出客观题统计',
  })
}
