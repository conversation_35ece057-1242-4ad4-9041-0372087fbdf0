<template>
  <Modal
    :model-value="modelValue"
    class="modal-import-student"
    title="按年级批量删除学生"
    width="730"
    @on-visible-change="handleVisibilityChange"
  >
    <div class="modal-body">
      <div class="tips-text">请选择要批量删除学生的年级（可多选）</div>
      <div>
        <!-- <Select v-model="currentGradeIds" multiple style="width: 240px" :max-tag-count="3">
          <Option v-for="item in gradeList" :key="item.gradeId" :value="item.gradeId">{{ item.gradeName }}</Option>
        </Select> -->
        <CheckboxGroup v-model="currentGradeIds">
          <Checkbox v-for="item in gradeList" :key="item.gradeId" :label="item.gradeId" border>{{
            item.gradeName
          }}</Checkbox>
        </CheckboxGroup>
      </div>
    </div>
    <template #footer>
      <div>
        <div class="modal-footer">
          <Button @click="handleCancel">取消</Button>
          <Button type="primary" @click="handleConfirm">确定删除</Button>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { apiDeleteStudentsByGrade } from '@/api/user/school'
  export default {
    props: {
      modelValue: Boolean,
      schoolId: String,
      gradeList: Array,
    },
    emits: ['update:modelValue', 'on-refresh'],
    data() {
      return {
        currentGradeIds: [],
      }
    },
    created() {},
    methods: {
      handleCancel() {
        this.currentGradeIds = []
        this.$emit('update:modelValue', false)
      },
      handleVisibilityChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
      handleConfirm() {
        const { currentGradeIds, schoolId } = this

        apiDeleteStudentsByGrade({
          gradeIds: currentGradeIds,
          schoolId,
        })
          .then(() => {
            this.$Message.success('删除成功')
            this.$emit('on-refresh')
            this.handleCancel()
          })
          .catch(err => {
            this.$Modal.error({
              title: '删除失败',
              content: err.msg || '',
            })
          })
      },
    },
  }
</script>

<style scoped lang="scss">
  .modal-body {
    margin-bottom: 20px;

    .tips-text {
      margin-bottom: 15px;
      font-size: 16px;
    }

    .ivu-checkbox-wrapper {
      margin-bottom: 10px;
    }
  }

  .modal-footer {
    @include flex(row, flex-end, center);
  }
</style>
