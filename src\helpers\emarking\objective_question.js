import Question from './question'
import { splitQuestionScore } from './miscellaneous'
import { isUniq, isUniqBy } from '@/utils/array'
import { default as BranchTypeEnum, ObjectiveBranchTypeEnum } from '@/enum/qlib/branch_type'
import AutoScoreTypeEnum from '@/enum/emarking/auto_score_type'
import { Max_Option_Count, Min_Option_Count } from '@/const/emarking'

export default class ObjectiveQuestion extends Question {
  constructor() {
    super()
    // 小题题型
    this.branchTypeId = 0
    // 选项个数
    this.optionCount = 0
    // 参考答案
    this.standardAnswer = ''
    // 其他答案分数
    this.answerScoreList = ''
    // 系统给分类型
    this.autoScoreTypeId = 0
  }

  static import(q) {
    let obj = new ObjectiveQuestion()
    obj.import(q)
    obj.branchName = obj.questionName
    obj.branchTypeId = Number(q.questionType)
    obj.optionCount = Number(q.optionCount)
    obj.standardAnswer = q.answer || ''
    obj.answerScoreList = q.answerScoreList || ''
    obj.autoScoreTypeId = Number(q.isGiven) || 0
    return obj
  }

  export(exportId = false) {
    return {
      id: exportId ? this.questionId : undefined,
      questionCode: this.questionCode,
      questionName: this.questionName,
      topicCode: this.topicCode,
      topicName: this.topicName,
      fullScore: this.fullScore,
      questionId: this.originalQuestionId || 0,
      branchId: this.originalBranchId || 0,
      isAdditional: this.isAdditional,
      questionType: this.branchTypeId,
      // 判断题选项个数始终为2
      optionCount: this.branchTypeId === BranchTypeEnum.TrueOrFalse.id ? 2 : this.optionCount,
      answer: this.standardAnswer,
      answerScoreList: this.answerScoreList,
      isGiven: this.autoScoreTypeId,
    }
  }

  check() {
    let message = super.check()
    if (message) {
      return message
    }

    // 题型
    this.branchTypeId = Number(this.branchTypeId)
    if (!this.branchTypeId) {
      return '无题型'
    } else if (!ObjectiveBranchTypeEnum.getIds().includes(this.branchTypeId)) {
      return '题型错误'
    }

    // 选项个数
    this.optionCount = Number(this.optionCount)
    if (
      !(
        Number.isInteger(this.optionCount) &&
        this.optionCount >= Min_Option_Count &&
        this.optionCount <= Max_Option_Count
      )
    ) {
      return '选项个数错误'
    }

    // 答案
    this.standardAnswer = String(this.standardAnswer || '')
      .replace(' ', '')
      .toUpperCase()
    if (this.standardAnswer) {
      if (this.branchTypeId === BranchTypeEnum.SingleChoice.id) {
        if (!(this.standardAnswer.length === 1 && this.isChoiceAnswerValid(this.standardAnswer))) {
          return '答案格式错误'
        }
      } else if (this.branchTypeId === BranchTypeEnum.MultipleChoice.id) {
        if (!this.isChoiceAnswerValid(this.standardAnswer)) {
          return '答案格式错误'
        }
        this.standardAnswer = this.getNormalizedChoiceAnswer(this.standardAnswer)
      } else if (this.branchTypeId === BranchTypeEnum.TrueOrFalse.id) {
        if (this.standardAnswer !== 'T' && this.standardAnswer !== 'F') {
          return '答案格式错误'
        }
      }
    }

    // 其他答案分数
    if (!(this.branchTypeId === BranchTypeEnum.MultipleChoice.id) || this.answerScoreList === '-') {
      this.answerScoreList = ''
    } else {
      this.answerScoreList = String(this.answerScoreList || '')
        .replace(' ', '')
        .replace('；', ';')
        .toUpperCase()

      let answerScores = []
      try {
        answerScores = splitQuestionScore(this.answerScoreList)
      } catch {
        return '其他答案分数格式错误'
      }

      let resultAnswerScores = []
      for (let pair of answerScores) {
        let answer = pair.name
        let score = pair.score
        if (!this.isChoiceAnswerValid(answer)) {
          return '其他答案分数格式错误'
        }
        if (!(score > 0 && score <= this.fullScore)) {
          return '其他答案分数格式错误'
        }
        resultAnswerScores.push({
          answer: this.getNormalizedChoiceAnswer(answer),
          score,
        })
      }
      if (!isUniqBy(resultAnswerScores, x => x.answer)) {
        return '其他答案分数格式错误'
      } else if (resultAnswerScores.some(x => x.answer === this.standardAnswer && x.score !== this.fullScore)) {
        return '其他答案分数与标准答案分数不一致'
      } else {
        this.answerScoreList = resultAnswerScores.map(x => `${x.answer}=${x.score}`).join(';')
      }
    }

    // 系统给分
    this.autoScoreTypeId = Number(this.autoScoreTypeId)
    if (!AutoScoreTypeEnum.getIds().includes(this.autoScoreTypeId)) {
      this.autoScoreTypeId = AutoScoreTypeEnum.No.id
    }
  }

  isChoiceAnswerValid(answer) {
    let maxAnswerChar = String.fromCharCode(65 + this.optionCount)
    let chars = Array.from(answer)
    return isUniq(chars) && chars.every(char => char >= 'A' && char < maxAnswerChar)
  }

  getNormalizedChoiceAnswer(answer) {
    return Array.from(answer).sort().join('')
  }

  clone() {
    let obj = new ObjectiveQuestion()
    Object.assign(obj, this)
    return obj
  }
}

export function getObjectiveQuestionAnswerChars(q) {
  if (!q) {
    return []
  }
  if (q.branchTypeId === ObjectiveBranchTypeEnum.TrueOrFalse.id) {
    return ['T', 'F']
  } else {
    let list = []
    for (let i = 65; i < 65 + q.optionCount; i++) {
      list.push(String.fromCharCode(i))
    }
    return list
  }
}
