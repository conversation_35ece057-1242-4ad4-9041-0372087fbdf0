/**
 * 本store主要功能是根据权限控制报表查看。
 * 权限控制的目的是一定不能看到不应该看到的学校、年级、班级、学科，一些空报表（有教学班时，可能存在进入该报表菜单后无数据的情形）并未被剔除。
 *
 * 权限控制流程：
 * 1. 找出登录用户与本次考试及模板相关的角色权限
 * 2. 根据报表权限配置，对每一个报表判定登录用户是否可见，再将所有可见报表按层级分组，生成报表菜单
 * 3. 进入任意一个报表后，先筛选出可见的学校、班级，再根据选定的学校班级，筛选出可见的学科
 */

import {
  apiGetReportTemplate,
  apiGetExamSchoolClasses,
  apiGetExamAttributes,
  apiGetNewEntranceGroupList,
  apiGetExamInstitutionTree,
  apiGetExamCenterSpotSchoolList,
  apiGetReportExamCombinationsOfInstitutions,
  apiGetHighSchoolGradeSubjectList,
  apiGetIntervalReportExamCombinationsOfInstitutions,
  apiGetCriticalExamCombinations,
} from '@/api/report'

import Router from '@/router'

import { groupArray } from '@/utils/array'
import { findRouteByName } from '@/utils/router'
import { deepCopy } from '@/utils/object'

import Role from '@/enum/user/role'
import UploadMode from '@/enum/emarking/upload_mode'
import SubjectCombinations from '@/enum/report/subject_combinations.js'

const Reports = {
  multipleSchool: [
    {
      key: 'union_overview',
      name: 'report-multipleSchool-general',
      text: '考情概览',
    },
    {
      key: 'union_stuScore',
      name: 'report-multipleSchool-score',
      text: '学生成绩',
    },
    // {
    //   key: 'union_newScore',
    //   name: 'report-multipleSchool-newScore',
    //   text: '新高考报告',
    // },
    {
      key: 'union_schCompare',
      name: 'report-multipleSchool-school',
      text: '学校对比',
    },
    {
      key: 'union_scoreInterval',
      name: 'report-multipleSchool-scoreInterval',
      text: '分数段统计',
    },
    {
      key: 'union_rankInterval',
      name: 'report-multipleSchool-rankInterval',
      text: '名次段统计',
    },
    {
      key: 'union_scoreLine',
      name: 'report-multipleSchool-passingAnalysis',
      text: '分档上线',
    },
    {
      key: 'union_scoreLevel',
      name: 'report-multipleSchool-scoreGrades',
      text: '成绩等级',
    },
    {
      key: 'union_answer',
      name: 'report-multipleSchool-answer',
      text: '作答统计',
    },
    {
      key: 'union_quesAnalyse',
      name: 'report-multipleSchool-question',
      text: '小题分析',
    },
    {
      key: 'union_topicAnalyse',
      name: 'report-multipleSchool-topic',
      text: '大题分析',
    },
    {
      key: 'union_knowAnalyse',
      name: 'report-multipleSchool-knowledge',
      text: '知识点分析',
    },
    {
      key: 'union_paper',
      name: 'report-multipleSchool-paper',
      text: '试卷分析',
    },
  ],
  school: [
    {
      key: 'sch_overview',
      name: 'report-school-general',
      text: '考情概览',
    },
    {
      key: 'sch_stuScore',
      name: 'report-school-score',
      text: '学生成绩',
    },
    // {
    //   key: 'sch_newScore',
    //   name: 'report-school-newScore',
    //   text: '新高考报告',
    // },
    {
      key: 'sch_clsCompare',
      name: 'report-school-class',
      text: '班级对比',
    },
    {
      key: 'sch_scoreInterval',
      name: 'report-school-scoreInterval',
      text: '分数段统计',
    },
    {
      key: 'sch_rankInterval',
      name: 'report-school-rankInterval',
      text: '名次段统计',
    },
    // {
    //   name: 'report-school-score-level-distribution',
    //   text: '学业等级分布'
    // },
    {
      key: 'sch_scoreLine',
      name: 'report-school-passingAnalysis',
      text: '分档上线', // 曾用名： 进线分析
    },
    {
      key: 'sch_critical',
      name: 'report-school-criticalStudent',
      text: '临界生统计',
    },
    {
      key: 'sch_scoreLevel',
      name: 'report-school-scoreGrades',
      text: '成绩等级',
    },
    // {
    //   name: 'report-school-subject-compare',
    //   text: '优劣势学科对比'
    // },
    // {
    //   name: 'report-school-paper',
    //   text: '试卷分析'
    // },
    {
      key: 'sch_answer',
      name: 'report-school-answer',
      text: '作答统计',
    },
    {
      key: 'sch_quesAnalyse',
      name: 'report-school-question',
      text: '小题分析',
    },
    {
      key: 'sch_topicAnalyse',
      name: 'report-school-topic',
      text: '大题分析',
    },
    {
      key: 'sch_knowAnalyse',
      name: 'report-school-knowledge',
      text: '知识点分析',
    },
    {
      key: 'sch_paper',
      name: 'report-school-paper',
      text: '试卷分析',
    },
  ],
  class: [
    {
      key: 'cls_overview',
      name: 'report-class-general',
      text: '考情概览',
    },
    {
      key: 'cls_stuScore',
      name: 'report-class-score',
      text: '学生成绩',
    },
    // {
    //   key: 'cls_newScore',
    //   name: 'report-class-newScore',
    //   text: '新高考报告',
    // },
    {
      key: 'cls_scoreInterval',
      name: 'report-class-scoreInterval',
      text: '分数段统计',
    },
    {
      key: 'cls_rankInterval',
      name: 'report-class-rankInterval',
      text: '名次段统计',
    },
    // {
    //   key: 'cls_scoreLine',
    //   name: 'report-class-passing-analysis',
    //   text: '分档上线',
    // },
    {
      key: 'cls_critical',
      name: 'report-class-criticalStudent',
      text: '临界生统计',
    },
    {
      key: 'cls_scoreLevel',
      name: 'report-class-scoreGrades',
      text: '成绩等级',
    },
    {
      key: 'cls_answer',
      name: 'report-class-answer',
      text: '作答统计',
    },
    {
      key: 'cls_quesAnalyse',
      name: 'report-class-question',
      text: '小题分析',
    },
    {
      key: 'cls_topicAnalyse',
      name: 'report-class-topic',
      text: '大题分析',
    },
    {
      key: 'cls_knowAnalyse',
      name: 'report-class-knowledge',
      text: '知识点分析',
    },
    {
      key: 'cls_paperComment',
      name: 'report-class-lecture',
      text: '试卷讲评',
    },
  ],
}

const ReportLevels = [
  {
    name: 'multipleSchool',
    text: '联考报告',
  },
  {
    name: 'school',
    text: '校级报告',
  },
  {
    name: 'class',
    text: '班级报告',
  },
]

export default {
  namespaced: true,

  state: {
    exam: null,
    template: null,
    examAttributes: null,
    currentReportName: '', // 当前报表名，进入报表页面后始终存在
    currentSchoolId: '', // 当前学校Id，进入报表页面后始终存在
    currentClassId: '', // 当前班级Id，进入报表页面后始终存在
    currentInstitutionId: '', // 当前机构Id, 可能不存在
    currentExamSubjectId: '', // 当前科目Id，可能不存在

    currentInstitutionSchools: [],

    // examSubjCombinations: [], // 高中科目组合

    subjectCombinationCode: 0, // 当前高中大类（总体 - 0 | 物理类 - 2 | 历史类 - 1）
    reportSeniorHighSchoolExamCategories: [], // 高中大类下属科目
    currentSeniorHighSchoolExamCategorySubjectId: '', // 高中查看科目Id

    newEntranceAttributes: {}, // 高中 新高考报表

    examInstitutionTree: {}, // 与考试项目相关的机构树

    centerSpotSchools: [], // 当前考试项目所有中心校教学点列表

    // 当前学校班级科目
    // 切换报告或参数时可能导致学校、年级、科目同时发生变化，组件内分别watch会调用多次
    // 故设此变量，每次操作更新一次
    currentParams: '',

    // 报表配置路由传参
    reportConfigRouteParams: {
      isNewTemplate: false,
      exam: null,
      template: null,
    },

    childOrgAndSchools: [],
  },

  getters: {
    exam(state) {
      return state.exam
    },

    template(state) {
      return state.template
    },

    reportConfigRouteParams(state) {
      return state.reportConfigRouteParams
    },

    examId(state) {
      return (state.exam && state.exam.examId) || ''
    },

    examName(state) {
      return (state.exam && state.exam.examName) || ''
    },

    examCreatedInstitutionId(state) {
      return (state.exam && state.exam.creator && state.exam.creator.schoolId) || ''
    },

    // 考试所有学校班级
    examSchools(state) {
      return (state.exam && state.exam.schools) || []
    },

    templateId(state) {
      return (state.template && state.template.templateId) || ''
    },

    templateName(state) {
      return (state.template && state.template.templateName) || ''
    },

    templateSettingScoreIntervals(state) {
      return (state.template && state.template.parameters && state.template.parameters.scoreIntervals) || null
    },

    templateSettingRankIntervals(state) {
      return (state.template && state.template.parameters && state.template.parameters.rankIntervals) || null
    },

    examAttributes(state) {
      return state.examAttributes
    },

    // 是否联考
    isMultipleSchool(state) {
      return state.exam && state.exam.examScope.id > 1
    },

    // 是否高中分科项目
    isSeniorHighSchoolSubjectCombinationSelectableProject(state, getters) {
      const IsSeniorHighSchoolProject = getters['isSeniorHighSchoolProject']
      if (IsSeniorHighSchoolProject) {
        const SelectedSubjects = ((state.exam && state.exam.subjects) || []).filter(s => s.subjectType === 1)
        const IsSubjectCombinationSelectableProject = [5, 7].every(item =>
          SelectedSubjects.some(s => s.subjectId === item)
        )

        return IsSubjectCombinationSelectableProject
      }
      return false
    },

    subjectCombinationCode(state) {
      return state.subjectCombinationCode
    },

    seniorHighSchoolSubjectCombinations(state, getters) {
      return (state.reportSeniorHighSchoolExamCategories || [])
        .filter(
          item =>
            item.show &&
            (getters['isSeniorHighSchoolSubjectCombinationSelectableProject'] &&
            (state.currentReportName || '').includes('passingAnalysis')
              ? item.id
              : true)
        )
        .map(item => ({
          id: item.id,
          name: item.name,
        }))
    },

    subjectCombinationName(state, getters) {
      return (
        getters['seniorHighSchoolSubjectCombinations'].find(item => item.id === state.subjectCombinationCode) || {
          name: '',
        }
      ).name
    },

    // 是否处于联考报告层级
    isMultipleSchoolLevel(state, getters) {
      return getters['currentLevelName'] === 'multipleSchool'
    },

    // 考试相关的机构树
    examInstitutionTree(state) {
      return state.examInstitutionTree
    },

    tiledInstitutions(state) {
      let result = []
      let fillResult = (node, level) => {
        result.push({
          institutionId: node.id,
          institutionName: node.name,
          isOrg: node.isOrg,
          level: level,
        })

        if (node.nodes && node.nodes.length) {
          node.nodes.forEach(x => fillResult(x, level + 1))
        }
      }

      fillResult(state.examInstitutionTree, 0)

      return result
    },

    underLevelInstitutions(state, getters, rootState, rootGetters) {
      if (getters['isOrgInstitution']) {
        let institutions = []

        let targetNodeTree = null
        let findTargetNodeTree = node => {
          if (!node.isOrg) {
            return
          }

          if (node.id === rootGetters['user/info'].schoolId) {
            targetNodeTree = node
          } else {
            node.nodes.forEach(n => findTargetNodeTree(n))
          }
        }
        findTargetNodeTree(state.examInstitutionTree)
        if (targetNodeTree) {
          let filtedInstitutions = []
          let filInstitution = (node, level) => {
            if (
              (node.nodes || []).some(
                node =>
                  state.exam.schools.some(school => school.schoolId === node.id) || filInstitution(node, level + 1)
              )
            ) {
              if (node.isOrg) {
                filtedInstitutions.push({
                  node: node,
                  level: level,
                })
              }
              return true
            } else {
              return false
            }
          }

          ;(targetNodeTree.nodes || []).forEach(node => filInstitution(node, 1))
          if (filtedInstitutions.length) {
            institutions = institutions.concat(
              filtedInstitutions
                .sort((a, b) => a.level - b.level)
                .map(x => ({
                  id: x.node.id,
                  name: x.node.name,
                }))
            )
          }
        }

        if (institutions.length && targetNodeTree) {
          institutions.unshift({
            id: targetNodeTree.id,
            name: targetNodeTree.name,
          })
        }

        return institutions
      } else {
        return []
      }
    },

    underLevelInstitutionsSchools(state, getters) {
      return getters['underLevelInstitutions']
        .filter(institution => institution.id !== getters['currentInstitutionId'])
        .map(institution => {
          const SchoolIds = []
          let institutionNode = null
          const FindInstitutionNode = node => {
            if (node) {
              if (node.id === institution.id) {
                institutionNode = node
              } else if (node.nodes && node.nodes.length) {
                node.nodes.forEach(cnode => FindInstitutionNode(cnode))
              }
            }
          }
          FindInstitutionNode(state.examInstitutionTree)

          if (institutionNode) {
            const WithdrawInstitutionSchoolIds = node => {
              if (node) {
                if (node.isOrg && node.nodes && node.nodes.length) {
                  node.nodes.forEach(cnode => WithdrawInstitutionSchoolIds(cnode))
                } else {
                  SchoolIds.push(node.id)
                }
              }
            }
            WithdrawInstitutionSchoolIds(institutionNode)
          }

          return {
            ...institution,
            schoolIds: SchoolIds,
          }
        })
    },

    // 高中科目组合
    // examSubjCombinations(state) {
    //   return state.examSubjCombinations
    // },

    // 考试项目年级
    examGradeId(state) {
      return (state.exam && state.exam.grade && state.exam.grade.id) || 0
    },

    isSeniorHighSchoolProject(state, getters) {
      const ExamGradeId = getters['examGradeId']
      return ExamGradeId && ExamGradeId > 9
    },

    // 判断本单位是否机构
    isOrgInstitution(state, getters, rootState, rootGetters) {
      let targetInstitution = getters['tiledInstitutions'].find(
        x => x.institutionId === rootGetters['user/info'].schoolId
      )
      if (targetInstitution) {
        return targetInstitution.isOrg
      }
      return false
    },

    // 判断现单位是否有同级机构和下级机构
    reportModelTypes(state, getters, rootState, rootGetters) {
      /**
       * under_schools_compare 下属学校对比
       * under_institutions_compare 下属机构对比
       * same_level_institutions_compare 同级机构对比
       */
      const Types = []

      const _CheckInstitutionHasUnderSchools = node => {
        if (node.nodes && node.nodes.length) {
          if (node.nodes.some(x => !x.isOrg)) {
            return true
          } else {
            return node.nodes.some(x => _CheckInstitutionHasUnderSchools(x))
          }
        } else {
          return false
        }
      }

      const CurrentReport = getters['currentReport']
      const RolePositions = (CurrentReport && CurrentReport.rolePositions) || []
      const DianBaiForbiddenCharacterSituation =
        ['2bzxr94olaf4', '2dm8lr0713pc', '2dm8a0eqvbwg', '2dm8141l9ips'].includes(getters['examId']) &&
        !RolePositions.some(position => [0, Role.ResearcherLeader].includes(position.roleId))

      const _SearchTargetInstitution = node => {
        if (node.nodes && node.nodes.length && node.nodes.some(x => x.isOrg)) {
          let targetIndex = node.nodes.findIndex(
            x => x.isOrg && x.id === (getters['currentInstitutionId'] || rootGetters['user/info'].schoolId || '')
          )
          if (targetIndex !== -1) {
            if (node.nodes[targetIndex].nodes && node.nodes[targetIndex].nodes.length) {
              if (_CheckInstitutionHasUnderSchools(node.nodes[targetIndex])) {
                Types.push({
                  title: '下属学校',
                  value: 'under_schools_compare',
                })
              }

              if (!DianBaiForbiddenCharacterSituation && node.nodes[targetIndex].nodes.some(x => x.isOrg)) {
                Types.push({
                  title: '下属机构',
                  value: 'under_institutions_compare',
                })
              }
            }

            // 禁用 同级机构 ， 通常子机构无权限查看同级机构的数据
            // if (node.nodes.filter(x => x.isOrg).length > 1) {
            //   types.push({
            //     title: '同级机构',
            //     value: 'same_level_institutions_compare',
            //   })
            // }
          } else {
            node.nodes.forEach(x => _SearchTargetInstitution(x))
          }
        }
      }

      _SearchTargetInstitution({
        id: '',
        isOrg: true,
        name: 'fakeNode',
        nodes: [state.examInstitutionTree],
      })

      return Types
    },

    // 模板中配置的科目
    templateSubjects(state) {
      if (!(state.exam && state.template)) {
        return []
      }

      let subjects = []
      state.template.parameters.totalScores.forEach(s => {
        let examSubject = state.exam.subjects.find(x => x.subjectId === s.subjectId)
        if (examSubject && s.enabled) {
          // 要配置了参与统计的科目才显示
          subjects.push({
            examSubjectId: examSubject.examSubjectId,
            subjectId: examSubject.subjectId,
            subjectName: examSubject.subjectName,
            uploadMode: (examSubject.uploadMode && examSubject.uploadMode.id) || UploadMode.Scan.id,
          })
        }
      })
      return subjects
    },

    isCreator(state, getters, rootState, rootGetters) {
      return state.exam.creator.userId === rootGetters['user/info'].userId
    },

    // 当前用户是否是该考试管理员
    isAdministrator(state, getters, rootState, rootGetters) {
      if (!state.exam) {
        return false
      }

      let userInfo = rootGetters['user/info']

      // 考试创建者或考试管理员
      if (
        state.exam.creator.userId == userInfo.userId ||
        state.exam.administrators.some(x => x.userId === userInfo.userId)
      ) {
        return true
      }

      // 单校考试 - 1. 用户学校为考试学校; 2. 用户是该校的学校管理员
      if (!getters.isMultipleSchool) {
        if (
          userInfo.roles.includes(Role.SchoolAdministrator.id) &&
          state.exam.schools.some(s => s.schoolId == userInfo.schoolId)
        ) {
          return true
        }
      }

      return false
    },

    // 与当前考试及模板相关的角色
    positions(state, getters, rootState, rootGetters) {
      if (!(state.exam && state.template)) {
        return []
      }

      let semesters = rootGetters['emarking/semesters']() || []
      let currentSemester = (semesters.length && semesters[0].semesterId) || 0
      let examSemester = state.exam.semesterId
      let semesterDifference = currentSemester - examSemester

      let userInfo = rootGetters['user/info']
      let userSchoolId = userInfo.schoolId
      let userPositions = deepCopy(userInfo.schoolPositions)
      let userTeachings = userInfo.teachings

      let examGradeId = getters['examGradeId']
      let stage = (rootGetters['emarking/stageGrades']() || []).find(s => s.grades.some(g => g.id === examGradeId))
      let examStageId = (stage && stage.id) || ''
      let examSchools = state.exam.schools
      let templateSubjectIds = state.template.parameters.totalScores.filter(s => s.enabled).map(s => s.subjectId)

      let positionList = []

      // 只在联考时教研领导和教研员才能查看报告
      // TODO: 判断是否是用户教育局内的联考
      if (getters['isMultipleSchool']) {
        // 教研领导
        let positionResearcherLeader = userPositions.find(x => x.roleId === Role.ResearcherLeader.id)
        if (positionResearcherLeader) {
          positionList.push(positionResearcherLeader)
        }

        // 包含模板下科目的教研员
        let positionResearchers = userPositions.filter(
          x =>
            x.roleId === Role.Researcher.id &&
            x.stageId === examStageId &&
            templateSubjectIds.some(id => id === x.subjectId)
        )
        if (positionResearchers.length > 0) {
          positionList.push(...positionResearchers)
        }
      }

      // 考试所包含学校内的角色
      if (examSchools.some(s => s.schoolId === userSchoolId)) {
        // 学校领导(学校管理员)
        let positionSchoolLeader = userPositions.find(x => x.roleId === Role.SchoolLeader.id)
        if (positionSchoolLeader) {
          positionList.push(positionSchoolLeader)
        } else {
          let positionSchoolAdministrator = userPositions.find(x => x.roleId === Role.SchoolAdministrator.id)
          if (positionSchoolAdministrator) {
            positionSchoolAdministrator.roleId = Role.SchoolLeader.id
            positionList.push(positionSchoolAdministrator)
          }
        }

        // 年级主任
        let positionGradeLeader = userPositions.find(x => {
          let transGradeId = x.gradeId - semesterDifference
          return (
            stage.grades.some(g => g.id === transGradeId) &&
            x.roleId === Role.GradeLeader.id &&
            transGradeId === examGradeId
          )
        })
        if (positionGradeLeader) {
          positionList.push(positionGradeLeader)
        }

        // 科组长
        let positionSubjectLeaders = userPositions.filter(
          x =>
            x.roleId === Role.SubjectLeader.id &&
            x.stageId === examStageId &&
            templateSubjectIds.some(id => id === x.subjectId)
        )
        if (positionSubjectLeaders.length > 0) {
          positionList.push(...positionSubjectLeaders)
        }

        // 备课组长
        let positionGradeSubjectLeaders = userPositions.filter(x => {
          let transGradeId = x.gradeId - semesterDifference
          return (
            stage.grades.some(g => g.id === transGradeId) &&
            x.roleId === Role.GradeSubjectLeader.id &&
            transGradeId === examGradeId &&
            templateSubjectIds.some(id => id === x.subjectId)
          )
        })
        if (positionGradeSubjectLeaders.length > 0) {
          positionList.push(...positionGradeSubjectLeaders)
        }

        // 班主任
        let classes = examSchools.find(s => s.schoolId === userSchoolId).classes
        let positionClassLeaders = userPositions.filter(
          x => x.roleId === Role.ClassLeader.id && classes.some(cls => cls.classId === x.classId)
        )
        if (positionClassLeaders.length > 0) {
          positionList.push(...positionClassLeaders)
        }

        // 任课教师
        let positionTeachers = userTeachings
          .filter(
            x => classes.some(cls => cls.classId === x.classId) && templateSubjectIds.some(id => id === x.subjectId)
          )
          .map(x => {
            return {
              roleId: Role.Teacher.id,
              classId: x.classId,
              subjectId: x.subjectId,
            }
          })
        if (positionTeachers.length > 0) {
          positionList.push(...positionTeachers)
        }
      }

      return positionList
    },

    // 当前用户角色能看到的所有报表
    reportsByLevel(state, getters, rootState, rootGetters) {
      if (!(state.exam && state.template)) {
        return []
      }

      // 该考试所有报表列表
      let reportListAll = []
      Object.keys(Reports).forEach(key => {
        if (!getters['isMultipleSchool'] && key === 'multipleSchool') {
          return
        }

        let levelReports = Reports[key]
        let levelText = ReportLevels.find(x => x.name === key).text

        levelReports.forEach(report => {
          if (report.text == '试卷分析' && getters['isMultipleSchool'] && key != 'multipleSchool') {
            return
          }

          if (report.text === '新高考报告') {
            const NewEntranceAttributesKeys = Object.keys(state.newEntranceAttributes)
            if (
              !getters['isSeniorHighSchoolProject'] ||
              !NewEntranceAttributesKeys.length ||
              !NewEntranceAttributesKeys.some(key => state.newEntranceAttributes[key].length)
            ) {
              return
            }
          }

          reportListAll.push({
            key: report.key,
            name: report.name,
            text: report.text,
            levelName: key,
            levelText: levelText,
          })
        })
      })

      let list = []

      // 考试管理员可见所有报表，标记roleId = 0
      if (getters['isAdministrator']) {
        list = reportListAll.map(r => ({
          ...r,
          roleId: 0,
          detail: null,
          positions: [],
        }))
      }
      // 根据用户角色查找可见报表
      else {
        let userRoles = groupArray(getters['positions'], x => x.roleId).map(role => ({
          roleId: role.group[0].roleId,
          positions: role.group,
        }))
        let roleReports = state.template.permissions.roleReports
        if (getters['isOrgInstitution']) {
          let eduSchoolLevel = (
            getters['tiledInstitutions'].find(item => item.institutionId === rootGetters['user/info'].schoolId) || {
              level: 0,
            }
          ).level
          if (eduSchoolLevel) {
            if (!roleReports.some(item => item.eduSchoolLevel === eduSchoolLevel)) {
              eduSchoolLevel--
              if (!roleReports.some(item => item.eduSchoolLevel === eduSchoolLevel) && eduSchoolLevel > 0) {
                eduSchoolLevel--
              }
            }
          }
          roleReports = roleReports.filter(item => item.levelName !== 'union' || item.eduSchoolLevel === eduSchoolLevel)
        }

        userRoles.forEach(item => {
          let roleReport = roleReports.find(r => r.id === item.roleId)
          if (!roleReport) {
            return
          }

          roleReport.reports.forEach(report => {
            if (report.visible) {
              let target = reportListAll.find(r => r.key === report.reportName)
              if (target) {
                list.push({
                  ...target,
                  roleId: item.roleId,
                  detail: report.detail,
                  positions: item.positions,
                })
              }
            }
          })
        })
      }

      let distinctReports = groupArray(list, x => x.name).map(g => {
        return {
          key: g.group[0].key,
          name: g.group[0].name,
          text: g.group[0].text,
          levelName: g.group[0].levelName,
          levelText: g.group[0].levelText,
          rolePositions: g.group.map(x => ({
            roleId: x.roleId,
            detail: x.detail,
            positions: x.positions,
          })),
        }
      })

      // sort distinctReports
      distinctReports = distinctReports
        .map(r => {
          let index = reportListAll.findIndex(x => x.key === r.key)
          if (index < 0) {
            index = Number.MAX_SAFE_INTEGER
          }
          return {
            index,
            item: r,
          }
        })
        .sort((a, b) => a.index - b.index)
        .map(x => x.item)

      let byLevel = groupArray(distinctReports, x => x.levelName).map(g => ({
        levelName: g.group[0].levelName,
        levelText: g.group[0].levelText,
        reports: g.group,
      }))

      return byLevel
    },

    // 当前报告层级
    currentReportLevel(state, getters) {
      return getters['reportsByLevel'].find(x => x.reports.some(report => report.name === state.currentReportName))
    },

    // 当前报告层级名称
    currentLevelName(state, getters) {
      let level = getters['currentReportLevel']
      return level && level.levelName
    },

    // 当前报告名
    currentReportName(state) {
      return state.currentReportName
    },

    // 当前报告
    currentReport(state, getters) {
      if (!getters['currentReportLevel']) {
        return null
      }

      return getters['currentReportLevel'].reports.find(x => x.name === state.currentReportName)
    },

    centerSpotSchools(state) {
      return state.centerSpotSchools || []
    },

    // 可见学校班级
    visibleExamSchools(state, getters, rootState, rootGetters) {
      const CurrentReport = getters['currentReport']
      if (CurrentReport) {
        const ExamCenterSpotSchools = getters['centerSpotSchools']
        const ExamSchoolClasses = deepCopy(getters['examSchools'])
        const RolePositions = CurrentReport.rolePositions
        const IsOrgInstitution = getters['isOrgInstitution']
        const UserSchoolId = rootGetters['user/info'].schoolId
        const TransformVisibleSchools = visibleSchools => {
          // 可见学校班级根据中心校改变结构
          const ChildSchoolIdList = ExamCenterSpotSchools.reduce((acc, cur) => {
            if (cur.nodes && cur.nodes.length) {
              return acc.concat(cur.nodes.map(node => node.id))
            } else {
              return acc
            }
          }, [])
          let visibleSchoolsBySpotTree = []
          let transformedSchools = []

          visibleSchools.forEach(vs => {
            if (!ChildSchoolIdList.includes(vs.schoolId)) {
              const TargetCenterSpotSchools = ExamCenterSpotSchools.find(ecss => ecss.id === vs.schoolId)
              if (TargetCenterSpotSchools && TargetCenterSpotSchools.nodes && TargetCenterSpotSchools.nodes.length) {
                let vsBySpotSchools = deepCopy(vs)
                vsBySpotSchools.childSchools = visibleSchools.filter(vs2 =>
                  TargetCenterSpotSchools.nodes.some(node => node.id === vs2.schoolId)
                )
                visibleSchoolsBySpotTree.push(vsBySpotSchools)
              } else {
                visibleSchoolsBySpotTree.push(vs)
              }
            }
          })

          visibleSchoolsBySpotTree.forEach(vs => {
            transformedSchools.push(vs)
            if (vs.childSchools) {
              transformedSchools.push(
                ...vs.childSchools.map(vsc => {
                  let vscCopy = deepCopy(vsc)
                  vscCopy.schoolName = '—— ' + vscCopy.schoolName
                  return vscCopy
                })
              )
            }
          })

          return transformedSchools
        }

        let schools = []
        if (IsOrgInstitution) {
          if (RolePositions.some(role => role.roleId === 0)) {
            // 考试管理员不过滤机构学校
            schools = deepCopy(ExamSchoolClasses)
          } else {
            // 机构只能看本机构所属的学校
            schools = ExamSchoolClasses.filter(x => state.currentInstitutionSchools.some(y => y === x.schoolId))
          }
        } else {
          // 学校只能查看本校（相应角色可看下属教学点）
          const School = deepCopy(ExamSchoolClasses.find(s => s.schoolId === UserSchoolId))
          if (School) {
            schools = [School]
          } else {
            return []
          }
        }

        // 【考试管理员】可见考试所有学校、【教研领导】 可见本机构所有学校班级
        if (RolePositions.some(role => [0, Role.ResearcherLeader.id].includes(role.roleId))) {
          return TransformVisibleSchools(schools)
        }

        // 【教研员】 可见本机构所有学校行政班及开设了其教研科目的行政班
        const UserResearcherRolePositions = RolePositions.find(role => role.roleId === Role.Researcher.id)
        if (UserResearcherRolePositions) {
          const ResearchSubjectIds = UserResearcherRolePositions.positions.map(x => x.subjectId)
          return TransformVisibleSchools(
            schools.map(school => ({
              schoolId: school.schoolId,
              schoolName: school.schoolName,
              classes: school.classes.filter(
                cls =>
                  cls.classType === 0 ||
                  (cls.classType === 1 &&
                    cls.teachingSubjectIds.some(subjectId => ResearchSubjectIds.includes(subjectId)))
              ),
            }))
          )
        }

        // 中心校角色不仅是【学校教师】和【班主任】的加入中心校的教学点
        const CenterSchoolSpot = ExamCenterSpotSchools.find(ecss => ecss.id === UserSchoolId)
        if (
          CenterSchoolSpot &&
          CenterSchoolSpot.nodes &&
          CenterSchoolSpot.nodes.length &&
          RolePositions.some(role =>
            [Role.SchoolLeader.id, Role.GradeLeader.id, Role.GradeSubjectLeader.id, Role.SubjectLeader.id].includes(
              role.roleId
            )
          )
        ) {
          const SchoolAndChildrenSchoolIds = schools
            .concat(
              CenterSchoolSpot.nodes.map(node => ({
                schoolId: node.id,
                schoolName: node.name,
              }))
            )
            .map(school => school.schoolId)
          schools = ExamSchoolClasses.filter(esc => SchoolAndChildrenSchoolIds.includes(esc.schoolId))
        }

        // 【校领导】、【年级主任】可以查看所有班级
        if (!RolePositions.some(role => [Role.SchoolLeader.id, Role.GradeLeader.id].includes(role.roleId))) {
          // 【学校其它角色】要过滤处理：
          schools.forEach(school => {
            school.filtedClasses = []
            school.classes.forEach(cls => {
              // 存在与班级相关的【科组长】、【备课组长】
              const IsSubjectLeader = RolePositions.some(
                role =>
                  [Role.SubjectLeader.id, Role.GradeSubjectLeader.id].includes(role.roleId) &&
                  (cls.classType === 0 ||
                    (cls.classType === 1 &&
                      role.positions.some(position => cls.teachingSubjectIds.includes(position.subjectId))))
              )

              // 存在与班级相关的【班主任】、【任课老师】
              const IsClassLeaderOrTeacher = RolePositions.some(
                role =>
                  [Role.ClassLeader.id, Role.Teacher.id].includes(role.roleId) &&
                  role.positions.some(position => position.classId === cls.classId)
              )

              if (IsSubjectLeader || IsClassLeaderOrTeacher) {
                school.filtedClasses.push(cls)
              }
            })
            school.classes = school.filtedClasses
          })
        }

        return TransformVisibleSchools(schools)
      }

      return []
    },

    // 当前学校Id
    currentSchoolId(state) {
      return state.currentSchoolId
    },

    // 当前机构Id
    currentInstitutionId(state) {
      return state.currentInstitutionId
    },

    currentInstitutionName(state, getters) {
      return (
        (
          (getters['tiledInstitutions'] || [])
            .filter(x => x.isOrg)
            .find(x => x.institutionId === state.currentInstitutionId) || {
            institutionName: '',
          }
        ).institutionName || ''
      )
    },

    isExamRootInstitution(state, getters) {
      return Boolean(
        getters['currentInstitutionId'] &&
          state.exam &&
          state.exam.creator &&
          state.exam.creator.schoolId === getters['currentInstitutionId']
      )
    },

    // 当前学校名称
    currentSchoolName(state, getters) {
      const CurrentSchool = getters['examSchools'].find(s => s.schoolId === state.currentSchoolId)
      return (CurrentSchool && CurrentSchool.schoolName) || ''
    },

    // 当前学校班级
    classes(state, getters) {
      let school = getters['visibleExamSchools'].find(s => s.schoolId === state.currentSchoolId)
      return (school && school.classes) || []
    },

    // 当前班级Id
    currentClassId(state) {
      return state.currentClassId
    },

    // 当前班级
    currentClass(state, getters) {
      return getters['classes'].find(x => x.classId === state.currentClassId)
    },

    // 当前班级名称
    currentClassName(state, getters) {
      let cls = getters['currentClass']
      return (cls && cls.className) || ''
    },

    // 当前报告可见科目
    subjectList(state, getters, rootState, rootGetters) {
      let currentReport = getters['currentReport']
      if (!currentReport) {
        return []
      }

      // 根据路由项判断当前报表是否分科，以及是否显示【全科】
      let route = findRouteByName(currentReport.name)
      if (!route) {
        return []
      }

      let routeMeta = route.meta || {}
      if (!routeMeta.showSubject) {
        return []
      }

      let templateSubjects = getters['templateSubjects']
      let rolePositions = currentReport.rolePositions
      let userSchoolId = rootGetters['user/info'].schoolId
      let currentSchoolId = getters['currentSchoolId']
      let currentClassId = getters['currentClassId']
      let currentClass = getters['currentClass']
      let isTeachingClass = currentReport.levelName === 'class' && currentClass && currentClass.classType === 1

      /**
       * 判断能否看所有科目
       */
      let canViewAllSubject = false
      let belongedSchoolIds = [userSchoolId] // 中心校及教学点
      const CenterSpotSchool = getters['centerSpotSchools'].find(school => school.id === userSchoolId)
      if (CenterSpotSchool && CenterSpotSchool.nodes && CenterSpotSchool.nodes.length) {
        CenterSpotSchool.nodes.forEach(node => belongedSchoolIds.push(node.id))
      }

      // 管理员、教研领导可见所有科目
      if (rolePositions.some(item => [0, Role.ResearcherLeader.id].includes(item.roleId))) {
        canViewAllSubject = true
      }
      // 学校领导、年级组长在联考报告及本校的校级、班级报告可见所有科目
      else if (
        rolePositions.some(item => [Role.SchoolLeader.id, Role.GradeLeader.id].includes(item.roleId)) &&
        (currentReport.levelName === 'multipleSchool' || belongedSchoolIds.some(x => x === currentSchoolId))
      ) {
        canViewAllSubject = true
      }
      // 班主任在本班的班级报告可见所有科目
      else if (
        rolePositions.some(
          item => item.roleId === Role.ClassLeader.id && item.positions.some(p => p.classId === currentClassId)
        )
      ) {
        canViewAllSubject = true
      }

      if (canViewAllSubject) {
        if (isTeachingClass) {
          return templateSubjects.filter(s => currentClass.teachingSubjectIds.includes(s.subjectId))
        } else if (routeMeta.showSubjectAll) {
          return [
            {
              examSubjectId: 0,
              subjectId: 0,
              subjectName: '总分',
            },
            ...templateSubjects,
          ]
        } else {
          return templateSubjects
        }
      }

      /**
       * 只能看部分科目
       */
      let visibleSubjects = []

      // 教研员，在教学班下要剔除该教学班不包含的科目
      let researcher = rolePositions.find(x => x.roleId === Role.Researcher.id)
      if (researcher) {
        if (isTeachingClass) {
          visibleSubjects.push(
            ...researcher.positions.filter(x => currentClass.teachingSubjectIds.includes(x.subjectId))
          )
        } else {
          visibleSubjects.push(...researcher.positions)
        }
      }

      // 科组长及备课组长在联考报告及本校的校级、班级报告，在教学班下要剔除该教学班不包含的科目
      let subjectLeaders = rolePositions.filter(
        x =>
          [Role.SubjectLeader.id, Role.GradeSubjectLeader.id].includes(x.roleId) &&
          (currentReport.levelName === 'multipleSchool' || belongedSchoolIds.some(x => x === currentSchoolId))
      )
      if (subjectLeaders.length > 0) {
        if (isTeachingClass) {
          subjectLeaders.forEach(role => {
            visibleSubjects.push(...role.positions.filter(x => currentClass.teachingSubjectIds.includes(x.subjectId)))
          })
        } else {
          subjectLeaders.forEach(role => {
            visibleSubjects.push(...role.positions)
          })
        }
      }

      // 科任教师在所任教班级
      let teacher = rolePositions.find(x => x.roleId === Role.Teacher.id)
      if (teacher) {
        visibleSubjects.push(...teacher.positions.filter(p => p.classId === currentClassId))
      }

      let distinctSubjectIds = Array.from(new Set(visibleSubjects.map(p => p.subjectId)))
      let subjects = []
      templateSubjects.forEach(s => {
        if (distinctSubjectIds.includes(s.subjectId)) {
          subjects.push(s)
        }
      })
      return subjects
    },

    // 当前科目examSubjectId
    currentExamSubjectId(state) {
      return state.currentExamSubjectId
    },
    currentSeniorHighSchoolExamCategorySubjectId(state) {
      return state.currentSeniorHighSchoolExamCategorySubjectId
    },

    // 当前科目
    currentExamSubject(state) {
      if (!state.exam) {
        return null
      }

      return state.exam.subjects.find(s => s.examSubjectId === state.currentExamSubjectId)
    },
    currentSeniorHighSchoolExamCategory(state, getters) {
      let _category = null

      if (
        state.exam &&
        getters['isSeniorHighSchoolSubjectCombinationSelectableProject'] &&
        state.reportSeniorHighSchoolExamCategories.length
      ) {
        _category =
          state.reportSeniorHighSchoolExamCategories.find(item => item.id === state.subjectCombinationCode) || null
      }

      return _category
    },
    currentSeniorHighSchoolExamCategorySubjects(state, getters) {
      const CurrentSeniorHighSchoolExamCategory = getters['currentSeniorHighSchoolExamCategory']
      const CategorySubjects =
        (CurrentSeniorHighSchoolExamCategory && CurrentSeniorHighSchoolExamCategory.subjects) || []
      const SubjectList = getters['subjectList']

      return CategorySubjects.length && SubjectList.length
        ? CategorySubjects.map(cs => {
            const TargetSubject = SubjectList.find(s => s.subjectName.includes(cs.subjectName))
            const Subject = {
              examSubjectId: (TargetSubject && TargetSubject.examSubjectId) || cs.examSubjectId || undefined,
              subjectId: TargetSubject ? TargetSubject.subjectId : undefined,
              uploadMode: TargetSubject ? TargetSubject.uploadMode : undefined,
              name: (TargetSubject && TargetSubject.subjectName) || cs.subjectName,
              subjectCode: cs.subjectCode,
              foreignSubjectId: cs.foreignSubjectId,
              fullScore: cs.fullScore,
              present: cs.present,
            }
            Subject.id = `${Subject.examSubjectId || ''}-${Subject.subjectId || ''}-${Subject.uploadMode || ''}-${
              Subject.subjectCode
            }-${Subject.foreignSubjectId}`

            return Subject
          })
        : []
    },
    currentSeniorHighSchoolExamCategorySubject(state, getters) {
      return (
        getters['currentSeniorHighSchoolExamCategorySubjects'].find(
          item => item.id === state.currentSeniorHighSchoolExamCategorySubjectId
        ) || null
      )
    },
    currentSeniorHighSchoolExamCategorySubjectName(state, getters) {
      return (getters['currentSeniorHighSchoolExamCategorySubject'] || { name: '' }).name
    },

    // 当前科目Id
    currentSubjectId(state, getters) {
      let subject = getters['currentExamSubject']
      return (subject && subject.subjectId) || ''
    },

    // 当前科目名称
    currentExamSubjectName(state, getters) {
      let subject = getters['currentExamSubject']
      return (subject && subject.subjectName) || ''
    },

    // 当前报表学校班级科目
    currentParams(state) {
      if (!state.exam) {
        return ''
      }

      return state.currentParams
    },

    // 是否错题反馈项目
    isFeedbackProject(state) {
      return state.exam && state.exam.apiExamTypeId && state.exam.apiExamTypeId === 6
    },

    // 能否导出校级PDF报告
    canExportSchoolPDF(state, getters, rootState, rootGetters) {
      if (getters['isAdministrator']) {
        // 考试管理员 -> true
        return true
      }

      if (getters['isOrgInstitution'] && !getters['isExamRootInstitution']) {
        return false
      }

      // 非考试管理员找到对应角色配置
      let permission = false
      let userRoles = (rootGetters['user/info'].roles || []).filter(role =>
        [Role.SchoolAdministrator, Role.SchoolLeader, Role.GradeLeader, Role.ResearcherLeader, Role.Researcher].some(
          r => r.id === role
        )
      )
      if (userRoles.length) {
        permission = state.template.permissions.roleReports
          .filter(p => userRoles.some(role => role === p.id))
          .some(p => {
            let targetSetting = p.reports.find(r => r.reportName === 'sch_overview')
            return targetSetting && targetSetting.detail && targetSetting.detail.showExportSchoolPDFBtn
          })
      }

      return permission
    },

    childOrgAndSchools(state) {
      return state.childOrgAndSchools || []
    },
  },

  mutations: {
    clear(state) {
      state.exam = null
      state.template = null
      state.examAttributes = null
      state.currentReportName = ''
      state.currentSchoolId = ''
      state.currentClassId = ''
      state.currentInstitutionId = ''
      state.currentExamSubjectId = ''
      state.currentParams = ''
      state.currentInstitutionSchools = []
      // state.examSubjCombinations = []
      state.reportSeniorHighSchoolExamCategories = []
      state.childOrgAndSchools = []
    },
    setReportConfigRouteParams(state, newConfig) {
      state.reportConfigRouteParams = newConfig || {
        isNewTemplate: false,
        exam: null,
        template: null,
      }
    },
    setNewEntranceAttributes(state, newEntranceAttributes) {
      Object.freeze(newEntranceAttributes)
      state.newEntranceAttributes = newEntranceAttributes
    },
    setCurrentParams(state) {
      state.currentParams = `${state.currentReportName}_${state.currentInstitutionId}_
				${state.currentSchoolId}_${state.currentClassId}_
				${
          state.currentExamSubjectId
        }_${state.subjectCombinationCode.toString()}_${state.currentSeniorHighSchoolExamCategorySubjectId.toString()}`
    },
    setReportSeniorHighSchoolExamCategoriesStructure(state) {
      state.reportSeniorHighSchoolExamCategories = (SubjectCombinations.getIdNames() || []).map(item => ({
        ...item,
        show: false,
      }))
    },
    setReportSeniorHighSchoolExamCategoriesVisible(state, value) {
      if (value && value.length) {
        state.reportSeniorHighSchoolExamCategories.forEach(
          item => (item.show = value.some(x => x.categoryId === item.id))
        )
      }
    },
    setReportSeniorHighSchoolExamCategoriesSubjects(state, value) {
      if (value && value.length) {
        state.reportSeniorHighSchoolExamCategories.forEach(item => {
          const Target = value.find(x => x.categoryId === item.id)
          if (Target) {
            item.subjects = Target.subjects || []
          }
        })
      } else {
        state.reportSeniorHighSchoolExamCategories.forEach(item => (item.subjects = undefined))
      }
    },
    setChildOrgAndSchools(state, value) {
      state.childOrgAndSchools = value || []
    },
  },

  actions: {
    // 进入报告，先获取考试模板相关信息
    setReportById(context, { examId, templateId, route }) {
      if (context.state.exam && context.state.template) {
        if (
          context.state.exam.examId === route.params.examId &&
          context.state.template.templateId === route.params.templateId
        ) {
          return Promise.resolve()
        }
      }

      context.commit('clear')

      return Promise.all([
        apiGetReportTemplate(templateId),
        apiGetExamSchoolClasses(examId),
        apiGetExamAttributes({ examId, templateId }),
        apiGetNewEntranceGroupList({ examId: examId, templateId: templateId }),
        apiGetExamInstitutionTree({ examId: examId, templateId: templateId }),
        apiGetExamCenterSpotSchoolList({ examId: examId, templateId: templateId }),
      ]).then(
        ([
          { exam, template },
          schoolClasses,
          attributes,
          newEntranceAttributes,
          institutionTree,
          centerSpotSchools,
        ]) => {
          exam.schools.forEach(s => {
            let school = schoolClasses.find(x => x.schoolId === s.schoolId)
            s.classes = (school && school.classes) || []
          })

          // detail数组转为对象
          template.permissions.roleReports.forEach(role => {
            role.reports.forEach(report => {
              let detail = {}
              if (report.detail && report.detail instanceof Array) {
                report.detail.forEach(item => {
                  detail[item.name] = item.value
                })
              }

              report.detail = detail
            })
          })

          Object.freeze(exam)
          Object.freeze(template)
          Object.freeze(attributes)

          context.commit('setNewEntranceAttributes', newEntranceAttributes)

          context.state.exam = exam
          context.state.template = template
          context.state.examAttributes = attributes

          context.state.examInstitutionTree = institutionTree
          context.state.centerSpotSchools = centerSpotSchools
        }
      )
    },

    // 设置第一份可见报告，返回路由
    setFirstReport(context, routeName) {
      let reportsByLevel = context.getters['reportsByLevel']
      let examId = context.state.exam.examId
      let templateId = context.state.template.templateId

      context.commit('setReportSeniorHighSchoolExamCategoriesStructure')

      if (routeName === 'report-exam') {
        let firstLevel = reportsByLevel[0]
        if (firstLevel) {
          return {
            name: firstLevel.reports[0].name,
            params: {
              examId,
              templateId,
            },
          }
        } else {
          // 暂无权限查看报表则清空store，使得再次点击查看时获取新的报表权限配置
          context.commit('clear')
          return null
        }
      } else {
        if (reportsByLevel.some(level => level.reports.some(report => report.name === routeName))) {
          return {
            name: routeName,
            params: {
              examId,
              templateId,
            },
          }
        } else {
          return null
        }
      }
    },

    // 改变报表层级
    changeReportLevel(context, levelName) {
      let level = context.getters['reportsByLevel'].find(x => x.levelName === levelName)
      if (level) {
        context.dispatch('changeReport', level.reports[0].name)
      }
    },

    // 首次进入，无需切换路由
    // reportName -> school -> class -> subject, 每处节点发生变化均引起下一节点变化，最后更新currentParams
    changeReportWithoutRoute(context, reportName) {
      if (context.getters['reportsByLevel'].some(level => level.reports.some(x => x.name === reportName))) {
        context.state.currentReportName = reportName
        if (context.getters['underLevelInstitutions'].length) {
          context.dispatch('changeInstitution')
        } else if (context.getters['isOrgInstitution']) {
          context.dispatch('changeInstitution', context.rootGetters['user/info'].schoolId)
        } else {
          context.dispatch('changeSchool')
        }
      }
    },

    fetchInstitutionExamCombinations(context) {
      const ReportType = (context.getters['currentReportName'] || '').split('-')[2] || ''
      const CurrentLevelName = context.getters['currentLevelName']
      const TemplateId = context.getters['templateId']
      const API = {
        api: null,
        requestParams: null,
      }

      if (['general', 'score', 'passingAnalysis', 'scoreGrades'].includes(ReportType)) {
        API.api = apiGetReportExamCombinationsOfInstitutions
        API.requestParams = {
          templateId: TemplateId,
          entityId:
            CurrentLevelName === 'class'
              ? context.getters['currentClassId']
              : CurrentLevelName === 'school'
                ? context.getters['currentSchoolId']
                : context.getters['currentInstitutionId'],
        }
      } else if (['school', 'class'].includes(ReportType)) {
        API.api = apiGetHighSchoolGradeSubjectList
        API.requestParams = {
          templateId: TemplateId,
          schoolId:
            CurrentLevelName === 'school'
              ? context.getters['currentSchoolId']
              : context.getters['currentInstitutionId'],
        }
      } else if (['scoreInterval', 'rankInterval'].includes(ReportType)) {
        API.api = apiGetIntervalReportExamCombinationsOfInstitutions
        API.requestParams = {
          templateId: TemplateId,
          organizationId: CurrentLevelName === 'multipleSchool' ? context.getters['currentInstitutionId'] : undefined,
          schoolId: CurrentLevelName === 'school' ? context.getters['currentSchoolId'] : undefined,
          classId: CurrentLevelName === 'class' ? context.getters['currentClassId'] : undefined,
        }
      } else if (ReportType === 'criticalStudent' && CurrentLevelName !== 'multipleSchool') {
        API.api = apiGetCriticalExamCombinations
        API.requestParams = {
          templateId: TemplateId,
          schoolId: context.getters['currentSchoolId'],
          classId: CurrentLevelName === 'class' ? context.getters['currentClassId'] : undefined,
        }
      }

      if (API.api && API.requestParams) {
        return API.api(API.requestParams).then(response => {
          context.commit('setReportSeniorHighSchoolExamCategoriesSubjects', ReportType !== 'general' ? response : [])
          context.commit('setReportSeniorHighSchoolExamCategoriesVisible', response)
        })
      } else {
        context.commit('setReportSeniorHighSchoolExamCategoriesStructure')
        return Promise.resolve()
      }
    },

    // 切换报表，同时切换路由
    changeReport(context, reportName) {
      if (context.getters['reportsByLevel'].some(level => level.reports.some(x => x.name === reportName))) {
        Router.replace({
          name: reportName,
        }).then(() => {
          context.state.currentReportName = reportName
          if (context.getters['underLevelInstitutions'].length) {
            context.dispatch('changeInstitution', context.getters['currentInstitutionId'])
          } else {
            context.dispatch('changeSchool')
          }
        })
      }
    },

    changeInstitution(context, institutionId) {
      if (institutionId === undefined) {
        institutionId = context.getters['underLevelInstitutions'][0].id
      }
      let rootInstitutionId = context.rootGetters['user/info'].schoolId

      let institution = undefined
      let rootInstitution = undefined

      let findInstitution = node => {
        if (node.id === institutionId) {
          institution = node
          return
        }

        if (node.nodes && node.nodes.length > 0) {
          node.nodes.filter(x => x.isOrg).forEach(x => findInstitution(x))
        }
      }
      let findRootInstitution = node => {
        if (node.id === rootInstitutionId) {
          rootInstitution = node
          return
        }

        if (node.nodes && node.nodes.length > 0) {
          node.nodes.filter(x => x.isOrg).forEach(x => findRootInstitution(x))
        }
      }

      findInstitution(context.getters['examInstitutionTree'])
      if (!institution) {
        institution = context.getters['examInstitutionTree']
      }

      findRootInstitution(context.getters['examInstitutionTree'])
      if (!rootInstitution) {
        rootInstitution = context.getters['examInstitutionTree']
      }

      context.state.currentInstitutionId = (institution && institution.id) || ''
      context.state.currentInstitutionSchools = []
      let fillInstitutionSchools = node => {
        if (node.isOrg) {
          if (node.nodes && node.nodes.length) {
            node.nodes.forEach(x => fillInstitutionSchools(x))
          }
        } else {
          context.state.currentInstitutionSchools.push(node.id)
        }
      }
      if (rootInstitution.nodes && rootInstitution.nodes.length) {
        rootInstitution.nodes.forEach(x => fillInstitutionSchools(x))
      }

      context.dispatch('changeSchool')
    },

    changeSchool(context, schoolId) {
      const SchoolClasses = context.getters['visibleExamSchools']

      if (schoolId === undefined) {
        schoolId = context.state.currentSchoolId
      }

      let school = SchoolClasses.find(s => s.schoolId === schoolId)
      if (!school) {
        school = SchoolClasses[0]
      }

      context.state.currentSchoolId = (school && school.schoolId) || ''

      context.dispatch('changeClass')
    },

    async changeClass(context, classId) {
      if (classId !== context.state.currentClassId) {
        if (classId === undefined) {
          classId = context.state.currentClassId
        }

        const Classes = context.getters['classes']
        const Cls = Classes.find(c => c.classId === classId) || Classes[0]
        context.state.currentClassId = (Cls && Cls.classId) || ''
      }

      if (context.getters['isSeniorHighSchoolSubjectCombinationSelectableProject']) {
        await context.dispatch('fetchInstitutionExamCombinations')
      }

      context.dispatch('changeSubject')
    },

    changeSubject(context, examSubjectId) {
      if (examSubjectId !== context.state.currentExamSubjectId) {
        if (examSubjectId === undefined) {
          examSubjectId = context.state.currentExamSubjectId
        }
        const SubjectList = context.getters['subjectList']
        const Subject = SubjectList.find(s => s.examSubjectId === examSubjectId) || SubjectList[0]
        context.state.currentExamSubjectId = Subject ? Subject.examSubjectId : ''
      }

      if (context.getters['isSeniorHighSchoolSubjectCombinationSelectableProject']) {
        context.dispatch('changeSubjectCombinationCode')
      } else {
        context.commit('setCurrentParams')
      }
    },

    changeSubjectCombinationCode(context, subjectCombinationCode) {
      if (subjectCombinationCode !== context.state.subjectCombinationCode) {
        const SeniorHighSchoolSubjectCombinations = context.getters['seniorHighSchoolSubjectCombinations']
        if (SeniorHighSchoolSubjectCombinations.length) {
          if (subjectCombinationCode === undefined) {
            subjectCombinationCode = context.state.subjectCombinationCode
          }

          context.state.subjectCombinationCode = SeniorHighSchoolSubjectCombinations.some(
            item => item.id === subjectCombinationCode
          )
            ? subjectCombinationCode
            : SeniorHighSchoolSubjectCombinations[0].id
        } else {
          context.state.subjectCombinationCode = 0
        }
      }

      context.dispatch('changeSeniorHighSchoolExamCategorySubjectId')
    },

    changeSeniorHighSchoolExamCategorySubjectId(context, id) {
      if (id !== context.state.currentSeniorHighSchoolExamCategorySubjectId) {
        const Items = context.getters['currentSeniorHighSchoolExamCategorySubjects']
        if (Items.length) {
          if (id === undefined) {
            id = context.state.currentSeniorHighSchoolExamCategorySubjectId
          }

          context.state.currentSeniorHighSchoolExamCategorySubjectId = Items.some(item => item.id === id)
            ? id
            : (Items[0] && Items[0].id) || ''
        } else {
          context.state.currentSeniorHighSchoolExamCategorySubjectId = ''
        }
      }

      context.commit('setCurrentParams')
    },
  },
}
