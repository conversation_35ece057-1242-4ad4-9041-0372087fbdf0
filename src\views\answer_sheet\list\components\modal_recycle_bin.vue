<script>
  import { Icon, Input } from 'view-ui-plus'
  import TextButton from '@/components/text_button'

  import { apiGetMyAnswerSheetList, apiUpdateAnswerSheetStatus } from '@/api/emarking/answer_sheet'

  import ModeEnum from '@/enum/answer_sheet/mode'
  import SourceTypeEnum from '@/enum/answer_sheet/source_type'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['update:modelValue', 'fetch-personal-answer-sheet-list', 'preview-answer-sheet'],

    data() {
      return {
        tableMaxHeight: window.innerHeight - 260,
        tableSearchByPaperNameActived: false,
        paperNameForSearch: '',

        paperList: [],
      }
    },

    computed: {
      modes() {
        return ModeEnum.getEntries()
      },
      sourceTypes() {
        return SourceTypeEnum.getEntries()
      },
      paperColumns() {
        return [
          {
            title: '答题卡',
            key: 'name',
            renderHeader: h =>
              h('div', {}, [
                h(
                  'div',
                  {
                    onClick: () => (this.tableSearchByPaperNameActived = !this.tableSearchByPaperNameActived),
                    class: 'render-table-header-item',
                  },
                  [
                    h(
                      'span',
                      {
                        class: 'render-table-header-text',
                      },
                      '答题卡 '
                    ),
                    h(Icon, {
                      type: 'ios-funnel',
                      color: this.tableSearchByPaperNameActived ? '#05c1ae' : '#c5c8ce',
                      class: 'render-table-header-icon',
                    }),
                  ]
                ),
                h(Input, {
                  search: true,
                  modelValue: this.paperNameForSearch,
                  placeholder: '请输入答题卡名称进行搜索',
                  onOnChange: e => (this.paperNameForSearch = e.target.value || ''),
                  onOnSearch: value => (this.paperNameForSearch = value || ''),
                  style: {
                    width: '220px',
                    margin: '0 0 0 1em',
                    diaplay: 'inline-block',
                    visibility: this.tableSearchByPaperNameActived ? 'visible' : 'hidden',
                  },
                }),
              ]),
          },
          {
            title: '创建时间',
            key: 'createTime',
            width: 200,
          },
          {
            title: '模式',
            width: 160,
            render: (h, params) =>
              h('span', {}, (this.modes.find(m => m.id === params.row.mode) || { name: '-' }).name),
          },
          {
            title: '来源',
            width: 160,
            render: (h, params) =>
              h('span', {}, (this.sourceTypes.find(s => s.id === params.row.sourceType) || { name: '-' }).name),
          },
          {
            title: '操作',
            width: 100,
            render: (h, params) =>
              h('div', {}, [
                h(
                  TextButton,
                  {
                    type: 'primary',
                    onClick: () => this.$emit('preview-answer-sheet', params.row),
                  },
                  () => '查看'
                ),
                h(
                  TextButton,
                  {
                    type: 'primary',
                    onClick: () => this.restoreAnswerSheet(params.row.id),
                  },
                  () => '还原'
                ),
              ]),
          },
        ]
      },
      filtedPaperList() {
        return (this.paperList || []).filter(p => p.name.includes(this.paperNameForSearch))
      },
    },

    methods: {
      handleVisibleChange(visibility) {
        if (visibility) {
          this.paperList = []
          this.fetchRecycleBinPaperList()
        } else {
          this.$emit('update:modelValue', false)
        }
      },

      fetchRecycleBinPaperList() {
        apiGetMyAnswerSheetList({ status: 1 }).then(response => (this.paperList = response || []))
      },
      restoreAnswerSheet(id) {
        apiUpdateAnswerSheetStatus({
          id: id,
          status: 0,
        })
          .then(() => {
            this.$Message.success({
              duration: 2,
              content: '已还原',
            })
          })
          .finally(() => {
            this.$emit('fetch-personal-answer-sheet-list')
            this.fetchRecycleBinPaperList()
          })
      },
    },
  }
</script>

<template>
  <Modal :model-value="modelValue" title="回收站" width="1000" footer-hide @on-visible-change="handleVisibleChange">
    <Table :columns="paperColumns" :data="filtedPaperList" :max-height="tableMaxHeight"></Table>
  </Modal>
</template>

<style lang="scss">
  .render-table-header-item {
    display: inline-block;
  }

  .render-table-header-item:hover {
    cursor: pointer;
  }

  .render-table-header-text {
    color: $color-content;
    font-size: $font-size-medium;
    user-select: none;
  }

  .render-table-header-icon {
    font-size: $font-size-medium;
  }
</style>
