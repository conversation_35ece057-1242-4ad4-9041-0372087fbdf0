import Rect from './rect'
import { randomId } from '@/utils/string'
export default class SubjectiveArea {
  constructor(params) {
    this.id = randomId()
    this.pageIndex = (params && params.pageIndex) || 0
    this.blockId = (params && params.blockId) || ''
    this.blockName = (params && params.blockName) || ''
    this.rect = params && params.rect ? new Rect(params.rect) : new Rect()
  }

  copy() {
    let clone = new SubjectiveArea()
    clone.id = this.id
    clone.pageIndex = this.pageIndex
    clone.blockId = this.blockId
    clone.blockName = this.blockName
    clone.rect = new Rect(this.rect)
    return clone
  }

  static isSameRect(a, b) {
    return (
      a.pageIndex == b.pageIndex &&
      a.rect.x == b.rect.x &&
      a.rect.y == b.rect.y &&
      a.rect.width == b.rect.width &&
      a.rect.height == b.rect.height
    )
  }
}
