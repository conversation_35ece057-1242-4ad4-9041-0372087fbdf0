import ajax from '@/api/ajax'

export function apiGetExamVenue(params) {
  return ajax
    .get({
      url: 'mark/examVenue/list',
      params: params,
      requestName: '获取考点列表',
    })
    .then(data => {
      return data
    })
}

export function apiUpdateExamVenue(params) {
  return ajax.post({
    url: 'mark/examVenue/createOrUpdate',
    params: {
      examId: params.examId,
    },
    data: {
      schools: params.schools,
      rooms: params.rooms,
      venueCode: params.venueCode,
      venueName: params.venueName,
      venueId: params.venueId,
    },
    requestName: '更新考点',
  })
}

export function apiDeleteExamVenue(params) {
  return ajax.delete({
    url: 'mark/examVenue/delete',
    params: {
      examId: params.examId,
      venueCode: params.venueCode,
    },
    requestName: '删除考点',
  })
}

export function apiAddExamVenueTeacher(params) {
  return ajax.post({
    url: 'mark/examVenue/addExamVenueTeacher',
    data: [
      {
        teacherIds: params.teacherIds,
        venueId: params.venueId,
      },
    ],
    requestName: '添加考点管理员',
  })
}

export function apiDeleteExamVenueTeacher(params) {
  return ajax.delete({
    url: 'mark/examVenue/deleteExamVenueTeacher',
    data: [
      {
        teacherIds: params.teacherIds,
        venueId: params.venueId,
      },
    ],
    requestName: '删除考点管理员',
  })
}
