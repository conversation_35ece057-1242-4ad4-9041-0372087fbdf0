import ajax from '@/api/ajax'

export function apiGetSystemSchools(params) {
  return ajax.get({
    url: 'user/sys/school/list',
    params: {
      regionId: params.regionId,
      gradeLevelIdsListStr: params.gradeLevelIdsListStr,
      keyword: params.keyword,
      page: params.currentPage,
      size: params.pageSize,
    },
    requestName: '获取学校',
  })
}

export function apiGetSystemEduSchools(params) {
  return ajax.get({
    url: 'user/sys/eduSchool/list',
    params: {
      regionId: params.regionId,
      keyword: params.keyword,
      page: params.currentPage,
      size: params.pageSize,
    },
    requestName: '获取机构',
  })
}

export function apiImportManger(file) {
  return ajax.upload({
    url: 'user/sys/manager/impNormalManager',
    data: {
      file,
    },
    requestName: '导入普通管理员',
    isPostRequest: false,
  })
}
