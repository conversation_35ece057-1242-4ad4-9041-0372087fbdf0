import ajax from '@/api/ajax'

export function apiSaveReviewConfig(data) {
  return ajax.post({
    url: 'review/reviewConfig/saveOrUpdate',
    data,
    requestName: '提交评审设置',
  })
}

export function apiGetReviewConfig(params) {
  return ajax.get({
    url: 'review/reviewConfig/getConfig',
    params: {
      activityId: params.activityId,
    },
    requestName: '查询评审设置',
  })
}

export function apiSaveScoreMultipleConfig(data) {
  return ajax.post({
    url: 'review/reviewConfig/saveOrUpdateScoreItems',
    data,
    requestName: '提交评分设置',
  })
}

export function apiGetScoreConfig(params) {
  return ajax.get({
    url: 'review/reviewConfig/listScoreItems',
    params: {
      activityId: params.activityId,
      categoryId: params.categoryId,
    },
    requestName: '查询评分设置',
  })
}

export function apiSaveScoreSingleConfig(data) {
  return ajax.put({
    url: 'review/reviewConfig/updateConfigFullScore',
    data,
    requestName: '提交评分设置',
  })
}

export function apiSaveScoreItemTemplate(data) {
  return ajax.post({
    url: 'review/reviewConfig/saveTemplate',
    data,
    requestName: '保存模板',
  })
}

export function apiGetScoreConfigTemplate() {
  return ajax.get({
    url: 'review/reviewConfig/listTemplate',
    requestName: '查询评分细则模板',
  })
}

export function apiDelelteScoreTemplate(params) {
  return ajax.delete({
    url: 'review/reviewConfig/deleteScoreTemplate',
    params: {
      id: params.id,
    },
    requestName: '查询评分设置',
  })
}
