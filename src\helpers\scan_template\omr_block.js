import Rect from './rect'
import { meanBy, roundNumber } from '@/utils/math'
import { randomId } from '@/utils/string'
import OmrDirectionEnum from '@/enum/scan_template/omr_direction'

export default class OmrBlock {
  constructor() {
    this.id = ''
    // 排列方向，H-水平，V-垂直
    this.direction = OmrDirectionEnum.Horizontal.id
    // 一道题的选项个数
    this.optionCount = 0
    // 题目数
    this.questionCount = 0
    // 题号
    this.questionCodes = []
    // 外框
    this.rect = null
    // 各选项
    this.options = []

    // 参数
    this.topLeftX = 0
    this.topLeftY = 0
    this.optionWidth = 24
    this.optionHeight = 12
    this.optionGapX = 12
    this.optionGapY = 12
  }

  // 由扫描模板格式导入
  static import(outer) {
    let omr = new OmrBlock()
    omr.id = outer.id
    omr.direction = outer.direction
    omr.optionCount = outer.optionCount
    omr.questionCount = outer.options.length / outer.optionCount
    omr.rect = new Rect()
    omr.options = outer.options.map(opt => new Rect(opt))
    omr.ensureRect()
    omr.initializeParams()
    return omr
  }

  // 由识别结果导入
  static importOptions(borderRect, optionMatrix) {
    let block = new OmrBlock()
    block.id = randomId()
    block.rect = new Rect(borderRect)
    if (optionMatrix.length == 0) {
      block.topLeftX = block.rect.x + 5
      block.topLeftY = block.rect.y + 5
      return block
    }

    block.optionCount = optionMatrix[0].length
    block.questionCount = optionMatrix.length
    block.options = optionMatrix.flat().map(opt => (opt == null ? null : new Rect(opt)))
    block.ensureRect()
    block.initializeParams()

    // 补充缺失的选项框
    block.generateOptions(block.optionCount, block.questionCount)

    return block
  }

  // 导出为扫描模板格式
  export() {
    return {
      id: this.id,
      direction: this.direction,
      optionCount: this.optionCount,
      options: this.options.map(opt => opt.export()),
    }
  }

  // 复制
  copy() {
    let newBlock = new OmrBlock()
    Object.assign(newBlock, this)
    newBlock.questionCodes = this.questionCodes.slice()
    newBlock.rect = { ...this.rect }
    newBlock.options = this.options.map(opt => opt.copy())
    return newBlock
  }

  // 选项矩阵，每一行为一题
  get optionMatrix() {
    let matrix = []
    let q = []
    this.options.forEach((opt, idx) => {
      if (idx % this.optionCount == 0) {
        q = []
        matrix.push(q)
      }
      q.push(opt)
    })
    return matrix
  }

  // 生成omr区域外框
  ensureRect() {
    if (this.options.length == 0) {
      return
    }
    let minX = Number.MAX_SAFE_INTEGER
    let maxX = 0
    let minY = Number.MAX_SAFE_INTEGER
    let maxY = 0
    this.options.forEach(({ x, y, width, height }) => {
      if (x < minX) {
        minX = x
      }
      if (x + width > maxX) {
        maxX = x + width
      }
      if (y < minY) {
        minY = y
      }
      if (y + height > maxY) {
        maxY = y + height
      }
    })
    this.rect.x = minX - 5
    this.rect.y = minY - 5
    this.rect.width = maxX - minX + 10
    this.rect.height = maxY - minY + 10
  }

  // 由选项位置初始化参数
  initializeParams() {
    // 第一题选项
    let firstQuestionOptions = this.options.slice(0, this.optionCount).filter(Boolean)
    // 最后一题选项
    let lastQuestionOptions = this.options.slice(-this.optionCount).filter(Boolean)
    // 每题第一个选项
    let firstIndexOptions = []
    for (let j = 0; j < this.questionCount; j++) {
      let index = j * this.optionCount
      let opt = this.options[index]
      if (opt) {
        firstIndexOptions.push(opt)
      }
    }
    // 每题最后一个选项
    let lastIndexOptions = []
    for (let j = 0; j < this.questionCount; j++) {
      let index = (j + 1) * this.optionCount - 1
      let opt = this.options[index]
      if (opt) {
        lastIndexOptions.push(opt)
      }
    }
    // 第一列X平均
    let meanFirstX = meanBy(
      this.direction == OmrDirectionEnum.Vertical.id ? firstQuestionOptions : firstIndexOptions,
      rect => rect.x
    )
    // 最后一列X平均
    let meanLastX = meanBy(
      this.direction == OmrDirectionEnum.Vertical.id ? lastQuestionOptions : lastIndexOptions,
      rect => rect.x
    )
    // 第一行Y平均
    let meanFirstY = meanBy(
      this.direction == OmrDirectionEnum.Vertical.id ? firstIndexOptions : firstQuestionOptions,
      rect => rect.y
    )
    // 最后一行X平均
    let meanLastY = meanBy(
      this.direction == OmrDirectionEnum.Vertical.id ? lastIndexOptions : lastQuestionOptions,
      rect => rect.y
    )
    // 列数
    let columnCount = this.direction == OmrDirectionEnum.Vertical.id ? this.questionCount : this.optionCount
    // 行数
    let rowCount = this.direction == OmrDirectionEnum.Vertical.id ? this.optionCount : this.questionCount

    // 所有选项框宽高平均
    let allOptions = this.options.filter(Boolean)
    let meanOptionWidth = meanBy(allOptions, rect => rect.width)
    let meanOptionHeight = meanBy(allOptions, rect => rect.height)

    // 参数
    this.topLeftX = roundNumber(meanFirstX, 1)
    this.topLeftY = roundNumber(meanFirstY, 1)
    this.optionWidth = roundNumber(meanOptionWidth, 1)
    this.optionHeight = roundNumber(meanOptionHeight, 1)
    if (columnCount > 1) {
      this.optionGapX = roundNumber((meanLastX - meanFirstX) / (columnCount - 1) - meanOptionWidth, 1)
    }
    if (rowCount > 1) {
      this.optionGapY = roundNumber((meanLastY - meanFirstY) / (rowCount - 1) - meanOptionHeight, 1)
    }
  }

  // 生成选项
  generateOptions(newOptionCount, newQuestionCount) {
    let options = []
    for (let qIdx = 0; qIdx < newQuestionCount; qIdx++) {
      for (let optIdx = 0; optIdx < newOptionCount; optIdx++) {
        // 找原有的选项框
        let rect = null
        if (qIdx < this.questionCount && optIdx < this.optionCount) {
          rect = this.options[qIdx * this.optionCount + optIdx]
        }
        // 如不存在则新建
        if (!rect) {
          rect = new Rect({
            x:
              this.topLeftX +
              (this.optionWidth + this.optionGapX) * (this.direction == OmrDirectionEnum.Vertical.id ? qIdx : optIdx),
            y:
              this.topLeftY +
              (this.optionHeight + this.optionGapY) * (this.direction == OmrDirectionEnum.Vertical.id ? optIdx : qIdx),
            width: this.optionWidth,
            height: this.optionHeight,
          })
        }
        rect.id = `block_${this.id}_opt_${qIdx}_${optIdx}`
        options.push(rect)
      }
    }
    this.options = options
  }

  // 改排列方向，选项方阵转置
  changeDirection(direction) {
    if (this.direction != direction) {
      let newOptionCount = this.questionCount
      let newQuestionCount = this.optionCount
      let newOptions = []
      this.options.forEach((opt, idx) => {
        let rowIdx = Math.floor(idx / this.optionCount)
        let colIdx = idx % this.optionCount
        opt.id = `block_${this.id}_opt_${colIdx}_${rowIdx}`
        newOptions[colIdx * newOptionCount + rowIdx] = opt
      })
      this.options = newOptions
      this.optionCount = newOptionCount
      this.questionCount = newQuestionCount
    }
    this.direction = direction
  }

  // 改题目选项个数，重新生成选项
  changeOptionCount(optionCount) {
    this.generateOptions(optionCount, this.questionCount)
    this.ensureRect()
    this.optionCount = optionCount
  }

  // 改题目个数，重新生成选项
  changeQuestionCount(questionCount) {
    this.generateOptions(this.optionCount, questionCount)
    this.ensureRect()
    this.questionCount = questionCount
  }

  // 改题号
  changeQuestionCodes(questionCodes) {
    this.questionCodes = questionCodes.sort((a, b) => a - b)
  }

  // 更新选项位置
  updateOptions() {
    for (let qIdx = 0; qIdx < this.questionCount; qIdx++) {
      for (let optIdx = 0; optIdx < this.optionCount; optIdx++) {
        let rect = this.options[qIdx * this.optionCount + optIdx]
        rect.width = this.optionWidth
        rect.height = this.optionHeight
        rect.x =
          this.topLeftX +
          (this.optionWidth + this.optionGapX) * (this.direction == OmrDirectionEnum.Vertical.id ? qIdx : optIdx)
        rect.y =
          this.topLeftY +
          (this.optionHeight + this.optionGapY) * (this.direction == OmrDirectionEnum.Vertical.id ? optIdx : qIdx)
      }
    }
  }

  // 移动外框
  moveOuterRect({ x, y }) {
    this.topLeftX += x - this.rect.x
    this.topLeftY += y - this.rect.y
    this.updateOptions()
    this.ensureRect()
  }

  /**
   * 修改参数
   */
  changeTopLeftX(x) {
    this.topLeftX = roundNumber(x, 1)
    this.updateOptions()
    this.ensureRect()
  }
  changeTopLeftY(y) {
    this.topLeftY = roundNumber(y, 1)
    this.updateOptions()
    this.ensureRect()
  }
  changeOptionWidth(width) {
    this.optionWidth = roundNumber(width, 1)
    this.updateOptions()
    this.ensureRect()
  }
  changeOptionHeight(height) {
    this.optionHeight = roundNumber(height, 1)
    this.updateOptions()
    this.ensureRect()
  }
  changeOptionGapX(width) {
    this.optionGapX = roundNumber(width, 1)
    this.updateOptions()
    this.ensureRect()
  }
  changeOptionGapY(height) {
    this.optionGapY = roundNumber(height, 1)
    this.updateOptions()
    this.ensureRect()
  }

  /**
   * 检查各选项大小是否一致
   */
  checkIsOptionsSameSize() {
    let optionsWidth = this.options.map(opt => opt.width)
    let optionsHeight = this.options.map(opt => opt.height)
    let minWidth = Math.min(...optionsWidth)
    let maxWidth = Math.max(...optionsWidth)
    let minHeight = Math.min(...optionsHeight)
    let maxHeight = Math.max(...optionsHeight)
    return (maxWidth - minWidth) / maxWidth <= 0.3 && (maxHeight - minHeight) / maxHeight <= 0.3
  }

  /**
   * 检查各选项间距是否一致
   */
  checkIsOptionsSameGap() {
    let gapsX = []
    let gapsY = []
    for (let i = 0; i < this.questionCount; i++) {
      for (let j = 0; j < this.optionCount; j++) {
        let optIdx = i * this.optionCount + j
        let opt = this.options[optIdx]
        let top, left
        if (this.direction == OmrDirectionEnum.Horizontal.id) {
          if (i > 0) {
            top = this.options[optIdx - this.optionCount]
          }
          if (j > 0) {
            left = this.options[optIdx - 1]
          }
        } else {
          if (j > 0) {
            top = this.options[optIdx - 1]
          }
          if (i > 0) {
            left = this.options[optIdx - this.optionCount]
          }
        }
        if (top) {
          gapsY.push(opt.y - top.y - top.height)
        }
        if (left) {
          gapsX.push(opt.x - left.x - left.width)
        }
      }
    }
    let minGapX = gapsX.length > 0 ? Math.min(...gapsX) : 1
    let maxGapX = gapsX.length > 0 ? Math.max(...gapsX) : 1
    let minGapY = gapsY.length > 0 ? Math.min(...gapsY) : 1
    let maxGapY = gapsY.length > 0 ? Math.max(...gapsY) : 1
    return (maxGapX - minGapX) / maxGapX <= 0.3 && (maxGapY - minGapY) / maxGapY <= 0.3
  }
}
