<template>
  <Modal
    :model-value="props.modelValue"
    title="识别目录"
    width="600"
    :mask-closable="false"
    draggable
    @on-visible-change="handleVisibleChange"
  >
    <div class="modal-body">
      <div v-if="loading" class="loading">
        <Icon type="ios-loading" size="18" class="loading-spin"></Icon>
        <div>识别中</div>
      </div>
      <div v-if="recognizeFailed" class="failed">
        <div class="error">
          <span class="label">识别失败</span>
          <Tooltip>
            <Icon type="ios-information-circle-outline"></Icon>
            <template #content>
              <div class="error-message">{{ errorMessage }}</div>
            </template>
          </Tooltip>
        </div>
        <Button type="default" @click="retryRecognize">重试</Button>
      </div>
      <div v-else-if="recognizeCompleted" class="result">
        <div v-if="tocItemList.length == 0" class="empty">未识别到目录项</div>
        <div v-else class="content">
          <div class="start-page">
            <span class="label">起始页码：</span>
            <InputNumber v-model="startPage" :min="1" :precision="0"></InputNumber>
          </div>
          <div class="toc-item-list">
            <div v-for="item in tocItemList" :key="item.id" class="toc-item">
              <span class="title">{{ item.title }}</span>
              <span class="page">{{ item.page }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <Button type="text" @click="handleCancel">取消</Button>
      <Button type="primary" :disabled="!recognizeCompleted || tocItemList.length == 0" @click="handleOK">确定</Button>
    </template>
  </Modal>
</template>

<script setup>
  import { ref, watch, computed } from 'vue'

  import { useCoachBookIngestionStore } from '@/store/qlib/coach_book_ingestion'

  import {
    apiCoachBookIngestionRecognizeToc,
    apiCoachBookIngestionGetRecognizeTocResult,
  } from '@/api/qlib/coach_book_ingestion'

  import CommonProgressEnum from '@/enum/qlib/ingestion/common_progress'

  const props = defineProps({
    modelValue: Boolean,
  })
  const emits = defineEmits(['update:modelValue', 'add'])
  const ingestionStore = useCoachBookIngestionStore()

  const loading = ref(false)
  const tocRecognizeResult = ref(null)
  const startPage = ref(1)
  const tocItemList = ref([])

  const recognizeCompleted = computed(() => {
    return CommonProgressEnum.Completed.id == tocRecognizeResult.value?.status
  })
  const recognizeFailed = computed(() => {
    return CommonProgressEnum.Failed.id == tocRecognizeResult.value?.status
  })
  const errorMessage = computed(() => {
    if (recognizeFailed.value && tocRecognizeResult.value?.errorMessage) {
      return tocRecognizeResult.value.errorMessage.substring(0, 100)
    }
    return ''
  })

  watch(
    () => props.modelValue,
    () => {
      if (props.modelValue) {
        init()
      }
    }
  )

  watch(tocRecognizeResult, buildTocItemList)

  async function init() {
    reset()

    await refreshTocRecognizeResult()
    if (
      ![CommonProgressEnum.Running.id, CommonProgressEnum.Completed.id, CommonProgressEnum.Failed.id].includes(
        tocRecognizeResult.value?.status
      )
    ) {
      startRecognize()
    }
  }

  function reset() {
    loading.value = false
    tocRecognizeResult.value = null
    startPage.value = 1
    tocItemList.value = []
  }

  async function startRecognize() {
    loading.value = true
    await apiCoachBookIngestionRecognizeToc(ingestionStore.ingestionInfo.id)
    await refreshTocRecognizeResult()
  }

  async function refreshTocRecognizeResult() {
    tocRecognizeResult.value = await apiCoachBookIngestionGetRecognizeTocResult(ingestionStore.ingestionInfo.id)
    if (tocRecognizeResult.value && tocRecognizeResult.value.status == CommonProgressEnum.Running.id) {
      setTimeout(() => {
        refreshTocRecognizeResult()
      }, 2000)
    } else {
      loading.value = false
    }
  }

  async function retryRecognize() {
    reset()
    startRecognize()
  }

  function buildTocItemList() {
    if (CommonProgressEnum.Completed.id != tocRecognizeResult.value?.status) {
      tocItemList.value = []
      return
    }
    let tree = tocRecognizeResult.value?.result
    if (!tree || tree.length == 0) {
      tocItemList.value = []
      return
    }

    let list = []
    traverse(tree, node => {
      list.push({
        id: node.id,
        title: node.title,
        page: node.page,
      })
    })

    tocItemList.value = list
  }

  function handleVisibleChange(visible) {
    if (!visible) {
      handleCancel()
    }
  }

  function handleCancel() {
    emits('update:modelValue', false)
  }

  function handleOK() {
    emits('add')
  }
</script>

<style lang="scss" scoped>
  .modal-body {
    min-height: 200px;
  }

  .loading {
    @include flex(column, center, center);
    height: 200px;
    color: $color-primary;

    .loading-spin {
      margin-bottom: 16px;
      animation: ani-loading-spin 1s linear infinite;
    }

    @keyframes ani-loading-spin {
      from {
        transform: rotate(0deg);
      }

      50% {
        transform: rotate(180deg);
      }

      to {
        transform: rotate(360deg);
      }
    }
  }

  .failed {
    @include flex(column, center, center);
    height: 200px;

    .error {
      @include flex(row, flex-start, center);
      margin-bottom: 16px;
      color: $color-error;

      .label {
        margin-right: 8px;
      }

      .error-message {
        white-space: normal;
        word-break: break-all;
      }
    }
  }

  .result {
    .empty {
      @include flex(row, center, center);
      height: 200px;
      color: $color-icon;
    }
  }
</style>
