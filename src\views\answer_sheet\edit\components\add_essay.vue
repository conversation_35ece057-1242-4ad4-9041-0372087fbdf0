<template>
  <div class="add-essay">
    <Form :label-width="80">
      <FormItem label="大题号" class="ivu-form-item-required">
        <Select :model-value="topicCode" class="input-short" @on-change="changeTopicCode">
          <Option v-for="t in topicChineseCodes" :key="t.code" :value="t.code">{{ t.codeInChinese }}</Option>
        </Select>
      </FormItem>
      <FormItem label="大题名" class="ivu-form-item-required">
        <Input v-model="topicName" :disabled="isExistTopic" maxlength="20"></Input>
      </FormItem>
      <FormItem label="题号" class="ivu-form-item-required">
        <InputNumber
          v-model="questionCodeFrom"
          class="input-short"
          :min="1"
          :precision="0"
          @on-change="generateQuestions"
        ></InputNumber>
        <span>——</span>
        <InputNumber
          v-model="questionCodeTo"
          class="input-short"
          :min="1"
          :precision="0"
          @on-change="generateQuestions"
        ></InputNumber>
      </FormItem>
      <FormItem label="每题行数" class="ivu-form-item-required">
        <InputNumber v-model="rows" :min="1" :max="20" :precision="0" class="input-short"></InputNumber>
        <span style="margin-left: 2em">生成答题线</span>
        <i-switch v-model="showLine"></i-switch>
      </FormItem>
      <FormItem label="分数" class="ivu-form-item-required">
        <InputNumber v-model="score" :min="0" class="input-short" @on-change="changeScore"></InputNumber>
      </FormItem>
    </Form>
    <Table :columns="tableColumns" :data="questions" border></Table>
  </div>
</template>

<script>
  import { Input } from 'view-ui-plus'
  import TextButton from '@/components/text_button'

  import { mapGetters } from 'vuex'
  import SubjectiveQuestion from '@/helpers/emarking/subjective_question'
  import { intersection, groupArray } from '@/utils/array'
  import { roundScore } from '@/utils/math'
  import { randomId } from '@/utils/string'
  import { isPositiveInteger, isPositiveFinite, numberToChinese } from '@/utils/number'
  import BranchTypeEnum from '@/enum/qlib/branch_type'
  import { Max_Question_Code } from '@/const/emarking'

  export default {
    props: {
      branchTypeId: Number,
      topicChineseCodes: Array,
    },
    data() {
      return {
        topicCode: 1,
        topicName: '',
        questionCodeFrom: 1,
        questionCodeTo: null,
        score: null,
        rows: 3,
        showLine: true,
        questions: [],
      }
    },
    computed: {
      ...mapGetters('answerSheet', ['questionCodes', 'maxQuestionCode', 'topicCodeNames', 'maxTopicCode', 'subject']),
      isExistTopic() {
        return this.topicCodeNames.some(t => t.topicCode == this.topicCode)
      },
      questionsGroupByCode() {
        return groupArray(this.questions, q => q.questionCode).map(g => ({
          questionCode: g.key,
          branches: g.group,
        }))
      },
      tableColumns() {
        let scoreRender = (h, params) =>
          h(Input, {
            size: 'small',
            type: 'number',
            modelValue: params.row.score,
            min: 0,
            style: {
              width: '80px',
            },
            onOnChange: e => {
              this.handleChangeScore(params.row, e)
            },
          })

        let actionRender = (h, params) => {
          let btns = [
            h(
              TextButton,
              {
                type: 'primary',
                onClick: () => {
                  this.handleAddBranch(params.row)
                },
              },
              () => '加小题'
            ),
            h(
              TextButton,
              {
                type: 'warning',
                disabled: this.questions.filter(q => q.questionCode == params.row.questionCode).length <= 1,
                onClick: () => {
                  this.handleDeleteBranch(params.row)
                },
              },
              () => '删小题'
            ),
          ]
          return h('div', {}, btns)
        }

        let columns = [
          {
            title: '题号',
            align: 'center',
            key: 'fullCode',
          },
          {
            title: '分数',
            align: 'center',
            render: scoreRender,
          },
          {
            title: '操作',
            align: 'center',
            render: actionRender,
          },
        ]

        return columns
      },
    },
    created() {
      this.topicCode = this.maxTopicCode + 1
      this.topicName = numberToChinese(this.topicCode) + '、' + BranchTypeEnum.getNameById(this.branchTypeId)
      this.questionCodeFrom = this.maxQuestionCode + 1
      this.showLine = !['数学', '物理', '化学'].includes(this.subject.name)
    },
    methods: {
      changeTopicCode(newTopicCode) {
        let oldTopicCode = this.topicCode
        this.topicCode = newTopicCode
        if (this.isExistTopic) {
          this.topicName = this.topicCodeNames.find(t => t.topicCode == this.topicCode).topicName
        } else {
          let oldTopicCodeChinese = numberToChinese(oldTopicCode)
          let newTopicCodeChinese = numberToChinese(newTopicCode)
          this.topicName = this.topicName.replace(new RegExp(`^${oldTopicCodeChinese}、`), newTopicCodeChinese + '、')
        }
      },
      generateQuestions() {
        let questions = []
        if (
          isPositiveInteger(this.questionCodeFrom) &&
          this.questionCodeFrom <= Max_Question_Code &&
          isPositiveInteger(this.questionCodeTo) &&
          this.questionCodeTo <= Max_Question_Code &&
          this.questionCodeFrom <= this.questionCodeTo
        ) {
          for (let code = this.questionCodeFrom; code <= this.questionCodeTo; code++) {
            questions.push({
              fullCode: '',
              questionCode: code,
              branchCode: 1,
              score: this.score,
            })
          }
        }
        this.questions = questions
        this.updateFullCode()
      },
      updateFullCode() {
        this.questionsGroupByCode.forEach(({ questionCode, branches }) => {
          if (branches.length == 1) {
            branches[0].fullCode = `${questionCode}`
            branches[0].branchCode = 1
          } else {
            branches.forEach((b, idx) => {
              b.branchCode = idx + 1
              b.fullCode = `${b.questionCode}.${b.branchCode}`
            })
          }
        })
      },
      changeScore() {
        this.questionsGroupByCode.forEach(({ branches }) => {
          if (branches.length == 1) {
            branches[0].score = this.score
          }
        })
      },
      handleChangeScore(row, e) {
        this.questions.find(q => q.fullCode == row.fullCode).score = Number(e.target.value) || 0
      },
      handleAddBranch(row) {
        let index = this.questions.findIndex(q => q.fullCode == row.fullCode)
        let newQuestion = {
          fullCode: '',
          questionCode: row.questionCode,
          branchCode: row.branchCode + 1,
          score: row.score,
        }
        this.questions.splice(index + 1, 0, newQuestion)
        this.updateFullCode()
      },
      handleDeleteBranch(row) {
        if (this.questions.filter(q => q.questionCode == row.questionCode).length > 1) {
          let index = this.questions.findIndex(q => q.fullCode == row.fullCode)
          this.questions.splice(index, 1)
          this.updateFullCode()
        }
      },
      check() {
        if (!this.topicCode) {
          return {
            message: '请选择大题',
          }
        }
        if (!this.topicName) {
          return {
            message: '请输入大题名',
          }
        }
        if (this.questions.length == 0) {
          return {
            message: '请添加题目',
          }
        }
        let newQuestionCodes = this.questionsGroupByCode.map(g => g.questionCode)
        let intersections = intersection(newQuestionCodes, this.questionCodes)
        if (intersections.length > 0) {
          return {
            message: `以下题号已存在：${intersections.join('，')}`,
          }
        }

        let scoreInvalidBranchNames = []
        let subjectiveQuestions = []
        this.questionsGroupByCode.forEach(({ questionCode, branches }) => {
          branches.forEach(b => {
            let subj = new SubjectiveQuestion()
            subj.questionId = randomId()
            subj.questionCode = questionCode
            subj.questionName = `${questionCode}`
            subj.branchCode = branches.length > 1 ? b.branchCode : 0
            subj.branchName = branches.length > 1 ? `${questionCode}.${b.branchCode}` : `${questionCode}`
            subj.topicCode = this.topicCode
            subj.topicName = this.topicName
            subj.fullScore = roundScore(b.score)
            if (!subj.fullScore || !isPositiveFinite(subj.fullScore)) {
              scoreInvalidBranchNames.push(subj.branchName)
            }
            subjectiveQuestions.push(subj)
          })
        })

        if (scoreInvalidBranchNames.length > 0) {
          return {
            message: `请输入分数：${scoreInvalidBranchNames.join('、')}`,
          }
        }

        return {
          message: '',
          data: {
            topicCode: this.topicCode,
            topicName: this.topicName,
            subjectiveQuestions,
            rows: this.rows,
            showLine: this.showLine,
          },
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  :deep(.ivu-form-item) {
    margin-bottom: 10px;
  }

  .input-short {
    width: 100px;
  }
</style>
