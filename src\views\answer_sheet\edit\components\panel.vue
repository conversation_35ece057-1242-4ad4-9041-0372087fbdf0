<template>
  <div class="sheet-settings">
    <template v-if="isCoachBookYSH">
      <div class="setting-item setting-item-book-name">
        <div class="setting-item-title">书名</div>
        <Input
          class="setting-item-body"
          :model-value="coachBookInfoBookName"
          @on-change="$event => changeCoachBookInfoBookName($event.target.value)"
        ></Input>
      </div>
      <div class="setting-item setting-item-book-name">
        <div class="setting-item-title">章节名</div>
        <Input
          class="setting-item-body"
          :model-value="coachBookInfoChapterName"
          @on-change="$event => changeCoachBookInfoChapterName($event.target.value)"
        ></Input>
      </div>
      <div class="setting-item setting-item-book-name">
        <div class="setting-item-title">起始页码</div>
        <InputNumber
          class="setting-item-body"
          :model-value="coachBookInfoStartPage"
          @on-change="changeCoachBookInfoStartPage"
        ></InputNumber>
      </div>
      <div v-if="showChangeScoreType" class="setting-item setting-item-score-type">
        <div class="setting-item-title">
          <span>打分模式</span>
        </div>
        <RadioGroup class="setting-item-body" :model-value="scoreType" @on-change="changeScoreType">
          <Radio v-for="t in scoreTypeListForCoachBook" :key="t.id" class="width-50" :label="t.id">{{ t.name }} </Radio>
        </RadioGroup>
      </div>
    </template>
    <template v-else>
      <div v-if="showStageSubject && !isCoachBookIgrade" class="setting-item setting-item-stage-subject">
        <div class="setting-item-title">
          <span>学段学科</span>
          <span>
            <span class="stage-subject-name">{{ stageSubjectName }}</span>
            <TextButton icon="md-create" type="primary" @click="changeStageSubject"></TextButton>
          </span>
        </div>
      </div>
      <div class="setting-item setting-item-columns">
        <div class="setting-item-title">布局</div>
        <RadioGroup class="setting-item-body" :model-value="sizeId" @on-change="confirmChangeSize">
          <Radio v-for="s in pageSizeList" :key="s.id" class="width-50" :label="s.id">{{ s.name }} </Radio>
        </RadioGroup>
      </div>
      <div v-if="isPrimaryStage" class="setting-item setting-item-base-style">
        <div class="setting-item-title">基础字号</div>
        <RadioGroup class="setting-item-body" :model-value="baseFontSize" @on-change="confirmChangeBaseFontSize">
          <Radio class="width-33" label="14px">14</Radio><Radio class="width-33" label="16px">16</Radio
          ><!--
          --><Radio class="width-33" label="17px">小四</Radio>
        </RadioGroup>
      </div>
      <div class="setting-item setting-item-ticket-number">
        <div class="setting-item-title">准考号</div>
        <div class="setting-item-body">
          <RadioGroup
            class="ticket-number-type"
            :model-value="ticketNumberType"
            @on-change="handleChangeTicketNumberType"
          >
            <Radio
              v-for="t in ticketNumberTypeList"
              :key="t.id"
              :label="t.id"
              :disabled="t.disabled"
              class="width-33"
              >{{ t.name }}</Radio
            >
          </RadioGroup>
          <div class="ticket-number-length-orientation">
            <span class="ticket-number-length width-33">
              <Select
                :model-value="ticketNumberLength"
                size="small"
                class="select-ticket-number-length"
                transfer
                @on-change="changeTicketNumberLength"
              >
                <Option v-for="n in ticketNumberLengthOptionList" :key="n" :value="n">{{ n }}</Option>
              </Select>
              <span>位</span></span
            ><!--
            --><RadioGroup
              v-if="isTicketNumberTypeOmr"
              class="ticket-number-orientation width-33"
              :model-value="Number(ticketNumberOmrHorizontal)"
              type="button"
              size="small"
              @on-change="changeTicketNumberOmrHorizontal(Boolean($event))"
            >
              <Radio :label="0">竖直</Radio>
              <Radio :label="1">水平</Radio>
            </RadioGroup>
          </div>
        </div>
      </div>
      <div v-if="!isCoachBookIgrade" class="setting-item">
        <div class="setting-item-title">考生填写</div>
        <CheckboxGroup class="setting-item-body" :model-value="stuInfoLabels" @on-change="checkAndChangeStuInfoLabels">
          <Checkbox
            v-for="infoItem in studentInfoList"
            :key="infoItem.id"
            class="width-33"
            :label="infoItem.name"
            :disabled="['准考号', '姓名'].includes(infoItem.name)"
            style="line-height: 24px"
          ></Checkbox>
        </CheckboxGroup>
      </div>
      <!-- <div class="setting-item qrcode">
      <div class="setting-item-title">
        <Tooltip placement="top-start">
          <span>显示答题卡二维码<Icon type="ios-help-circle"></Icon></span>
          <template #content>
            <div>扫描时系统会检查此二维码，<br />防止扫错答题卡</div>
          </template>
        </Tooltip>
        <i-switch :model-value="showQrcode" @on-change="handleChangeShowQrcode"></i-switch>
      </div>
    </div> -->
      <div v-if="!isCoachBookIgrade" class="setting-item cautions">
        <div class="setting-item-title">
          <span>显示注意事项</span>
          <i-switch :model-value="showCautions" @on-change="changeShowCautions"></i-switch>
        </div>
      </div>
      <div v-if="!isCoachBookIgrade" class="setting-item missing">
        <div class="setting-item-title">
          <span>显示缺考标记</span>
          <i-switch :model-value="showMissingMark" @on-change="changeShowMissingMark"></i-switch>
        </div>
      </div>
      <div v-if="isFromPaper" class="setting-item show-question-content">
        <div class="setting-item-title">
          <span>显示题目内容</span>
          <i-switch :model-value="showQuestionContent" @on-change="confirmChangeShowQuestionContent"></i-switch>
        </div>
      </div>
      <div v-if="showQuestionContent" class="setting-item setting-item-objectives-position">
        <div class="setting-item-title">
          <span>客观题填涂集中排在顶部</span>
          <i-switch :model-value="!objectivesSeparate" @on-change="changeObjectivesSeparate"></i-switch>
        </div>
      </div>
      <div v-if="showChangeMode && !isCoachBookIgrade" class="setting-item setting-item-mode">
        <div class="setting-item-title">答题卡模式</div>
        <RadioGroup class="setting-item-body" :model-value="sheetMode" @on-change="confirmChangeMode">
          <Radio v-for="m in modeList" :key="m.id" class="width-50" :label="m.id">{{ m.name }} </Radio>
        </RadioGroup>
      </div>
      <div v-if="showChangeScoreType && !isCoachBookIgrade" class="setting-item setting-item-score-type">
        <div class="setting-item-title">
          <span>打分模式</span>
          <TextButton type="primary" icon="md-information-circle" @click="showModalScoreTip = true"
            >先阅后扫打分规范</TextButton
          >
        </div>
        <RadioGroup class="setting-item-body" :model-value="scoreType" @on-change="changeScoreType">
          <Radio v-for="t in scoreTypeList" :key="t.id" class="width-50" :label="t.id" :disabled="t.disabled"
            >{{ t.name }}
          </Radio>
        </RadioGroup>
      </div>
    </template>
    <div v-if="enableEditQuestion" class="setting-item setting-item-add-question">
      <div class="setting-item-title">添加题目</div>
      <div class="setting-item-body">
        <div
          v-for="t in branchTypeList"
          :key="t.id"
          class="btn-add"
          :class="{ disabled: t.disabled }"
          @click="handleBtnAddQuestionClick(t)"
        >
          <Icon class="btn-icon" type="md-add"></Icon>
          <span class="btn-text">{{ t.name }}</span>
        </div>
      </div>
    </div>
    <div v-if="isCoachBookIgrade" class="setting-item setting-item-page-number">
      <div class="setting-item-title">页码</div>
      <div class="setting-item-body">
        <Input
          v-model="copyPageFooterContent"
          placeholder="页码格式，如：第 {{page}} 页，共 {{total}} 页"
          @on-blur="changePageFooterContent"
        ></Input>
      </div>
    </div>
    <div v-if="isCoachBookIgrade && isPrimaryStage" class="setting-item setting-item-actions">
      <div class="setting-item-title">其他操作</div>
      <div class="setting-item-body">
        <Button @click="addTriangle">添加选择题括号内三角形</Button>
        <Button style="margin-top: 10px" @click="removeTriangle">移除选择题括号内三角形</Button>
      </div>
    </div>
    <div v-if="showSettingMask" class="setting-mask"></div>

    <ModalAddQuestion v-model="showModalAddQuestion" :branch-type-id="addQuestionBranchTypeId"></ModalAddQuestion>

    <ModalScoreTipSimple v-model="showModalScoreTip"></ModalScoreTipSimple>

    <Modal v-model="showModalStageSubject" title="请选择学段学科" :closable="false">
      <div class="stage-subject-form">
        <div class="form-item">
          <div class="form-item-label">学段</div>
          <ComRadioGroup v-model="stageId" :radioes="stageSubjects"></ComRadioGroup>
        </div>
        <div class="form-item">
          <div class="form-item-label">学科</div>
          <ComRadioGroup v-model="subjectId" :radioes="selectedStageSubjects"></ComRadioGroup>
        </div>
      </div>
      <template #footer>
        <Button v-if="hasStageSubject" type="text" @click="showModalStageSubject = false">取消</Button>
        <Button v-else type="text" @click="exitEditPage">取消并退出编辑</Button>
        <Button type="primary" :disabled="!selectedStage || !selectedSubject" @click="handleModalStageSubjectOK"
          >确定</Button
        >
      </template>
    </Modal>
  </div>
</template>

<script>
  import ModalAddQuestion from './modal_add_question'
  import ModalScoreTipSimple from '@/components/modal_post_scan_score_tip_simple'
  import ComRadioGroup from '@/components/radio_group'

  import { mapGetters, mapMutations } from 'vuex'
  import { sleep } from '@/utils/promise'
  import BranchTypeEnum from '@/enum/qlib/branch_type'
  import PageSizeEnum from '@/enum/answer_sheet/page_size'
  import TicketNumberTypeEnum from '@/enum/answer_sheet/ticket_number_type'
  import StudentInfoEnum from '@/enum/answer_sheet/student_info'
  import ModeEnum from '@/enum/answer_sheet/mode'
  import ScoreTypeEnum from '@/enum/answer_sheet/score_type'
  import QuestionGroupTypeEnum from '@/enum/answer_sheet/question_group_type'
  import TrueFalseTypeEnum from '@/enum/answer_sheet/true_false_type'
  import { MaxTicketNumberLength, MinTicketNumberLength } from '@/const/answer_sheet'

  export default {
    components: {
      ModalAddQuestion,
      ModalScoreTipSimple,
      ComRadioGroup,
    },
    props: {
      beforeSave: Boolean,
    },
    emits: ['back'],
    data() {
      return {
        // 选择学段学科
        showModalStageSubject: false,
        stageId: 0,
        subjectId: 0,
        // 页面尺寸
        sizeId: PageSizeEnum.A4_1.id,
        // 基础字号
        baseFontSize: 14,
        // 显示题目内容
        showQuestionContent: false,
        // 答题卡模式
        sheetMode: ModeEnum.Online.id,
        // 添加题目
        showModalAddQuestion: false,
        addQuestionBranchTypeId: 0,
        // 打分规范说明
        showModalScoreTip: false,
        // 页码
        copyPageFooterContent: '',
      }
    },
    computed: {
      ...mapGetters('answerSheet', [
        'pageSizeId',
        'baseStyle',
        'ticketNumberType',
        'ticketNumberLength',
        'ticketNumberOmrHorizontal',
        'stuInfoLabels',
        'topics',
        'pages',
        'mode',
        'isPostScan',
        'from',
        'isFromManual',
        'isFromPaper',
        'isFromExam',
        'scoreType',
        'enableEditQuestion',
        'showQrcode',
        'qrcodeImageSrc',
        'showCautions',
        'showMissingMark',
        'hasAiScoreQuestion',
        'stage',
        'subject',
        'sheetId',
        'isCoachBookYSH',
        'isCoachBookIgrade',
        'coachBookInfoBookName',
        'coachBookInfoChapterName',
        'coachBookInfoStartPage',
        'objectivesSeparate',
        'pageFooterContent',
      ]),
      ...mapGetters('user', ['isSystemSchoolAdministrator', 'isQuestionLibrarySystemUser']),
      isSystemUserOrEditor() {
        return this.isSystemSchoolAdministrator || this.isQuestionLibrarySystemUser
      },
      showStageSubject() {
        return this.beforeSave && this.isFromManual
      },
      hasStageSubject() {
        return this.stage.id && this.subject.id
      },
      isPrimaryStage() {
        return this.stage.id == 1
      },
      stageSubjectName() {
        return (this.stage.name || '') + (this.subject.name || '')
      },
      /**
       * 创建新卡选择学段学科相关
       */
      stageSubjects() {
        return this.$store.getters['school/schoolStageSubjects'].map(stage => ({
          id: stage.stageId,
          name: stage.stageName,
          subjects: stage.subjects.map(subj => ({
            id: subj.subjectId,
            name: subj.subjectName,
          })),
        }))
      },
      selectedStage() {
        return this.stageSubjects.find(s => s.id == this.stageId)
      },
      selectedStageSubjects() {
        return (this.selectedStage && this.selectedStage.subjects) || []
      },
      selectedSubject() {
        return this.selectedStageSubjects.find(s => s.id == this.subjectId)
      },
      pageSizeList() {
        let list = PageSizeEnum.getEntries()
        // 仅同级生教辅小学答题卡支持正三栏反两栏
        if (!(this.isCoachBookIgrade && this.isPrimaryStage)) {
          list = list.filter(entry => entry.id != PageSizeEnum.A3_3_2.id)
        }
        return list
      },
      ticketNumberTypeList() {
        let entries = TicketNumberTypeEnum.getEntries()
        entries.forEach(entry => {
          entry.disabled = entry.id == TicketNumberTypeEnum.Handwriting.id && !this.isSystemUserOrEditor
        })
        if (this.isCoachBookIgrade) {
          entries = entries.filter(entry => entry.id == TicketNumberTypeEnum.Handwriting.id)
        }
        return entries
      },
      ticketNumberLengthOptionList() {
        let list = []
        for (let i = MinTicketNumberLength; i <= MaxTicketNumberLength; i++) {
          list.push(i)
        }
        return list
      },
      isTicketNumberTypeOmr() {
        return this.ticketNumberType == TicketNumberTypeEnum.Omr.id
      },
      studentInfoList() {
        return StudentInfoEnum.getEntries()
      },
      showChangeMode() {
        return this.isFromManual || this.isFromPaper
      },
      modeList() {
        return ModeEnum.getEntries()
      },
      showChangeScoreType() {
        return this.mode == ModeEnum.PostScan.id
      },
      scoreTypeList() {
        return [
          {
            id: ScoreTypeEnum.Bar.id,
            name: ScoreTypeEnum.Bar.name,
            disabled: false,
          },
          // {
          //   id: ScoreTypeEnum.Number.id,
          //   name: ScoreTypeEnum.Number.name,
          //   disabled: !this.isSystemUserOrEditor,
          // },
        ]
      },
      scoreTypeListForCoachBook() {
        return [
          {
            id: ScoreTypeEnum.Bar.id,
            name: ScoreTypeEnum.Bar.name,
          },
          {
            id: ScoreTypeEnum.TrueFalse.id,
            name: ScoreTypeEnum.TrueFalse.name,
          },
        ]
      },
      branchTypeList() {
        let entries = BranchTypeEnum.getEntries()
        entries.forEach(entry => {
          entry.disabled = this.isPostScan && entry.id == BranchTypeEnum.FillBlank.id && !this.isSystemUserOrEditor
        })
        if (this.subject.name != '语文' && this.subject.name != '日语') {
          entries = entries.filter(entry => entry.name != '语文作文')
        }
        if (this.subject.name != '英语') {
          entries = entries.filter(entry => entry.name != '英语作文')
        }
        return entries
      },
      showSettingMask() {
        return this.pages.length === 0
      },
    },
    watch: {
      // 手动创建答题卡后设置学段学科
      sheetId(newSheetId, oldSheetId) {
        if (newSheetId && !oldSheetId && this.isFromManual && !this.hasStageSubject && this.beforeSave) {
          this.changeStageSubject()
        }
      },
      // 导入答题卡后刷新布局、模式
      topics() {
        this.refreshData()
      },
      pageFooterContent() {
        this.copyPageFooterContent = this.pageFooterContent
      },
    },
    created() {
      this.refreshData()
    },
    methods: {
      ...mapMutations('answerSheet', [
        'changeSize',
        'changeBaseStyle',
        'changeTicketNumberType',
        'changeTicketNumberLength',
        'changeTicketNumberOmrHorizontal',
        'changeStuInfoLabels',
        'changeModeToPostScan',
        'changeModeToOnline',
        'changeAllScoreType',
        'changeShowQrcode',
        'changeShowCautions',
        'changeShowMissingMark',
        'displayQuestionContent',
        'hideQuestionContent',
        'changeCoachBookInfoBookName',
        'changeCoachBookInfoChapterName',
        'changeCoachBookInfoStartPage',
        'addContentObjectiveTriangle',
        'removeContentObjectiveTriangle',
        'mergeObjectiveTopics',
        'splitObjectiveTopics',
        'generatePages',
      ]),
      changeStageSubject() {
        if (this.hasStageSubject) {
          this.stageId = this.stage.id
          this.subjectId = this.subject.id
        } else {
          this.stageId = 0
          this.subjectId = 0
          // 默认选中任教学段学科
          let userTeachings = this.$store.state.user.teachings
          if (userTeachings.length > 0) {
            if (this.stageSubjects.some(s => s.id == userTeachings[0].stageId)) {
              this.stageId = userTeachings[0].stageId
            }
            if (this.selectedStageSubjects.some(s => s.id == userTeachings[0].subjectId)) {
              this.subjectId = userTeachings[0].subjectId
            }
          }
        }
        this.showModalStageSubject = true
      },
      async handleModalStageSubjectOK() {
        if (!this.selectedStage || !this.selectedSubject) {
          this.$Message.warning({
            content: '请选择学段学科',
          })
          return
        }
        this.showModalStageSubject = false
        this.$store.commit('answerSheet/changeStage', {
          id: this.selectedStage.id,
          name: this.selectedStage.name,
        })
        this.$store.commit('answerSheet/changeSubject', {
          id: this.selectedSubject.id,
          name: this.selectedSubject.name,
        })
        this.$store.commit('answerSheet/changeTitle', `${this.selectedStage.name}${this.selectedSubject.name}答题卡`)
      },
      exitEditPage() {
        this.showModalStageSubject = false
        this.$emit('back')
      },

      refreshData() {
        this.sizeId = this.pageSizeId
        this.baseFontSize = this.baseStyle.fontSize
        this.sheetMode = this.mode
        this.showQuestionContent = this.$store.getters['answerSheet/showQuestionContent']
        this.copyPageFooterContent = this.pageFooterContent
      },
      confirmChangeSize(sizeId) {
        let oldSizeId = this.sizeId
        this.sizeId = sizeId
        this.$Modal.confirm({
          title: '切换布局',
          content: '切换布局将引起答题卡版式变化，是否确定？',
          onCancel: () => {
            this.sizeId = oldSizeId
          },
          onOk: () => {
            this.changeSize(sizeId)
            // 页面尺寸变化时各页面块会发出高度变化事件，此处无需调用generatePages
          },
        })
      },
      confirmChangeBaseFontSize(fontSize) {
        let oldFontSize = this.baseFontSize
        this.baseFontSize = fontSize
        this.$Modal.confirm({
          title: '修改基础字号',
          content: '修改基础字号将引起答题卡版式变化，是否确定？',
          onCancel: () => {
            this.baseFontSize = oldFontSize
          },
          onOk: () => {
            this.changeBaseStyle({ fontSize })
            // 基础样式变化时各页面块会发出高度变化事件，此处无需调用generatePages
          },
        })
      },
      confirmChangeShowQuestionContent(value) {
        let oldValue = this.showQuestionContent
        this.showQuestionContent = value
        if (this.isPostScan && !this.isSystemUserOrEditor) {
          this.$Message.info({
            content: '请先转为线上阅卷模式',
            duration: 3,
            closable: true,
          })
          setTimeout(() => {
            this.showQuestionContent = oldValue
          }, 0)
          return
        }
        let title = value ? '显示题目内容' : '隐藏题目内容'
        this.$Modal.confirm({
          title,
          content: '将重置答题卡内容，是否确定？',
          onOk: async () => {
            await sleep(0)
            this.$Spin.show(h => {
              h(
                'span',
                {
                  style: {
                    fontSize: '24px',
                  },
                },
                `正在${title}，请稍等`
              )
            })
            try {
              await sleep(0)
              if (value) {
                await this.$store.dispatch('answerSheet/preProcessPaper')
                this.displayQuestionContent()
              } else {
                this.hideQuestionContent()
              }
              this.generatePages()
              await sleep(100)
            } finally {
              this.$Spin.hide()
            }
          },
          onCancel: () => {
            this.showQuestionContent = oldValue
          },
        })
      },
      confirmChangeMode(mode) {
        let oldMode = this.sheetMode
        this.sheetMode = mode
        if (this.hasAiScoreQuestion) {
          this.$Message.warning({
            content: '存在智能评分题目，无法切换为先阅后扫模式',
            duration: 5,
            closable: true,
          })
          setTimeout(() => {
            this.sheetMode = oldMode
          }, 100)
          return
        }
        this.$Modal.confirm({
          title: '切换模式',
          content: '切换模式将重置已编辑的内容，是否确定？',
          onCancel: () => {
            this.sheetMode = oldMode
          },
          onOk: () => {
            if (mode == ModeEnum.Online.id) {
              this.changeModeToOnline()
              this.generatePages()
            } else if (mode == ModeEnum.PostScan.id) {
              if (!this.isSystemUserOrEditor) {
                this.topics.forEach(topic => {
                  topic.questionGroups.forEach(g => {
                    if (g.type == QuestionGroupTypeEnum.FillBlank.id) {
                      this.$store.commit('answerSheet/changeQuestionGroupType', {
                        questionGroupId: g.id,
                        type: QuestionGroupTypeEnum.Essay.id,
                        rows: 1,
                        showLine: !this.showQuestionContent,
                      })
                    }
                  })
                })
              }
              this.changeModeToPostScan()
              this.generatePages()
            }
          },
        })
      },
      changeScoreType(scoreTypeId) {
        this.changeAllScoreType(scoreTypeId)
        if (scoreTypeId == ScoreTypeEnum.Number.id || scoreTypeId == ScoreTypeEnum.TrueFalse.id) {
          this.$Modal.info({
            title: `${scoreTypeId == ScoreTypeEnum.Number.id ? '手写打分框' : '对错框'}可能压住题目内容`,
            content: '<strong><br>请您检查并调整题目内容！</strong>',
            okText: '知道了',
          })
        }
      },
      handleChangeShowQrcode(value) {
        this.changeShowQrcode(value)
        if (!this.qrcodeImageSrc) {
          this.$store.dispatch('answerSheet/generateQrcodeImage')
        }
      },
      handleBtnAddQuestionClick(branchType) {
        if (branchType.disabled) {
          return
        }
        this.addQuestionBranchTypeId = branchType.id
        this.showModalAddQuestion = true
      },
      handleChangeTicketNumberType(type) {
        this.changeTicketNumberType(type)
        if (type != TicketNumberTypeEnum.Barcode.id) {
          this.changeStuInfoLabels(this.stuInfoLabels.filter(label => label != '准考号'))
        } else if (!this.stuInfoLabels.includes('准考号')) {
          let labels = this.stuInfoLabels.slice()
          labels.unshift('准考号')
          this.changeStuInfoLabels(labels)
        }
      },
      checkAndChangeStuInfoLabels(labels) {
        // if (labels.length > 4) {
        //   this.$Message.warning({
        //     content: '考生填写部分最多支持 4 项',
        //     duration: 5,
        //     closable: true,
        //   })
        //   return
        // }
        this.changeStuInfoLabels(labels)
      },
      changeShowCautionsAndMissingMark(value) {
        this.changeShowCautions(value)
        this.changeShowMissingMark(value)
      },
      addTriangle() {
        this.addContentObjectiveTriangle()
        this.generatePages()
      },
      removeTriangle() {
        this.removeContentObjectiveTriangle()
        this.generatePages()
      },
      changeObjectivesSeparate(value) {
        let params = {
          groupLength: 5,
          trueOrFalseType: this.subject.id == 3 ? TrueFalseTypeEnum.TF.id : TrueFalseTypeEnum.TC.id,
        }
        if (value) {
          this.mergeObjectiveTopics(params)
        } else {
          this.splitObjectiveTopics(params)
        }
        this.generatePages()
      },
      changePageFooterContent() {
        // 检查格式
        if (!this.copyPageFooterContent.includes('{{page}}') || !this.copyPageFooterContent.includes('{{total}}')) {
          this.$Modal.warning({
            title: '页码格式不正确',
            content: '请包含{{page}}及{{total}}',
            okText: '知道了',
            onOk: () => {
              this.copyPageFooterContent = this.pageFooterContent
            },
          })
          return
        }
        this.$store.commit('answerSheet/changePageFooterContent', this.copyPageFooterContent)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .sheet-settings {
    position: relative;
    flex-grow: 0;
    flex-shrink: 0;
    width: 320px;
    max-height: calc(100vh - 135px);
    margin-left: 16px;
    padding: 10px 16px 20px 16px;
    overflow-y: auto;
    background-color: white;
    user-select: none;

    .setting-mask {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 1;
    }

    .setting-item {
      &:not(:first-child) {
        margin-top: 10px;
      }

      .setting-item-title {
        @include flex(row, space-between, center);
        color: $color-primary-dark;
        line-height: 30px;
      }

      .setting-item-body {
        width: 100%;

        .width-50 {
          display: inline-block;
          min-width: calc(50% - 1px);
          margin-right: 0;
          margin-left: 0;
        }

        .width-33 {
          display: inline-block;
          min-width: calc(33.33% - 1px);
          margin-right: 0;
          margin-left: 0;
        }
      }
    }

    .setting-item-ticket-number .setting-item-body {
      .ticket-number-type {
        width: 100%;
      }

      .ticket-number-length-orientation {
        margin-top: 10px;
      }

      .select-ticket-number-length {
        width: 50px;
        margin-right: 4px;
      }
    }

    .setting-item-add-question .setting-item-body {
      display: grid;
      grid-template-columns: 90px 90px 90px;
      justify-content: space-between;
      padding-top: 7px;

      .btn-add {
        width: 84px;
        height: 32px;
        margin-bottom: 10px;
        border: 1px solid $color-border;
        border-radius: 3px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;

        &:hover {
          border-color: $color-primary;
          color: white;
          background-color: $color-primary;
        }

        &.disabled {
          color: $color-disabled;
          cursor: not-allowed;

          &:hover {
            border-color: $color-border;
            color: $color-disabled;
            background-color: transparent;
          }
        }

        .btn-text {
          margin-left: -2px;
        }
      }
    }

    .stage-subject-name {
      color: $color-content;
    }
  }

  .stage-subject-form {
    .form-item {
      @include flex(row, flex-start, flex-start);

      &:not(:first-child) {
        margin-top: 16px;
      }

      .form-item-label {
        flex-grow: 0;
        flex-shrink: 0;
        width: 50px;
        color: $color-icon;
        line-height: 24px;
      }
    }
  }
</style>
