import ajax from '@/api/ajax'

export function apiFindStudentByAdmissionNum(params) {
  return ajax.get({
    url: 'fbscan/exception/findStudentByAdmissionNum',
    params: {
      examSubjectId: params.examSubjectId,
      admissionNum: params.admissionNum,
    },
    requestName: '由准考号查找考生',
  })
}

export function apiFindStudentsByStudentName(params) {
  return ajax.get({
    url: 'fbscan/exception/findStudentsByStudentName',
    params: {
      examSubjectId: params.examSubjectId,
      studentName: params.studentName,
    },
    requestName: '由姓名查找考生',
  })
}
