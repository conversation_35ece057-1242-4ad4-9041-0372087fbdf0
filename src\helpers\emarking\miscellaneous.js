export function splitQuestionScore(str) {
  if (typeof str !== 'string') {
    return []
  }

  str = str.trim().replace('；', ';')

  let questionScoreStrs = str.split(';').filter(Bo<PERSON>an)

  let list = []

  for (let qsStr of questionScoreStrs) {
    let arr = qsStr.split('=')
    if (arr.length !== 2) {
      throw new Error({
        code: 1,
        msg: '拆分题目分数出错',
      })
    }
    let name = arr[0],
      score = Number(arr[1])

    if (name && score >= 0) {
      list.push({
        name,
        score,
      })
    } else {
      throw new Error({
        code: 1,
        msg: '拆分题目分数出错',
      })
    }
  }

  return list
}

export function sortQuestionFunction(a, b) {
  if (a.questionCode < b.questionCode) {
    return -1
  } else if (a.questionCode > b.questionCode) {
    return 1
  } else {
    return (a.branchCode || 0) - (b.branchCode || 0)
  }
}

export function getBlockQuestionsDefine(branches) {
  return (branches || []).map(q => ({
    questionCode: q.code,
    questionName: q.name,
    fullScore: q.fullScore,
    minScore: q.minScore || 0,
    scoreInterval: q.scoreInterval,
  }))
}

export function getFullCode(questionCode, branchCode) {
  if (questionCode == null) {
    return ''
  }
  return branchCode > 0 ? `${questionCode}.${branchCode}` : `${questionCode}`
}

export function getQuestionFullCode(question) {
  let { questionCode, branchCode } = question || {}
  return getFullCode(questionCode, branchCode)
}
