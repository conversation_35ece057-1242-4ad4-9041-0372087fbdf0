<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 427318971">
<g id="Group 427318840">
<g id="Frame 1000006547">
<rect id="Rectangle 346240781" x="3.5" y="4" width="17" height="19" rx="1.54545" fill="url(#paint0_linear_2271_8074)"/>
<g id="Frame 1000006651">
<g id="Frame 1000006541">
<rect width="7" height="1.5" rx="0.75" transform="matrix(1 -8.74228e-08 -8.74228e-08 -1 5.5 15.5)" fill="white"/>
</g>
<g id="Frame 1000006540">
<rect width="10" height="1.5" rx="0.75" transform="matrix(1 -8.74228e-08 -8.74228e-08 -1 5.5 18)" fill="white"/>
</g>
<g id="Frame 1000006539">
<rect width="12" height="1.5" rx="0.75" transform="matrix(1 -8.74228e-08 -8.74228e-08 -1 5.5 20.5)" fill="white"/>
</g>
</g>
<g id="Ellipse 210" filter="url(#filter0_b_2271_8074)">
<circle cx="12.5" cy="6" r="5" fill="url(#paint1_linear_2271_8074)" fill-opacity="0.4"/>
<circle cx="12.5" cy="6" r="4.45" stroke="url(#paint2_linear_2271_8074)" stroke-width="1.1"/>
</g>
<path id="Vector" d="M13.7382 4.20044C13.6889 4.20083 13.6405 4.21419 13.598 4.23918C13.5554 4.26416 13.5202 4.29989 13.4958 4.34279C13.4714 4.38568 13.4587 4.43424 13.459 4.48358C13.4593 4.53292 13.4726 4.58132 13.4975 4.62392C13.7003 4.97976 13.8042 5.38339 13.7985 5.79294C13.7896 6.20289 13.6694 6.60269 13.4507 6.94955C13.4099 7.01225 13.3953 7.08842 13.41 7.16176C13.4247 7.23509 13.4675 7.29976 13.5293 7.34191C13.5933 7.38025 13.6694 7.39332 13.7426 7.37856C13.8159 7.361 13.8794 7.31545 13.9195 7.25166C14.193 6.81899 14.3433 6.31994 14.3543 5.80822C14.3626 5.29679 14.2324 4.79264 13.9773 4.34926C13.9541 4.30797 13.9209 4.27317 13.8807 4.24801C13.8405 4.22286 13.7947 4.20817 13.7474 4.20527L13.7382 4.20044ZM14.7098 3.64203C14.6604 3.64243 14.612 3.65579 14.5695 3.68077C14.5269 3.70575 14.4917 3.74149 14.4673 3.78438C14.4429 3.82728 14.4303 3.87583 14.4306 3.92517C14.4308 3.97452 14.4441 4.02291 14.469 4.06551C14.7777 4.59757 14.9347 5.20406 14.923 5.81909C14.9112 6.43412 14.7311 7.03416 14.4023 7.55403C14.3754 7.59588 14.3599 7.64407 14.3575 7.69377C14.3551 7.74347 14.3657 7.79294 14.3885 7.83721C14.4112 7.88149 14.4451 7.919 14.4869 7.94601C14.5287 7.97301 14.5769 7.98855 14.6266 7.99106C14.6763 7.99215 14.7254 7.9803 14.7691 7.95665C14.8128 7.93301 14.8495 7.8984 14.8758 7.85623C15.2588 7.24989 15.4685 6.5503 15.4822 5.83327C15.4959 5.11625 15.3131 4.40915 14.9536 3.78861C14.93 3.74441 14.8949 3.70742 14.8519 3.68161C14.809 3.65579 14.7599 3.64212 14.7098 3.64203ZM12.627 3.06013C12.5303 3.05905 12.437 3.09565 12.3668 3.16218L11.2291 4.25716C11.2065 4.2807 11.1791 4.2992 11.1488 4.31144C11.1185 4.32369 11.086 4.32942 11.0533 4.32825L10.4254 4.31624C10.201 4.31257 9.98434 4.39785 9.82268 4.55344C9.66102 4.70903 9.56753 4.92229 9.56262 5.14661L9.54101 6.27689C9.53734 6.50123 9.62262 6.7179 9.77821 6.87956C9.9338 7.04122 10.1471 7.13472 10.3714 7.13962L10.9993 7.15163C11.0324 7.15138 11.0651 7.1582 11.0954 7.17161C11.1256 7.18503 11.1526 7.20474 11.1746 7.22943L12.2719 8.36947C12.3218 8.42103 12.3859 8.45664 12.456 8.47179C12.5262 8.48694 12.5992 8.48096 12.666 8.4546C12.7327 8.42823 12.7901 8.38267 12.831 8.32367C12.8718 8.26468 12.8943 8.19489 12.8955 8.12314L12.9852 3.42992C12.986 3.35689 12.9649 3.28529 12.9246 3.22439C12.8843 3.16348 12.8266 3.11606 12.7591 3.08825C12.7149 3.07011 12.6678 3.05977 12.6201 3.05767L12.627 3.06013ZM10.382 6.58378C10.3443 6.58308 10.307 6.57479 10.2725 6.55941C10.2381 6.54402 10.207 6.52186 10.1813 6.49423C10.1556 6.4666 10.1356 6.43408 10.1227 6.3986C10.1098 6.36312 10.1042 6.3254 10.1062 6.28769L10.1278 5.15741C10.1285 5.12046 10.1364 5.084 10.1512 5.05013C10.166 5.01626 10.1874 4.98563 10.214 4.96C10.2406 4.93437 10.272 4.91424 10.3065 4.90075C10.3409 4.88726 10.3776 4.88068 10.4146 4.88138L11.0425 4.89339C11.2587 4.89976 11.4685 4.8201 11.626 4.67189L12.4129 3.91453L12.3418 7.63562L11.5823 6.83938C11.5076 6.76315 11.4186 6.7024 11.3204 6.6606C11.2221 6.6188 11.1167 6.59678 11.0099 6.59579L10.382 6.58378Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_b_2271_8074" x="1.21429" y="-5.28571" width="22.5714" height="22.5714" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="3.14286"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2271_8074"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_2271_8074" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2271_8074" x1="0.483872" y1="3.49395" x2="5.37287" y2="24.4105" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEC57D"/>
<stop offset="0.623338" stop-color="#F16018"/>
</linearGradient>
<linearGradient id="paint1_linear_2271_8074" x1="10.2273" y1="2.36364" x2="16.2317" y2="9.04986" gradientUnits="userSpaceOnUse">
<stop stop-color="#F1611A"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_2271_8074" x1="15.2273" y1="11.4545" x2="14.4732" y2="0.319477" gradientUnits="userSpaceOnUse">
<stop offset="0.0314633" stop-color="white"/>
<stop offset="0.643139" stop-color="white" stop-opacity="0.54"/>
<stop offset="1" stop-color="#FFE391" stop-opacity="0.2"/>
</linearGradient>
</defs>
</svg>
