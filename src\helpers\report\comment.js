/**
 * canvas绘制评卷批注
 * 待合并至student_paper_draw_helper
 */

import tickSrc from '@/assets/images/common/tick.svg'
import tickGreenSrc from '@/assets/images/common/tick_green.svg'
import crossSrc from '@/assets/images/common/cross.svg'
import { loadImage } from '@/utils/promise'

export async function drawBlockComments(ctx, comments, offsetX, offsetY) {
  ctx.fillStyle = 'red'
  ctx.strokeStyle = 'red'
  for (let c of comments) {
    if (c.type == 'tick' || c.type == 'cross') {
      try {
        let img = await loadImage(c.type == 'tick' ? tickSrc : crossSrc)
        ctx.drawImage(img, c.x + offsetX, c.y + offsetY, c.width, c.height)
      } catch (err) {
        // do nothing
      }
    } else if (c.type == 'rect') {
      ctx.lineWidth = c.strokeWidth
      ctx.strokeRect(c.x + offsetX, c.y + offsetY, c.width, c.height)
    } else if (c.type == 'path') {
      ctx.lineWidth = c.strokeWidth
      ctx.beginPath()
      ctx.moveTo(c.points[0][0] + offsetX, c.points[0][1] + offsetY)
      for (let i = 1; i < c.points.length; i++) {
        let point = c.points[i]
        ctx.lineTo(point[0] + offsetX, point[1] + offsetY)
      }
      ctx.stroke()
    } else if (c.type == 'text') {
      ctx.font = `${c.fontSize}px sans-serif`
      ctx.textBaseline = 'top'
      ctx.fillText(c.content, c.x + offsetX, c.y + offsetY)
    }
  }
}

export async function drawSingleBlockComments(ctx, comments = [], offsetX = 0, offsetY = 0) {
  ctx.fillStyle = 'red'
  ctx.strokeStyle = 'red'
  for (let c of comments) {
    if (c.type == 'tick' || c.type == 'cross') {
      try {
        let img = await loadImage(c.type == 'tick' ? tickGreenSrc : crossSrc)
        ctx.drawImage(img, c.x + c.width - c.iconWidth, c.y + c.height - c.iconHeight, c.iconWidth, c.iconHeight)
      } catch (err) {
        // do nothing
      }
    } else if (c.type == 'rect') {
      ctx.lineWidth = c.strokeWidth
      ctx.strokeRect(c.x, c.y, c.width, c.height)
    } else if (c.type == 'path') {
      ctx.lineWidth = c.strokeWidth
      ctx.beginPath()
      ctx.moveTo(c.points[0][0] + offsetX, c.points[0][1] + offsetY)
      for (let i = 1; i < c.points.length; i++) {
        let point = c.points[i]
        ctx.lineTo(point[0] + offsetX, point[1] + offsetY)
      }
      ctx.stroke()
    } else if (c.type == 'text') {
      ctx.font = `${c.fontSize}px sans-serif`
      ctx.textBaseline = 'top'
      ctx.fillText(c.content, c.x + offsetX, c.y + offsetY)
    }
  }
}
