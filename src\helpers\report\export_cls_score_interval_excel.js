import { apiDownloadClassesScoreIntervalExcel } from '@/api/report'

import Store from '@/store/index'

export function downloadClsScoreIntervalExcel(templateId) {
  let ranges = []
  const StoreTemplateSettingIntervals = Store.getters['report/templateSettingScoreIntervals']
  const AllSubjectSettingIntervals =
    StoreTemplateSettingIntervals && StoreTemplateSettingIntervals.find(x => x.subjectId === 0)
  const AllSubjectIntervals = ((AllSubjectSettingIntervals && AllSubjectSettingIntervals.intervals) || []).map(
    (interval, idx) => ({
      name: interval.range,
      range: interval.range,
      sortCode: idx + 1,
    })
  )

  if (AllSubjectIntervals.length) {
    ranges = AllSubjectIntervals
  } else {
    const ExamAttributes = Store.getters['report/examAttributes']
    const ExamFullScore = ExamAttributes.fullScoreMap[0]
    const Interval = ExamFullScore < 100 ? 10 : Math.round(ExamFullScore / 100) * 10

    let range = ''
    let i = 0
    let j = 1
    for (i = Interval; i < ExamFullScore; i += Interval, j++) {
      range = '[' + (i - Interval) + ', ' + i + ')'
      ranges.push({
        sortCode: j,
        name: range,
        range: range,
      })
    }
    range = '[' + (i - Interval) + ', ' + ExamFullScore + ']'
    ranges.push({
      sortCode: j,
      name: range,
      range: range,
    })
  }

  ranges.reverse()
  ranges.forEach((r, rdx) => (r.sortCode = rdx + 1))

  return apiDownloadClassesScoreIntervalExcel({
    templateId: templateId,
    ranges: ranges,
  })
}
