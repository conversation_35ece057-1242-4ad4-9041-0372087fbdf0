/**
 * 页面定位参数
 */

export default class TemplatePageParam {
  constructor() {
    // 页面校正方式：ANCHORMARK-定位点校正, TITLELINE-直线文字校正
    this.correctType = ''
    // 页面定位点: 左上，右上，右下，左下
    // Array[MarkArea]
    this.anchorMarks = []
    // 标题
    // Object(Rect)
    this.titleArea = null
    // 标题2
    // Object(Rect)
    this.titleArea2 = null
    // 水平校正线
    // Object(Rect)
    this.lineArea = null
    // 文字定位点
    // Object(Rect)
    this.textArea = null
    // 文字定位点2
    // Object(Rect)
    this.textArea2 = null
    // 页码标记
    // Object(MarkArea)
    this.pageMark = null
    // 文字类型页码标记
    // Object(MarkArea)
    this.pageMark2 = null
    // 科目标记
    // Object(Rect)
    this.subjectArea = null
    // 科目二维码位置
    // Object(Rect)
    this.qrcodeArea = null
    // 科目二维码文本
    // String
    this.qrcodeText = ''
    // 遮挡检测跳过区域
    // Array[Rect]
    this.blockDetectSkipAreas = []
  }
}
