import { apiGetExpandTopicAnalysis, apiGetTopicAnalysis } from '@/api/report'

import { generateExcelBlob } from '@/utils/excel_export'
import { getInstitutionName } from '../../tools/tools'

import { UUID_ZERO } from '@/const/string'

import Store from '@/store/index'

// 大题分析
export function generateExcelReportTopicAnalysisBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let fileName = `${subject.subjectName}/${Store.getters['report/examName']}_大题分析_`
  let requestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    examSubjectId: (subject && subject.examSubjectId) || undefined,
  }

  if (category && category.categoryName) {
    fileName = category.categoryName + '/' + fileName
  }
  if (isMultipleSchoolLevel) {
    if (isNextLevel) {
      if (school) {
        fileName = `下级机构或学校报表/(学校)${school.schoolName}/${fileName}_${school.schoolName}`
        requestParams.schoolId = school.schoolId
      } else {
        fileName = `下级机构或学校报表/(机构)${institution.name}/${fileName}_${institution.name}`
        requestParams.organizationId = institution.id
        requestParams.reportName = undefined
      }
    } else {
      if (school) {
        fileName = `学校报表/${school.schoolName}/${fileName}_${school.schoolName}`
        requestParams.schoolId = school.schoolId
      } else {
        fileName = `${fileName}_联考`
        requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']
        requestParams.reportName = undefined
      }
    }
  } else {
    fileName = `${fileName}${Store.getters['report/currentSchoolName']}`
    requestParams.schoolId = Store.getters['report/currentSchoolId']
  }
  fileName = `${fileName}_${subject.subjectName}.xlsx`

  return fetchSheets(requestParams)
    .then(sheets => generateExcelBlob(sheets))
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}

async function fetchSheets(requestParams) {
  const tableColumns = [
    {
      title: '大题名称',
      key: 'topicName',
      width: 'auto',
      group: true,
    },
    {
      title: '统计人数',
      key: 'calNum',
    },
    {
      title: '最高分',
      key: 'max',
    },
    {
      title: '最低分',
      key: 'min',
    },
    {
      title: '平均分',
      key: 'avg',
    },
    {
      title: '得分率',
      key: 'scoreRate',
    },
    {
      title: '满分率',
      key: 'fullRate',
    },
    {
      title: '难度',
      key: 'diff',
    },
    {
      title: '标准差',
      key: 'sqrt',
    },
    {
      title: '差异系数',
      key: 'cv',
    },
  ]

  let tableData
  if (requestParams.organizationId) {
    tableColumns.splice(
      1,
      0,
      {
        title: '包含小题',
        key: 'includedQuestions',
        width: 'auto',
      },
      {
        title: '满分',
        key: 'totalFullScore',
      }
    )
    tableData = transTopicAnalysisResponse(await apiGetTopicAnalysis(requestParams))
  } else {
    tableColumns.splice(
      1,
      0,
      {
        title: '满分',
        key: 'totalFullScore',
      },
      {
        title: '单位名称',
        key: 'affiliatedName',
        width: 'auto',
      }
    )
    tableData = transExpandTopicAnalyseResponse(await apiGetExpandTopicAnalysis(requestParams))
  }

  tableColumns.forEach((x, xIndex) => {
    x.width = x.width || 12

    if (!requestParams.organizationId && ['大题名称', '满分'].includes(x.title)) {
      x.group = true
    }

    if (x.title === '包含小题') {
      x.key = row => row.branchName || row.questionStr || '-'
    } else if (xIndex > 3) {
      // cells transfer to digital
      if (x.title.includes('率')) {
        x.cellNumberFormat = value =>
          isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%'
      } else {
        x.cellNumberFormat = value => (Number.isInteger(value) ? undefined : '0.##')
      }
    }
  })

  if (tableData && tableData.length) {
    return [
      {
        sheetName: '大题分析',
        rows: tableData,
        columns: tableColumns,
      },
    ]
  } else {
    throw {
      code: 9998,
      // msg: '暂无数据',
      msg: '[ignore]',
    }
  }
}

function transTopicAnalysisResponse(data) {
  let result = []

  if (data && data.length) {
    data.sort((a, b) => a.sortCode - b.sortCode)
    result = data.map(item => {
      item.questionStr = item.questionStr.replace(/\.0/g, '')

      if (item.school === UUID_ZERO) {
        item.affiliatedName = '联考'
      } else {
        item.affiliatedName = item.schoolName
      }

      return item
    })
  }

  return result
}

function transExpandTopicAnalyseResponse(data) {
  let result = []

  if (data && data.length) {
    data
      .sort((a, b) => a.sortCode - b.sortCode)
      .forEach(x => {
        if (!Store.getters['report/isMultipleSchool']) {
          x.childVos = x.childVos.filter(cv => cv.schoolId !== UUID_ZERO)
        }
        x.childVos
          .sort((a, b) => b.sortCode - a.sortCode)
          .forEach((y, yIndex) => {
            result.push({
              topicName: x.topicName || '-',
              totalFullScore: y.totalFullScore || 0,
              affiliatedName: getInstitutionName(y.schoolId, y.classId, x.schoolName, y.schoolName),
              calNum: y.countNum || 0,
              max: (y.max && Number(y.max)) || 0,
              min: (y.min && Number(y.min)) || 0,
              avg: (y.avg && Number(y.avg)) || 0,
              scoreRate: (y.scoreRate && Number(y.scoreRate.slice(0, -1) / 100)) || 0,
              fullRate: (y.fullRate && Number(y.fullRate.slice(0, -1) / 100)) || 0,
              diff: (y.diff && Number(y.diff)) || 0,
              sqrt: (y.sqrt && Number(y.sqrt)) || 0,
              cv: (y.cv && Number(y.cv)) || 0,
              // 合并单元格
              isFirstKindItem: !yIndex,
              kindLength: x.childVos.length,
            })
          })
      })
  }

  return result
}
