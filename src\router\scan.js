export default {
  name: 'scan',
  path: 'scan',
  meta: {
    title: '扫描',
  },
  component: () => import('@/views/scan/index.vue'),
  children: [
    {
      name: 'scan-answerSheet',
      path: 'answersheet/:examId/:examSubjectId',
      meta: {
        showHeader: false,
        showFooter: false,
        pageWidth: 'full',
      },
      component: () => import('@/views/scan/answer_sheet/scan/index.vue'),
    },
    {
      name: 'scan-answerSheet-exception',
      path: 'answersheet/exception/:examId/:examSubjectId',
      meta: {
        showHeader: false,
        showFooter: false,
        pageWidth: 'full',
      },
      component: () => import('@/views/scan/answer_sheet/exception/index.vue'),
    },
    {
      name: 'scan-answerSheet-review',
      path: 'answersheet/review/:examId/:examSubjectId',
      meta: {
        showHeader: false,
        showFooter: false,
        pageWidth: 'full',
      },
      component: () => import('@/views/scan/answer_sheet/review/index.vue'),
    },
    {
      name: 'scan-wrongQuestionSheet',
      path: 'wrongquestionsheet',
      meta: {
        showHeader: false,
        showFooter: false,
        pageWidth: 'full',
      },
      component: () => import('@/views/scan/wrong_question_sheet/index.vue'),
    },
    {
      name: 'scan-supplementary-exercise',
      path: 'supplementaryexercise/:examId/:examSubjectId',
      meta: {
        showHeader: false,
        showFooter: false,
        pageWidth: 'full',
      },
      component: () => import('@/views/scan/supplementary_exercise/scan/index.vue'),
    },
  ],
}
