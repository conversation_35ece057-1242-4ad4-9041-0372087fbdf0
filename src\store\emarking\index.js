import basicData from './basic_data'
import adminExam from './admin_exam'
import specialExam from './special_exam'

export default {
  namespaced: true,
  state: {
    ...basicData.state,
    ...adminExam.state,
    ...specialExam.state,
  },
  getters: {
    ...basicData.getters,
    ...adminExam.getters,
    ...specialExam.getters,
  },
  mutations: {
    ...basicData.mutations,
    ...adminExam.mutations,
    ...specialExam.mutations,
  },
  actions: {
    ...basicData.actions,
    ...adminExam.actions,
    ...specialExam.actions,
  },
}
