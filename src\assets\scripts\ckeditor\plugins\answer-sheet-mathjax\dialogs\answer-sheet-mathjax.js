﻿CKEDITOR.dialog.add('answer-sheet-mathjax', function (editor) {
	var preview = null, lastValue = '', timer = null;
	
	function debouncedShowMathFormula() {
		if (timer) {
			clearTimeout(timer)
		}
		timer = setTimeout(function() {
			if (!preview) {
				return
			}
			let innerHTML = lastValue
			if (!innerHTML.startsWith('<math') && !innerHTML.startsWith('$')) {
				innerHTML = '$' + innerHTML + '$'
			}
			if (innerHTML == '$$') {
				preview.setHtml('')
			}
			else {
				preview.setHtml('$' + lastValue + '$')
			}
			if (MathJax) {
				MathJax.Hub.Queue(['Typeset', MathJax.Hub, preview.$])
			}
		}, 500)
	}

	return {
		title: '公式编辑器',
		minWidth: 500,
		minHeight: 200,
		contents: [
			{
				id: 'info',
				elements: [
					{
						id: 'equation',
						type: 'textarea',
						label: '在此编写Tex或MathML代码',

						onLoad: function () {
							this.getInputElement().on('keyup', function () {
								var value = this.getValue()
								if (lastValue !== value) {
									lastValue = value;
									debouncedShowMathFormula();
								}
							});
						},

						setup: function (widget) {
							lastValue = ''
							var mjx = widget.element.$.querySelector('.mjx-chtml')
							if (mjx) {
								lastValue = mjx.dataset.mathtex || mjx.dataset.mathml || ''
							}
							this.setValue(lastValue)
							debouncedShowMathFormula()
						},

						commit: function (widget) {
							var value = lastValue
							if (!value.startsWith('<math') && !value.startsWith('$')) {
								value = '$' + value + '$'
							}
							if (value =='$$') {
								return
							}
							MathJax.Hub.queue.queue = []
							preview.setHtml(value)

							// 新插入公式先设置一个不为空的内容，否则会报错
							if (!widget.data.math) {
								widget.element.setHtml('<span style="display: none">placeholder</span>')
							}
							
							window.igradeAnswerSheetRenderMath(preview.$).then(function() {
								var math = preview.$.querySelector('.igrade-answer-sheet-math').innerHTML
								widget.setData('math', math);
								widget.element.setHtml(math);
								widget.editor.fire('change')
							})
						}
					},
					{
						id: 'preview',
						type: 'html',
						html:
							'<div style="width:100%;text-align:center;font-size:16px; scroll: visible">' +
							'<span class="math-formula"></span>' +
							'</div>',

						onLoad: function () {
							preview = CKEDITOR.document.getById(this.domId);
							
							// 清除dialog全局样式
							var el = preview.$
							while(el != window.document.documentElement) {
								if (el.classList.contains('cke_reset_all')) {
									el.classList.remove('cke_reset_all');
									break;
								}
								else {
									el = el.parentElement;
								}
							}
						},
					}
				]
			}
		],
	};
});
