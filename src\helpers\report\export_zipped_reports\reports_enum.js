import { generateExcelReportGeneralBlob } from './reports/general'
import { generateExcelReportScoreBlob } from './reports/score'
import { generateExcelReportScoreDetailBlob } from './reports/score_detail'
import { generateExcelReportTopicDetailBlob } from './reports/topic_detail'
import { generateExcelReportClassesCompareBlob } from './reports/next_level_compare'
import { generateExcelReportScoreIntervalBlob } from './reports/score_interval'
import { generateExcelReportRankIntervalBlob } from './reports/rank_interval'
import { generateExcelReportScoreGradeBlob } from './reports/score_grade'
import { generateExcelReportScoreGradeCompareBlob } from './reports/score_grade_compare'
import { generateExcelReportAnswerAnalysisBlob } from './reports/answer_analysis'
import { generateExcelReportQuestionAnalysisBlob } from './reports/question_analysis'
import { generateExcelReportTopicAnalysisBlob } from './reports/topic_analysis'
import { generateExcelReportKnowledgeAnalysisBlob } from './reports/knowledge_analysis'
import { generateExcelReportTeacherAnalyseBlob } from './reports/teacher_analyse'
import { generateExcelReportUnionClassCompareBlob } from './reports/union_class_compare'
import { generateExcelReportTeacherByMergedClassAnalyseBlob } from './reports/teacher_analyse_by_merged_class'

// 导出报表压缩包 报表种类
export const REPORTS = [
  {
    type: 'general',
    name: '考情概览',
    mname: '考情概览',
    totalRelated: true,
    subjectRelated: false,
    function: generateExcelReportGeneralBlob,
    seniorHighCategoriesId: 0,
  },
  {
    type: 'union_class_compare',
    name: '',
    mname: '联考班级对比分析报表',
    totalRelated: true,
    subjectRelated: false,
    function: generateExcelReportUnionClassCompareBlob,
  },
  {
    type: 'score',
    name: '学生成绩',
    mname: '学生成绩',
    totalRelated: true,
    subjectRelated: true,
    function: generateExcelReportScoreBlob,
    seniorHighCategoriesId: 0,
  },
  {
    type: 'score_detail',
    name: '学生得分明细',
    mname: '',
    totalRelated: false,
    subjectRelated: true,
    function: generateExcelReportScoreDetailBlob,
  },
  {
    type: 'topic_detail',
    name: '学生大题得分明细',
    mname: '',
    totalRelated: false,
    subjectRelated: true,
    function: generateExcelReportTopicDetailBlob,
  },
  {
    type: 'next_level_compare',
    name: '班级对比',
    mname: '学校对比',
    totalRelated: true,
    subjectRelated: true,
    function: generateExcelReportClassesCompareBlob,
    seniorHighCategoriesId: 1,
  },
  {
    type: 'score_interval',
    name: '分数段统计',
    mname: '分数段统计',
    totalRelated: true,
    subjectRelated: true,
    function: generateExcelReportScoreIntervalBlob,
    seniorHighCategoriesId: 2,
  },
  {
    type: 'rank_interval',
    name: '名次段统计',
    mname: '名次段统计',
    totalRelated: true,
    subjectRelated: true,
    function: generateExcelReportRankIntervalBlob,
    seniorHighCategoriesId: 2,
  },
  {
    type: 'score_grade',
    name: '科目等级结构分析',
    mname: '科目等级结构分析',
    totalRelated: true,
    subjectRelated: false,
    function: generateExcelReportScoreGradeBlob,
    seniorHighCategoriesId: 0,
  },
  {
    type: 'score_grade_compare',
    name: '班级成绩等级结构对比',
    mname: '学校成绩等级结构对比',
    totalRelated: true,
    subjectRelated: true,
    function: generateExcelReportScoreGradeCompareBlob,
    seniorHighCategoriesId: 0,
  },
  {
    type: 'answer_analysis',
    name: '作答统计',
    mname: '作答统计',
    totalRelated: false,
    subjectRelated: true,
    function: generateExcelReportAnswerAnalysisBlob,
  },
  {
    type: 'question_analysis',
    name: '小题分析',
    mname: '小题分析',
    totalRelated: false,
    subjectRelated: true,
    function: generateExcelReportQuestionAnalysisBlob,
  },
  {
    type: 'topic_analysis',
    name: '大题分析',
    mname: '大题分析',
    totalRelated: false,
    subjectRelated: true,
    function: generateExcelReportTopicAnalysisBlob,
  },
  {
    type: 'knowledge_analysis',
    name: '知识点分析',
    mname: '知识点分析',
    totalRelated: false,
    subjectRelated: true,
    function: generateExcelReportKnowledgeAnalysisBlob,
  },
  {
    type: 'teacher_analyse',
    name: '',
    mname: '教师教学质量分析报表',
    totalRelated: true,
    subjectRelated: false,
    function: generateExcelReportTeacherAnalyseBlob,
  },
  {
    type: 'teacher_analyse_by_merged_class',
    name: '',
    mname: '教师教学质量分析报表(合并任教班级)',
    totalRelated: true,
    subjectRelated: false,
    function: generateExcelReportTeacherByMergedClassAnalyseBlob,
  },
]

export const REPORTINFOS = REPORTS.map(r => ({
  type: r.type,
  name: r.name,
  mname: r.mname,
}))
