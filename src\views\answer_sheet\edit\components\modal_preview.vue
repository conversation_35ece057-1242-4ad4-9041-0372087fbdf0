<template>
  <Modal
    :model-value="modelValue"
    class="modal-preview"
    :closable="true"
    :mask-closable="false"
    :fullscreen="true"
    :footer-hide="true"
    @on-visible-change="handleVisibleChange"
  >
    <template #header>
      <div class="modal-header">
        <div class="title">答题卡预览</div>
        <div class="actions">
          <span class="switcher">
            <span>显示扫描模板元素位置</span>
            <i-switch v-model="showRects"></i-switch>
          </span>
          <span class="switcher">
            <span>显示标签</span>
            <i-switch v-model="showLabels" :disabled="!showRects"></i-switch>
          </span>
          <Divider type="vertical"></Divider>
          <TextButton
            class="btn"
            type="default"
            title="放大"
            style="vertical-align: middle"
            @click="zoomPreviewImage('in')"
          >
            <img src="@/assets/images/common/zoom_in.svg" style="width: 16px"
          /></TextButton>
          <TextButton
            class="btn"
            type="default"
            title="缩小"
            style="vertical-align: middle"
            @click="zoomPreviewImage('out')"
          >
            <img src="@/assets/images/common/zoom_out.svg" style="width: 16px"
          /></TextButton>
        </div>
      </div>
    </template>
    <div class="modal-body">
      <div class="image-container" :style="imageContainerStyle">
        <TemplatePageView
          v-for="p in pages"
          :key="p.pageIndex"
          class="template-page"
          :page="p"
          :show-rects="showRects"
          :show-labels="showLabels"
          :rect-border-width="1"
          :style="pageStyle"
          @image-loaded="handleImageLoaded"
        ></TemplatePageView>
      </div>
    </div>
  </Modal>
</template>

<script>
  import TemplatePageView from '@/views/scan_template/components/template_page_view.vue'
  import { setPages } from '@/helpers/scan_template/view'

  export default {
    components: {
      TemplatePageView,
    },
    props: {
      modelValue: Boolean,
      sheet: Object,
    },
    emits: ['update:modelValue'],
    data() {
      return {
        // 页面
        pages: [],
        // 显示矩形框
        showRects: false,
        // 显示标签
        showLabels: false,
        // 图片宽度
        imageWidth: 0,
        // 缩放
        scale: 1,
      }
    },
    computed: {
      pageWidth() {
        return this.imageWidth * this.scale
      },
      pageStyle() {
        return {
          width: this.pageWidth + 'px',
        }
      },
      imageContainerStyle() {
        return {
          width: `${this.pageWidth + 40}px`,
        }
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.scale = 1
          this.pages = setPages({
            imageUrls: this.sheet.imageUrls.split(',').filter(Boolean),
            template: JSON.parse(this.sheet.templateJson),
            objectiveDefines: JSON.parse(this.sheet.objectivesJson),
            blockDefines: JSON.parse(this.sheet.blocksJson),
          })
        }
      },
    },
    methods: {
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.$emit('update:modelValue', false)
        }
      },
      zoomPreviewImage(direction) {
        if (direction == 'in') {
          this.scale = Math.min(this.scale + 0.1, 4)
        } else if (direction == 'out') {
          this.scale = Math.max(this.scale - 0.1, 0.2)
        }
      },
      handleImageLoaded({ naturalWidth }) {
        this.imageWidth = Math.max(this.imageWidth, naturalWidth)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .modal-header {
    @include flex(row, space-between, center);
    margin-right: 50px;
    padding-top: 2px;

    .title {
      font-size: $font-size-medium-x;
    }

    .switcher {
      margin-left: 8px;
    }
  }

  .modal-body {
    height: calc(100vh - 86px);
    overflow: auto;
    background-color: #333;

    .image-container {
      margin: 0 auto;
      padding: 20px;
      background-color: #333;

      .template-page:not(:first-child) {
        margin-top: 20px;
      }
    }
  }
</style>
