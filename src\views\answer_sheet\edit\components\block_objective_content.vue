<template>
  <div class="block-objective-content">
    <com-box-content
      v-if="showContent"
      ref="contentBox"
      :content="block.content"
      :width="pageBodyWidth"
      :height="block.contentHeight"
      :border="contentBorderStyle"
      :resizable="true"
      :padding-top="5"
      @height-change="handleContentHeightChange"
      @resize="handleResize"
      @resize-end="handleResizeEnd"
      @content-change="changeContent"
    ></com-box-content>
    <div v-if="showOptions" ref="objectiveOptions" class="objective-options" :style="optionsStyle">
      <!--题号位置始终保留-->
      <span class="question-code" :style="questionCodeStyle">
        <span v-if="!hasStem">{{ noStemQuestionCodeContent }}</span></span
      ><!--
      --><span class="opition-list" :style="optionListStyle">
        <span v-for="opt in options" :key="opt.label" class="opt-item" :style="optionItemStyle">
          <span class="opt-label" :style="optionLabelStyle"
            ><img
              v-if="objectivesSeparate"
              ref="optionOmr"
              :src="getOptionLabelSrc(opt.label)"
              class="opt-omr"
              :data-label="opt.label"
              style="margin-right: 4px" /><template v-else>{{ getOptionLabelText(opt.label) }}.</template
            ><span class="no-print btn-edit" title="修改" @click="changeOption(opt)">
              <Icon type="md-create"></Icon> </span></span
          ><!--
      --><span class="opt-content" :style="optionContentStyle">
            <span ref="optionContent" style="display: inline-block" v-html="opt.content"></span>
          </span>
        </span>
      </span>
    </div>

    <Modal
      v-model="showModalChangeOptionContent"
      :title="modalChangeOptionContentTitle"
      @on-ok="handleChangeOptionContentOK"
    >
      <AnswerSheetEditor
        v-if="showModalChangeOptionContent"
        class="option-editor"
        :value="optionInModal.content"
        :style="baseStyle"
        @change="optionInModal.content = $event"
      ></AnswerSheetEditor>
      <div style="clear: both"></div>
    </Modal>
  </div>
</template>

<script>
  import comBoxContent from './box_content.vue'
  import AnswerSheetEditor from './editor.vue'

  import { mapGetters, mapMutations } from 'vuex'

  import TemplateRect from '@/helpers/scan_template_define/template_rect'
  import TemplateOmrArea from '@/helpers/scan_template_define/template_omr_area'
  import { OmrSVG } from '@/const/answer_sheet'

  export default {
    components: {
      comBoxContent,
      AnswerSheetEditor,
    },
    props: {
      block: {
        type: Object,
        required: true,
      },
    },
    emits: ['height-change'],
    data() {
      return {
        // 选项总高度
        optionsHeight: 0,
        // 选项每行排几个
        optionsPerRow: 1,
        // 选项标签及填涂宽度
        optionLabelOMRWidth: 26,
        optionLabelLetterWidth: 20,
        // 修改选项内容
        showModalChangeOptionContent: false,
        optionInModal: null,
      }
    },
    computed: {
      ...mapGetters('answerSheet', ['pageContentWidth', 'stage', 'subject', 'objectivesSeparate', 'baseStyle']),
      emptySealOmrSrc() {
        return OmrSVG.cell_empty_seal
      },
      pageSize() {
        return this.$store.getters['answerSheet/pageSize'](this.block.pageIndex)
      },
      baseStyle() {
        return this.$store.getters['answerSheet/baseStyle']
      },

      /**
       * 样式
       */
      pageBodyWidth() {
        return this.pageSize.width - this.pageSize.left - this.pageSize.right
      },
      contentBorderStyle() {
        return {
          left: false,
          right: false,
          top: false,
          bottom: false,
        }
      },

      hasStem() {
        let stem = this.block.questionGroup.questions[0].originalBranch?.stem
        if (!stem) {
          return false
        }
        // 全空白字符
        if (!stem.trim()) {
          return false
        }
        // 只有括号
        stem = stem.replace(/&nbsp;/g, ' ')
        if (/^\s*(<p>)?\s*[(（]\s*[）)]\s*(<\/p>)?\s*$/.test(stem)) {
          return false
        }
        return true
      },
      showContent() {
        if (!this.hasStem) {
          return false
        }
        // 最后一块没有内容则不显示
        if (
          this.block.part == this.block.questionGroup.blocks.length - 1 &&
          !this.block.content &&
          this.block.contentHeight == 0
        ) {
          return false
        }
        return true
      },
      showOptions() {
        // 最后一块显示
        if (this.block.part != this.block.questionGroup.blocks.length - 1) {
          return false
        }
        // 客观题集中填涂时，选项内容均为空则不显示
        if (!this.objectivesSeparate) {
          return this.options.some(opt => {
            if (!opt.content) {
              return false
            }
            let trimContent = opt.content.replace(/&nbsp;/g, '').trim()
            return trimContent.length > 0
          })
        }
        return true
      },
      optionsStyle() {
        let lineHeight = this.block.questionGroup.optionLineHeight
        if (lineHeight && lineHeight != 1.5) {
          return {
            lineHeight,
          }
        } else {
          return null
        }
      },

      // 选项左边题号括号宽度
      questionCodeBracketWidth() {
        return Math.ceil(5.5 * Number.parseFloat(this.baseStyle.fontSize)) + 10
      },
      // 题号位置宽度
      questionCodeWidth() {
        return this.hasStem ? this.block.questionGroup.optionPaddingLeft : this.questionCodeBracketWidth
      },
      questionCodeStyle() {
        return {
          width: this.questionCodeWidth + 'px',
          paddingLeft: '10px',
          display: 'inline-block',
          verticalAlign: 'top',
        }
      },
      noStemQuestionCodeContent() {
        let questionCode = this.block.questionGroup.questions[0].questionCode
        let objectiveQuestionBracketContent = this.block.questionGroup.objectiveQuestionBracketContent
        // 小学英语括号在前
        if (this.stage.name == '小学' && this.subject.name == '英语') {
          return `${objectiveQuestionBracketContent}${questionCode}. `
        } else {
          return `${questionCode}. ${objectiveQuestionBracketContent}`
        }
      },
      optionListWidth() {
        return this.pageContentWidth - this.questionCodeWidth
      },
      optionListStyle() {
        return {
          width: this.optionListWidth + 'px',
          display: 'inline-block',
        }
      },
      options() {
        return this.block.questionGroup.options
      },
      optionItemWidth() {
        return Math.floor(this.optionListWidth / this.optionsPerRow)
      },
      optionItemStyle() {
        return {
          width: this.optionItemWidth + 'px',
          display: 'inline-block',
        }
      },
      optionLabelWidth() {
        if (this.objectivesSeparate) {
          return this.optionLabelOMRWidth
        } else {
          return this.optionLabelLetterWidth
        }
      },
      optionLabelStyle() {
        return {
          width: this.optionLabelWidth + 'px',
          display: 'inline-block',
          verticalAlign: 'top',
        }
      },
      optionContentStyle() {
        return {
          width: this.optionItemWidth - this.optionLabelWidth + 'px',
          display: 'inline-block',
        }
      },
      modalChangeOptionContentTitle() {
        let title = '修改选项内容'
        if (this.optionInModal) {
          title += `（${this.optionInModal.label}）`
        }
        return title
      },
    },
    watch: {
      optionsHeight() {
        this.changeHeight()
      },
      optionListWidth() {
        this.typesetOptions()
      },

      /**
       * 显示选项变化需排版选项，情形：对同一道题，
       * 一开始题干选项均在第二页，
       * 调整后题干在第一页选项在第二页（此时题干实例不显示选项），
       * 再调整后题干选项均在第一页（此时若不排版选项，optionsPerRow = 1，optionsHeight = 0)
       */
      showOptions() {
        this.$nextTick().then(() => {
          this.typesetOptions()
        })
      },
      optionsStyle() {
        this.$nextTick().then(() => {
          this.updateOptionsHeight()
        })
      },
    },
    mounted() {
      this.typesetOptions()
    },
    created() {
      this.changeInstance()
    },
    updated() {
      this.changeInstance()
    },
    methods: {
      ...mapMutations('answerSheet', [
        'changePageBlockInstance',
        'changePageBlockContent',
        'changePageBlockOptionContent',
        'changePageBlockHeight',
      ]),
      changeInstance() {
        this.changePageBlockInstance({
          blockId: this.block.id,
          instance: this,
        })
      },
      changeContent(content) {
        this.changePageBlockContent({
          blockId: this.block.id,
          content: content,
        })
      },
      changeHeight(contentHeight = this.block.contentHeight, emitEvent) {
        if (!this.showContent) {
          contentHeight = 0
        }
        let height = contentHeight + this.optionsHeight
        let heightChanged = height != this.block.height
        this.changePageBlockHeight({
          blockId: this.block.id,
          height,
          contentHeight,
        })
        if (emitEvent || (emitEvent == null && heightChanged)) {
          this.$emit('height-change', height)
        }
      },
      handleContentHeightChange(contentHeight) {
        this.changeHeight(contentHeight)
      },
      // 调整高度过程中不重排
      handleResize(contentHeight) {
        this.changeHeight(contentHeight, false)
      },
      handleResizeEnd() {
        this.changeHeight(this.contentHeight, true)
      },
      updateOptionsHeight() {
        if (this.$refs.objectiveOptions) {
          this.optionsHeight = this.$refs.objectiveOptions.offsetHeight
        } else {
          this.optionsHeight = 0
        }
      },
      typesetOptions() {
        let elOptionContents = this.$refs.optionContent
        if (!elOptionContents || elOptionContents.length == 0) {
          this.updateOptionsHeight()
          return
        }
        this.optionsPerRow = 1

        let optionScrollWidths = elOptionContents.map(el => el.scrollWidth)
        let maxScrollWidth = Math.max(...optionScrollWidths)
        let minOptionGap = 20

        let candidates = []
        // 选项无内容的，尝试一行排
        if (this.options.every(opt => !opt.content || opt.content == ' ')) {
          let max = Math.max(8, this.options.length)
          for (let i = max; i >= 2; i--) {
            candidates.push(i)
          }
        } else {
          if (this.options.length >= 4) {
            candidates.push(4, 2)
          } else if (this.options.length == 3) {
            candidates.push(3, 2)
          } else if (this.options.length == 2) {
            candidates.push(2)
          }
        }

        for (let optionsPerRow of candidates) {
          if (maxScrollWidth <= this.optionListWidth / optionsPerRow - this.optionLabelWidth - minOptionGap) {
            this.optionsPerRow = optionsPerRow
            break
          }
        }

        this.$nextTick().then(() => {
          this.updateOptionsHeight()
        })
      },
      getOptionLabelSrc(label) {
        if (label == 'T') {
          if (this.subject.id != 3) {
            label = 'Tick'
          }
        }
        if (label == 'F') {
          if (this.subject.id != 3) {
            label = 'Cross'
          }
        }
        return OmrSVG[`opt_label_${label}`]
      },
      getOptionLabelText(label) {
        if (label == 'T') {
          if (this.subject.id != 3) {
            label = '√'
          }
        }
        if (label == 'F') {
          if (this.subject.id != 3) {
            label = '×'
          }
        }
        return label
      },

      /**
       * 操作
       */
      changeOption(opt) {
        this.optionInModal = {
          label: opt.label,
          content: opt.content,
        }
        this.showModalChangeOptionContent = true
      },
      handleChangeOptionContentOK() {
        if (!this.optionInModal) {
          return
        }
        let oldOption = this.options.find(x => x.label == this.optionInModal.label)
        if (!oldOption) {
          return
        }
        if (this.optionInModal.content == oldOption.content) {
          this.$Message.info({
            content: '未作修改',
          })
          return
        }
        this.changePageBlockOptionContent({
          blockId: this.block.id,
          label: this.optionInModal.label,
          content: this.optionInModal.content,
        })
        this.$nextTick().then(() => {
          this.typesetOptions()
        })
      },

      /**
       * 拆分
       */
      split(getNextPageAvailableHeightFunc) {
        let optionsHeight = 0
        // 选项高度从题组最后一个实例中拿
        let questionGroupBlocks = this.block.questionGroup.blocks
        let lastBlock = questionGroupBlocks[questionGroupBlocks.length - 1]
        if (lastBlock.instance) {
          optionsHeight = lastBlock.instance.optionsHeight
        }
        // 若不显示题干，则只有打分框，无需拆分
        if (!this.hasStem) {
          let splitResult = [
            {
              content: '',
              contentHeight: 0,
              height: optionsHeight,
            },
          ]
          let pageAvailableHeight = getNextPageAvailableHeightFunc(0)
          if (pageAvailableHeight < optionsHeight) {
            splitResult.unshift(null)
          }
          return splitResult
        }

        let blocks
        try {
          blocks = this.$refs['contentBox'].split(getNextPageAvailableHeightFunc, this.block.questionGroup, {
            contentBottomHeight: optionsHeight,
          })
        } catch {
          blocks = []
        }
        return blocks
      },

      getTemplateElements(pageLeft) {
        if (!this.showOptions) {
          return []
        }
        if (!this.objectivesSeparate) {
          return []
        }
        let addLeft = pageLeft + this.pageSize.left
        let addTop = this.block.top + this.pageSize.top
        let blockRect = this.$el.getBoundingClientRect()
        let elOptionOmrs = this.$refs.optionOmr
        if (!elOptionOmrs || elOptionOmrs.length != this.options.length) {
          throw '显示的选项数量与题目不一致'
        }
        let omrs = elOptionOmrs
          .map(el => {
            let label = el.dataset.label
            if (!label) {
              throw '无选项标签（ABCD）'
            }
            if (label == '√') {
              label = 'A'
            } else if (label == '×') {
              label = 'B'
            } else if (label == 'T') {
              label = 'A'
            }
            let elRect = el.getBoundingClientRect()
            return {
              label: label.toUpperCase(),
              rect: {
                left: elRect.left - blockRect.left + addLeft,
                top: elRect.top - blockRect.top + addTop,
                width: elRect.width,
                height: elRect.height,
              },
            }
          })
          .sort((a, b) => (a.label < b.label ? -1 : 1))

        let questions = this.block.questionGroup.questions.map(q => ({
          questionCode: q.questionCode,
        }))
        let omrArea = new TemplateOmrArea()
        omrArea.id = `ObjectiveArea-${questions.map(q => q.questionCode).join(',')}`
        omrArea.direction = 'H'
        omrArea.optionCount = this.options.length
        omrArea.options = omrs.map(omr =>
          TemplateRect.createFromAnswerSheetPageRect(omr.rect.left, omr.rect.top, omr.rect.width, omr.rect.height)
        )
        omrArea._extra = {
          questions,
        }
        return [omrArea]
      },
    },
  }
</script>

<style lang="scss" scoped>
  .btn-edit {
    position: absolute;
    left: -16px;
    display: none;
    width: 20px;
  }

  .opt-item:hover .btn-edit {
    display: inline;
    color: $color-primary;
    cursor: pointer;
  }

  .opt-label {
    position: relative;
  }

  .option-editor {
    padding: 4px;
    border: 1px solid $color-primary;
  }

  .opt-content {
    /** 着重号 */

    :deep(.char-emphasis) {
      position: relative;
      display: inline-block;
    }

    :deep(.char-emphasis::after) {
      position: absolute;
      bottom: 0;
      left: 50%;
      font-size: 12px;
      line-height: 4px;
      transform: translateX(-50%);
      content: '•';
    }

    /** 波浪线 */

    :deep(.wavy-underline) {
      position: relative;
      display: inline-block;
    }

    :deep(.wavy-underline::after) {
      position: absolute;
      bottom: 0px; /* 控制波浪线与文字的间距 */
      left: 0;
      width: 400%;
      height: 20px; /* 波浪线的高度 */
      background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='40' height='10' viewBox='0 0 40 10'><path d='M0 5 Q5 0, 10 5 T20 5 T30 5 T40 5' fill='none' stroke='black' stroke-width='2'/></svg>");
      background-repeat: repeat-x;
      background-size: 4em 20px; /* 确保波浪线与字体大小匹配 */
      transform: scale(0.25); /* 背景图片尺寸设为实际尺寸的4倍再缩小，避免html2canvas导出后模糊 */
      transform-origin: left bottom;
      content: '';
    }
  }
</style>
