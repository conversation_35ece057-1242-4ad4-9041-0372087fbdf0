import { getOptionLabelByIndex } from '@/helpers/qlib/miscellaneous'
import { deepCopy } from '@/utils/object'
import { roundNumber } from '@/utils/math'
import { groupArray } from '@/utils/array'
import { loadImage } from '@/utils/promise'

import BranchTypeEnum from '@/enum/qlib/branch_type'
import ObjectiveRecognizeStatusEnum from '@/enum/scan/objective_recognize_status'
import ScoreRecognizeStatusEnum from '@/enum/scan/score_recognize_status'
import ExceptionStatusEnum from '@/enum/scan/exception_status'
import ScoreTypeEnum from '@/enum/answer_sheet/score_type'
import BarScoreStatusEnum from '@/enum/scan/bar_score_status'
import TrueFalseScoreResultEnum from '@/enum/scan/true_false_score_result'
import TrueFalseScoreStatusEnum from '@/enum/scan/true_false_score_status'
import SelectResultEnum from '@/enum/scan/select_result'
import SelectRecognizeStatusEnum from '@/enum/scan/select_recognize_status'

/**
 * 是否缺考
 */
export function isScanUnitAbsent(scanUnit) {
  if (!scanUnit) {
    return false
  }
  return scanUnit.isAbsent == ExceptionStatusEnum.Yes.id
}

/**
 * 加载一页异常
 * 若总数大于0且当前页为空，则切换回第一页重新加载
 */
export function fetchExceptionPage(api, params) {
  return api(params).then(data => {
    if (data.total > 0 && data.records.length == 0 && params.currentPage > 1) {
      let newParams = Object.assign({}, params)
      newParams.currentPage = 1
      return fetchExceptionPage(api, newParams)
    } else {
      data.currentPage = params.currentPage
      return data
    }
  })
}

/**
 * 预加载图片
 * 为避免切换时客观题、主观题、缺考等显示当前考生而答卷图片仍为上一考生，先将图片加载一遍，然后页面上再显示该图片时一般不会再发起网络请求
 */
export function prefetchScanUnitAdjustImages(scanUnit) {
  if (!(scanUnit && scanUnit.adjustImgUrls.length > 0)) {
    return Promise.resolve()
  }
  return loadImages(scanUnit.adjustImgUrls).catch(() => {})
}

export function prefetchScanUnitRawImages(scanUnit) {
  if (!(scanUnit && scanUnit.papers.length > 0)) {
    return Promise.resolve()
  }
  let urls = []
  scanUnit.papers.forEach(paper => urls.push(...paper.imgUrls))
  return loadImages(urls).catch(() => {})
}

function loadImages(urls) {
  return Promise.all(urls.map(url => loadImage(url, false)))
}

/**
 * 补充模板、客观题、主观题、选做题定义
 */
export function buildTemplateObjectiveSubjectiveDefine(template, objectiveDefine, subjectiveDefine) {
  objectiveDefine.forEach(addOptionList)
  addScanTemplateObjectiveDefine(template, objectiveDefine)
  addScanTemplateSubjectiveDefine(template, subjectiveDefine)
  let selectDefine = getScanTemplateSelectDefine(template)
  Object.freeze(template)
  return {
    template,
    objectiveDefine,
    subjectiveDefine,
    selectDefine,
  }
}

function addOptionList(q) {
  if (q.branchTypeId == BranchTypeEnum.TrueOrFalse.id) {
    q.optionList = [
      {
        id: 'T',
        name: 'T',
      },
      {
        id: 'F',
        name: 'F',
      },
    ]
  } else {
    let list = []
    for (let i = 0; i < q.optionCount; i++) {
      let char = getOptionLabelByIndex(i)
      list.push({
        id: char,
        name: char,
      })
    }
    q.optionList = list
  }
}

function addScanTemplateObjectiveDefine(template, objectiveDefine) {
  template.questions.objectives.forEach(area => {
    area.questions = area.questions
      .map(q => {
        let define = objectiveDefine.find(x => x.questionCode == q.questionCode)
        if (define) {
          let copy = deepCopy(define)
          copy.fullCode = define.fullCode
          return copy
        } else {
          return null
        }
      })
      .filter(Boolean)
  })
}

function addScanTemplateSubjectiveDefine(template, subjectiveDefine) {
  let subjectManualScoreAreas = []
  let subjectBarScoreAreas = []
  let subjectTrueFalseScoreAreas = []
  template.tpl.tplFile.pages.forEach(page => {
    subjectManualScoreAreas.push(...(page.manualScoreAreas || []))
    subjectBarScoreAreas.push(...(page.barScoreAreas || []))
    subjectTrueFalseScoreAreas.push(...(page.trueFalseScoreAreas || []))
  })

  // 打分条格子分组
  subjectBarScoreAreas.forEach(splitBarScoreAreaCellsIntoGroup)

  template.questions.subjectives.forEach(block => {
    block.questions = block.questions
      .map(q => {
        // 找题目对应的打分框
        let manualScoreArea = null
        let barScoreArea = null
        let trueFalseScoreAreas = []
        if (q.scoreType == ScoreTypeEnum.Number.id) {
          manualScoreArea = subjectManualScoreAreas.find(area => area.id == q.scoreAreaIds[0])
        } else if (q.scoreType == ScoreTypeEnum.Bar.id) {
          barScoreArea = subjectBarScoreAreas.find(area => area.id == q.scoreAreaIds[0])
        } else if (q.scoreType == ScoreTypeEnum.TrueFalse.id) {
          trueFalseScoreAreas = subjectTrueFalseScoreAreas.filter(area => q.scoreAreaIds.includes(area.id))
        }

        // 题目定义
        let define = subjectiveDefine.find(x => x.questionCode == q.questionCode && x.branchCode == q.branchCode)
        if (define) {
          define.manualScoreAreaId = manualScoreArea ? manualScoreArea.id : ''
          define.manualScorePrecision = manualScoreArea ? manualScoreArea.digitCount : 0
          define.manualScoreInteger = manualScoreArea ? manualScoreArea.digits.length - manualScoreArea.digitCount : 0

          define.barScoreAreaId = barScoreArea ? barScoreArea.id : ''
          define.barScoreCells = barScoreArea ? barScoreArea.cells : []

          define.trueFalseScoreAreaIds = trueFalseScoreAreas.map(area => area.id)
          define.trueFalseScoreEnableHalfScores = trueFalseScoreAreas.map(area => area.enableHalfScore)
          define.trueFalseScoreEmptyDefaults = trueFalseScoreAreas.map(area =>
            area.emptyDefault ? TrueFalseScoreResultEnum.Tick.id : TrueFalseScoreResultEnum.Cross.id
          )

          let copy = deepCopy(define)
          copy.fullCode = define.fullCode
          copy.scoreType = q.scoreType
          copy.scoreAreaIds = q.scoreAreaIds
          return copy
        } else {
          return null
        }
      })
      .filter(Boolean)
  })
}

/**
 * 打分条格子分组
 */
function splitBarScoreAreaCellsIntoGroup(area) {
  // 从左到右排
  let orderByX = area.cells.slice().sort((a, b) => a.searchArea.x - b.searchArea.x)
  // 满分、0.5分单独一组，与前一格间距超过格子宽度一半的新开一组
  let group = 0
  let preCell = null
  orderByX.forEach(cell => {
    let newGroup = false
    if (preCell == null) {
      newGroup = true
    } else if (cell.value == 100 || cell.value == 0.5 || preCell.value == 100 || preCell.value == 0.5) {
      newGroup = true
    } else if (cell.searchArea.x - preCell.searchArea.x - preCell.searchArea.width > preCell.searchArea.width * 0.5) {
      newGroup = true
    }
    if (newGroup) {
      group++
    }
    cell.group = group
    preCell = cell
  })
}

function getScanTemplateSelectDefine(template) {
  if (!template.selectGroups) {
    return []
  }
  return template.selectGroups.map(g => {
    let blocks = g.blockIds
      .map(blockId => {
        let block = template.questions.subjectives.find(x => x.subjectiveId == blockId)
        return {
          blockId,
          questionCode: (block && block.questions[0].questionCode) || '',
        }
      })
      .sort((a, b) => a.questionCode - b.questionCode)
    return {
      selectGroupName: g.selectGroupName,
      selectCount: g.selectCount,
      blocks,
    }
  })
}

/**
 * 给ScanUnit添加题目属性
 */
// 给ScanUnit添加客观题属性信息
export function addScanUnitObjectivesInfo(unit, objectiveDefine) {
  if (!unit) {
    return
  }
  unit.objectives = unit.objectives.map(q => {
    let question = objectiveDefine.find(x => x.questionCode == q.questionId)
    if (question) {
      return {
        ...question,
        ...q,
      }
    } else {
      return {
        ...q,
      }
    }
  })
}

// 给ScanUnit添加主观题打分属性信息
export function addScanUnitScoresInfo(unit, subjectiveDefine) {
  if (!unit) {
    return
  }
  unit.scores = {
    manualScores: (unit.scores && unit.scores.manualScores) || [],
    barScores: (unit.scores && unit.scores.barScores) || [],
    trueFalseScores: (unit.scores && unit.scores.trueFalseScores) || [],
  }
  unit.scores.manualScores.forEach(s => {
    s.scoreType = ScoreTypeEnum.Number.id
    s.fullCode = s.questionId
    let define = subjectiveDefine.find(x => x.fullCode === s.fullCode)
    s.fullScore = (define && define.fullScore) || 0
    s.manualScorePrecision = (define && define.manualScorePrecision) || 0
    s.manualScoreInteger = (define && define.manualScoreInteger) || 0
  })
  unit.scores.barScores.forEach(s => {
    s.scoreType = ScoreTypeEnum.Bar.id
    s.fullCode = s.questionId
    let define = subjectiveDefine.find(x => x.fullCode === s.fullCode)
    s.fullScore = (define && define.fullScore) || 0
    s.barScoreCells = (define && define.barScoreCells) || []
  })
  unit.scores.trueFalseScores.forEach(s => {
    s.scoreType = ScoreTypeEnum.TrueFalse.id
    s.fullCode = s.questionId
    let define = subjectiveDefine.find(x => x.fullCode === s.fullCode)
    s.fullScore = (define && define.fullScore) || 0
    let trueFalseScoreAreaIds = (define && define.trueFalseScoreAreaIds) || []
    let trueFalseScoreEnableHalfScores = (define && define.trueFalseScoreEnableHalfScores) || []
    let trueFalseScoreEmptyDefaults = (define && define.trueFalseScoreEmptyDefaults) || []
    s.answers.forEach(item => {
      let index = trueFalseScoreAreaIds.indexOf(item.areaId)
      item.enableHalfScore = trueFalseScoreEnableHalfScores[index] || false
      item.emptyDefault = trueFalseScoreEmptyDefaults[index] || TrueFalseScoreResultEnum.Cross.id
    })
  })
}

// 给ScanUnit添加选做题题块信息
export function addScanUnitSelectsInfo(unit, selectDefine) {
  if (!unit) {
    return
  }
  unit.selects.forEach(block => {
    let group = selectDefine.find(g => g.blocks.some(b => b.blockId == block.blockId))
    if (!group) {
      return
    }
    let selectBlock = group.blocks.find(b => b.blockId == block.blockId)
    block.questionCode = selectBlock.questionCode
    block.selectGroupName = group.selectGroupName
    block.selectCount = group.selectCount
  })
}

/**
 *  给ScanUnit添加新准考号、客观题答案、主观题打分、选做题号，便于页面修改
 */
// 给ScanUnit设置/重置新准考号
export function resetScanUnitNewAdmissionNumForEdit(unit) {
  if (!unit) {
    return
  }
  unit.admissionNumNew = unit.admissionNum
}

// 给ScanUnit设置/重置新客观题答案
export function resetScanUnitNewObjectivesForEdit(unit) {
  if (!unit) {
    return
  }
  unit.objectives.forEach(obj => {
    obj.newAnswer = obj.answer
    obj.newStatus = obj.status
  })
}

// 给ScanUnit设置/重置新主观题打分
export function resetScanUnitNewScoresForEdit(unit) {
  if (!unit) {
    return
  }
  unit.scores.manualScores.forEach(q => {
    q.newScore = q.score
    q.newScoreNumber = isManualScoreValid(q.fullScore, q.newScore) ? Number(q.newScore) : null
    q.newStatus = q.status
  })
  unit.scores.barScores.forEach(q => {
    q.newScore = q.score
    q.newStatus = q.status
    q.newAnswers = q.answers.slice()
  })
  unit.scores.trueFalseScores.forEach(q => {
    q.newStatus = q.status
    q.answers.forEach(item => {
      item.newAnswer = item.answer
      item.newStatus = item.status
    })
  })
}

// 给ScanUnit设置/重置新选做题号
export function resetScanUnitNewSelectsForEdit(unit) {
  if (!unit) {
    return
  }
  unit.selects.forEach(block => {
    block.newAnswer = block.answer
    block.newStatus = block.status
  })
}

/**
 * ScanUnit主观题
 */
// 获取ScanUnit主观题列表
export function getScanUnitSubjectives(unit) {
  if (!unit || !unit.scores) {
    return []
  }
  return [...unit.scores.manualScores, ...unit.scores.barScores, ...unit.scores.trueFalseScores].sort(
    (a, b) => a.fullCode - b.fullCode
  )
}

// 组织答题卡图片上显示需要的打分数据
export function getScanUnitScoresDataForShow(unitSubjectives) {
  return unitSubjectives
    .map(q => {
      if (q.scoreType == ScoreTypeEnum.Number.id) {
        return {
          scoreType: q.scoreType,
          fullCode: q.fullCode,
          score: q.newScore,
          status: q.newStatus,
        }
      } else if (q.scoreType == ScoreTypeEnum.Bar.id) {
        return {
          scoreType: q.scoreType,
          fullCode: q.fullCode,
          status: q.newStatus,
          answers: q.newAnswers,
        }
      } else if (q.scoreType == ScoreTypeEnum.TrueFalse.id) {
        return {
          scoreType: q.scoreType,
          fullCode: q.fullCode,
          answers: q.answers.map(x => ({
            areaId: x.areaId,
            answer: x.newAnswer,
            status: x.newStatus,
          })),
        }
      } else {
        return null
      }
    })
    .filter(Boolean)
}

// ScanUnit原始打分是否全部有效
export function isScanUnitRawScoresValid(unitSubjectives) {
  return unitSubjectives.every(q => {
    if (q.scoreType == ScoreTypeEnum.Number.id) {
      return isManualScoreValid(q.fullScore, q.score)
    } else if (q.scoreType == ScoreTypeEnum.Bar.id) {
      return isBarScoreValid(q.fullScore, q.barScoreCells, q.answers)
    } else if (q.scoreType == ScoreTypeEnum.TrueFalse.id) {
      return q.answers.every(answerItem => isTrueFalseScoreValid(answerItem.enableHalfScore, answerItem.answer))
    } else {
      return false
    }
  })
}

// ScanUnit新打分是否全部有效
export function isScanUnitNewScoresValid(unitSubjectives) {
  return unitSubjectives.every(q => {
    if (q.scoreType == ScoreTypeEnum.Number.id) {
      return isManualScoreValid(q.fullScore, q.newScore)
    } else if (q.scoreType == ScoreTypeEnum.Bar.id) {
      return isBarScoreValid(q.fullScore, q.barScoreCells, q.newAnswers)
    } else if (q.scoreType == ScoreTypeEnum.TrueFalse.id) {
      return q.answers.every(answerItem => isTrueFalseScoreValid(answerItem.enableHalfScore, answerItem.newAnswer))
    } else {
      return false
    }
  })
}

// 获取ScanUnit已修改的打分
export function getScanUnitChangedSubjectives(unitSubjectives) {
  return {
    manualScores: getChangedManualScores(unitSubjectives).map(q => ({
      questionId: q.questionId,
      score: q.newScore,
      status: q.newStatus,
    })),
    barScores: getChangedBarScores(unitSubjectives).map(q => ({
      questionId: q.questionId,
      score: q.newScore,
      status: q.newStatus,
      answers: q.newAnswers,
    })),
    trueFalseScores: getChangedTrueFalesScores(unitSubjectives).map(q => ({
      questionId: q.questionId,
      status: q.newStatus,
      answers: q.answers.map(item => ({
        areaId: item.areaId,
        answer: item.newAnswer,
        status: item.newStatus,
      })),
    })),
  }
}

// 获取ScanUnit已修改打分文本内容
export function getScanUnitChangedSubjectivesTextContent(unitSubjectives) {
  let list = []
  getChangedManualScores(unitSubjectives).forEach(q => {
    list.push({
      questionId: q.questionId,
      text: String(q.score),
      newText: String(q.newScore),
    })
  })
  getChangedBarScores(unitSubjectives).forEach(q => {
    list.push({
      questionId: q.questionId,
      text: getBarScoreTextContent(q.score, q.answers, q.barScoreCells, q.score == null || q.score == q.newScore),
      newText: getBarScoreTextContent(
        q.newScore,
        q.newAnswers,
        q.barScoreCells,
        q.newScore == null || q.score == q.newScore
      ),
    })
  })
  getChangedTrueFalesScores(unitSubjectives).forEach(q => {
    list.push({
      questionId: q.questionId,
      text: q.answers.map(item => getTrueFalseScoreTextContent(item.answer)).join(''),
      newText: q.answers.map(item => getTrueFalseScoreTextContent(item.newAnswer)).join(''),
    })
  })
  list.sort((a, b) => a.questionId - b.questionId)
  return list.map(x => `${x.questionId}：${x.text} 改为 ${x.newText}`).join('<br>')
}

function getChangedManualScores(unitSubjectives) {
  return unitSubjectives.filter(q => q.scoreType == ScoreTypeEnum.Number.id).filter(q => q.newScore != q.score)
}

function getChangedBarScores(unitSubjectives) {
  return unitSubjectives
    .filter(q => q.scoreType == ScoreTypeEnum.Bar.id)
    .filter(
      q => q.newAnswers.length != q.answers.length || q.newAnswers.some((answer, idx) => answer != q.answers[idx])
    )
}

function getChangedTrueFalesScores(unitSubjectives) {
  return unitSubjectives
    .filter(q => q.scoreType == ScoreTypeEnum.TrueFalse.id)
    .filter(q => q.answers.some(item => item.newAnswer != item.answer))
}

export function getBarScoreTextContent(score, answers, cells, showAnswers) {
  let str = score == null ? '异常' : String(score)
  if (showAnswers) {
    str += `[${answers
      .map((answer, idx) => {
        if (answer == BarScoreStatusEnum.UnMark.id) {
          return ''
        }
        let value = cells[idx].value
        return value == 100 ? '满' : String(value)
      })
      .filter(Boolean)
      .join(',')}]`
  }
  return str
}

export function getTrueFalseScoreTextContent(result) {
  if (result == TrueFalseScoreResultEnum.Empty.id) {
    return '[空]'
  }
  let entry = TrueFalseScoreResultEnum.getEntryById(result)
  return entry ? entry.display : ''
}

export function changeScanUnitSubjectiveScore(unitSubjectives, data) {
  let question = unitSubjectives.find(q => q.fullCode == data.fullCode)
  if (question) {
    if (data.scoreType == ScoreTypeEnum.Number.id) {
      setManualScoreNewScoreStr(question, data.scoreStr)
    } else if (data.scoreType == ScoreTypeEnum.Bar.id) {
      setBarScoreNewAnswers(question, data.answers)
    } else if (data.scoreType == ScoreTypeEnum.TrueFalse.id) {
      setTrueFalseScoreNewAnswer(question, data.areaId, data.answer)
    }
  }
}

/**
 * ScanUnit选做题
 */
// 获取ScanUnit已修改选做文本内容
export function getScanUnitChangedSelectsTextContent(unitSelects) {
  let list = []
  unitSelects.forEach(select => {
    if (select.blocks.some(block => block.answer != block.newAnswer)) {
      let oldAnswerStr = select.blocks
        .filter(block => block.answer == SelectResultEnum.Marked.id)
        .map(block => block.questionCode)
        .join(',')
      let newAnswerStr = select.blocks
        .filter(block => block.newAnswer == SelectResultEnum.Marked.id)
        .map(block => block.questionCode)
        .join(',')
      list.push(`${select.selectGroupName}：${oldAnswerStr} 改为 ${newAnswerStr}`)
    }
  })
  return list.join('<br>')
}

/**
 * 改客观题
 */
// 由答案字符串转为数组
export function getObjectiveAnswerList(answer) {
  return Array.from((answer || '').replace(/[^(a-zA-Z)]/g, ''))
}

// 改客观题答案
export function setObjectiveNewAnswer(q, newAnswer) {
  q.newAnswer = newAnswer
  q.newStatus = ObjectiveRecognizeStatusEnum.Normal.id
}

/**
 * 改手写打分
 */
// 判断手写打分是否有效
export function isManualScoreValid(fullScore, scoreStr) {
  if (typeof scoreStr != 'string' || scoreStr.length == 0) {
    return false
  }
  // 非零整数最后一位不能为空
  let intStr = scoreStr.split('.')[0]
  if (/[1-9]\s+$/.test(intStr)) {
    return false
  }
  let score = Number(scoreStr)
  return score >= 0 && score <= fullScore
}

// 由打分数值转为打分字符串
export function getManualScoreStr(integer, precision, score) {
  if (typeof score != 'number') {
    return ''
  }

  // 分成整数部分和小数部分
  let scoreStr = String(score)
  let [integerStr, decimalStr] = scoreStr.split('.')
  if (!integerStr) {
    integerStr = ''
  }
  if (!decimalStr) {
    decimalStr = ''
  }

  // 整数部分截取
  integerStr = integerStr.substring(integerStr.length - integer)
  // 整数部分前缀空格
  integerStr = integerStr.padStart(integer, ' ')
  // 小数部分截取
  decimalStr = decimalStr.substring(0, precision)
  // 小数部分后缀空格
  decimalStr = decimalStr.padEnd(precision, ' ')
  if (decimalStr.length) {
    return `${integerStr}.${decimalStr}`
  } else {
    return integerStr
  }
}

// 改手写打分分数（分数数值）
export function setManualScoreNewScoreNumber(q, scoreNumber) {
  // 清空时恢复原数据
  if (typeof scoreNumber != 'number' || Number.isNaN(scoreNumber)) {
    q.newScoreNumber = null
    q.newScore = q.score
    q.newStatus = q.status
    return
  }
  q.newScoreNumber = scoreNumber
  let roundedNumber = roundNumber(scoreNumber, q.manualScorePrecision)
  q.newScore = getManualScoreStr(q.manualScoreInteger, q.manualScorePrecision, roundedNumber)
  q.newStatus = ScoreRecognizeStatusEnum.Normal.id
  // 输入框闪烁
  setTimeout(() => (q.newScoreNumber = roundedNumber), 0)
}

// 改手写打分分数（分数字符串）
export function setManualScoreNewScoreStr(q, scoreStr) {
  q.newScore = scoreStr
  q.newStatus = ScoreRecognizeStatusEnum.Normal.id
  if (isManualScoreValid(q.fullScore, scoreStr)) {
    q.newScoreNumber = roundNumber(Number(scoreStr), 1)
  } else {
    q.newScoreNumber = null
  }
}

/**
 * 改打分条打分
 */
// 判断打分条打分是否有效
export function isBarScoreValid(fullScore, cells, answers) {
  if (answers.length != cells.length) {
    return false
  }
  // 1. 至少划一格
  // 2. 每组最多划一格
  // 3. 划了满分则不能划其他分
  // 4. 划分不超过满分
  let markedGroupSet = new Set()
  let markedFullScore = false
  let score = 0
  for (let i = 0; i < cells.length; i++) {
    let cell = cells[i]
    let answer = answers[i]
    // 未涂跳过，存疑当作已涂
    if (answer == BarScoreStatusEnum.UnMark.id) {
      continue
    }
    // 同一组内最多涂一个
    if (markedGroupSet.has(cell.group)) {
      return false
    }
    markedGroupSet.add(cell.group)
    if (cell.value == 100) {
      markedFullScore = true
      score += fullScore
    } else {
      score += cell.value
    }
  }
  if (markedGroupSet.size == 0) {
    return false
  } else if (markedFullScore) {
    return markedGroupSet.size == 1
  } else {
    return score <= fullScore
  }
}

// 获取打分条分数
export function getBarScoreValue(fullScore, cells, answers) {
  let score = 0
  for (let i = 0; i < cells.length; i++) {
    if (answers[i] != BarScoreStatusEnum.UnMark.id) {
      let value = cells[i].value
      if (value == 100) {
        value = fullScore
      }
      score += value
    }
  }
  return score
}

// 改打分条分数（打分格）
export function setBarScoreNewAnswers(q, newAnswers) {
  q.newAnswers = newAnswers
  if (isBarScoreValid(q.fullScore, q.barScoreCells, q.newAnswers)) {
    q.newScore = getBarScoreValue(q.fullScore, q.barScoreCells, q.newAnswers)
  } else {
    q.newScore = null
  }
  q.newStatus = ScoreRecognizeStatusEnum.Normal.id
}

// 改打分条分数（分数数值）
export function setBarScoreNewScore(q, newScore) {
  let notNumber = typeof newScore != 'number' || Number.isNaN(newScore)
  let overflow = !notNumber && !(newScore <= q.fullScore && newScore >= 0)
  let emptyAnswers = q.answers.map(() => BarScoreStatusEnum.UnMark.id)
  // 输入错误则清空
  if (notNumber || overflow) {
    q.newScore = notNumber ? null : newScore
    q.newStatus = ScoreRecognizeStatusEnum.Normal.id
    q.newAnswers = emptyAnswers
    return
  }

  // 分组按分值从大到小排
  let groups = groupArray(q.barScoreCells, cell => cell.group).map(g => g.group.sort((a, b) => b.value - a.value))
  groups.sort((a, b) => {
    let maxValueA = Math.max(...a.map(cell => cell.value))
    let maxValueB = Math.max(...b.map(cell => cell.value))
    return maxValueB - maxValueA
  })
  // 依次在每组找一个格子
  let markedCells = []
  let score = newScore
  for (let g = 0; g < groups.length; g++) {
    let cell = groups[g].find(cell => {
      if (cell.value == 100) {
        return q.fullScore == score
      } else {
        return cell.value <= score
      }
    })
    if (cell) {
      markedCells.push(cell)
      score -= cell.value
      if (score <= 0) {
        break
      }
    }
  }

  if (score <= 0) {
    q.newAnswers = q.barScoreCells.map(cell =>
      markedCells.includes(cell) ? BarScoreStatusEnum.Marked.id : BarScoreStatusEnum.UnMark.id
    )
  } else {
    q.newAnswers = emptyAnswers
  }
  q.newScore = newScore
  q.newStatus = ScoreRecognizeStatusEnum.Normal.id
}

/**
 * 改对错框打分
 */
// 判断对错框打分是否有效
export function isTrueFalseScoreValid(enableHalfScore, answer) {
  let validResults = [
    TrueFalseScoreResultEnum.Tick.id,
    TrueFalseScoreResultEnum.Cross.id,
    TrueFalseScoreResultEnum.Empty.id,
  ]
  if (enableHalfScore) {
    validResults.push(TrueFalseScoreResultEnum.Half.id)
  }
  return validResults.includes(answer)
}

// 改对错框分数
export function setTrueFalseScoreNewAnswer(q, areaId, newAnswer) {
  let answerItem = q.answers.find(x => x.areaId == areaId)
  if (answerItem) {
    q.status = ScoreRecognizeStatusEnum.Normal.id
    answerItem.newAnswer = newAnswer
    answerItem.newStatus = TrueFalseScoreStatusEnum.Certitude.id
  }
}

/**
 * 改选做题号
 */
// 判断选做题号是否有效
export function isSelectValid(selectCount, blockAnswers) {
  return blockAnswers.filter(answer => answer == SelectResultEnum.Marked.id).length == selectCount
}

// 改选做题号
export function setSelectNewAnswer(selectGroup, blockId, checked) {
  let block = selectGroup.blocks.find(x => x.blockId == blockId)
  if (!block) {
    return
  }
  block.newAnswer = checked ? SelectResultEnum.Marked.id : SelectResultEnum.UnMark.id
  let checkedCount = selectGroup.blocks.filter(block => block.newAnswer == SelectResultEnum.Marked.id).length
  let newStatus =
    checkedCount == 0
      ? SelectRecognizeStatusEnum.EmptyChoice.id
      : checkedCount < selectGroup.selectCount
      ? SelectRecognizeStatusEnum.LessChoice.id
      : checkedCount > selectGroup.selectCount
      ? SelectRecognizeStatusEnum.MultiChoice.id
      : SelectRecognizeStatusEnum.Normal.id
  selectGroup.blocks.forEach(block => {
    block.newStatus = newStatus
  })
}
