/**
 * 扫描模板
 */

import { isPositiveInteger } from '@/utils/number'
import { sumBy } from '@/utils/math'
import { groupArray, duplicate } from '@/utils/array'
import LocateModeEnum from '@/enum/scan_template/locate_mode'
import AdmissionTypeEnum from '@/enum/scan_template/admission_type'

export default class Template {
  constructor() {
    // 是否双面
    this.isDoubleSide = true
    // 页面信息
    // Array[Page]
    this.pages = []
    // 题目信息
    this.questions = {
      // Array[Objective]
      objectives: [],
      // Array[Subjective]
      subjectives: [],
    }
  }

  check({ subjectObjectives, subjectBlocks, pageSizeList, checkSubjectMark }) {
    if (this.pages.length == 0) {
      return {
        errors: ['无页面'],
        warnings: [],
      }
    }

    let errors = []
    let warnings = []
    let checkFunctions = [
      this.checkLocate,
      this.checkTitle,
      this.checkAdmissionNum,
      this.checkObjectives,
      this.checkSubjectives,
      this.checkPageMark,
      this.checkMissingMark,
      this.checkPage,
      this.checkRect,
      this.checkQuestions,
      this.checkSelect,
    ]
    if (checkSubjectMark) {
      checkFunctions.push(this.checkSubjectMark)
    }
    checkFunctions.forEach(func => {
      let checkFuncResult = func.apply(this, [{ subjectObjectives, subjectBlocks, pageSizeList }])
      if (checkFuncResult.errors && checkFuncResult.errors.length > 0) {
        errors.push(...checkFuncResult.errors)
      }
      if (checkFuncResult.warnings && checkFuncResult.warnings.length > 0) {
        warnings.push(...checkFuncResult.warnings)
      }
    })
    return {
      errors,
      warnings,
    }
  }
  getPageNamesText(pages) {
    return pages
      .map(page => {
        if (this.isDoubleSide) {
          return `第${Math.ceil(page.pageIndex / 2)}张${page.pageIndex % 2 == 1 ? '正面' : '背面'}`
        } else {
          return `第${page.pageIndex}张`
        }
      })
      .join('、')
  }
  checkLocate({ pageSizeList }) {
    let correctType = this.pages[0].pageParam.correctType
    if (this.pages.some(page => page.pageParam.correctType != correctType)) {
      return {
        errors: ['各页面定位方式不一致'],
      }
    }

    let errors = []
    let warnings = []
    if (correctType == LocateModeEnum.Anchor.id) {
      let anchorNotEnoughPages = []
      let anchor3Pages = []
      let anchorNotInCornerPages = []
      this.pages.forEach(p => {
        let anchorCount = 0
        let hasAnchorNotInCorner = false
        let pageSize = pageSizeList[p.pageIndex - 1]
        if (!pageSize) {
          errors.push(`不存在序号为${p.pageIndex}的页面`)
          return
        }
        p.pageParam.anchorMarks.forEach((anchor, idx) => {
          if (!anchor) {
            return
          }
          anchorCount++
          if (hasAnchorNotInCorner) {
            return
          }
          let distanceX =
            idx == 1 || idx == 2 ? pageSize.width - anchor.matchArea.x : anchor.matchArea.x + anchor.matchArea.width
          let distanceY =
            idx == 2 || idx == 3 ? pageSize.height - anchor.matchArea.y : anchor.matchArea.y + anchor.matchArea.height
          if (
            !(distanceX > 0 && distanceX <= pageSize.width / 4) ||
            !(distanceY > 0 && distanceY <= pageSize.height / 4)
          ) {
            hasAnchorNotInCorner = true
          }
        })
        if (anchorCount < 3) {
          anchorNotEnoughPages.push(p)
        } else if (anchorCount == 3) {
          anchor3Pages.push(p)
        }
        if (hasAnchorNotInCorner) {
          anchorNotInCornerPages.push(p)
        }
      })
      if (anchorNotEnoughPages.length > 0) {
        errors.push(`以下页面缺少定位点：${this.getPageNamesText(anchorNotEnoughPages)}`)
      }
      if (anchor3Pages.length > 0) {
        warnings.push(
          `以下页面只有三个定位点：${this.getPageNamesText(anchor3Pages)}，框选第四个定位点可提高定位准确度`
        )
      }
      if (anchorNotInCornerPages.length > 0) {
        warnings.push(
          `以下页面定位点不在边角区域内：${this.getPageNamesText(
            anchorNotInCornerPages
          )}，在边角区域内框选定位点可提高定位准确度`
        )
      }
    } else if (correctType == LocateModeEnum.Line.id) {
      let noLinePages = this.pages.filter(p => !p.pageParam.lineArea)
      if (noLinePages.length > 0) {
        errors.push(`以下页面无定位线：${this.getPageNamesText(noLinePages)}`)
      }
    } else if (correctType == LocateModeEnum.Text.id) {
      let noTextPages = this.pages.filter(p => !p.pageParam.textArea)
      if (noTextPages.length > 0) {
        errors.push(`以下页面无定位文字：${this.getPageNamesText(noTextPages)}`)
      }
    } else {
      errors.push('定位方式错误')
    }

    return {
      errors,
      warnings,
    }
  }
  checkTitle() {
    let errors = []
    let noTitlePages = this.pages.filter(
      p => !p.pageParam.titleArea && p.pageParam.correctType != LocateModeEnum.Text.id
    )
    if (noTitlePages.length > 0) {
      errors.push(`以下页面未框选标题：${this.getPageNamesText(noTitlePages)}`)
    }
    return {
      errors,
    }
  }
  checkAdmissionNum() {
    let frontPages = this.isDoubleSide ? this.pages.filter(p => (p.pageIndex - 1) % 2 == 0) : this.pages
    let admissionType = frontPages[0].studentInfo.admissionType
    if (frontPages.some(p => p.studentInfo.admissionType != admissionType)) {
      return {
        errors: ['各页面准考号类型不一致'],
      }
    }

    let errors = []
    let warnings = []
    if (admissionType == AdmissionTypeEnum.Omr.id) {
      let noAdmissionNumPages = frontPages.filter(p => !p.studentInfo.admissionOmrArea)
      if (noAdmissionNumPages.length > 0) {
        errors.push(`以下页面未框选准考号：${this.getPageNamesText(noAdmissionNumPages)}`)
      }
      let firstPageAdmissionOmrArea = frontPages[0].studentInfo.admissionOmrArea
      if (
        frontPages.some(
          p =>
            p.studentInfo.admissionOmrArea.optionCount != firstPageAdmissionOmrArea.optionCount ||
            p.studentInfo.admissionOmrArea.options.length != firstPageAdmissionOmrArea.options.length
        )
      ) {
        errors.push(`各页面准考号长度或选项个数不一致`)
      }
      let admissionNumLength = firstPageAdmissionOmrArea.options.length / firstPageAdmissionOmrArea.optionCount
      if (!isPositiveInteger(admissionNumLength)) {
        errors.push(`准考号各位选项个数不一致`)
      }
      if (firstPageAdmissionOmrArea.optionCount != 10) {
        warnings.push(
          `准考号每一位选项一般为 0 - 9，本模板准考号区域选项个数为${firstPageAdmissionOmrArea.optionCount}`
        )
      }
    } else if (admissionType == AdmissionTypeEnum.Barcode.id) {
      let noAdmissionNumPages = frontPages.filter(
        p => !p.studentInfo.admissionBarcodeArea || !p.studentInfo.admissionBarcodeArea.searchArea
      )
      if (noAdmissionNumPages.length > 0) {
        errors.push(`以下页面未框选准考号：${this.getPageNamesText(noAdmissionNumPages)}`)
      }
    } else if (admissionType == AdmissionTypeEnum.Handwriting.id) {
      let noAdmissionNumPages = frontPages.filter(
        p =>
          !p.studentInfo.admissionHandwritingArea ||
          !p.studentInfo.admissionHandwritingArea.cells ||
          p.studentInfo.admissionHandwritingArea.cells.length == 0
      )
      if (noAdmissionNumPages.length > 0) {
        errors.push(`以下页面未框选准考号：${this.getPageNamesText(noAdmissionNumPages)}`)
      }
    } else {
      errors.push('准考号类型错误')
    }

    return {
      errors,
      warnings,
    }
  }
  checkObjectives({ subjectObjectives }) {
    let errors = []

    let templateObjectiveQuestionCodes = []
    this.pages.forEach(page => {
      let hasUnknownObjectiveArea = false
      page.objectiveAreas.forEach(objArea => {
        let templateObjective = this.questions.objectives.find(x => x.areaId == objArea.id)
        if (!templateObjective) {
          hasUnknownObjectiveArea = true
          return
        }

        let questionCodes = templateObjective.questions.map(q => q.questionCode).sort((a, b) => a - b)
        if (templateObjective.questions.length * objArea.optionCount != objArea.options.length) {
          let objAreaName = '客观题区域题'
          let firstCode = questionCodes[0]
          let lastCode = questionCodes[questionCodes.length - 1]
          if (firstCode == lastCode) {
            objAreaName += `${firstCode}`
          } else if (lastCode - firstCode + 1 == questionCodes.length) {
            objAreaName += `${firstCode} ~ ${lastCode}`
          } else {
            objAreaName += questionCodes.join(',')
          }
          errors.push(`${objAreaName}选项总数错误`)
          return
        }

        let notFoundQuestionCodes = []
        let optionCountErrorQuestionCodes = []
        questionCodes.forEach(questionCode => {
          let obj = subjectObjectives.find(x => x.questionCode == questionCode)
          if (!obj) {
            notFoundQuestionCodes.push(questionCode)
            return
          }
          templateObjectiveQuestionCodes.push(questionCode)
          if (obj.optionCount != objArea.optionCount) {
            optionCountErrorQuestionCodes.push(questionCode)
          }
        })
        if (notFoundQuestionCodes.length > 0) {
          errors.push(`找不到客观题：${notFoundQuestionCodes.join('、')}`)
        }
        if (optionCountErrorQuestionCodes.length > 0) {
          errors.push(`客观题选项个数错误：${optionCountErrorQuestionCodes.join('、')}`)
        }
      })
      if (hasUnknownObjectiveArea) {
        errors.push(`${this.getPageNamesText([page])}存在无题目信息的客观题区域`)
      }
    })

    let duplicateQuestionCodes = groupArray(templateObjectiveQuestionCodes, q => q)
      .filter(g => g.group.length > 1)
      .map(g => g.key)
    if (duplicateQuestionCodes.length > 0) {
      errors.push(`客观题区域中以下题号重复：${duplicateQuestionCodes.join('、')}`)
    }
    if (errors.length == 0) {
      let unAddQuestionCodes = subjectObjectives
        .filter(q => !templateObjectiveQuestionCodes.includes(q.questionCode))
        .map(q => q.questionCode)
      if (unAddQuestionCodes.length > 0) {
        errors.push(`存在未框选的客观题：${unAddQuestionCodes.join('、')}`)
      }
    }

    return {
      errors,
    }
  }
  checkSubjectives({ subjectBlocks }) {
    let errors = []

    // 检查模板题块定义与科目题块定义是否一致
    let notExistBlockNames = []
    let wrongQuestionBlockNames = []
    this.questions.subjectives.forEach(block => {
      let subjectBlock = subjectBlocks.find(x => x.blockId == block.subjectiveId)
      if (!subjectBlock) {
        notExistBlockNames.push(block.blockName)
        return
      }
      let blockQuestionCodesText = block.questions
        .map(q => `${q.questionCode}.${q.branchCode}`)
        .sort()
        .join(',')
      let subjectBlockQuestionCodesText = subjectBlock.questions
        .map(q => `${q.questionCode}.${q.branchCode}`)
        .sort()
        .join(',')
      if (blockQuestionCodesText != subjectBlockQuestionCodesText) {
        wrongQuestionBlockNames.push(subjectBlock.blockName)
      }
    })
    if (notExistBlockNames.length > 0) {
      errors.push(`科目评卷题块中不存在以下题块：${notExistBlockNames.filter(Boolean).join('、')}`)
    }
    if (wrongQuestionBlockNames.length > 0) {
      errors.push(`以下题块包含题目与科目评卷题块不一致：${wrongQuestionBlockNames.join('、')}`)
    }

    // 检查是否有未添加区域的评卷题块
    let noAreaBlockNames = subjectBlocks
      .filter(block => this.questions.subjectives.every(({ subjectiveId }) => subjectiveId != block.blockId))
      .map(block => block.blockName)
    if (noAreaBlockNames.length > 0) {
      errors.push(`以下题块尚未添加主观题区域：${noAreaBlockNames.join('、')}`)
    }

    // 检查题块包含区域是否与页面主观题区域一致
    let blockAreas = []
    this.pages.forEach(page => {
      let hasUnknownSubjectiveArea = false
      page.subjectiveAreas.forEach(subjArea => {
        let templateBlock = this.questions.subjectives.find(x => x.areaIds.includes(subjArea.id))
        if (!templateBlock) {
          hasUnknownSubjectiveArea = true
        } else {
          let block = blockAreas.find(x => x.subjectiveId == templateBlock.subjectiveId)
          if (!block) {
            block = {
              subjectiveId: templateBlock.subjectiveId,
              areaIds: [],
            }
            blockAreas.push(block)
          }
          block.areaIds.push(subjArea.id)
        }
      })
      if (hasUnknownSubjectiveArea) {
        errors.push(`${this.getPageNamesText([page])}存在无题块信息的主观题区域`)
      }
    })
    let wrongAreaBlockNames = []
    this.questions.subjectives.forEach(templateBlock => {
      let block = blockAreas.find(x => x.subjectiveId == templateBlock.subjectiveId)
      if (!block || block.areaIds.length != templateBlock.areaIds.length) {
        wrongAreaBlockNames.push(templateBlock.blockName)
      }
    })
    if (wrongAreaBlockNames.length > 0) {
      errors.push(`以下题块包含了不存在的主观题区域：${wrongAreaBlockNames.filter(Boolean).join('、')}`)
    }

    return {
      errors,
    }
  }
  checkPageMark() {
    let warnings = []
    let noPageMarkPages = this.pages.filter(p => !p.pageParam.pageMark && !p.pageParam.pageMark2)
    if (noPageMarkPages.length > 0) {
      warnings.push(`以下页面未框选页码标记：${this.getPageNamesText(noPageMarkPages)}`)
    }
    return {
      warnings,
    }
  }
  checkMissingMark() {
    let warnings = []
    if (!this.pages[0].studentInfo.missingMarkArea) {
      warnings.push('未框选缺考标记')
    }
    return {
      warnings,
    }
  }
  checkSubjectMark() {
    let warnings = []
    let noSubjectMarkPages = this.pages.filter(
      p => (!this.isDoubleSide || (p.pageIndex - 1) % 2 == 0) && !p.pageParam.subjectArea
    )
    if (noSubjectMarkPages.length > 0) {
      warnings.push(`以下页面未框选科目标记：${this.getPageNamesText(noSubjectMarkPages)}`)
    }
    return {
      warnings,
    }
  }
  checkPage() {
    let warnings = []
    let noAreaPages = this.pages.filter(p => p.objectiveAreas.length == 0 && p.subjectiveAreas.length == 0)
    if (noAreaPages.length > 0) {
      warnings.push(`以下页面中未框选客观题或主观题：${this.getPageNamesText(noAreaPages)}`)
    }
    return {
      warnings,
    }
  }
  // 检查矩形框
  checkRect({ pageSizeList }) {
    let errors = []
    this.pages.forEach(p => {
      let objectiveOptions = []
      p.objectiveAreas.forEach(objArea => objectiveOptions.push(...objArea.options))
      let rectGroups = [
        {
          name: '客观题选项',
          rects: objectiveOptions,
        },
        {
          name: '主观题区域',
          rects: p.subjectiveAreas.map(subjArea => subjArea.area),
        },
        {
          name: '定位点',
          rects: p.pageParam.anchorMarks.filter(Boolean).map(anchor => anchor.matchArea),
        },
        {
          name: '定位线',
          rects: [p.pageParam.lineArea],
        },
        {
          name: '标题',
          rects: [p.pageParam.titleArea],
        },
        {
          name: '页码标记',
          rects: [p.pageParam.pageMark && p.pageParam.pageMark.matchArea],
        },
        {
          name: '科目标记',
          rects: [p.pageParam.subjectArea],
        },
        {
          name: '准考号填涂区',
          rects: p.studentInfo.admissionOmrArea ? p.studentInfo.admissionOmrArea.options : [],
        },
        {
          name: '准考号条码区',
          rects: p.studentInfo.admissionBarcodeArea ? [p.studentInfo.admissionBarcodeArea.searchArea] : [],
        },
        {
          name: '缺考标记',
          rects: p.studentInfo.missingMarkArea ? [p.studentInfo.missingMarkArea.matchArea] : [],
        },
        {
          name: '选做标记',
          rects: (p.selectMarkAreas || []).map(area => area.matchArea),
        },
      ]

      let pageSize = pageSizeList[p.pageIndex - 1]
      rectGroups.forEach(({ name, rects }) => {
        if (rects.some(rect => rect && this.isRectOutOfPage(rect, pageSize))) {
          errors.push(`第${p.pageIndex}页：${name}超出页面`)
        }
      })
    })
    return {
      errors,
    }
  }
  isRectOutOfPage(rect, pageSize) {
    return (
      rect.x < 0 ||
      rect.y < 0 ||
      rect.width < 0 ||
      rect.height < 0 ||
      rect.x + rect.width >= pageSize.width ||
      rect.y + rect.height >= pageSize.height
    )
  }
  // 检查题号
  checkQuestions({ subjectObjectives, subjectBlocks }) {
    let errors = []
    let warnings = []
    // 客观题题号重复
    let objectiveQuestionCodes = subjectObjectives.map(q => q.questionCode)
    let duplicateObjectiveQuestionCodes = duplicate(objectiveQuestionCodes)
    if (duplicateObjectiveQuestionCodes.length > 0) {
      errors.push(`以下客观题题号重复：${duplicateObjectiveQuestionCodes.join('、')}`)
    }
    // 主观题按题号分组
    let subjectivesGroupByQuestionCode = []
    subjectBlocks.forEach(block =>
      block.questions.forEach(b => {
        let q = subjectivesGroupByQuestionCode.find(x => x.questionCode == b.questionCode)
        if (!q) {
          q = {
            questionCode: b.questionCode,
            branches: [],
          }
          subjectivesGroupByQuestionCode.push(q)
        }
        q.branches.push(b)
      })
    )
    // 检查主观题
    let duplicateSubjectiveQuestionCodes = []
    let duplicateSubjectiveQuestionCodes2 = []
    let duplicateFullCodes = []
    let branchCodeNotContinuous = []
    let topicCodeNotSame = []
    subjectivesGroupByQuestionCode.forEach(({ questionCode, branches }) => {
      // 与客观题题号重复
      if (objectiveQuestionCodes.includes(questionCode)) {
        duplicateSubjectiveQuestionCodes.push(questionCode)
      }
      // 主观题内部题号重复
      else if (branches.length > 1 && branches.some(b => b.branchCode == 0)) {
        duplicateSubjectiveQuestionCodes2.push(questionCode)
      } else {
        // 小题号重复
        let branchCodes = branches.map(b => b.branchCode).sort((a, b) => a - b)
        let duplicateBranchCodes = duplicate(branchCodes)
        if (duplicateBranchCodes.length > 0) {
          duplicateFullCodes.push(duplicateBranchCodes.map(branchCode => `${questionCode}.${branchCode}`).join(','))
        }
        // 小题号不连续
        else if (
          (branchCodes.length == 1 && branchCodes[0] != 0) ||
          (branchCodes.length > 1 && (branchCodes[0] != 1 || branchCodes[branchCodes.length - 1] != branchCodes.length))
        ) {
          branchCodeNotContinuous.push(branchCodes.map(branchCode => `${questionCode}.${branchCode}`).join(','))
        }
        // 大题号不一致
        if (branches.some(b => b.topicCode != branches[0].topicCode)) {
          topicCodeNotSame.push(branchCodes.map(branchCode => `${questionCode}.${branchCode}`).join(','))
        }
      }
    })
    if (duplicateSubjectiveQuestionCodes.length > 0) {
      errors.push(`以下主观题题号与客观题重复：${duplicateSubjectiveQuestionCodes.join('、')}`)
    }
    if (duplicateSubjectiveQuestionCodes2.length > 0) {
      errors.push(`以下主观题题号重复：${duplicateSubjectiveQuestionCodes2.join('、')}`)
    }
    if (duplicateFullCodes.length > 0) {
      errors.push(`以下小题号重复：${duplicateFullCodes.join('、')}`)
    }
    if (branchCodeNotContinuous.length > 0) {
      warnings.push(`以下小题号不连续：${branchCodeNotContinuous.join('、')}`)
    }
    if (topicCodeNotSame.length > 0) {
      errors.push(`以下小题大题号不一致：${topicCodeNotSame.join('、')}`)
    }
    return {
      errors,
      warnings,
    }
  }
  // 检查选做
  checkSelect({ subjectBlocks }) {
    let errors = []

    // 选做题组
    let selectBlocks = subjectBlocks.filter(block => block.selectGroupName)
    let selectGroups = groupArray(selectBlocks, block => block.selectGroupName).map(g => ({
      selectGroupName: g.group[0].selectGroupName,
      selectCount: g.group[0].selectCount,
      blocks: g.group,
      selectMarkAreaPageIndexes: [],
    }))

    // 检查选做题定义
    selectGroups.forEach(group => {
      let title = `选做题组【${group.selectGroupName}】`
      // 检查选做组内题数
      if (group.blocks.length == 1) {
        errors.push(`${title}内不应只有一个题块`)
      } else if (
        !(isPositiveInteger(group.selectCount) && group.selectCount >= 1 && group.selectCount < group.blocks.length)
      ) {
        errors.push(`${title}选做个数错误：共${group.blocks.length}个题块，而选做个数为${group.selectCount}`)
      } else if (group.blocks.some(block => block.selectCount != group.selectCount)) {
        errors.push(`${title}内各题块记录的选做个数不一致`)
      }

      // 检查题块题目
      let blocksQuestionCodes = []
      let blockTopicCodeSet = new Set()
      let blockFullScoreSet = new Set()
      let questionCodeNotSameBlockNames = []
      let otherBlockHasSameQuestionCodeBlockNames = []
      group.blocks.forEach(block => {
        let questionCode = block.questions[0].questionCode
        if (block.questions.some(q => q.questionCode != questionCode)) {
          questionCodeNotSameBlockNames.push(block.blockName)
        } else if (
          subjectBlocks.some(x => x.blockId != block.blockId && x.questions.some(q => q.questionCode == questionCode))
        ) {
          otherBlockHasSameQuestionCodeBlockNames.push(block.blockName)
        } else {
          blocksQuestionCodes.push(questionCode)
          blockTopicCodeSet.add(block.questions[0].topicCode)
          if (block.questions.some(q => !q.fullScore)) {
            blockFullScoreSet.add(null)
          } else {
            blockFullScoreSet.add(sumBy(block.questions, q => q.fullScore))
          }
        }
      })
      blocksQuestionCodes.sort((a, b) => a - b)
      if (questionCodeNotSameBlockNames.length > 0) {
        errors.push(`${title}内以下题块不应包含多道题目：${questionCodeNotSameBlockNames.join('、')}`)
      } else if (otherBlockHasSameQuestionCodeBlockNames.length > 0) {
        errors.push(`${title}内以下题块未包含题目的所有小题：${otherBlockHasSameQuestionCodeBlockNames.join('、')}`)
      } else if (blockTopicCodeSet.size > 1) {
        errors.push(`${title}内各题目应属于同一大题`)
      } else if (blocksQuestionCodes.some((code, idx) => idx > 0 && code != blocksQuestionCodes[idx - 1] + 1)) {
        errors.push(`${title}内各题号不连续`)
      } else if (blockFullScoreSet.has(null)) {
        errors.push(`${title}内存在题目未设置分数`)
      } else if (blockFullScoreSet.size > 1) {
        errors.push(`${title}内各题目分数不相同`)
      } else {
        let groupNameCodes = group.selectGroupName.split('_').map(Number)
        if (
          blocksQuestionCodes.length != groupNameCodes.length ||
          blocksQuestionCodes.some(code => !groupNameCodes.includes(code))
        ) {
          errors.push(`${title}名称与包含题目的题号不一致`)
        }
      }
    })

    // 检查选做标记
    let selectMarkIdSubjectiveMap = new Map()
    let groupBySelectMarkId = groupArray(
      this.questions.subjectives.filter(subjective => subjective.selectMarkId),
      subjective => subjective.selectMarkId
    )
    groupBySelectMarkId.forEach(g => {
      selectMarkIdSubjectiveMap.set(g.key, g.group[0])
      if (g.group.length > 1) {
        errors.push(`以下选做题块对应的选做标记不应相同：${g.group.map(subjective => subjective.blockName).join('、')}`)
      }
    })
    this.pages.forEach(page => {
      let hasUnknownSelectMarkArea = false
      page.selectMarkAreas.forEach(area => {
        let subjective = selectMarkIdSubjectiveMap.get(area.id)
        if (!subjective) {
          hasUnknownSelectMarkArea = true
          return
        }
        selectMarkIdSubjectiveMap.delete(area.id)

        let selectGroup = selectGroups.find(g => g.blocks.some(b => b.blockId == subjective.subjectiveId))
        if (selectGroup) {
          selectGroup.selectMarkAreaPageIndexes.push(page.pageIndex)
        }

        let blockIdx = selectBlocks.findIndex(block => block.blockId == subjective.subjectiveId)
        if (blockIdx < 0) {
          hasUnknownSelectMarkArea = true
        } else {
          selectBlocks.splice(blockIdx, 1)
        }
      })
      if (hasUnknownSelectMarkArea) {
        errors.push(`${this.getPageNamesText([page])}存在无对应题块的选做标记`)
      }
    })
    if (selectMarkIdSubjectiveMap.size > 0) {
      errors.push(
        `模板中以下题块在科目评卷题块中未设置为选做题：${Array.from(selectMarkIdSubjectiveMap.values())
          .map(subjective => subjective.blockName)
          .join('、')}`
      )
    }
    if (selectBlocks.length > 0) {
      errors.push(`以下选做题块无对应选做标记：${selectBlocks.map(block => block.blockName).join('、')}`)
    }

    // 选做题组的所有选做标记应在一个扫描单元，此处简化处理为限制在同一张（更严格）
    selectGroups.forEach(group => {
      if (group.selectMarkAreaPageIndexes.length == 0) {
        return
      }
      let getPaperIndex = pageIndex => (this.isDoubleSide ? Math.ceil(pageIndex / 2) : pageIndex)
      let paperIndex = getPaperIndex(group.selectMarkAreaPageIndexes[0])
      if (group.selectMarkAreaPageIndexes.some(pageIndex => getPaperIndex(pageIndex) != paperIndex)) {
        errors.push(`选做题组【${group.selectGroupName}】的各选做标记应位于同一张`)
      }
    })

    return {
      errors,
    }
  }
}
