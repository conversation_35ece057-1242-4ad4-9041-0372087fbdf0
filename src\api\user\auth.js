import ajax from '@/api/ajax'

// 获取手机验证码
export function apiGetVerificationCode({ mobile, identifier }) {
  return ajax.post({
    url: 'user/auth/forgetTeacherPwd',
    params: {
      mobile,
      identifier,
    },
    requestName: '请求发送验证码到指定手机',
  })
}

// 根据手机验证码重置密码
export function apiResetPasswordByVerificationCode(params) {
  return ajax.put({
    url: 'user/auth/resetPwd',
    params: {
      mobile: params.phoneNumber,
      verifyCode: params.verificationCode,
      newPwd: params.newPassword,
    },
    requestName: '重置密码',
  })
}

export function apiGetAuthByWeChat(params) {
  params.checkSchoolLogin = true
  return ajax.get({
    url: 'user/oauth/address/login/weixin',
    params,
  })
}

export function apiGetAuthByDingTalk(params) {
  params.checkSchoolLogin = true
  return ajax.get({
    url: 'user/oauth/address/login/dingding',
    params,
  })
}

export function apiGetAuthByQQ(params) {
  params.checkSchoolLogin = true
  return ajax.get({
    url: 'user/oauth/address/login/qq',
    params,
  })
}

export function apiGetBindingAuthByWeChat(params) {
  return ajax.get({
    url: 'user/oauth/bindUser/address/weixin',
    params,
  })
}

export function apiGetBindingAuthByDingTalk(params) {
  return ajax.get({
    url: 'user/oauth/bindUser/address/dingding',
    params,
  })
}

export function apiGetBindingAuthByQQ(params) {
  return ajax.get({
    url: 'user/oauth/bindUser/address/qq',
    params,
  })
}

export function apiLoginByTheThird(params) {
  return ajax
    .get({
      url: `user/oauth/login${params}`,
      params: {},
    })
    .then(data => {
      if (data.token) {
        //微信已绑定账号
        return {
          token: data.token,
          info: {
            userId: data.userInfo.userId || '',
            userName: data.userInfo.userName || '',
            realName: data.userInfo.realName || '',
            identity: data.userInfo.identity || '',
            sex: data.userInfo.sex || '',
            mobile: data.userInfo.mobile || '',
            email: data.userInfo.email || '',
            avatarUrl: data.userInfo.avatar || '',
            schoolId: data.userInfo.schoolId || '',
            schoolName: data.userInfo.schoolName || '',
            schoolType: data.userInfo.schoolType || 0,
            roles: data.userInfo.roles.map(id => Number(id)),
            teachings: data.userInfo.teachings || [],
            schoolPositions: data.userInfo.schoolPositions || [],
            isSystem: data.userInfo.isSys || false,
            needChangePassword: data.userInfo.needChangePwd || false,
            needUpdatePassword: data.userInfo.isExpiredPwd || false,
          },
        }
      }
      //微信未绑定账号
      return data
    })
}

export function apiBindAccountToThird(params) {
  return ajax
    .post({
      url: 'user/oauth/login/bindAccount',
      data: params,
    })
    .then(data => {
      return {
        token: data.token,
        info: {
          userId: data.userInfo.userId || '',
          userName: data.userInfo.userName || '',
          realName: data.userInfo.realName || '',
          identity: data.userInfo.identity || '',
          sex: data.userInfo.sex || '',
          mobile: data.userInfo.mobile || '',
          email: data.userInfo.email || '',
          avatarUrl: data.userInfo.avatar || '',
          schoolId: data.userInfo.schoolId || '',
          schoolName: data.userInfo.schoolName || '',
          schoolType: data.userInfo.schoolType || 0,
          roles: data.userInfo.roles.map(id => Number(id)),
          teachings: data.userInfo.teachings || [],
          schoolPositions: data.userInfo.schoolPositions || [],
          isSystem: data.userInfo.isSys || false,
          needChangePassword: data.userInfo.needChangePwd || false,
          needUpdatePassword: data.userInfo.isExpiredPwd || false,
        },
      }
    })
}

export function apiBindThirdToAccount(params) {
  return ajax.get({
    url: 'user/oauth/bindOpenId',
    params: params,
  })
}

export function apiUnbindAccountToThird(params) {
  return ajax.post({
    url: 'user/oauth/unbindOpenId',
    params: params,
  })
}

export function apiGetOpenIdBinding(params) {
  return ajax.get({
    url: `user/oauth/login/getOpenId${params}`,
    params: {},
  })
}

export function apiGetMobileByUserId(params) {
  return ajax.get({
    url: 'user/auth/getMobileByUserId',
    params,
  })
}

export function apiBindOpenIdByCodeAndState(params) {
  return ajax.put({
    url: `user/auth/bindOpenIdByOpenIdAndState${params}`,
    params: {},
  })
}

export function apiGetQRCodeInitParams(params) {
  return ajax.get({
    url: 'user/oauth/address/getParam/weixin',
    params: params,
  })
}

export function apiGetSmsCode(params) {
  return ajax.get({
    url: 'user/miniapp/teacher/auth/sendLoginSms',
    params: {
      mobile: params.mobile,
    },
  })
}

export function apiLoginBySmsCode(params) {
  return ajax
    .post({
      url: `user/auth/loginWithSms`,
      params: {
        mobile: params.mobile,
        smsCode: params.smsCode,
        from: 'pc',
      },
    })
    .then(data => {
      if (data.token) {
        return {
          token: data.token,
          info: {
            userId: data.userInfo.userId || '',
            userName: data.userInfo.userName || '',
            realName: data.userInfo.realName || '',
            identity: data.userInfo.identity || '',
            sex: data.userInfo.sex || '',
            mobile: data.userInfo.mobile || '',
            email: data.userInfo.email || '',
            avatarUrl: data.userInfo.avatar || '',
            schoolId: data.userInfo.schoolId || '',
            schoolName: data.userInfo.schoolName || '',
            schoolType: data.userInfo.schoolType || 0,
            roles: data.userInfo.roles.map(id => Number(id)),
            teachings: data.userInfo.teachings || [],
            schoolPositions: data.userInfo.schoolPositions || [],
            isSystem: data.userInfo.isSys || false,
            needChangePassword: data.userInfo.needChangePwd || false,
            needUpdatePassword: data.userInfo.isExpiredPwd || false,
          },
        }
      }
      return data
    })
}
