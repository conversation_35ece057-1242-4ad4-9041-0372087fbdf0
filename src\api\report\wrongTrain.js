import ajax from '@/api/ajax'
import { formatDate } from '@/utils/date'

export function apiGetWrongTrainExamByGrade(params) {
  return ajax.get({
    url: 'report/wrongTrain/wrongTrainExamGrade',
    params: {
      gradeId: params.gradeId,
      subjectId: params.subjectId,
      beginTime: params.startDate ? formatDate(params.startDate) : undefined,
      endTime: params.endDate ? formatDate(params.endDate) : undefined,
    },
    requestName: '获取学情组卷考试',
  })
}

export function apiGetWrongTrainExamByClass(params) {
  return ajax.get({
    url: 'report/wrongTrain/wrongTrainExamClass',
    params: {
      gradeId: params.gradeId,
      subjectId: params.subjectId,
      classIds: params.classIds.join(','),
      beginTime: params.startDate ? formatDate(params.startDate) : undefined,
      endTime: params.endDate ? formatDate(params.endDate) : undefined,
    },
    requestName: '获取学情组卷考试',
  })
}

export function apiGetWrongTrainQuestionByGrade(params) {
  return ajax.get({
    url: 'report/wrongTrain/wrongTrainQuesGrade',
    params: {
      gradeId: params.gradeId,
      subjectId: params.subjectId,
      beginTime: params.startDate ? formatDate(params.startDate) : undefined,
      endTime: params.endDate ? formatDate(params.endDate) : undefined,
      examIds: params.examIds.join(','),
      minScoreRate: params.minScoreRate,
      maxScoreRate: params.maxScoreRate,
      order: params.order,
      maxQuestionCount: params.maxQuestionCount,
    },
    requestName: '获取学情组卷题目',
  })
}

export function apiGetWrongTrainQuestionByClass(params) {
  return ajax.get({
    url: 'report/wrongTrain/wrongTrainQuesClass',
    params: {
      gradeId: params.gradeId,
      subjectId: params.subjectId,
      classIds: params.classIds.join(','),
      beginTime: params.startDate ? formatDate(params.startDate) : undefined,
      endTime: params.endDate ? formatDate(params.endDate) : undefined,
      examIds: params.examIds.join(','),
      minScoreRate: params.minScoreRate,
      maxScoreRate: params.maxScoreRate,
      order: params.order,
      maxQuestionCount: params.maxQuestionCount,
    },
    requestName: '获取学情组卷题目',
  })
}
