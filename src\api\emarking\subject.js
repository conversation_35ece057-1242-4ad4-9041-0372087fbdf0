import ajax from '@/api/ajax'
import { transformUser } from './exam'
import { Paper } from '@/helpers/qlib/paper'
import { Question } from '@/helpers/qlib/question'
import { sortQuestionFunction } from '@/helpers/emarking/miscellaneous'
import ObjectiveQuestion from '@/helpers/emarking/objective_question'
import SubjectiveQuestion from '@/helpers/emarking/subjective_question'

function transformStudent(responseStudent) {
  return {
    studentId: responseStudent.studentId,
    studentName: responseStudent.realName || '',
    studentCode: responseStudent.studentCode || '',
    admissionNumber: responseStudent.admission || '',
    schoolId: responseStudent.schoolId,
    schoolName: responseStudent.schoolName || '',
    classId: responseStudent.classId,
    className: responseStudent.className || '',
    roomNo: responseStudent.room || '',
    seatNo: responseStudent.seat || '',
    idPhoto: responseStudent.idPhoto || '',
    venueName: responseStudent.venueName || '',
    room: responseStudent.room || '',
  }
}

/**
 * 考生
 */
export function apiGetExamStudents(params) {
  let sendParams = {
    examId: params.examId,
    examSubjectId: params.examSubjectId,
    schoolId: params.schoolId || null,
    classId: params.classId || null,
    key: params.keyword || null,
    size: params.pageSize,
    page: params.currentPage,
  }

  if (sendParams.examSubjectId)
    return ajax
      .get({
        url: 'mark/subject/studentListByExamSubject',
        params: sendParams,
        requestName: '获取科目考生',
      })
      .then(data => {
        return {
          total: data.total,
          students: data.records.map(s => transformStudent(s)),
        }
      })
  else
    return ajax
      .get({
        url: 'mark/subject/studentListByExam',
        params: sendParams,
        requestName: '获取全部科目考生',
      })
      .then(data => {
        return {
          total: data.total,
          students: data.records.map(s => transformStudent(s)),
        }
      })
}

export function apiGetStudentRoomNoAndSeatNo(params) {
  return ajax.get({
    url: 'mark/subject/examStudent/rooms',
    params: {
      examId: params.examId,
      studentId: params.studentId,
    },
    requestName: '获取考生考场号座位号',
  })
}

export function apiImportExamStudentsFromExcel(data) {
  let sendData = {
    examId: data.examId,
    // examSubjectId: data.examSubjectId,
    file: data.file,
    // sheetName: data.sheetName,
    checkPrefixExamNum: data.checkPrefixExamNum,
  }

  if (data.examSubjectId) {
    sendData.examSubjectId = data.examSubjectId
  }

  if (data.sheetName) {
    sendData.sheetName = data.sheetName
  }

  if (sendData.examSubjectId)
    return ajax.upload({
      url: 'mark/subject/importSubjectStudentFromExcel',
      data: sendData,
    })
  else
    return ajax.upload({
      url: 'mark/subject/importStudentFromExcel',
      data: sendData,
    })
}
export function apiImportMultipleSubjectStudent(requestParams) {
  return ajax.upload({
    url: 'mark/subject/importMultiSubjectStudent',
    data: {
      file: requestParams.file, // *file
      sheetsJson: requestParams.sheetsJson, // String
      examId: requestParams.examId, // String
      checkPrefixExamNum: requestParams.checkPrefixExamNum, // Boolean
    },
    // requestName: '从Excel导入多科学生',
  })
}

// 导入补考学生
export function apiImportMakeUpStudents({ examId, examSubjectId, checkPrefixExamNum, file }) {
  return ajax.upload({
    url: 'mark/subject/makeUpStu',
    data: {
      examId,
      examSubjectId,
      checkPrefixExamNum,
      file,
    },
  })
}

export function apiGetExamStudentStatistic(params) {
  let sendParams = {
    examId: params.examId,
    examSubjectId: params.examSubjectId,
  }

  if (sendParams.examSubjectId) {
    return ajax.get({
      url: 'mark/subject/getSchoolStudentStatByExamSubjectId',
      params: sendParams,
      requestName: '获取科目考生统计',
    })
  } else {
    return ajax.get({
      url: 'mark/subject/getSchoolStudentStatByExamId',
      params: sendParams,
      requestName: '获取考生统计',
    })
  }
}

export function apiGetExamStudentExportExcel(params) {
  let sendParams = {
    examId: params.examId,
    examSubjectId: params.examSubjectId,
    schoolId: params.schoolId,
  }

  if (sendParams.examSubjectId)
    return ajax.download({
      url: 'mark/subject/downExamSubjectStudentList',
      params: sendParams,
      requestName: '导出科目考生列表',
    })
  else
    return ajax.download({
      url: 'mark/subject/downExamStudentList',
      params: sendParams,
      requestName: '导出考生列表',
    })
}

export function apiEditExamStudent(data) {
  let sendParams = {
    examId: data.examId,
    examSubjectId: data.examSubjectId,
  }
  let sendData = {
    studentId: data.studentId,
    admission: data.admissionNumber || null,
    room: data.roomNo || null,
    seat: data.seatNo || null,

    studentCode: data.studentCode,
    realName: data.realName,
    sex: data.sex,
    className: data.className,
    schoolName: data.schoolName,
    schoolId: data.schoolId,
  }

  if (sendParams.examSubjectId)
    return ajax.put({
      url: 'mark/subject/examSubjectStudent',
      params: {
        examSubjectId: sendParams.examSubjectId,
      },
      data: sendData,
      requestName: '修改考生信息',
    })
  else
    return ajax.put({
      url: 'mark/subject/examStudent',
      params: {
        examId: sendParams.examId,
      },
      data: sendData,
      requestName: '修改考生信息',
    })
}

export function apiRemoveExamStudents(data) {
  if (data.examSubjectId)
    return ajax.put({
      url: 'mark/subject/delExamSubjectStudent',
      params: {
        examSubjectId: data.examSubjectId,
      },
      data: data.studentIds,
      requestName: '删除指定学生',
    })
  else
    return ajax.put({
      url: 'mark/subject/delExamStudent',
      params: {
        examId: data.examId,
      },
      data: data.studentIds,
      requestName: '删除指定学生',
    })
}

export function apiRemoveAllExamStudents(data) {
  if (data.examSubjectId)
    return ajax.delete({
      url: 'mark/subject/allExamSubjectStudent',
      data: data.classIds,
      params: {
        examSubjectId: data.examSubjectId,
        schoolId: data.schoolId,
      },
      requestName: '批量删除考生',
    })
  else
    return ajax.delete({
      url: 'mark/subject/allExamStudent',
      data: data.classIds,
      params: {
        examId: data.examId,
        schoolId: data.schoolId,
      },
      requestName: '批量删除考生',
    })
}

export function apiAddGradeStudentsIntoExamStudents(data) {
  return ajax.put({
    url: 'mark/subject/reInitExamStudent',
    params: {
      examId: data.examId,
    },
    data: data.schoolIds,
    requestName: '添加本年级所有考生',
  })
}

export function apiGetPaperStudentInfo(params) {
  return ajax.get({
    url: 'mark/subject/stuMsg',
    params: {
      subjectiveId: params.blockId,
      subjectiveItemId: params.studentBlockId,
    },
    requestName: '获取考生信息',
  })
}

// 获取未添加为考生的学生
export function apiGetUnAddExamStudents(params) {
  let url = params.examSubjectId ? '/mark/subject/unAddExamSubjectStudents' : '/mark/subject/unAddExamStudents'
  return ajax.get({
    url,
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      schoolId: params.schoolId,
    },
    requestName: '获取学校学生',
  })
}

export function apiAddExamStudents(data) {
  let url = data.examSubjectId ? '/mark/subject/addExamSubjectStudents' : '/mark/subject/addExamStudents'
  return ajax.post({
    url,
    params: {
      examId: data.examId,
      examSubjectId: data.examSubjectId,
      schoolId: data.schoolId,
    },
    data: data.studentIds,
    requestName: '添加考生',
  })
}

export function apiDownloadSeatTablePDF(params) {
  return ajax.download({
    url: '/mark/subject/seatTablePDF',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      schoolId: params.schoolId,
      roomNos: (params.roomNos || []).join(','),
      columns: params.columns,
      rows: params.rows,
      startFromRight: params.startFromRight,
      wrapReverse: params.wrapReverse,
      extendRow: params.extendRow,
      isPodiumBottom: params.isPodiumBottom || false,
      showSubjectName: params.showSubjectName || false,
      showSchoolName: params.showSchoolName || false,
    },
    requestName: '导出考场座位表',
  })
}

export function apiGetExamStudentSeatCard(params) {
  return ajax.download({
    url: 'mark/subject/seatCardPDF',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      schoolId: params.schoolId,
    },
    requestName: '导出桌贴',
  })
}

/**
 * 扫描员
 */
export function apiAddScanner(data) {
  return ajax.post({
    url: 'mark/subject/scanner',
    data: {
      examId: data.examId,
      examSubjectId: data.examSubjectId,
      userId: data.userId,
      stationIds: data.stationIds,
      enableEditTemplate: data.enableEditTemplate,
      enableUpTemplate: data.enableUploadTemplate,
      role: data.role,
    },
    requestName: '添加扫描员',
  })
}

export function apiEditScannerEditTemplate(data) {
  return ajax.put({
    url: 'mark/subject/scanner/editTemplate',
    params: {
      examId: data.examId,
      examSubjectId: data.examSubjectId,
      scannerId: data.userId,
      enabled: data.enableEditTemplate,
    },
    requestName: '修改扫描员修改模板权限',
  })
}

export function apiEditScannerUploadTemplate(data) {
  return ajax.put({
    url: 'mark/subject/scanner/upTemplate',
    params: {
      examId: data.examId,
      examSubjectId: data.examSubjectId,
      scannerId: data.userId,
      enabled: data.enableUploadTemplate,
    },
    requestName: '修改扫描员上传模板权限',
  })
}

export function apiEditScannerRole(data) {
  return ajax.put({
    url: 'mark/subject/scanner/role',
    params: {
      examId: data.examId,
      examSubjectId: data.examSubjectId,
      scannerId: data.userId,
      role: data.role,
    },
    requestName: '修改扫描员角色',
  })
}

export function apiRemoveScanner(params) {
  return ajax.delete({
    url: 'mark/subject/scanner',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      scannerId: params.userId,
    },
    requestName: '删除扫描员',
  })
}

export function apiGetScanner(params) {
  return ajax
    .get({
      url: 'mark/subject/scanner',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
      },
      requestName: '获取扫描员',
    })
    .then(data =>
      (data || []).map(x => ({
        scanStations: x.scanStations || [],
        scanner: transformUser(x.scanner),
        enableEditTemplate: x.enableEditTemplate,
        enableUploadTemplate: x.enableUpTemplate,
        role: x.role,
      }))
    )
}

export function apiCopyScanners(data) {
  return ajax.post({
    url: 'mark/subject/scanner/copy',
    data: data,
    requestName: '复制扫描员',
  })
}

export function apiImportScanner({ examSubjectId, file }) {
  return ajax.upload({
    url: 'mark/subject/scanner/import',
    params: {
      examSubjectId,
    },
    data: { file },
  })
}

export function apiCheckDeleteScannerBatchIsFeasible(requestParams) {
  return ajax.post({
    url: 'mark/subject/scanner/checkCanDeleteScannerBatch',
    params: {
      examSubjectId: requestParams.examSubjectId, // String*
    },
    data: requestParams.scanUserIds, // Array[String]*
  })
}

export function apiDeleteScannerBatch(requestParams) {
  return ajax.delete({
    url: 'mark/subject/scanner/deleteBatch',
    params: {
      examId: requestParams.examId, // String*
      examSubjectId: requestParams.examSubjectId, // String*
    },
    data: requestParams.scannerIds,
    requestName: '批量删除扫描员',
  })
}

/**
 * 试卷结构
 */
export function apiGetBindPaper(params) {
  return ajax
    .get({
      url: 'mark/subject/paper/bangPaper',
      params: {
        examSubjectId: params.examSubjectId,
      },
      requestName: '获取已绑定试卷',
    })
    .then(data => ({
      id: data && data.id && data.id != '0' ? data.id : '',
      name: (data && data.name) || '',
    }))
}

// 绑定试卷
export function apiBindPaper({ examSubjectId, paperId, updateAnswer }) {
  return ajax.post({
    url: 'mark/subject/paper/bangPaper',
    params: { examSubjectId, paperId, updateAnswer },
  })
}

export function apiGetBindPaperContent(params) {
  return ajax
    .get({
      url: 'mark/subject/paper/bangPaperContent',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
      },
    })
    .then(data => {
      if (!data) {
        return null
      }
      return new Paper(
        data.paperInfo,
        data.basketPaperStyle,
        data.paperStructure,
        (data.questions || []).map(q => Question.import(q))
      )
    })
}

export function apiUnBindPaper(params) {
  return ajax.put({
    url: 'mark/subject/paper/unbindPaper',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
    },
    requestName: '解除绑定试卷',
  })
}

export function apiGetObjectiveQuestions(params) {
  return ajax
    .get({
      url: 'mark/subject/paper/objective',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
      },
      requestName: '获取客观题',
    })
    .then(data => (data || []).map(q => ObjectiveQuestion.import(q)))
}

export function apiSaveObjectiveQuestions(paper) {
  return ajax.post({
    url: 'mark/subject/paper/objective',
    params: {
      examId: paper.examId,
      examSubjectId: paper.examSubjectId,
    },
    data: paper.objectiveQuestions.map(q => q.export()),
    requestName: '保存客观题',
  })
}

export function apiModifyObjectiveWhenever(data) {
  return ajax.put({
    url: 'mark/subject/paper/obj',
    params: {
      examId: data.examId,
      examSubjectId: data.examSubjectId,
    },
    data: data.question,
    requestName: '修改客观题',
  })
}

export function apiGetSubjectiveQuestions(params) {
  return ajax
    .get({
      url: 'mark/subject/paper/subjective',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
      },
      requestName: '获取主观题',
    })
    .then(data => (data || []).map(q => SubjectiveQuestion.import(q)))
}

// 保存主观题，若出错弹窗提示
export function apiSaveSubjectiveQuestions(paper) {
  return ajax.post({
    url: 'mark/subject/paper/subjective',
    params: {
      examId: paper.examId,
      examSubjectId: paper.examSubjectId,
    },
    data: paper.subjectiveQuestions.map(q => q.export()),
  })
}

export function apiSetAdditionalQuestion(params) {
  return ajax.put({
    url: 'mark/subject/paper/isAdditional',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      questionCode: params.questionCode,
      branchCode: params.branchCode,
      isAdditional: params.isAdditional,
    },
    requestName: '设置附加题',
  })
}

/**
 * 题块设置
 */
export function apiGetBlocks(params) {
  return ajax
    .get({
      url: 'mark/subject/block',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
      },
      requestName: '获取题块',
    })
    .then(data =>
      (data || []).map(x => ({
        blockId: x.blockId,
        blockName: x.blockName,
        strictMark: x.strictMark || false,
        autoScoreTypeId: x.isGiven || 0,
        selectGroupName: x.selectGroupName || null,
        selectCount: x.selectCount || null,
        imgPixelType: x.imgPixelType || null,
        aiScoreType: x.aiScoreType,
        aiScoreStatus: x.aiScoreStatus,
        aiScoreExtraJson: x.aiScoreExtraJson,
        questions: x.subjectives
          .map(q => {
            q.fullScore = q.score
            let subj = SubjectiveQuestion.import(q)
            subj.scoreInterval = q.scoreInterval
            return subj
          })
          .sort(sortQuestionFunction),
      }))
    )
}

export function apiAddBlock(data) {
  return ajax.post({
    url: 'mark/subject/block',
    data: {
      examId: data.examId,
      examSubjectId: data.examSubjectId,
      blockName: data.blockName,
      strictMark: data.strictMark || false,
      subjectives: data.questions.map(q => ({
        id: q.questionId,
        topicCode: q.topicCode,
        questionCode: q.questionCode,
        branchCode: q.branchCode,
        score: q.fullScore,
        scoreInterval: q.scoreInterval || null,
      })),
    },
    requestName: '新增题块',
  })
}

// 修改题块
export function apiEditBlock(data) {
  return ajax.put({
    url: 'mark/subject/block',
    data: {
      examId: data.examId,
      examSubjectId: data.examSubjectId,
      blockId: data.blockId,
      blockName: data.blockName,
      strictMark: data.strictMark || false,
      subjectives: data.questions.map(q => ({
        id: q.questionId,
        topicCode: q.topicCode,
        questionCode: q.questionCode,
        branchCode: q.branchCode,
        score: q.fullScore,
        scoreInterval: q.scoreInterval || null,
      })),
    },
  })
}

export function apiDeleteBlock(blockId) {
  return ajax.delete({
    url: 'mark/subject/block',
    params: {
      blockId,
    },
    requestName: '删除题块',
  })
}

// 合并给分
export function apiMergeBlockQuestions({
  blockId,
  questionCode,
  questionName,
  branchCode,
  branchName,
  topicCode,
  topicName,
  changeFullScore,
}) {
  return ajax.post({
    url: 'mark/subject/block/mergeMark',
    params: {
      blockId,
    },
    data: {
      questionCode,
      questionName,
      branchCode,
      branchName,
      topicCode,
      topicName,
      changeFullScore,
    },
  })
}

// 删除本地上传的扫描模板
export function apiDeleteLocalUploadScanTemplate(params) {
  return ajax.delete({
    url: 'mark/subject/template',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
    },
    requestName: '删除扫描模板',
  })
}

// 添加选做
export function apiAddSelectGroup({ examSubjectId, selectCount, blockIds }) {
  return ajax.post({
    url: 'mark/subject/block/addSelectGroup',
    data: {
      examSubjectId,
      selectCount,
      blockIds,
    },
  })
}

// 删除选做
export function apiDeleteSelectGroup({ examSubjectId, selectGroupName }) {
  return ajax.delete({
    url: 'mark/subject/block/delSelectGroup',
    params: {
      examSubjectId,
      groupName: selectGroupName,
    },
  })
}

/**
 * 上传模式
 */
export function apiChangeSubjectUploadMode(data) {
  return ajax.put({
    url: 'mark/subject/uploadMode',
    params: {
      examSubjectId: data.examSubjectId,
      uploadMode: data.uploadModeId,
      beginTime: data.beginTimeStr,
      endTime: data.endTimeStr,
    },
    requestName: '修改上传模式',
  })
}

export function apiGetProcessedSubjectFullScore(examSubjectId) {
  return ajax
    .get({
      url: 'mark/subject/fullScore',
      params: {
        examSubjectId,
      },
      requestName: '获取科目满分',
    })
    .then(data => ({
      objectiveFullScore: data.totalObjectiveScore,
      subjectiveFullScore: data.totalSubjectiveScore,
      writtenFullScore: data.writtenFullScore,
      otherFullScore: data.otherFullScore,
    }))
}

export function apiSetSubjectWrittenFullScore(params) {
  return ajax.put({
    url: 'mark/subject/writtenFullScore',
    params: {
      examSubjectId: params.examSubjectId,
      writtenFullScore: params.writtenFullScore,
    },
    requestName: '设置科目笔试满分',
  })
}

export function apiSetSubjectOtherFullScore(params) {
  return ajax.put({
    url: 'mark/subject/otherFullScore',
    params: {
      examSubjectId: params.examSubjectId,
      otherFullScore: params.otherFullScore,
    },
    requestName: '设置科目其他满分',
  })
}

export function apiGetSubjectiveBlockAnswer(requestParams) {
  // 仅限开评后使用
  return ajax.get({
    url: '/mark/subject/block/answerImgUrl',
    params: {
      blockId: requestParams.blockId,
    },
    requestName: '获取题块答案图',
  })
}

export function apiGetSubjectiveAnswers(requestParams) {
  // 只有考试管理员和科目负责人可以调
  return ajax.get({
    url: '/mark/subject/block/answerImgUrlMap',
    params: {
      examSubjectId: requestParams.examSubjectId,
    },
    requestName: '获取科目各个题块答案图',
  })
}

export function apiSetSubjectiveAnswer(requestParams) {
  return ajax.upload({
    url: '/mark/subject/block/setAnswerImg',
    params: {
      blockId: requestParams.blockId,
    },
    data: {
      file: requestParams.file,
    },
    requestName: '设置科目主观题题块答案',
  })
}

export function apiDeleteSubjectiveAnswer(requestParams) {
  return ajax.delete({
    url: '/mark/subject/block/deleteAnswerImg',
    params: {
      blockId: requestParams.blockId,
    },
    requestName: '删除主观题题块答案图',
  })
}

export function apiSetSubjectMarkingBlocksImagePixelType(requestParams) {
  return ajax.put({
    url: '/mark/subject/block/setBlockImgPixelType',
    params: {
      examSubjectId: requestParams.examSubjectId, // String*
    },
    data: requestParams.blocksImgPixelTypeSetting, // Array* [{blockId: String*, imgPixelType: String*}]
    requestName: '设置科目评卷题块扫描图像颜色',
  })
}

export function apiSetExamSubjectBatchScoreSize(requestParams) {
  return ajax.put({
    url: 'mark/subject/block/saveBatchScoreSize',
    params: {
      examSubjectId: requestParams.examSubjectId, // String*
    },
    data: requestParams.batchScoreSettings, // Array* [{batchScoreSize: Number, blockId: String}]
    requestName: '批量阅卷题块相关设置',
  })
}

// 试卷结构模板
export function apiGetPaperStructTemplate(requestParams) {
  return ajax.get({
    url: '/mark/subject/paper/templates',
    params: {
      gradeLevelId: (requestParams && requestParams.gradeLevelId) || '',
      gradeId: (requestParams && requestParams.gradeId) || '',
      subjectId: (requestParams && requestParams.subjectId) || '',
      keyword: (requestParams && requestParams.keyword) || '',
    },
    requestName: '获取试卷结构模板',
  })
}

export function apiSavePaperStructTemplate(requestParams) {
  return ajax.post({
    url: '/mark/subject/paper/saveAsTemplate',
    params: {
      paperTemplateName: requestParams.paperTemplateName,
      examSubjectId: requestParams.examSubjectId,
    },
    requestName: '保存试卷结构模板',
  })
}

export function apiApplyPaperStructTemplate(requestParams) {
  return ajax.put({
    url: '/mark/subject/paper/applyTemplate',
    params: {
      paperTemplateId: requestParams.paperTemplateId,
      examSubjectId: requestParams.examSubjectId,
    },
    requestName: '应用试卷结构模板',
  })
}

export function apiDeletePaperStructTemplate(requestParams) {
  return ajax.delete({
    url: '/mark/subject/paper/deletePaperTemplate',
    params: {
      paperTemplateId: requestParams.paperTemplateId,
    },
    requestName: '删除试卷结构模板',
  })
}

export function apiChangeBlockAutoScoreType(params) {
  return ajax.put({
    url: '/mark/subject/paper/addGivenQues',
    params: {
      examSubjectId: params.examSubjectId,
      subjectiveId: params.blockId,
      isGiven: params.autoScoreTypeId,
    },
    requestName: '设置主观题系统给分',
  })
}

export function apiSaveTestObjectiveQuestions(requestParams) {
  return ajax.post({
    url: 'mark/subject/paper/objective2',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
    },
    data: requestParams.objectives,
    // [
    //   {
    //     answer: 'string',
    //     answerScoreList: 'string',
    //     branchId: 'string',
    //     branchName: 'string',
    //     fullScore: 0,
    //     id: 0,
    //     isGiven: 0,
    //     optionCount: 0,
    //     questionCode: 0,
    //     questionId: 'string',
    //     questionName: 'string',
    //     questionType: 0,
    //     topicCode: 0,
    //     topicName: 'string',
    //   },
    // ],
    requestName: '批量更改手阅测试客观题设置',
  })
}

export function apiSaveTestSubjectiveQuestions(requestParams) {
  return ajax.post({
    url: 'mark/subject/paper/subjective2',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
    },
    data: requestParams.subjectives,
    //  [
    //   {
    //     blankScores: 'string',
    //     branchCode: 0,
    //     branchId: 'string',
    //     branchName: 'string',
    //     fullScore: 0,
    //     id: 0,
    //     isAdditional: true,
    //     questionCode: 0,
    //     questionId: 'string',
    //     questionName: 'string',
    //     topicCode: 0,
    //     topicName: 'string',
    //   },
    // ],
    requestName: '批量更改手阅测试主观题设置',
  })
}

export function apiCopyExamSubjectPaperStructure(requestParams) {
  return ajax.put({
    url: 'mark/subject/paper/copyTemplate',
    params: {
      isStructure: requestParams.isStructure,
      isTemplate: requestParams.isTemplate,
      examSubjectId: requestParams.examSubjectId,
      sourceExamSubjectId: requestParams.sourceExamSubjectId,
      isCrossSchool: requestParams.isCrossSchool,
    },
    requestName: '复制其它考试试卷结构及答题卡',
  })
}

export function apiDownloadClassExamStudentZip(requestParams) {
  return ajax.download({
    url: 'mark/subject/downClassExamStudentZip',
    params: {
      examId: requestParams.examId,
      schoolId: requestParams.schoolId,
    },
    requestName: '按班级导出考生（全部科目）',
  })
}

export function apiDownloadClassExamSubjectStudentZip(requestParams) {
  return ajax.download({
    url: 'mark/subject/downClassExamSubjectStudentZip',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      schoolId: requestParams.schoolId,
    },
    requestName: '按班级导出考生（具体科目）',
  })
}

export function apiDownloadVenueSeatCardPDF(requestParams) {
  return ajax.download({
    url: 'mark/subject/venueSeatCardPDF',
    params: {
      examId: requestParams.examId, // [string]
      examSubjectId: requestParams.examSubjectId, // [string]
      venueId: requestParams.venueId, // [integer]
    },
    requestName: '导出考点桌贴',
  })
}

export function apiDownloadVenuesSeatCardZip(requestParams) {
  return ajax.download({
    url: 'mark/subject/venuesSeatCardPDF',
    params: {
      examId: requestParams.examId, // [string]
      examSubjectId: requestParams.examSubjectId, // [string]
    },
    data: requestParams.venuesIds, // [Array[integer]],
    requestName: '导出选择的考点桌贴',
  })
}

export function apiGetSubjectiveBlockSolutions(requestParams) {
  return ajax
    .get({
      url: '/mark/subject/block/blockAnswer',
      params: {
        blockId: requestParams.blockId,
      },
      requestName: '获取题块答案',
    })
    .then(res => {
      return {
        answerImgUrl: res.answerImgUrl,
        quesDetailList: (res.quesDetailList || []).map(q => Question.import(q)),
        quesList: res.quesList,
      }
    })
}

export function apiSetBlockScoreIntervalAndStrictMarkBatch(requestParams) {
  return ajax.put({
    url: '/mark/subject/block/updateScoreIntervalStrictMarkBatch',
    params: {
      examId: requestParams.examId, // *String
      examSubjectId: requestParams.examSubjectId, // *String
    },
    data: requestParams.settings, // *Array [{blockId(String), blockName(String), strictMark(Boolean), subjectives(Array)[{id(Number), score(Number), scoreInterval(Number)}]}]
    requestName: '批量设置科目题块给分间隔及是否规范给分',
  })
}

export function apiSetScanEnable(params) {
  return ajax.put({
    url: 'mark/subject/setScanEnable',
    params: {
      examSubjectId: params.examSubjectId,
      enable: params.enable,
    },
    requestName: '设置科目扫描开启状态',
  })
}

export function apiGetClassExamSubjectStudents(params) {
  return ajax.get({
    url: 'mark/subject/classExamSubjectStudents',
    params: {
      examSubjectId: params.examSubjectId,
      classId: params.classId,
    },
    requestName: '查询班级科目考生',
  })
}
