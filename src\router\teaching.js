import Role from '@/enum/user/role'

export default {
  name: 'teaching',
  path: 'teaching',
  meta: {
    title: '精准教学',
    menu: '精准教学',
    name: '精准教学',
    roles: user => {
      return (
        user.schoolType == 0 &&
        user.roles.some(r =>
          [
            Role.SchoolAdministrator.id,
            Role.SchoolLeader.id,
            Role.GradeLeader.id,
            Role.SubjectLeader.id,
            Role.GradeSubjectLeader.id,
            Role.ClassLeader.id,
            Role.Teacher.id,
            Role.SystemAdministrator.id,
          ].includes(r)
        )
      )
    },
  },
  component: () => import('@/views/teaching/index.vue'),
  redirect: { name: 'subject-situation' },
  children: [
    {
      name: 'subject-situation',
      path: 'subject-situation',
      meta: {
        menu: '学科学情',
      },
      component: () => import('@/views/teaching/subject_situation/index.vue'),
      children: [
        {
          name: 'subject-situation-home',
          path: 'home/:gradeId?/:classId?/:subjectId?',
          meta: {
            menu: 'home',
          },
          component: () => import('@/views/teaching/subject_situation/home.vue'),
        },
        {
          name: 'knowledge-details',
          path: 'knowledge-details/:knowledgeId',
          meta: {
            menu: 'detail',
          },
          component: () => import('@/views/teaching/subject_situation/knowledgeDetails.vue'),
        },
      ],
    },
    {
      name: 'student-situation',
      path: 'student-situation',
      meta: {
        menu: '学生学情',
      },
      component: () => import('@/views/teaching/student_situation/index.vue'),
      children: [
        {
          name: 'student-situation-home',
          path: 'home/:gradeId?/:classId?/:subjectId?',
          meta: {
            menu: 'home',
          },
          component: () => import('@/views/teaching/student_situation/home.vue'),
        },
        {
          name: 'student-exam-details',
          path: 'student-exam-details/:studentId',
          meta: {
            menu: 'detail',
          },
          component: () => import('@/views/teaching/student_situation/studentExamDetails.vue'),
        },
      ],
    },
    {
      name: 'trace',
      path: 'trace',
      meta: {
        menu: '学业跟踪',
      },
      component: () => import('@/views/teaching/trace/index.vue'),
    },
    {
      name: 'increment_comment',
      path: 'increment_comment',
      meta: {
        menu: '增值评价',
      },
      component: () => import('@/views/research/increment_comment/index.vue'),
      children: [
        {
          name: 'increment_comment_home',
          path: 'increment_comment_home',
          meta: {
            menu: 'increment_comment_home',
          },
          component: () => import('@/views/research/increment_comment/home.vue'),
        },
        {
          name: 'comment_template',
          path: 'comment_template',
          meta: {
            menu: '评价模板',
          },
          component: () => import('@/views/research/comment_template/index.vue'),
          children: [
            {
              name: 'comment_template_home',
              path: 'comment_template_home',
              meta: {
                menu: 'comment_template_home',
              },
              component: () => import('@/views/research/comment_template/home.vue'),
            },
            {
              name: 'comment_template_edit',
              path: 'comment_template_edit/:templateId?',
              meta: {
                menu: 'comment_template_edit',
              },
              component: () => import('@/views/research/comment_template/comment_template_edit.vue'),
            },
          ],
        },
        {
          name: 'increment_comment_details',
          path: 'increment_comment_details/:projectId?',
          meta: {
            menu: 'increment_comment_details',
          },
          component: () => import('@/views/research/increment_comment/comment_details.vue'),
        },
      ],
    },
  ],
}
