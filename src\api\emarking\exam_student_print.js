import ajax from '@/api/ajax'

export function apiDownloadSeatTablePDFBySchool(params) {
  return ajax.download({
    url: '/mark/examStudentPrint/seatTablePDFBySchool',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      schoolId: params.schoolId,
      columnSeats: params.columnSeats.join(','),
      startFromRight: params.startFromRight,
      wrapReverse: params.wrapReverse,
      isPodiumBottom: params.isPodiumBottom || false,
    },
    requestName: '导出考场座位表',
  })
}

export function apiDownloadSeatTablePDFByVenue(requestParams) {
  return ajax.download({
    url: 'mark/examStudentPrint/seatTablePDFByVenue',
    params: {
      examId: requestParams.examId, // [string]
      examSubjectId: requestParams.examSubjectId, // [string]
      venueId: requestParams.venueId, // [integer]
    },
    data: {
      columnSeats: requestParams.columnSeats, // Array[Integer]
      startFromRight: requestParams.startFromRight, // [boolean]
      wrapReverse: requestParams.wrapReverse, // [boolean]
    },
    requestName: '导出考场座位表PDF（按考点）',
  })
}

export function apiDownloadSeatTableZipByVenues(requestParams) {
  return ajax.download({
    url: 'mark/examStudentPrint/seatTablePDFByVenues',
    params: {
      examId: requestParams.examId, // String
      examSubjectId: requestParams.examSubjectId, // String
    },
    data: {
      columnSeats: requestParams.columnSeats, // Array[Integer]
      startFromRight: requestParams.startFromRight, // Boolean
      wrapReverse: requestParams.wrapReverse, // Boolean
      venueIds: requestParams.venueIds, // Array[Integer]
    },
    requestName: '导出考场座位表Zip（按所选考点）',
  })
}
