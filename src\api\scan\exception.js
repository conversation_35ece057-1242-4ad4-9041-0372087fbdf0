import ajax from '@/api/ajax'
import { transformScanUnit } from './scanUnit'

// 个人查看异常统计
export function apiGetPersonalExceptionStats({ examSubjectId }) {
  return ajax.get({
    url: 'scan/exception/personalStat',
    params: {
      examSubjectId,
    },
    requestName: '获取异常统计',
  })
}

// 管理员查看扫描点异常统计
export function apiGetStationExceptionStats({ examSubjectId, scanStationId }) {
  return ajax.get({
    url: 'scan/exception/scanStationStat',
    params: {
      examSubjectId,
      scanStationId,
    },
    requestName: '获取异常统计',
  })
}

// 管理员查看扫描员异常统计
export function apiGetUserExceptionStats({ examSubjectId, scanUserId }) {
  return ajax.get({
    url: 'scan/exception/scanUserStat',
    params: {
      examSubjectId,
      scanUserId,
    },
    requestName: '获取异常统计',
  })
}

export function apiGetLocateExceptionPage({ examSubjectId, scanStationId, scanUserId, currentPage, pageSize }) {
  return ajax
    .get({
      url: 'scan/exception/locateEx/page',
      params: {
        examSubjectId,
        scanStationId,
        scanUserId,
        page: currentPage,
        size: pageSize,
      },
      requestName: '获取定位异常',
    })
    .then(data => {
      data.records.forEach(transformScanUnit)
      return data
    })
}

export function apiGetSubjectExceptionPage({ examSubjectId, scanStationId, scanUserId, currentPage, pageSize }) {
  return ajax
    .get({
      url: 'scan/exception/subjectEx/page',
      params: {
        examSubjectId,
        scanStationId,
        scanUserId,
        page: currentPage,
        size: pageSize,
      },
      requestName: '获取科目异常',
    })
    .then(data => {
      data.records.forEach(transformScanUnit)
      return data
    })
}

export function apiGetCornerExceptionPage({ examSubjectId, scanStationId, scanUserId, currentPage, pageSize }) {
  return ajax
    .get({
      url: 'scan/exception/cornerEx/page',
      params: {
        examSubjectId,
        scanStationId,
        scanUserId,
        page: currentPage,
        size: pageSize,
      },
      requestName: '获取折角异常',
    })
    .then(data => {
      data.records.forEach(transformScanUnit)
      return data
    })
}

export function apiGetAdmissionExceptionPage({ examSubjectId, scanStationId, scanUserId, currentPage, pageSize }) {
  return ajax
    .get({
      url: 'scan/exception/admissionEx/page',
      params: {
        examSubjectId,
        scanStationId,
        scanUserId,
        page: currentPage,
        size: pageSize,
      },
      requestName: '获取考号异常',
    })
    .then(data => {
      data.records.forEach(transformScanUnit)
      return data
    })
}

export function apiFindStudentByAdmissionNum(params) {
  return ajax.get({
    url: 'scan/exception/findStudentByAdmissionNum',
    params: {
      examSubjectId: params.examSubjectId,
      admissionNum: params.admissionNum,
    },
    requestName: '由准考号查找考生',
  })
}

export function apiFindStudentsByStudentName(params) {
  return ajax.get({
    url: 'scan/exception/findStudentsByStudentName',
    params: {
      examSubjectId: params.examSubjectId,
      studentName: params.studentName,
    },
    requestName: '由姓名查找考生',
  })
}

export function apiGetDuplicateExceptionPage({ examSubjectId, scanStationId, scanUserId, currentPage, pageSize }) {
  return ajax
    .get({
      url: 'scan/exception/duplicateEx/page',
      params: {
        examSubjectId,
        scanStationId,
        scanUserId,
        page: currentPage,
        size: pageSize,
      },
      requestName: '获取多页异常',
    })
    .then(data => {
      data.records.forEach(units => {
        units.forEach(transformScanUnit)
      })
      return data
    })
}

export function apiGetDuplicateExceptionRoomNos({ examSubjectId, scanStationId, scanUserId }) {
  return ajax.get({
    url: 'scan/exception/duplicateEx/roomNos',
    params: {
      examSubjectId,
      scanStationId,
      scanUserId,
    },
    requestName: '获取多页异常考场',
  })
}

// 删除多页异常学生所有答卷
export function apiDeleteDuplicateStuAllUnits({ examSubjectId, scanStationId, scanUserId, studentIds }) {
  return ajax.delete({
    url: 'scan/exception/duplicateEx/delStudentAllUnitByStudentIds',
    params: {
      examSubjectId,
      scanStationId,
      scanUserId,
    },
    data: studentIds,
  })
}

// 删除多页异常学生旧答卷，保留最新
export function apiDeleteDuplicateStuOldUnits({ examSubjectId, scanStationId, scanUserId, studentIds }) {
  return ajax.delete({
    url: 'scan/exception/duplicateEx/delStudentOldUnitByStudentIds',
    params: {
      examSubjectId,
      scanStationId,
      scanUserId,
    },
    data: studentIds,
  })
}

// 删除多页异常学生新答卷，保留最初
export function apiDeleteDuplicateStuNewUnits({ examSubjectId, scanStationId, scanUserId, studentIds }) {
  return ajax.delete({
    url: 'scan/exception/duplicateEx/delStudentNewUnitByStudentIds',
    params: {
      examSubjectId,
      scanStationId,
      scanUserId,
    },
    data: studentIds,
  })
}

// 按考场删除多页异常学生所有答卷
export function apiDeleteDuplicateStuAllUnitsByRoom({ examSubjectId, scanStationId, scanUserId, roomNos }) {
  return ajax.delete({
    url: 'scan/exception/duplicateEx/delStudentAllUnitByRoomNos',
    params: {
      examSubjectId,
      scanStationId,
      scanUserId,
    },
    data: roomNos,
  })
}

// 按考场删除多页异常学生旧答卷，保留最新
export function apiDeleteDuplicateStuOldUnitsByRoom({ examSubjectId, scanStationId, scanUserId, roomNos }) {
  return ajax.delete({
    url: 'scan/exception/duplicateEx/delDuplicateExStudentOldUnitByRoomNos',
    params: {
      examSubjectId,
      scanStationId,
      scanUserId,
    },
    data: roomNos,
  })
}

// 按考场删除多页异常学生新答卷，保留最初
export function apiDeleteDuplicateStuNewUnitsByRoom({ examSubjectId, scanStationId, scanUserId, roomNos }) {
  return ajax.delete({
    url: 'scan/exception/duplicateEx/delDuplicateExStudentNewUnitByRoomNos',
    params: {
      examSubjectId,
      scanStationId,
      scanUserId,
    },
    data: roomNos,
  })
}

export function apiGetMissingExceptionPage({ examSubjectId, scanStationId, scanUserId, currentPage, pageSize }) {
  return ajax
    .get({
      url: 'scan/exception/missingEx/page',
      params: {
        examSubjectId,
        scanStationId,
        scanUserId,
        page: currentPage,
        size: pageSize,
      },
      requestName: '获取缺页异常',
    })
    .then(data => {
      data.records.forEach(units => {
        units.forEach(transformScanUnit)
      })
      return data
    })
}

export function apiGetMissingExceptionRoomNos({ examSubjectId, scanStationId, scanUserId }) {
  return ajax.get({
    url: 'scan/exception/missingEx/roomNos',
    params: {
      examSubjectId,
      scanStationId,
      scanUserId,
    },
    requestName: '获取缺页异常考场',
  })
}

// 删除指定缺页考生所有扫描单元
export function apiDeleteMissingStuAllUnits({ examSubjectId, scanStationId, scanUserId, studentIds }) {
  return ajax.delete({
    url: 'scan/exception/missingEx/delStudentAllUnitByStudentIds',
    params: {
      examSubjectId,
      scanStationId,
      scanUserId,
    },
    data: studentIds,
  })
}

// 删除指定考场缺页考生所有扫描单元
export function apiDeleteMissingStuAllUnitsByRoom({ examSubjectId, scanStationId, scanUserId, roomNos }) {
  return ajax.delete({
    url: 'scan/exception/missingEx/delStudentAllUnitByRoomNos',
    params: {
      examSubjectId,
      scanStationId,
      scanUserId,
    },
    data: roomNos,
  })
}

export function apiGetAbsentExceptionPage({ examSubjectId, scanStationId, scanUserId, currentPage, pageSize }) {
  return ajax
    .get({
      url: 'scan/exception/absentEx/page',
      params: {
        examSubjectId,
        scanStationId,
        scanUserId,
        page: currentPage,
        size: pageSize,
      },
      requestName: '获取缺考异常',
    })
    .then(data => {
      data.records.forEach(transformScanUnit)
      return data
    })
}

export function apiGetObjectiveExceptionPage({ examSubjectId, scanStationId, scanUserId, currentPage, pageSize }) {
  return ajax
    .get({
      url: 'scan/exception/objectiveEx/page',
      params: {
        examSubjectId,
        scanStationId,
        scanUserId,
        page: currentPage,
        size: pageSize,
      },
      requestName: '获取客观题异常',
    })
    .then(data => {
      data.records.forEach(transformScanUnit)
      return data
    })
}

export function apiGetSubjectiveExceptionPage({ examSubjectId, scanStationId, scanUserId, currentPage, pageSize }) {
  return ajax
    .get({
      url: 'scan/exception/subjectiveEx/page',
      params: {
        examSubjectId,
        scanStationId,
        scanUserId,
        page: currentPage,
        size: pageSize,
      },
      requestName: '获取打分异常',
    })
    .then(data => {
      data.records.forEach(transformScanUnit)
      return data
    })
}

export function apiGetSelectExceptionPage({ examSubjectId, scanStationId, scanUserId, currentPage, pageSize }) {
  return ajax
    .get({
      url: 'scan/exception/selectEx/page',
      params: {
        examSubjectId,
        scanStationId,
        scanUserId,
        page: currentPage,
        size: pageSize,
      },
      requestName: '获取选做题异常',
    })
    .then(data => {
      data.records.forEach(transformScanUnit)
      return data
    })
}
