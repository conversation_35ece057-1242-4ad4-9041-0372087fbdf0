/**
 * 大题，与试卷结构的大题一致，除客观题全部统一排版在顶部时所有客观题单独成为一个大题
 */
import QuestionGroup from './question_group'
import { groupArray } from '@/utils/array'
import QuestionGroupTypeEnum from '@/enum/answer_sheet/question_group_type'
import ScoreTypeEnum from '@/enum/answer_sheet/score_type'
import { sortQuestionFunction } from '../emarking/miscellaneous'
import { ScoreTypeTrueFalseTipEmptyFalse, ScoreTypeTrueFalseTipEmptyTrue } from '@/const/answer_sheet'

export default class Topic {
  constructor(topic = {}, structureQuestions = [], subjectName) {
    // 大题号，客观题单独成大题时code为0
    this.code = Number(topic.code) || 0
    // 大题说明内容
    this.descriptionContent = topic.descriptionContent || ''
    // 大题说明高度
    this.descriptionHeight = topic.descriptionHeight || 0
    // 隐藏材料
    this.hideTrunkQuestionGroup = Boolean(topic.hideTrunkQuestionGroup)
    // 题组
    this.questionGroups = (topic.questionGroups || []).map(g => new QuestionGroup(g, structureQuestions, subjectName))
  }

  export() {
    return {
      code: this.code,
      descriptionContent: this.descriptionContent,
      descriptionHeight: this.descriptionHeight,
      hideTrunkQuestionGroup: this.hideTrunkQuestionGroup,
      questionGroups: this.questionGroups.map(g => g.export()),
    }
  }

  // 向已有的客观题题组中添加新客观题
  addObjectiveQuestions({ objectiveQuestions, objectivesSeparate, subjectName, groupLength, trueOrFalseType }) {
    if (objectivesSeparate) {
      // 相邻的客观题一组
      let nearQuestions = []
      objectiveQuestions.forEach(obj => {
        let lastPartition = nearQuestions[nearQuestions.length - 1]
        if (lastPartition && lastPartition[lastPartition.length - 1].questionCode + 1 == obj.questionCode) {
          lastPartition.push(obj)
        } else {
          lastPartition = [obj]
          nearQuestions.push(lastPartition)
        }
      })
      nearQuestions.forEach(partition => {
        let questionGroup = this.questionGroups.find(g => {
          if (g.type != QuestionGroupTypeEnum.Objective.id) {
            return false
          }
          let startQuestionCode = g.questions[0].questionCode
          let endQuestionCode = g.questions[g.questions.length - 1].questionCode
          let partStartQuestionCode = partition[0].questionCode
          let partEndQuestionCode = partition[partition.length - 1].questionCode
          return endQuestionCode + 1 == partStartQuestionCode || startQuestionCode - 1 == partEndQuestionCode
        })
        if (questionGroup) {
          questionGroup.addObjectiveQuestions(partition)
        } else {
          questionGroup = QuestionGroup.createObjectiveQuestionGroup({
            objectiveQuestions: partition,
            subjectName,
            groupLength,
            trueOrFalseType,
          })
          this.insertQuestionGroup(questionGroup)
        }
      })
    } else {
      let questionGroup = this.questionGroups.find(g => g.type == QuestionGroupTypeEnum.Objective.id)
      if (questionGroup) {
        questionGroup.addObjectiveQuestions(objectiveQuestions)
      } else {
        questionGroup = QuestionGroup.createObjectiveQuestionGroup({
          objectiveQuestions,
          subjectName,
          groupLength,
          trueOrFalseType,
        })
        this.insertQuestionGroup(questionGroup)
      }
    }
  }

  // 插入新题组
  insertQuestionGroup(newQuestionGroup) {
    let insertQuestionGroupPosition = this.questionGroups.findIndex(
      g => sortQuestionFunction(g.questions[0], newQuestionGroup.questions[0]) > 0
    )
    if (insertQuestionGroupPosition < 0) {
      insertQuestionGroupPosition = this.questionGroups.length
    }
    this.questionGroups.splice(insertQuestionGroupPosition, 0, newQuestionGroup)
  }

  // 设置对错框说明
  changeShowScoreTypeTrueFalseTip({ show, emptyDefault }) {
    // let hasScoreTypeTrueFalse = this.questionGroups.some(
    //   g =>
    //     [
    //       QuestionGroupTypeEnum.FillBlank.id,
    //       QuestionGroupTypeEnum.Essay.id,
    //       QuestionGroupTypeEnum.ChineseWriting.id,
    //       QuestionGroupTypeEnum.EnglishWriting.id,
    //     ].includes(g.type) && g.scoreType.name == ScoreTypeEnum.TrueFalse.id
    // )
    // if (hasScoreTypeTrueFalse && show) {
    //   this.addScoreTypeTrueFalseTip(emptyDefault)
    // } else {
    //   this.removeScoreTypeTrueFalseTip()
    // }
  }
  addScoreTypeTrueFalseTip(emptyDefault) {
    let thisDefaultTip
    let anotherDefaultTip
    if (emptyDefault) {
      thisDefaultTip = ScoreTypeTrueFalseTipEmptyTrue
      anotherDefaultTip = ScoreTypeTrueFalseTipEmptyFalse
    } else {
      thisDefaultTip = ScoreTypeTrueFalseTipEmptyFalse
      anotherDefaultTip = ScoreTypeTrueFalseTipEmptyTrue
    }
    if (this.descriptionContent.includes(thisDefaultTip)) {
      return
    } else if (this.descriptionContent.includes(anotherDefaultTip)) {
      this.descriptionContent = this.descriptionContent.replace(anotherDefaultTip, thisDefaultTip)
    } else {
      let index = this.descriptionContent.lastIndexOf('</span')
      if (index < 0) {
        this.descriptionContent = this.descriptionContent + thisDefaultTip
      } else {
        this.descriptionContent =
          this.descriptionContent.substring(0, index) + thisDefaultTip + this.descriptionContent.substring(index)
      }
    }
  }
  removeScoreTypeTrueFalseTip() {
    if (this.descriptionContent.includes(ScoreTypeTrueFalseTipEmptyFalse)) {
      this.descriptionContent = this.descriptionContent.replace(ScoreTypeTrueFalseTipEmptyFalse, '')
    }
    if (this.descriptionContent.includes(ScoreTypeTrueFalseTipEmptyTrue)) {
      this.descriptionContent = this.descriptionContent.replace(ScoreTypeTrueFalseTipEmptyTrue, '')
    }
  }

  // 设置客观题说明
  changeObjectiveTopicDescriptionShowTopicName(showTopicName) {
    let objectiveQuestionGroup = this.questionGroups.find(g => g.type == QuestionGroupTypeEnum.Objective.id)
    if (!objectiveQuestionGroup) {
      return
    }
    let newDescription
    if (showTopicName) {
      let topicName = objectiveQuestionGroup.questions[0].topicName
      newDescription = this.descriptionContent.replace(
        /(^|>)\s*客观题\s*($|[^\u4e00-\u9fa5])/,
        (match, p1, p2) => `${p1 || ''}${topicName}${p2 || ''}`
      )
    } else {
      newDescription = this.descriptionContent.replace(
        /(^|>)\s*[一二三四五六七八九十]+\s*、\s*(单选题|多选题|判断题)($|[^\u4e00-\u9fa5])/,
        (match, p1, p2, p3) => `${p1 || ''}客观题${p3 || ''}`
      )
    }
    if (newDescription != this.descriptionContent) {
      this.descriptionContent = newDescription
    }
  }

  static create(topicCode, topicName, questionGroups) {
    let topic = new Topic()
    topic.code = topicCode
    topic.descriptionContent = `<span style="font-weight: bold">${topicName}</span>`
    if (questionGroups && questionGroups.length > 0) {
      topic.questionGroups.push(...questionGroups)
    }
    return topic
  }

  // 从试卷结构创建大题
  static generateTopicsFromPaperStructure({ objectiveQuestions, subjectiveQuestions, blocks }, subjectName, stageName) {
    let topics = []
    if (objectiveQuestions.length > 0) {
      topics.push(Topic.generateObjectiveTopicFromPaperStructure({ objectiveQuestions, topicCode: 0, subjectName }))
    }
    if (subjectiveQuestions.length > 0 && blocks.length > 0) {
      topics.push(
        ...Topic.generateSubjectiveTopicsFromPaperStructure({ subjectiveQuestions, blocks, subjectName, stageName })
      )
    }
    return topics
  }

  static generateObjectiveTopicFromPaperStructure({ objectiveQuestions, topicCode, subjectName }) {
    let objectiveQuestionGroup = QuestionGroup.createObjectiveQuestionGroup({
      objectiveQuestions,
      subjectName,
    })
    return Topic.create(topicCode, '以下为客观题答题区（必须用2B铅笔将选中项涂满、涂黑）', [objectiveQuestionGroup])
  }

  static generateSubjectiveTopicsFromPaperStructure({ subjectiveQuestions, blocks, subjectName, stageName }) {
    // 找每个小题对应的题块
    let branchBlocks = []
    blocks.forEach(block => {
      block.questions.forEach(subj => {
        branchBlocks.push({
          questionCode: subj.questionCode,
          branchCode: subj.branchCode,
          blockId: block.blockId,
          blockName: block.blockName,
          selectGroupName: block.selectGroupName,
          selectCount: block.selectCount,
        })
      })
    })
    // 由主观题汇总各题实际小题数
    let questionCodeBranchCountMap = new Map()
    groupArray(subjectiveQuestions, x => x.questionCode).forEach(g => {
      questionCodeBranchCountMap.set(g.key, g.group.length)
    })
    // 由题块汇总各题小题数，若某小题未加入题块，则对应题目的小题数小于实际小题数
    let blockQuestionCodeBranchCountMap = new Map()
    groupArray(branchBlocks, x => x.questionCode).forEach(g => {
      blockQuestionCodeBranchCountMap.set(g.key, g.group.length)
    })

    // 分大题
    let topics = []
    groupArray(subjectiveQuestions, subj => subj.topicCode).forEach(gTopic => {
      let topicCode = gTopic.key
      let topicBranches = gTopic.group

      // 合并相邻小题
      let nearBranches = []
      let current = null
      topicBranches.forEach(b => {
        let block = branchBlocks.find(x => x.questionCode == b.questionCode && x.branchCode == b.branchCode)
        // 剔除未划分进题块的题目
        if (!block) {
          return
        }
        // 与当前小组题号相同、题块相同则合并
        if (current && current.questionCode == b.questionCode && current.blockId == block.blockId) {
          current.branches.push(b)
        } else {
          current = {
            questionCode: b.questionCode,
            blockId: block.blockId,
            blockName: block.blockName,
            selectGroupName: block.selectGroupName,
            selectCount: block.selectCount,
            branches: [b],
          }
          nearBranches.push(current)
        }
      })

      // 合并相邻题目
      let nearQuestions = []
      current = null
      nearBranches.forEach(item => {
        item.includeAllBranches = item.branches.length == blockQuestionCodeBranchCountMap.get(item.questionCode)
        // 与当前组题块相同或同一选做，且两组都包含了所有小题，则合并
        if (
          current &&
          current.includeAllBranches &&
          item.includeAllBranches &&
          (current.blockId == item.blockId ||
            (current.selectGroupName && current.selectGroupName == item.selectGroupName))
        ) {
          item.branches.forEach(b => {
            current.branches.push(b)
          })
        } else {
          current = {
            blockId: item.blockId,
            blockName: item.blockName,
            selectGroupName: item.selectGroupName,
            selectCount: item.selectCount,
            includeAllBranches: item.includeAllBranches,
            branches: [...item.branches],
          }
          nearQuestions.push(current)
        }
      })

      // 创建题组
      let questionGroups = nearQuestions.map(({ branches, selectGroupName, selectCount }) => {
        let questionGroup = QuestionGroup.createSubjectiveQuestionGroup({
          subjectiveQuestions: branches,
          subjectName,
          stageName,
          // 选做题必须为解答题题组
          questionGroupType: selectGroupName ? QuestionGroupTypeEnum.Essay.id : '',
        })
        if (selectGroupName) {
          questionGroup.setSelect(selectGroupName, selectCount)
        }
        return questionGroup
      })

      topics.push(Topic.create(topicCode, topicBranches[0].topicName, questionGroups))
    })
    return topics
  }

  // 创建带题干的大题
  static generateQuestionContentTopicsFromPaperStructure(
    { objectiveQuestions, subjectiveQuestions, blocks },
    { paperStructure, paperStyle }
  ) {
    // 主观题组
    let topics = Topic.generateSubjectiveTopicsFromPaperStructure({ subjectiveQuestions, blocks })
    // 客观题组
    objectiveQuestions.forEach(objectiveQuestion => {
      let g = QuestionGroup.createContentObjectiveQuestionGroup(objectiveQuestion)
      let topic = topics.find(t => t.code == objectiveQuestion.topicCode)
      if (topic) {
        topic.questionGroups.push(g)
      } else {
        topics.push(Topic.create(objectiveQuestion.topicCode, objectiveQuestion.topicName, [g]))
      }
    })
    // 材料题组
    groupArray([...objectiveQuestions, ...subjectiveQuestions], q => q.originalQuestionId).forEach(
      ({ group: questions }) => {
        questions.sort(sortQuestionFunction)
        let originalQuestion = questions[0].originalQuestion
        if (!originalQuestion || !originalQuestion.trunk) {
          return
        }

        // 若该题没有小题题干及小题选项，且所有小题包含在同一个填空题组中，则该题不创建材料块，材料内容放在填空题组中
        if (originalQuestion.branches.every(b => !b.stem && (!b.options || b.options.length == 0))) {
          let sameOriginalQuestionGroups = []
          topics.forEach(t => {
            t.questionGroups.forEach(g => {
              if (g.questions.some(q => q.originalQuestionId == originalQuestion.id)) {
                sameOriginalQuestionGroups.push(g)
              }
            })
          })
          if (sameOriginalQuestionGroups.length == 1) {
            let targetGroup = sameOriginalQuestionGroups[0]
            if (
              targetGroup.type == QuestionGroupTypeEnum.FillBlank.id &&
              targetGroup.questions.every(q => q.originalQuestionId == originalQuestion.id)
            ) {
              targetGroup.trunkInContent = true
              return
            }
          }
        }

        let g = QuestionGroup.createTrunkQuestionGroup(questions)
        let topic = topics.find(t => t.code == questions[0].topicCode)
        topic.questionGroups.push(g)
      }
    )
    // 排序
    topics.sort((a, b) => a.code - b.code)
    topics.forEach(t => {
      t.questionGroups.sort((a, b) => {
        let value = sortQuestionFunction(a.questions[0], b.questions[0])
        if (value == 0) {
          return b.type == QuestionGroupTypeEnum.Trunk.id ? 1 : -1
        } else {
          return value
        }
      })
    })
    // 加大题说明及分数
    topics.forEach(t => {
      let paperTopic = paperStructure.topics.find(x => x.code == t.code)
      if (!paperTopic) {
        return
      }
      t.descriptionContent = `<span style="font-weight: bold">${paperTopic.content}</span>`
      // 第一大题说明加试卷情境
      if (t.code == 1 && paperStyle.situation.enabled) {
        t.descriptionContent = `<span>${paperStyle.situation.content}</span>` + '<br>' + t.descriptionContent
      }
    })
    return topics
  }
}
