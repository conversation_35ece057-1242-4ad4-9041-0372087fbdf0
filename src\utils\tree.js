/**
 * 树结构相关算法工具函数
 * Tree structure related algorithms and utilities
 */

/**
 * 深度优先遍历树结构
 * Depth-first traversal of tree structure
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} callback - 对每个节点执行的回调函数
 * @param {Function} getChildren - 获取子节点的函数，默认获取 children 属性
 */
export function traverse(tree, callback, getChildren = node => (node && node.children) || []) {
  if (Array.isArray(tree)) {
    tree.forEach(node => traverse(node, callback, getChildren))
    return
  }

  callback(tree)
  const children = getChildren(tree)
  children.forEach(child => traverse(child, callback, getChildren))
}

/**
 * 广度优先遍历树结构
 * Breadth-first traversal of tree structure
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} callback - 对每个节点执行的回调函数
 * @param {Function} getChildren - 获取子节点的函数
 */
export function traverseBFS(tree, callback, getChildren = node => (node && node.children) || []) {
  const queue = Array.isArray(tree) ? [...tree] : [tree]

  while (queue.length > 0) {
    const node = queue.shift()
    callback(node)

    const children = getChildren(node)
    queue.push(...children)
  }
}

/**
 * 在树中查找节点
 * Find node in tree
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} predicate - 查找条件函数
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {Object|null} 找到的节点或null
 */
export function findNode(tree, predicate, getChildren = node => (node && node.children) || []) {
  if (Array.isArray(tree)) {
    for (const node of tree) {
      const result = findNode(node, predicate, getChildren)
      if (result) return result
    }
    return null
  }

  if (predicate(tree)) {
    return tree
  }

  const children = getChildren(tree)
  for (const child of children) {
    const result = findNode(child, predicate, getChildren)
    if (result) return result
  }

  return null
}

/**
 * 在树中查找所有匹配的节点
 * Find all matching nodes in tree
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} predicate - 查找条件函数
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {Array} 匹配的节点数组
 */
export function findAllNodes(tree, predicate, getChildren = node => (node && node.children) || []) {
  const results = []

  traverse(
    tree,
    node => {
      if (predicate(node)) {
        results.push(node)
      }
    },
    getChildren
  )

  return results
}

/**
 * 获取节点的路径（从根节点到目标节点）
 * Get path from root to target node
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} predicate - 查找条件函数
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {Array|null} 路径数组或null
 */
export function getNodePath(tree, predicate, getChildren = node => (node && node.children) || []) {
  function findPath(node, path = []) {
    const currentPath = [...path, node]

    if (predicate(node)) {
      return currentPath
    }

    const children = getChildren(node)
    for (const child of children) {
      const result = findPath(child, currentPath)
      if (result) return result
    }

    return null
  }

  if (Array.isArray(tree)) {
    for (const node of tree) {
      const result = findPath(node)
      if (result) return result
    }
    return null
  }

  return findPath(tree)
}

/**
 * 获取树的最大深度
 * Get maximum depth of tree
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {number} 最大深度
 */
export function getMaxDepth(tree, getChildren = node => (node && node.children) || []) {
  if (Array.isArray(tree)) {
    return Math.max(...tree.map(node => getMaxDepth(node, getChildren)))
  }

  const children = getChildren(tree)
  if (children.length === 0) {
    return 1
  }

  return 1 + Math.max(...children.map(child => getMaxDepth(child, getChildren)))
}

/**
 * 获取节点的深度（从根节点开始计算）
 * Get depth of a specific node
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} predicate - 查找条件函数
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {number} 节点深度，未找到返回-1
 */
export function getNodeDepth(tree, predicate, getChildren = node => (node && node.children) || []) {
  function findDepth(node, depth = 0) {
    if (predicate(node)) {
      return depth
    }

    const children = getChildren(node)
    for (const child of children) {
      const result = findDepth(child, depth + 1)
      if (result !== -1) return result
    }

    return -1
  }

  if (Array.isArray(tree)) {
    for (const node of tree) {
      const result = findDepth(node)
      if (result !== -1) return result
    }
    return -1
  }

  return findDepth(tree)
}

/**
 * 过滤树节点
 * Filter tree nodes
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} predicate - 过滤条件函数
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {Array} 过滤后的树数组
 */
export function filterTree(tree, predicate, getChildren = node => (node && node.children) || []) {
  function filterNode(node) {
    const children = getChildren(node)
    const filteredChildren = children.map(filterNode).filter(Boolean)

    if (predicate(node) || filteredChildren.length > 0) {
      return {
        ...node,
        children: filteredChildren,
      }
    }

    return null
  }

  if (Array.isArray(tree)) {
    return tree.map(filterNode).filter(Boolean)
  }

  const result = filterNode(tree)
  return result ? [result] : []
}

/**
 * 映射树节点
 * Map tree nodes
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} mapper - 映射函数
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {Array} 映射后的树数组
 */
export function mapTree(tree, mapper, getChildren = node => (node && node.children) || []) {
  function mapNode(node) {
    const children = getChildren(node)
    const mappedChildren = children.map(mapNode)

    return mapper({
      ...node,
      children: mappedChildren,
    })
  }

  if (Array.isArray(tree)) {
    return tree.map(mapNode)
  }

  return [mapNode(tree)]
}

/**
 * 扁平化树结构
 * Flatten tree structure
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {Array} 扁平化后的节点数组
 */
export function flattenTree(tree, getChildren = node => (node && node.children) || []) {
  const result = []

  traverse(
    tree,
    node => {
      result.push(node)
    },
    getChildren
  )

  return result
}

/**
 * 从扁平数组构建树结构
 * Build tree from flat array
 * @param {Array} flatArray - 扁平数组
 * @param {string} idKey - ID字段名，默认'id'
 * @param {string} parentIdKey - 父ID字段名，默认'parentId'
 * @param {string} childrenKey - 子节点字段名，默认'children'
 * @returns {Array} 树结构数组
 */
export function buildTreeFromFlat(flatArray, idKey = 'id', parentIdKey = 'parentId', childrenKey = 'children') {
  const map = new Map()
  const roots = []

  // 创建节点映射
  flatArray.forEach(item => {
    map.set(item[idKey], { ...item, [childrenKey]: [] })
  })

  // 构建树结构
  flatArray.forEach(item => {
    const node = map.get(item[idKey])
    const parentId = item[parentIdKey]

    if (parentId && map.has(parentId)) {
      const parent = map.get(parentId)
      parent[childrenKey].push(node)
    } else {
      roots.push(node)
    }
  })

  return roots
}

/**
 * 获取叶子节点
 * Get leaf nodes
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {Array} 叶子节点数组
 */
export function getLeafNodes(tree, getChildren = node => (node && node.children) || []) {
  const leafNodes = []

  traverse(
    tree,
    node => {
      const children = getChildren(node)
      if (children.length === 0) {
        leafNodes.push(node)
      }
    },
    getChildren
  )

  return leafNodes
}

/**
 * 获取父节点
 * Get parent node
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} predicate - 查找条件函数
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {Object|null} 父节点或null
 */
export function getParentNode(tree, predicate, getChildren = node => (node && node.children) || []) {
  function findParent(node, parent = null) {
    if (predicate(node)) {
      return parent
    }

    const children = getChildren(node)
    for (const child of children) {
      const result = findParent(child, node)
      if (result !== undefined) return result
    }

    return undefined
  }

  if (Array.isArray(tree)) {
    for (const node of tree) {
      const result = findParent(node)
      if (result !== undefined) return result
    }
    return null
  }

  const result = findParent(tree)
  return result !== undefined ? result : null
}

/**
 * 获取兄弟节点
 * Get sibling nodes
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} predicate - 查找条件函数
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {Array} 兄弟节点数组（不包括自己）
 */
export function getSiblingNodes(tree, predicate, getChildren = node => (node && node.children) || []) {
  const parent = getParentNode(tree, predicate, getChildren)

  if (!parent) {
    // 如果没有父节点，说明是根节点，返回其他根节点
    if (Array.isArray(tree)) {
      return tree.filter(node => !predicate(node))
    }
    return []
  }

  const children = getChildren(parent)
  return children.filter(node => !predicate(node))
}

/**
 * 排序树节点
 * Sort tree nodes
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} compareFn - 比较函数
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {Array} 排序后的树数组
 */
export function sortTree(tree, compareFn, getChildren = node => (node && node.children) || []) {
  function sortNode(node) {
    const children = getChildren(node)
    const sortedChildren = children.map(sortNode).sort(compareFn)

    return {
      ...node,
      children: sortedChildren,
    }
  }

  if (Array.isArray(tree)) {
    return tree.map(sortNode).sort(compareFn)
  }

  return [sortNode(tree)]
}

/**
 * 深度克隆树结构
 * Deep clone tree structure
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {Array} 克隆后的树数组
 */
export function cloneTree(tree, getChildren = node => (node && node.children) || []) {
  function cloneNode(node) {
    const children = getChildren(node)
    const clonedChildren = children.map(cloneNode)

    return {
      ...node,
      children: clonedChildren,
    }
  }

  if (Array.isArray(tree)) {
    return tree.map(cloneNode)
  }

  return [cloneNode(tree)]
}

/**
 * 验证树结构是否有效（无循环引用）
 * Validate tree structure (no circular references)
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} getChildren - 获取子节点的函数
 * @param {Function} getId - 获取节点ID的函数，默认获取id属性
 * @returns {boolean} 是否有效
 */
export function validateTree(tree, getChildren = node => (node && node.children) || [], getId = node => node.id) {
  const visited = new Set()

  function validate(node, ancestors = new Set()) {
    const nodeId = getId(node)

    if (ancestors.has(nodeId)) {
      return false // 发现循环引用
    }

    if (visited.has(nodeId)) {
      return true // 已经验证过的节点
    }

    visited.add(nodeId)
    ancestors.add(nodeId)

    const children = getChildren(node)
    for (const child of children) {
      if (!validate(child, new Set(ancestors))) {
        return false
      }
    }

    ancestors.delete(nodeId)
    return true
  }

  if (Array.isArray(tree)) {
    return tree.every(node => validate(node))
  }

  return validate(tree)
}

/**
 * 计算树的节点总数
 * Count total nodes in tree
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {number} 节点总数
 */
export function countNodes(tree, getChildren = node => (node && node.children) || []) {
  let count = 0

  traverse(
    tree,
    () => {
      count++
    },
    getChildren
  )

  return count
}

/**
 * 获取树的所有路径
 * Get all paths in tree
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Function} getChildren - 获取子节点的函数
 * @returns {Array} 所有路径数组
 */
export function getAllPaths(tree, getChildren = node => (node && node.children) || []) {
  const paths = []

  function collectPaths(node, currentPath = []) {
    const newPath = [...currentPath, node]
    const children = getChildren(node)

    if (children.length === 0) {
      // 叶子节点，添加路径
      paths.push(newPath)
    } else {
      // 继续遍历子节点
      children.forEach(child => collectPaths(child, newPath))
    }
  }

  if (Array.isArray(tree)) {
    tree.forEach(node => collectPaths(node))
  } else {
    collectPaths(tree)
  }

  return paths
}

/**
 * 根据路径获取节点
 * Get node by path
 * @param {Object|Array} tree - 树节点或树数组
 * @param {Array} path - 路径数组（节点ID或索引）
 * @param {Function} getChildren - 获取子节点的函数
 * @param {Function} getId - 获取节点ID的函数
 * @returns {Object|null} 找到的节点或null
 */
export function getNodeByPath(
  tree,
  path,
  getChildren = node => (node && node.children) || [],
  getId = node => node.id
) {
  if (!path || path.length === 0) {
    return null
  }

  let currentNodes = Array.isArray(tree) ? tree : [tree]

  for (const pathSegment of path) {
    const targetNode = currentNodes.find(node => getId(node) === pathSegment)

    if (!targetNode) {
      return null
    }

    if (path.indexOf(pathSegment) === path.length - 1) {
      return targetNode
    }

    currentNodes = getChildren(targetNode)
  }

  return null
}

/**
 * 比较两个树是否相等
 * Compare if two trees are equal
 * @param {Object|Array} tree1 - 第一个树
 * @param {Object|Array} tree2 - 第二个树
 * @param {Function} getChildren - 获取子节点的函数
 * @param {Function} compareFn - 比较节点的函数，默认使用JSON.stringify
 * @returns {boolean} 是否相等
 */
export function isTreeEqual(
  tree1,
  tree2,
  getChildren = node => (node && node.children) || [],
  compareFn = (a, b) => JSON.stringify(a) === JSON.stringify(b)
) {
  function compareNodes(node1, node2) {
    if (!compareFn(node1, node2)) {
      return false
    }

    const children1 = getChildren(node1)
    const children2 = getChildren(node2)

    if (children1.length !== children2.length) {
      return false
    }

    for (let i = 0; i < children1.length; i++) {
      if (!compareNodes(children1[i], children2[i])) {
        return false
      }
    }

    return true
  }

  const array1 = Array.isArray(tree1) ? tree1 : [tree1]
  const array2 = Array.isArray(tree2) ? tree2 : [tree2]

  if (array1.length !== array2.length) {
    return false
  }

  for (let i = 0; i < array1.length; i++) {
    if (!compareNodes(array1[i], array2[i])) {
      return false
    }
  }

  return true
}
