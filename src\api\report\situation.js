import ajax from '@/api/ajax'

export function apiGetClassList() {
  return ajax
    .get({
      url: 'report/situations/teacherGradeClassSubjects',
      requestName: '获取班级列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetGraduatedClassList() {
  return ajax
    .get({
      url: 'report/situations/graduateGradeClassSubjects',
      params: {},
      requestName: '获取已毕业班级列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetListSituationExam(params) {
  return ajax
    .get({
      url: 'report/situations/listSituationExam',
      params,
      requestName: '获取指定年级班级科目考试列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetSubjectTemplate(params) {
  return ajax
    .get({
      url: 'report/situations/subjAnalyseTem',
      params: {
        classId: params.classId,
        gradeId: params.gradeId,
        schoolId: params.schoolId,
        semesterId: params.semesterId,
        subjectId: params.subjectId,
      },
      requestName: '获取学科学情模板',
    })
    .then(data => {
      return data
    })
}

export function apiGetStudentTemplate(params) {
  return ajax
    .get({
      url: 'report/situations/stuAnalyseTem',
      params: {
        classId: params.classId,
        gradeId: params.gradeId,
        schoolId: params.schoolId,
        semesterId: params.semesterId,
        subjectId: params.subjectId,
      },
      requestName: '获取学生学情模板',
    })
    .then(data => {
      return data
    })
}

export function apiGetThePointCount(params) {
  return ajax
    .post({
      url: 'report/situations/getSituationWeekKnowCount',
      params: {
        classId: params.classId,
        subjectId: params.subjectId,
      },
      data: params.temps,
      requestName: '获取学科知识点数量',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheWeakPoint(params) {
  return ajax
    .post({
      url: 'report/situations/subjWeekKnow',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        size: params.size,
        current: params.current,
        scoreRate: params.scoreRate,
      },
      data: params.temps,
      requestName: '获取学科薄弱知识点',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheTopicsList(params) {
  return ajax
    .post({
      url: 'report/situations/subjWrongQues',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        size: params.size,
        current: params.current,
        fromRate: params.fromRate,
        toRate: params.toRate,
        quesTypeId: params.quesTypeId,
      },
      data: params.temps,
      requestName: '获取关联错题列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheKnowledgeList(params) {
  return ajax
    .post({
      url: 'report/situations/subjKnows',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
      },
      data: params.temps,
      requestName: '获取知识点得分率列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheKnowledgeDetails(params) {
  return ajax
    .post({
      url: 'report/situations/subjKnowStat',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        knowledgeId: params.knowledgeId,
      },
      data: params.temps,
      requestName: '获取知识点得分详情',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheKnowledgeMaster(params) {
  return ajax
    .post({
      url: 'report/situations/subjClsStuKnow',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        knowledgeId: params.knowledgeId,
      },
      data: params.temps,
      requestName: '获取学生知识点得分',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheStudentKnowledgeScore(params) {
  return ajax
    .post({
      url: 'report/situations/stuKnowList',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        studentName: params.studentName,
      },
      data: params.temps,
      requestName: '获取学生知识点得分情况',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheStudentKnowledge(params) {
  return ajax
    .post({
      url: 'report/situations/stuKnow',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        studentId: params.studentId,
      },
      data: params.temps,
      requestName: '获取某学生知识点得分情况',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheStudentSubjectTrend(params) {
  return ajax
    .post({
      url: 'report/situations/stuScoreTrend',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        studentId: params.studentId,
      },
      data: params.temps,
      requestName: '获取学生单科考试趋势',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheStudentList(params) {
  return ajax
    .post({
      url: 'report/situations/clsStu',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
      },
      data: params.temps,
      requestName: '获取某班级学生列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheStudentTopics(params) {
  return ajax
    .post({
      url: 'report/situations/stuWrongQues',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        size: params.size,
        current: params.current,
        fromRate: params.fromRate,
        toRate: params.toRate,
        quesTypeId: params.quesTypeId,
        studentId: params.studentId,
      },
      data: params.temps,
      requestName: '获取学生关联错题列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheKnowledgeTopics(params) {
  return ajax
    .post({
      url: 'report/situations/subjKnowQues',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        size: params.size,
        current: params.current,
        fromRate: params.fromRate,
        toRate: params.toRate,
        quesTypeId: params.quesTypeId,
        knowledgeId: params.knowledgeId,
      },
      data: params.temps,
      requestName: '获取学生关联错题列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheClassTrace(params) {
  return ajax
    .post({
      url: 'report/situations/classTrace',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
      },
      data: params.templateIds,
      requestName: '获取班级学业跟踪',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheStudentTrace(params) {
  return ajax
    .post({
      url: 'report/situations/stuStudyTrace',
      params: {
        schoolId: params.schoolId,
        classId: params.classId,
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        orderStr: params.orderStr,
        isAsc: params.isAsc,
        pageNum: params.current,
        pageSize: params.size,
        studentName: params.studentName,
      },
      data: params.templateIds,
      requestName: '获取学生学业跟踪',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheClassScoreLevelTrace(params) {
  return ajax
    .post({
      url: 'report/situations/classScoreLevelTraceByTemplateIds',
      data: {
        scoreLevels: params.config,
        templateIds: params.templateIds,
      },
      params: params.params,
      requestName: '获取班级学业等级跟踪',
    })
    .then(data => {
      return data
    })
}
