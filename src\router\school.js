import Role from '@/enum/user/role'
import Store from '@/store/index'
import ModuleEnum from '@/enum/user/module'

export default {
  name: 'school',
  path: 'school',
  meta: {
    title: '学校管理',
    menu: '管理',
    name: '学校管理',
    roles: user => {
      return (
        user.schoolType == 0 && user.roles.some(r => [Role.SchoolAdministrator.id, Role.SchoolLeader.id].includes(r))
      )
    },
  },
  component: () => import('@/views/school/index.vue'),
  children: [
    {
      name: 'school-class',
      path: 'class',
      meta: {
        menu: '班级管理',
      },
      component: () => import('@/views/school/class/index.vue'),
    },
    {
      name: 'teching_class',
      path: 'teching_class',
      meta: {
        menu: '我的班级',
        roles: user => {
          return user.teachings.length > 0
        },
      },
      component: () => import('@/views/teacher_classes/index.vue'),
    },
    {
      name: 'school-teacher',
      path: 'teacher',
      meta: {
        menu: '教师管理',
      },
      component: () => import('@/views/school/teacher/index.vue'),
      children: [
        {
          name: 'school-teacher-list',
          path: 'list',
          meta: {
            menu: '教师列表',
          },
          component: () => import('@/views/school/teacher/list/index.vue'),
        },
        {
          name: 'school-teacher-teaching',
          path: 'teaching',
          meta: {
            menu: '任课教师',
          },
          component: () => import('@/views/school/teacher/teaching/index.vue'),
        },
        {
          name: 'school-teacher-gradeSubjectLeader',
          path: 'gradesubjectleader',
          meta: {
            menu: '备课组长',
          },
          component: () => import('@/views/school/teacher/grade_subject_leader/index.vue'),
        },
        {
          name: 'school-teacher-subjectLeader',
          path: 'subjectleader',
          meta: {
            menu: '学科组长',
          },
          component: () => import('@/views/school/teacher/subject_leader/index.vue'),
        },
        {
          name: 'school-teacher-gradeLeader',
          path: 'gradeleader',
          meta: {
            menu: '年级主任',
          },
          component: () => import('@/views/school/teacher/grade_leader/index.vue'),
        },
        {
          name: 'school-teacher-schoolLeader',
          path: 'schoolleader',
          meta: {
            menu: '学校领导',
          },
          component: () => import('@/views/school/teacher/school_leader/index.vue'),
        },
        {
          name: 'school-teacher-administrator',
          path: 'schooladmin',
          meta: {
            menu: '学校管理员',
          },
          component: () => import('@/views/school/teacher/school_administrator/index.vue'),
        },
      ],
    },
    {
      name: 'school-others',
      path: 'others',
      meta: {
        menu: '财务管理',
      },
      component: () => import('@/views/school/other/index.vue'),
      children: [
        {
          name: 'school-others-financial',
          path: 'financial',
          meta: {
            menu: '财务信息',
          },
          component: () => import('@/views/school/other/financial_staff/index.vue'),
        },
      ],
    },
    {
      name: 'school-config',
      path: 'config',
      meta: {
        menu: '配置管理',
      },
      component: () => import('@/views/school/config/index.vue'),
      children: [
        {
          name: 'school-create-exam',
          path: 'create',
          meta: {
            menu: '创建考试',
          },
          component: () => import('@/views/school/config/create/index.vue'),
        },
        {
          name: 'school-scan',
          path: 'scan',
          meta: {
            menu: '扫描员配置',
          },
          component: () => import('@/views/school/config/scan/index.vue'),
        },
        {
          name: 'school-reference-book-scan',
          path: 'referencescan',
          meta: {
            menu: '教辅扫描员配置',
            roles: user => {
              return (
                user.schoolType === 0 &&
                (user.roles.includes(Role.SchoolAdministrator.id) || user.isSystem) &&
                Store.getters['user/isModuleNormal'](ModuleEnum.CoachHomework.id)
              )
            },
          },
          component: () => import('@/views/school/config/reference_book_scan/index.vue'),
        },
        {
          name: 'school-config-grade',
          path: 'grade',
          meta: {
            menu: '年级学科',
          },
          component: () => import('@/views/school/config/grade/index.vue'),
        },
        {
          name: 'school-config-semester',
          path: 'semester',
          meta: {
            menu: '学年学期',
          },
          component: () => import('@/views/school/config/semester/index.vue'),
        },
        // {
        //   name: 'school-feedback-manage',
        //   path: 'feedback',
        //   meta: {
        //     menu: '错题反馈',
        //   },
        //   component: () => import('@/views/school/config/feedback/index.vue'),
        // },
        {
          name: 'school-custom-report-setting',
          path: 'customReport',
          meta: {
            menu: '定制报告配置',
            roles: () => Store.getters['user/isSystem'],
          },
          component: () => import('@/views/school/config/custom_report/index.vue'),
        },
        // {
        //   name: 'school-feedback-resource-kit',
        //   path: 'resourcekit',
        //   meta: {
        //     menu: '学情资源包',
        //   },
        //   component: () => import('@/views/school/config/resource_kit/index.vue'),
        // },
        {
          name: 'school-config-scan-device-lisence',
          path: 'scanDeviceLisence',
          meta: {
            menu: '扫描仪授权',
          },
          component: () => import('@/views/school/config/scan_device_lisence/index.vue'),
        },
        {
          name: 'school-config-download-supplementary-exercise',
          path: 'downloadSupplementaryExercise',
          meta: {
            menu: '教辅下载权限配置',
            roles: () => Store.getters['user/isSystem'],
          },
          component: () => import('@/views/school/config/download_supplementary_exercise/index.vue'),
        },
      ],
    },
    {
      name: 'system-log',
      path: 'log',
      meta: {
        menu: '日志管理',
        roles: user =>
          user.roles.some(r => r === Role.SystemAdministrator.id || r === Role.SchoolAdministrator.id) || user.isSystem,
      },
      component: () => import('@/views/school/log/index.vue'),
    },
  ],
}
