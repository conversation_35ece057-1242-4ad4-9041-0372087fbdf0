/**
 * 枚举类
 * _keys_：私有字段，保存创建时各枚举项的key
 * 其余字段：与创建时传入的一致
 */
export default class Enum {
  constructor(rawObj) {
    this._keys_ = []
    for (let key in rawObj) {
      this._keys_.push(key)
      this[key] = rawObj[key]
    }
  }

  /**
   * 获取各枚举项键值，创建后不受增删对象字段（如：this.anotherField = anotherValue）的影响
   * @returns Array
   */
  getKeys() {
    return this._keys_.slice()
  }

  /**
   * 获取值数组
   * @returns Array
   */
  getIds() {
    return this.getKeys().map(key => this[key].id)
  }

  /**
   * 枚举中是否存在给定的id
   * @param {Number|String |Boolean} id
   * @returns Boolean
   */
  hasId(id) {
    return this.getKeys().some(key => this[key].id === id)
  }

  /**
   * 获取名称数组
   * @returns Array
   */
  getNames() {
    return this.getKeys().map(key => this[key].name)
  }

  /**
   * 获取值、名称对数组
   * @returns Array
   */
  getIdNames() {
    return this.getKeys().map(key => ({
      id: this[key].id,
      name: this[key].name,
    }))
  }

  /**
   * 获取枚举项数组
   * @returns Array
   */
  getEntries() {
    return this.getKeys().map(key => ({
      ...this[key],
      key,
    }))
  }

  /**
   * 根据Key查找枚举项
   * @param {String} key
   * @returns Object
   */
  getEntryByKey(key) {
    if (this.getKeys().includes(key)) {
      return {
        ...this[key],
        key,
      }
    }
  }

  /**
   * 根据Id查找枚举项
   * @param {Number|String|Boolean} id
   * @returns Object
   */
  getEntryById(id) {
    for (let key of this.getKeys()) {
      if (this[key].id === id) {
        return {
          ...this[key],
          key,
        }
      }
    }
  }

  /**
   * 根据Id查找名称
   * @param {Number|String|Boolean} id
   * @returns String
   */
  getNameById(id) {
    for (let key of this.getKeys()) {
      if (this[key].id === id) {
        return this[key].name
      }
    }
  }

  /**
   * 根据名称查找枚举项
   * @param {String} name
   * @returns Object
   */
  getEntryByName(name) {
    for (let key of this.getKeys()) {
      if (this[key].name === name) {
        return {
          ...this[key],
          key,
        }
      }
    }
  }

  /**
   * 根据名称查找Id
   * @param {String} name
   * @returns Number
   */
  getIdByName(name) {
    for (let key of this.getKeys()) {
      if (this[key].name === name) {
        return this[key].id
      }
    }
  }

  /**
   * 枚举子对象
   * @param {Array} keys
   * @returns Object
   */
  subEnum(keys = []) {
    let enumKeys = this.getKeys()
    let obj = {}
    keys.forEach(key => {
      if (enumKeys.includes(key)) {
        obj[key] = this[key]
      }
    })
    return new Enum(obj)
  }
}
