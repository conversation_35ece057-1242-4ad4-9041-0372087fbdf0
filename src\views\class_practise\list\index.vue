<template>
  <div class="container-practise-home">
    <div v-if="isServiceActive" class="relative-btns">
      <div class="title">AI测练</div>
      <div>
        <Button
          type="primary"
          class="relative-btn btn-normal-height btn-success"
          icon="md-paper"
          ghost
          @click="handleBtnSkipToAnswerSheetList"
          >制作答题卡</Button
        >
        <Button type="primary" class="relative-btn btn-normal-height btn-success" icon="md-add" @click="createNewTest"
          >新建测练</Button
        >
      </div>
    </div>
    <FilterBar
      class="filter-bar"
      :fields="['term', 'grade', 'subject']"
      search-placeholder="测验名称"
      :params="requestParams"
      @change="changeParams"
    ></FilterBar>

    <Alert v-if="!isServiceActive" class="alert-service" type="warning" show-icon>{{ alertContent }}</Alert>

    <div v-if="testList && testList.length" class="tests-panel">
      <div v-for="item of testList" :key="item.examId" class="test-item">
        <div class="informations">
          <div class="box-icon">
            <img :src="item.statusIcon" alt="" />
          </div>
          <div>
            <div class="info-top">
              <span
                class="test-title"
                :class="{ 'test-title-entry': !isBtnReportDisabled(item) }"
                @click="onToReport(item)"
                >{{ item.examName }}</span
              >
              <!-- <span v-show="item.isFinish" class="sign color-green-sign">已完成</span> -->
            </div>
            <div class="info-bottom">
              <div>
                <span class="test-subject">{{ item.gradeName + item.subjectName }}</span>
              </div>
              <Divider type="vertical" />
              <div>
                <Tooltip placement="top" max-width="200">
                  <TextButton type="primary">共 {{ item.classList.length }} 个班级</TextButton>
                  <template #content>
                    <p>{{ getClassName(item) }}</p>
                  </template>
                </Tooltip>
              </div>
              <Divider type="vertical" />
              <div>
                <Icon type="ios-contact-outline" />
                <span class="test-crator">创建人：{{ item.creatorName }}</span>
              </div>
              <Divider type="vertical" />
              <div>
                <Icon type="md-time" />
                <span class="test-create-time">创建时间：{{ item.formatCreateTime }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="test-operations">
          <div class="box-timeline-btn">
            <Timeline>
              <TimelineItem>
                <Button type="default" class="btn" @click="handleBtnGoToTestSetting(item)">
                  <span>设置</span>
                </Button>
              </TimelineItem>
              <TimelineItem>
                <div class="box-scan">
                  <Button type="info" ghost class="btn btn-info" @click="handleBtnScanTestClick(item)">
                    <span>扫描</span>
                  </Button>
                  <div class="box-item-progress">
                    <el-progress
                      class="progress-item-body"
                      :stroke-width="8"
                      :percentage="Math.min(item.scanProgress, 100)"
                      :color="customColorMethod"
                    ></el-progress>
                  </div>
                </div>
              </TimelineItem>
              <TimelineItem>
                <div class="box-scan">
                  <Button
                    type="success"
                    ghost
                    class="btn btn-success"
                    :disabled="!item.needMark"
                    @click="onToMark(item)"
                  >
                    <span>批改</span>
                  </Button>
                  <div class="box-item-progress">
                    <el-progress
                      v-if="item.needMark"
                      class="progress-item-body"
                      :stroke-width="8"
                      :percentage="Math.min(item.markProgress, 100)"
                      :color="customColorMethod"
                    ></el-progress>
                    <span v-else class="text-tips">（无需线上阅卷）</span>
                  </div>
                </div>
              </TimelineItem>
              <TimelineItem>
                <Button
                  :disabled="isBtnReportDisabled(item)"
                  type="info"
                  class="btn btn-info"
                  ghost
                  @click="onToReport(item)"
                >
                  <span>报表</span>
                </Button>
              </TimelineItem>
            </Timeline>
          </div>
        </div>
      </div>

      <div v-if="totalTestCount > 10" class="page-elevator-panel">
        <span class="test-count">共 {{ totalTestCount }} 个测验项目</span>
        <Page
          class="page-elevator"
          :total="totalTestCount"
          :model-value="requestParams.currentPage"
          :page-size="10"
          @on-change="changePage"
        ></Page>
      </div>
    </div>
    <NoData v-else :visible="!loading">暂无AI测练项目</NoData>
  </div>
</template>

<script>
  import { ElProgress } from 'element-plus'
  import 'element-plus/es/components/progress/style/css'
  import FilterBar from './components/filter_bar.vue'
  import Timeline from '@/components/timeline.vue'
  import TimelineItem from '@/components/timeline_item.vue'
  import NoData from '@/components/no_data'

  import { apiGetPractiseList, apiAddAsScanner, apiAddAsMarker } from '@/api/emarking/practise'

  import { scrollToTop } from '@/utils/scroll'
  import { formatDate } from '@/utils/date'
  import { roundNumber } from '@/utils/math'
  import PageCache from '@/utils/page_cache'
  import ModuleEnum from '@/enum/user/module'
  // import iconGoing from '@/assets/images/emarking/practise/icon_going.png'
  // import iconFinished from '@/assets/images/emarking/practise/icon_finished.png'
  import IconUnfinished from '@/assets/images/emarking/practise/测练进行中.svg'
  import IconFinished from '@/assets/images/emarking/practise/测练已完成.svg'

  const uniquePageName = 'class-practise-home'

  export default {
    components: {
      FilterBar,
      Timeline,
      TimelineItem,
      'el-progress': ElProgress,
      NoData,
    },
    data() {
      return {
        totalTestCount: 0,
        testList: [],
        requestParams: {
          semesterId: '',
          term: '',
          gradeId: '',
          subjectId: '',
          keyword: '',
          currentPage: 1,
          pageSize: 10,
        },
        loading: true,
      }
    },

    computed: {
      isServiceActive() {
        return this.$store.getters['user/isModuleNormal'](ModuleEnum.ClassPractise.id)
      },

      alertContent() {
        if (this.$store.getters['user/isModuleExpired'](ModuleEnum.ClassPractise.id)) {
          let serviceEndDate = this.$store.getters['user/moduleEndDate'](ModuleEnum.ClassPractise.id)
          return `您的${ModuleEnum.ClassPractise.name}已于 ${serviceEndDate} 到期`
        } else {
          return `您尚未开通${ModuleEnum.ClassPractise.name}`
        }
      },

      isSchoolAdministrator() {
        return this.$store.getters['user/isSchoolAdministrator']
      },

      teachInfos() {
        return this.$store.state.user.teachings || []
      },

      grades() {
        return this.$store.getters['emarking/grades']() || []
      },
    },

    created() {
      PageCache.fill(this.requestParams, uniquePageName)
    },

    beforeUnmount() {
      PageCache.save(uniquePageName, this.requestParams)
    },

    methods: {
      isBtnReportDisabled(item) {
        return (item.needMark && item.markedCount == 0) || (!item.needMark && item.scannedCount == 0)
      },
      getClassName(item) {
        return item.classList.map(c => c.className).join('、')
      },
      getIconStatus(item) {
        return item.status == 2 ? IconFinished : IconUnfinished
      },
      customColorMethod(percentage) {
        if (percentage > 0 && percentage < 100) {
          // return '#37cdbe'
          return '#1890FF'
        } else {
          return '#19be6b'
        }
      },
      fetchTestList() {
        scrollToTop()
        this.loading = true
        apiGetPractiseList(this.requestParams)
          .then(response => {
            this.totalTestCount = response.total
            this.testList = response.list.map(x => {
              let scanProgress = 0
              let markProgress = 0

              scanProgress = x.totalScanCount
                ? roundNumber((Number(x.scannedCount || 0) / Number(x.totalScanCount)) * 100, 1)
                : 0
              markProgress = x.totalMarkCount
                ? roundNumber((Number(x.markedCount || 0) / Number(x.totalMarkCount)) * 100, 1)
                : 0

              return {
                ...x,
                statusText: x.status == 2 ? '已完成' : '进行中',
                gradeName: (this.grades.find(y => y.id === x.gradeId) || { name: '' }).name,
                formatCreateTime: formatDate(new Date(x.createTime)),
                statusIcon: this.getIconStatus(x),
                scanProgress,
                markProgress,
              }
            })
          })
          .finally(() => {
            this.loading = false
          })
      },

      createNewTest() {
        if (!this.isSchoolAdministrator && !this.teachInfos.length) {
          this.$Message.warning({
            duration: 4,
            content: '您尚无任教班级，不能新建测验~',
          })
          return
        }

        this.$router.push({
          name: 'class-practise-create',
        })
      },

      handleBtnSkipToAnswerSheetList() {
        this.$router.push({
          name: 'answerSheet',
        })
      },

      changeParams(data) {
        Object.keys(data).forEach(key => {
          if (key in this.requestParams) {
            this.requestParams[key] = data[key]
          }
        })
        this.changePage(data.currentPage)
      },

      changePage(page) {
        this.requestParams.currentPage = page
        this.fetchTestList()
      },

      handleBtnGoToTestSetting({ examSubjectId, examId }) {
        this.$router.push({
          name: 'class-practise-setting',
          params: {
            examSubjectId,
            examId,
          },
        })
      },

      async handleBtnScanTestClick(item) {
        await apiAddAsScanner(item.examSubjectId)
        this.$router.push({
          name: 'scan-answerSheet',
          params: {
            examId: item.examId,
            examSubjectId: item.examSubjectId,
          },
        })
      },
      onToReport(item) {
        if (this.isBtnReportDisabled(item)) return
        this.$router.push({ name: 'class-practise-report', params: { examId: item.examId } })
      },
      async onToMark(item) {
        await apiAddAsMarker(item.examSubjectId)
        // await this.$store.dispatch('emarking/setAdminExamById', item.examId)
        this.$router.push({
          name: 'emarking-subject-index',
          params: {
            examSubjectId: item.examSubjectId,
            isFromHomePage: 0,
          },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-practise-home {
    position: relative;
    padding: 10px 20px 20px;
    background-color: white;

    :deep(.el-progress__text) {
      min-width: 28px;
      font-size: 13px !important;
    }

    .btn-normal-height {
      height: 28px;
      font-size: 14px;
    }

    .btn-success {
      border-color: #13ce66;
      color: #13ce66;
      background-color: #e8faf0;
    }

    .btn-info {
      border-color: #409eff;
      color: #409eff;
      background-color: #ecf5ff;
    }

    .btn-warning {
      border-color: #fea860;
      color: #fea860;
      background-color: #fffaf5;
    }

    .btn-error {
      border-color: #ff4949;
      color: #ff4949;
      background-color: #ffeded;
    }

    .btn {
      height: 28px;
    }

    .btn[disabled] {
      border-color: #ccc;
      color: #ccc;
      background-color: #fff;
    }

    .filter-bar {
      margin-bottom: 16px;
    }

    .tests-panel {
      .test-item {
        @include flex(row, space-between, center);
        position: relative;
        padding: 24px;
        border: 1px solid transparent;
        border-radius: 4px;
        box-shadow: 2px 2px 12px 0px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;

        &:hover {
          border-color: $color-primary;
          transform: scale(1.02);
        }

        &:not(:last-child) {
          margin-bottom: 24px;
        }

        .informations {
          @include flex(row, flex-start, center);
          flex-grow: 1;

          .info-top {
            height: 30px;

            .test-title {
              font-weight: bold;
              font-size: $font-size-medium-x;
            }

            .test-title-entry {
              cursor: pointer;

              &:hover {
                color: $color-primary;
              }
            }

            .sign {
              margin-left: 20px;
              padding: 2px 5px;
              border: 1px solid #2abc7c;
              border-radius: 2px;
              color: #2abc7c;
              font-size: $font-size-small;
              background-color: rgba(91, 219, 140, 0.1);
            }
          }

          .info-bottom {
            @include flex(row, flex-start, center);
            height: 20px;
            margin-top: 15px;
            color: $color-icon;
            font-size: 12px;

            .test-grade {
              display: inline-block;
            }

            .test-crator {
              display: inline-block;
              min-width: 100px;
            }
          }
        }

        .box-icon {
          flex-shrink: 0;
          margin-right: 16px;
          font-size: 0;

          img {
            width: 74px;
            height: 74px;
          }
        }

        .text-status {
          font-size: 12px;
        }

        .text-status-warning {
          color: $color-warning;
        }

        .text-status-primary {
          color: $color-primary;
        }

        .test-operations {
          .re-cal-report-sign {
            margin-right: 6px;
            color: $color-warning;
            font-size: 18px;
            line-height: 18px;
          }

          .btn-test-operation {
            width: 120px;
          }
        }

        .box-scan,
        .box-mark {
          @include flex(row, flex-start, center);
        }

        .box-item-progress {
          width: 120px;
          margin-left: 10px;
        }

        .text-tips {
          color: #c5c8ce;
        }
      }

      .page-elevator-panel {
        @include flex(row, flex-end, center);
        margin-top: 20px;

        .test-count {
          margin-right: 10px;
        }
      }
    }
  }

  .template-item {
    @include flex(row, space-between, center);

    &:not(:last-child) {
      margin-bottom: 20px;
    }
  }

  .relative-btns {
    @include flex(row, space-between, center);
    margin-bottom: 15px;
    padding: 0 0 10px;
    border-bottom: 1px solid #dfdfdf;

    .title {
      // color: $color-primary;
      font-size: 16px;
    }

    .relative-btn {
      margin-left: 10px;
    }
  }
</style>
