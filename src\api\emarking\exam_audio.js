import ajax from '@/api/ajax'
import store from '@/store/index'
import { formatDate } from '@/utils/date'
import ExamScopeEnum from '@/enum/emarking/exam_scope'
import ExamTypeEnum from '@/enum/emarking/exam_type'

function transformAudioGroup(g) {
  return {
    audioGroupId: String(g.id),
    audioGroupName: g.name,
    schoolId: g.schoolId,
    createBy: g.createBy,
    createTime: g.createTime,
    updateBy: g.updateBy,
    updateTime: g.updateTime,
    beginPlayTime: g.beginPlayTime,
    endPlayTime: g.endPlayTime,
    examNames: g.examNames,
  }
}

function transformAudioItem(item) {
  return {
    ...item,
    audioItemId: String(item.id),
    audioItemName: item.name,
  }
}

export function transformExam(responseExam) {
  let examScope = ExamScopeEnum.getEntryById(responseExam.examScope) || {}
  let examType = ExamTypeEnum.getEntryById(responseExam.examType) || {}
  let gradeSubjects = store.getters['emarking/gradeSubjects']()
  let grade = gradeSubjects.find(x => x.id === responseExam.gradeId) || {}
  let startDate = new Date(responseExam.beginTime)
  let endDate = new Date(responseExam.endTime)
  let examDateText = `${formatDate(startDate)} ~ ${formatDate(endDate)}`
  return {
    examId: responseExam.examId,
    examName: responseExam.examName,
    examScopeId: responseExam.examScope,
    examScopeName: examScope.name || '',
    examTypeId: responseExam.examType,
    examTypeName: examType.name || '',
    gradeId: responseExam.gradeId,
    gradeName: grade.name || '',
    startDate,
    endDate,
    examDateText,
  }
}

/**
 * 语音组
 */
export function apiGetAudioGroupList({ semesterId, term, keyword, pageSize, currentPage }) {
  return ajax
    .get({
      url: 'mark/examAudio/listAudioGroup',
      params: {
        pageNum: currentPage,
        pageSize: pageSize,
        name: keyword,
        semesterId,
        term,
      },
      requestName: '获取语音组',
    })
    .then(data => ({
      total: data.total,
      records: data.list.map(transformAudioGroup),
    }))
}

export function apiGetAudioGroupById(audioGroupId) {
  return ajax
    .get({
      url: 'mark/examAudio/getAudioGroupById',
      params: {
        id: audioGroupId,
      },
      requestName: '获取语音组',
    })
    .then(audioGroup => {
      if (audioGroup) {
        return transformAudioGroup(audioGroup)
      } else {
        return audioGroup
      }
    })
}

export function apiCreateAudioGroup(name) {
  return ajax.post({
    url: 'mark/examAudio/createAudioGroup',
    params: {
      name,
    },
    requestName: '创建语音组',
  })
}

export function apiUpdateAudioGroup(data) {
  return ajax.post({
    url: 'mark/examAudio/updateAudioGroup',
    params: {
      id: data.audioGroupId,
      name: data.audioGroupName,
    },
    requestName: '更新语音组',
  })
}

export function apiDeleteAudioGroup(id) {
  return ajax.delete({
    url: 'mark/examAudio/deleteAudioGroup',
    params: {
      id,
    },
    requestName: '删除语音组',
  })
}

/**
 * 考试与语音组
 */
export function apiGetAudioGroupListByExamId(examId) {
  return ajax
    .get({
      url: 'mark/examAudio/getAudioGroupByExamId',
      params: {
        examId,
      },
      requestName: '获取语音组',
    })
    .then(list => list.map(transformAudioGroup))
}

export function apiGetAudioGroupExams(audioGroupId) {
  return ajax
    .get({
      url: 'mark/examAudio/listExamByAudioGroupId',
      params: {
        groupId: audioGroupId,
      },
      requestName: '获取语音组关联考试',
    })
    .then(exams => exams.map(transformExam))
}

export function apiGetAudioGroupAvailableExams(audioGroupId) {
  return ajax
    .get({
      url: 'mark/examAudio/listAvailableExam',
      params: {
        groupId: audioGroupId,
      },
      requestName: '获取语音组可关联的考试',
    })
    .then(exams => exams.map(transformExam))
}

export function apiBindExamToAudioGroup(params) {
  return ajax.post({
    url: 'mark/examAudio/bindExamAudioGroup',
    params: {
      groupId: params.audioGroupId,
      examId: params.examId,
    },
    requestName: '关联考试到语音组',
  })
}

export function apiUnBindExamToAudioGroup(params) {
  return ajax.delete({
    url: 'mark/examAudio/deleteExamAudioGroup',
    params: {
      groupId: params.audioGroupId,
      examId: params.examId,
    },
    requestName: '解除考试与语音组的关联',
  })
}

/**
 * 组内语音
 */
export function apiGetAudioItemsByGroupId(audioGroupId) {
  return ajax
    .get({
      url: 'mark/examAudio/listAudioByGroupId',
      params: {
        groupId: audioGroupId,
      },
      requestName: '获取语音',
    })
    .then(list => list.map(transformAudioItem))
}

export function apiGetAudioItemUrl(params) {
  return ajax.get({
    url: 'mark/examAudio/getAudioUrlByGroupIdAndId',
    params: {
      groupId: params.audioGroupId,
      id: params.audioItemId,
    },
    requestName: '获取语音链接',
  })
}

export function apiGetCommonAudioItems() {
  return ajax
    .get({
      url: 'mark/examAudio/listCommonAudio',
      requestName: '获取公共语音',
    })
    .then(list => list.map(transformAudioItem))
}

export function apiAddAudioItem(data) {
  return ajax.upload({
    url: 'mark/examAudio/createAudioDetail',
    data: {
      groupId: data.audioGroupId,
      name: data.audioItemName,
      type: data.type,
      url: data.url || '',
      content: data.content || '',
      duration: data.duration || '',
      stage: data.stage || '',
      playTime: data.playTime,
      isSecret: data.isSecret,
      remark: data.remark || '',
    },
    requestName: '添加语音',
  })
}

export function apiAddAudioItemUploadingFile(data) {
  return ajax.upload({
    url: 'mark/examAudio/uploadAudioFile',
    data: {
      groupId: data.audioGroupId,
      name: data.audioItemName,
      type: data.type,
      content: data.content,
      stage: data.stage,
      playTime: data.playTime,
      isSecret: data.isSecret,
      remark: data.remark,
      file: data.file,
    },
    requestName: '添加语音',
  })
}

export function apiUpdateAudioItem(data) {
  return ajax.post({
    url: 'mark/examAudio/updateAudioDetail',
    params: {
      id: data.audioItemId,
      name: data.audioItemName,
      type: data.type,
      stage: data.stage,
      playTime: data.playTime,
      isSecret: data.isSecret,
      remark: data.remark,
    },
    requestName: '更新语音信息',
  })
}

export function apiDeleteAudioItem(data) {
  return ajax.delete({
    url: 'mark/examAudio/deleteAudioByGroupIdAndIds',
    params: {
      groupId: data.audioGroupId,
      ids: data.audioItemIds.join(','),
    },
    requestName: '删除语音',
  })
}

export function apiDownloadListeningZip(groupId) {
  return ajax.download({
    url: 'mark/examAudio/downloadAudioZip',
    params: {
      groupId: groupId,
    },
    requestName: '下载语音压缩包文件',
  })
}
