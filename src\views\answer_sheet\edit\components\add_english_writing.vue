<template>
  <div class="add-english-writing">
    <Form :label-width="80">
      <FormItem label="大题号" class="ivu-form-item-required">
        <Select :model-value="topicCode" class="input-short" @on-change="changeTopicCode">
          <Option v-for="t in topicChineseCodes" :key="t.code" :value="t.code">{{ t.codeInChinese }}</Option>
        </Select>
      </FormItem>
      <FormItem label="大题名" class="ivu-form-item-required">
        <Input v-model="topicName" :disabled="isExistTopic" maxlength="20"></Input>
      </FormItem>
      <FormItem label="题号" class="ivu-form-item-required">
        <InputNumber v-model="questionCode" class="input-short" :min="1" :precision="0"></InputNumber>
      </FormItem>
      <FormItem label="行数" class="ivu-form-item-required">
        <Select v-model="rows" class="input-short">
          <Option v-for="r in rowsList" :key="r.id" :value="r.id">{{ r.name }}</Option>
        </Select>
      </FormItem>
      <FormItem label="分数" class="ivu-form-item-required">
        <InputNumber v-model="score" :min="0" class="input-short"></InputNumber>
      </FormItem>
    </Form>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import SubjectiveQuestion from '@/helpers/emarking/subjective_question'
  import { roundScore } from '@/utils/math'
  import { randomId } from '@/utils/string'
  import { isPositiveInteger, isPositiveFinite, numberToChinese } from '@/utils/number'
  import BranchTypeEnum from '@/enum/qlib/branch_type'
  import { Max_Question_Code } from '@/const/emarking'

  export default {
    props: {
      branchTypeId: Number,
      topicChineseCodes: Array,
    },
    data() {
      return {
        topicCode: 1,
        topicName: '',
        questionCode: 1,
        rows: 9,
        score: null,
      }
    },
    computed: {
      ...mapGetters('answerSheet', ['questionCodes', 'maxQuestionCode', 'topicCodeNames', 'maxTopicCode']),
      isExistTopic() {
        return this.topicCodeNames.some(t => t.topicCode == this.topicCode)
      },
      rowsList() {
        let list = []
        for (let i = 4; i <= 15; i++) {
          list.push({
            id: i,
            name: `${i}行`,
          })
        }
        return list
      },
    },
    created() {
      this.topicCode = this.maxTopicCode + 1
      this.topicName = numberToChinese(this.topicCode) + '、' + BranchTypeEnum.getNameById(this.branchTypeId)
      this.questionCode = this.maxQuestionCode + 1
    },
    methods: {
      changeTopicCode(newTopicCode) {
        let oldTopicCode = this.topicCode
        this.topicCode = newTopicCode
        if (this.isExistTopic) {
          this.topicName = this.topicCodeNames.find(t => t.topicCode == this.topicCode).topicName
        } else {
          let oldTopicCodeChinese = numberToChinese(oldTopicCode)
          let newTopicCodeChinese = numberToChinese(newTopicCode)
          this.topicName = this.topicName.replace(new RegExp(`^${oldTopicCodeChinese}、`), newTopicCodeChinese + '、')
        }
      },
      check() {
        if (!this.topicCode) {
          return {
            message: '请选择大题',
          }
        }
        if (!this.topicName) {
          return {
            message: '请输入大题名',
          }
        }
        if (!isPositiveInteger(this.questionCode)) {
          return {
            message: '请输入正确的题号',
          }
        }
        if (this.questionCode > Max_Question_Code) {
          return {
            message: '题号过大',
          }
        }
        if (!this.rows) {
          return {
            message: '请选择行数',
          }
        }
        if (this.questionCodes.includes(this.questionCode)) {
          return {
            message: `题号${this.questionCode}已存在`,
          }
        }
        let score = roundScore(this.score)
        if (!score || !isPositiveFinite(score)) {
          return {
            message: '请输入分数',
          }
        }

        let subj = new SubjectiveQuestion()
        subj.questionId = randomId()
        subj.questionCode = this.questionCode
        subj.questionName = `${this.questionCode}`
        subj.branchCode = 0
        subj.branchName = subj.questionName
        subj.topicCode = this.topicCode
        subj.topicName = this.topicName
        subj.fullScore = score

        return {
          message: '',
          data: {
            topicCode: this.topicCode,
            topicName: this.topicName,
            rows: this.rows,
            subjectiveQuestion: subj,
          },
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .input-short {
    width: 100px;
  }
</style>
