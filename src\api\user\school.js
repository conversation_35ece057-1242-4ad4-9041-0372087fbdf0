import ajax from '@/api/ajax'
import { formatDate } from '@/utils/date'

function transformSemester(s) {
  return {
    semesterId: s.semesterId,
    semesterName: s.semesterName,
    termBeginTime1: new Date(s.termBeginTime1),
    termEndTime1: new Date(s.termEndTime1),
    termBeginTime2: new Date(s.termBeginTime2),
    termEndTime2: new Date(s.termEndTime2),
  }
}

export function apiGetSystemSemesters() {
  return ajax
    .get({
      url: 'user/school/sysSemesters',
      requestName: '获取学年',
    })
    .then(data => (data || []).map(transformSemester))
}

export function apiGetSchoolSemesters() {
  return ajax
    .get({
      url: 'user/school/semester',
      requestName: '获取学年',
    })
    .then(data => (data || []).map(transformSemester))
}

export function apiGetSchoolTerms() {
  return ajax.get({
    url: 'user/school/listTerm',
    requestName: '获取学年学期',
  })
}

export function apiGetEducationBureau() {
  return ajax
    .get({
      url: 'user/school/edulist',
      requestName: '获取教育局信息',
    })
    .then(data => {
      return data.map(bureau => ({
        id: bureau.schoolId,
        name: bureau.schoolName,
        sortCode: bureau.sortCode,
        // gradeIds: bureau.gradeLevelIds && bureau.gradeLevelIds.split(',')
      }))
    })
}

export function apiEditSchoolSemester(data) {
  return ajax.put({
    url: 'user/school/semester',
    data: {
      semesterId: data.semesterId,
      termBeginTime1: data.termBeginTime1 ? formatDate(data.termBeginTime1) : null,
      termEndTime1: data.termEndTime1 ? formatDate(data.termEndTime1) : null,
      termBeginTime2: data.termBeginTime2 ? formatDate(data.termBeginTime2) : null,
      termEndTime2: data.termEndTime2 ? formatDate(data.termEndTime2) : null,
    },
    requestName: '修改学期',
  })
}

export function apiSetSchoolGradeSubject(params) {
  return ajax.put({
    url: 'user/school/gradesubject',
    params: {
      gradeId: params.gradeId,
      subjectId: params.subjectId,
      check: params.checked,
    },
    requestName: '修改年级学科',
  })
}

export function apiUpgradeSemester() {
  return ajax.put({
    url: 'user/school/upgrade',
    requestName: '升级至最新学年',
  })
}

export function apiGetMySchoolGradeSubjects() {
  return ajax.get({
    url: 'user/school/gradesubject',
    requestName: '获取本校年级学科信息',
  })
}

export function apiGetSchoolGradeSubjects(data) {
  return ajax
    .get({
      url: 'user/school/gradesubject2',
      params: {
        schoolId: data && data.schoolId,
      },
      requestName: '获取学校年级学科信息',
    })
    .then(data => {
      return data
    })
}

export function apiGetChildOrgAndSchool(data) {
  return ajax
    .get({
      url: 'user/sys/eduSchool/childOrgAndSchool',
      params: {
        eduSchoolId: data && data.eduSchoolId,
      },
      requestName: '获取子机构和学校',
    })
    .then(data => {
      return data
    })
}

export function apiGetStudentList(data) {
  return ajax.get({
    url: 'user/student/listStudent',
    params: data,
    requestName: '获取学生列表',
  })
}

export function apiGetGradeClassList(data) {
  return ajax
    .get({
      url: 'user/school/getGradeClass',
      params: data,
      requestName: '获取学校年级班级列表',
    })
    .then(data => {
      return data
    })
}

export function apiImportOwnSchoolStudents(excelFile, options) {
  return ajax.upload({
    url: 'user/student/sysImportOneSchoolStudent',
    data: {
      file: excelFile,
      chkRealname: true,
      chkClass: !options.updateClass,
      isClean: options.cleanParentRelate,
      schoolId: options.schoolId,
    },
  })
}

export function apiImportMultipleSchoolStudents(excelFile, options) {
  return ajax.upload({
    url: 'user/student/sysImportManySchoolStudent',
    data: {
      file: excelFile,
      chkRealname: true,
      chkClass: !options.updateClass,
      isClean: options.cleanParentRelate,
      eduSchoolId: options.schoolId,
    },
  })
}

export function apiDeleteStudentsByGrade(data) {
  return ajax.delete({
    url: 'user/student/sysDelStudent',
    data: data,
  })
}

export function apiExportStudentsByGradeAndClass(data) {
  return ajax
    .download({
      url: 'user/student/export',
      params: data,
      requestName: '导出学生',
    })
    .then(data => {
      return data
    })
}

export function apiGetTeacherList(data) {
  return ajax
    .get({
      url: 'user/teacher/listTeacher',
      params: data,
      requestName: '获取教师列表',
    })
    .then(data => ({
      total: data.total,
      teachers: data.records.map(responseTeacher => {
        return {
          userId: responseTeacher.teacher.teacherId,
          userName: responseTeacher.teacher.userName,
          realName: responseTeacher.teacher.realName,
          sex: responseTeacher.teacher.sex,
          mobile: responseTeacher.teacher.mobile || '',
          teachings: (responseTeacher.teachings || []).map(x => ({
            gradeId: x.gradeId,
            gradeName: x.gradeName,
            classId: x.classId,
            className: x.className,
            subjectId: x.subjectId,
            subjectName: x.subjectName,
          })),
          roles: (responseTeacher.roles || []).map(r => ({
            roleId: r.roleId,
            roleName: r.roleName,
            stageId: r.gradeLevelId,
            stageName: r.gradeLevelName,
            gradeId: r.gradeId,
            gradeName: r.gradeName,
            classId: r.classId,
            className: r.className,
            subjectId: r.subjectId,
            subjectName: r.subjectName,
          })),
        }
      }),
    }))
}

export function apiImportMultipleSchoolTeachers(excelFile, options) {
  return ajax.upload({
    url: 'user/teacher/sysImportManySchoolTeacher',
    data: {
      file: excelFile,
      updateName: options.updateName,
      eduSchoolId: options.schoolId,
    },
  })
}

export function apiImportOwnSchoolTeachers(excelFile, options) {
  return ajax.upload({
    url: 'user/teacher/sysImportOneSchoolTeacher',
    data: {
      file: excelFile,
      updateName: options.updateName,
      schoolId: options.schoolId,
    },
  })
}

export function apiExportTeachersBySchool(data) {
  return ajax
    .download({
      url: 'user/teacher/exportBySchool',
      params: data,
      requestName: '导出教师',
    })
    .then(data => {
      return data
    })
}

// 所有用户查看所在学校的管理员
export function apiUserGetSchoolManagers() {
  return ajax.get({
    url: 'user/school/managers',
    requestName: '获取学校管理员名单',
  })
}

export function apiGetSchoolFeedbackScanPriority(params) {
  return ajax.get({
    url: '/user/school/isFeedbackScanPriority',
    params,
    requestName: '查询学校反馈项目是否优先取扫描数据',
  })
}

export function apiGetSchoolFeedbackIsScanPriority(schoolId) {
  return ajax.get({
    url: 'user/school/isSchoolFeedbackScanPriority',
    params: {
      schoolId: schoolId,
    },
    requestName: '查询学校反馈项目是否优先取扫描数据',
  })
}

export function apiSetSchoolFeedbackPriority(requestParams) {
  return ajax.put({
    url: 'user/school/setSchoolFeedbackScanPriority',
    params: {
      schoolId: requestParams.schoolId,
      isSchoolFeedbackScanPriority: requestParams.isSchoolFeedbackScanPriority,
    },
    requestName: '设置学校反馈项目数据来源优先级',
  })
}

export function apiGetUserSchoolListByPhoneOrName({ key, schoolId, pageNum, pageSize }) {
  return ajax.get({
    url: 'user/school/sysAdminGetUserSchoolListByPhoneOrName',
    params: {
      schoolId,
      key,
      pageNum,
      pageSize,
    },
    requestName: '查询管理员账号',
  })
}

export function apiDownloadInstitutionGradeNumber(requestParams) {
  return ajax.download({
    url: 'user/school/exportSchoolGradeStudentCountList',
    params: {
      orgSchoolId: requestParams.institutionId, // *String
      gradeLevel: requestParams.gradeLevel, // Number
      gradeId: requestParams.gradeId, // Number
    },
    requestName: '导出下属学校年级学生人数',
  })
}
