export default class PageCache {
  static save(pageName, pageData) {
    try {
      let pageDataStr = JSON.stringify(pageData)
      window.sessionStorage.setItem(pageName, pageDataStr)
      return true
    } catch (err) {
      return false
    }
  }
  static fetch(pageName) {
    try {
      let pageDataStr = window.sessionStorage.getItem(pageName)
      let pageData = JSON.parse(pageDataStr)
      return pageData
    } catch (err) {
      return null
    }
  }
  static remove(pageName) {
    window.sessionStorage.removeItem(pageName)
  }
  static fill(obj, pageName) {
    let cacheData = PageCache.fetch(pageName)
    if (cacheData == null || typeof cacheData != 'object') {
      return false
    }

    try {
      Object.keys(cacheData).forEach(key => {
        if (key in obj) {
          obj[key] = cacheData[key]
        }
      })
      return true
    } catch (err) {
      return false
    }
  }
}
