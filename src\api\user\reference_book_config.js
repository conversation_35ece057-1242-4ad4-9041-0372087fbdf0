import ajax from '@/api/ajax'

export function apiGetReferenceBookScanenrList() {
  return ajax.get({
    url: 'user/coachBookScanner/list',
    requestName: '获取教辅扫描员列表',
  })
}

export function apiAddReferenceBookScanner(requestParams) {
  return ajax.post({
    url: 'user/coachBookScanner/add',
    data: {
      userName: requestParams.userName, // *String  账号
      realName: requestParams.realName, // *String  姓名
      password: requestParams.password, // *String  密码
    },
    requestName: '添加教辅扫描员',
  })
}

export function apiModifyReferenceBookScannerRealName(requestParams) {
  return ajax.put({
    url: 'user/coachBookScanner/edit',
    data: {
      userName: requestParams.userName, // *String  账号
      realName: requestParams.realName, // *String  姓名
      password: requestParams.password, // *String  密码
    },
    requestName: '编辑教辅扫描员（仅修改呢称）',
  })
}

export function apiModifyReferenceBookScannerPassword(requestParams) {
  return ajax.put({
    url: 'user/coachBookScanner/resetPwd',
    params: {
      userId: requestParams.userId, // *String
      pwd: requestParams.password, // *String
    },
    requestName: '重置教辅扫描员密码',
  })
}

export function apiDeleteReferenceBookScanner(userId) {
  return ajax.delete({
    url: 'user/coachBookScanner',
    params: {
      userId: userId, // *String
    },
    requestName: '删除教辅扫描员',
  })
}
