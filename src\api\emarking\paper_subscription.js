import ajax from '@/api/ajax'

export function apiGetPaperSubscriptionSetting(requestParams) {
  return ajax.get({
    url: 'mark/subscribe/getPaperSubscribeConfig',
    params: {
      examId: requestParams.examId,
    },
    // requestName: '查看试卷报订设置',
  })
}

// 保存试卷报订设置
export function apiSavePaperSubscriptionSetting(requestParams) {
  return ajax.post({
    url: 'mark/subscribe/saveOrUpdatePaperSubscribeConfig',
    params: {
      examId: requestParams.examId,
      subscribeType: requestParams.subscriptionWay,
      beginTime: requestParams.beginTime,
      endTime: requestParams.endTime,
      check: requestParams.check, // 后端是否校验信息 默认为 true
    },
  })
}

export function apiGetPaperPackingSpecifications(requestParams) {
  return ajax.get({
    url: 'mark/subscribe/getPaperBagConfigList',
    params: {
      examId: requestParams.examId,
    },
    requestName: '查看试卷包装规格列表',
  })
}

export function apiSavePaperSubscriptionPackingSpecifications(requestParams) {
  return ajax.post({
    url: 'mark/subscribe/saveOrUpdatePaperBagConfig',
    params: {
      examId: requestParams.examId,
      bagName: requestParams.bagName,
      volume: requestParams.volume,
      extraNum: requestParams.extraNum,
      id: requestParams.id,
    },
    requestName: '保存试卷报订包装规则',
  })
}

// 删除试卷报订包装规则
export function apiDeletePaperSubscriptionPackingSpecifications(requestParams) {
  return ajax.post({
    url: 'mark/subscribe/deletePaperBagConfig',
    params: {
      id: requestParams.id,
      check: requestParams.check, // 后端是否校验信息 默认为 true
    },
  })
}

export function apiGetSchoolsPaperSubscriptionList(examId) {
  return ajax.get({
    url: 'mark/subscribe/getTotalSubscribeList',
    params: {
      examId: examId, // String*
    },
    // requestName: '查看学校报订情况（列表）',
  })
}

export function apiSchoolGetPaperSubscription(requestParams) {
  return ajax.get({
    url: 'mark/subscribe/getPaperSchoolSubscribeList',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
    },
    requestName: '学校查看试卷报订情况（列表）',
  })
}

export function apiSchoolSavePaperSubscription(requestParams) {
  return ajax.post({
    url: 'mark/subscribe/savePaperSchoolSubscribe',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      bagIdCountStr: requestParams.bagIdCountStr,
    },
    requestName: '学校保存试卷报订',
  })
}

export function apiReportSchoolPaperSubscription(requestParams) {
  return ajax.post({
    url: 'mark/subscribe/schoolUpSubscribe',
    params: {
      examId: requestParams.examId,
    },
    requestName: '学校上报试卷报订',
  })
}

export function apiWithdrawSchoolPaperSubscription(requestParams) {
  return ajax.post({
    url: 'mark/subscribe/schoolUndoSubscribe',
    params: {
      examId: requestParams.examId,
    },
    requestName: '学校撤回试卷报订',
  })
}
