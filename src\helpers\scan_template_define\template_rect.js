/**
 * 矩形区
 */

import { TemplateResolutionScale } from '@/const/answer_sheet'

export default class TemplateRect {
  constructor(params) {
    this.x = params && Number.isFinite(params.x) ? Math.round(params.x) : 0
    this.y = params && Number.isFinite(params.y) ? Math.round(params.y) : 0
    this.width = params && Number.isFinite(params.width) ? Math.round(params.width) : 0
    this.height = params && Number.isFinite(params.height) ? Math.round(params.height) : 0
  }

  static createFromAnswerSheetPageRect(left, top, width, height) {
    let rect = new TemplateRect()
    rect.x = Math.round(TemplateResolutionScale * left)
    rect.y = Math.round(TemplateResolutionScale * top)
    rect.width = Math.round(TemplateResolutionScale * width)
    rect.height = Math.round(TemplateResolutionScale * height)
    return rect
  }
}
