<template>
  <div class="block-chinese-writing">
    <ScorePanel v-if="showScorePanel" ref="scorePanel" :block="block"></ScorePanel>
    <com-box-content
      v-if="block.part === 0 || block.contentHeight"
      ref="contentBox"
      :content="block.content"
      :width="pageBodyWidth"
      :height="block.contentHeight"
      :border="contentBorderStyle"
      :padding-top="contentPaddingTop"
      @height-change="handleContentHeightChange"
      @content-change="changeContent"
    >
    </com-box-content>

    <div v-if="block.words > 0" class="grid-wrapper" :style="gridWrapperStyle">
      <div class="grid" style="position: relative" :style="gridStyle">
        <div
          v-for="column in gridColumns"
          :key="column.id"
          class="line-vertical"
          style="position: absolute; top: 0; z-index: 1; height: 100%; background-color: var(--base-border-color)"
          :style="column.style"
        ></div>
        <div
          v-for="row in gridRows"
          :key="row.id"
          class="gap"
          style="position: absolute; left: 0; z-index: 2; width: 100%; background-color: white"
          :style="row.style"
        >
          <div
            v-if="row.wordsCount"
            class="words-count"
            style="position: absolute; top: 0; height: 100%; text-align: center"
            :style="row.wordsCount.style"
          >
            {{ row.wordsCount.number }}
          </div>
        </div>
      </div>
    </div>

    <div class="page-block-toolbar no-print">
      <div class="btn btn-primary" title="设置" @click="showModalChangeType = true">
        <Icon class="btn-icon" type="ios-settings"></Icon>
      </div>
      <div v-if="enableEditQuestion" class="btn btn-error" title="删除" @click="handleDelete">
        <Icon class="btn-icon" type="md-close"></Icon>
      </div>
    </div>

    <ModalChangeType v-model="showModalChangeType" :question-group="block.questionGroup"></ModalChangeType>
  </div>
</template>
<script>
  import comBoxContent from './box_content.vue'
  import ModalChangeType from './modal_change_type'
  import ScorePanel from './score_panel'

  import { mapMutations } from 'vuex'
  import ScoreTypeEnum from '@/enum/answer_sheet/score_type'

  export default {
    components: {
      comBoxContent,
      ModalChangeType,
      ScorePanel,
    },
    props: {
      block: {
        type: Object,
        required: true,
      },
    },
    emits: ['height-change'],
    data() {
      return {
        // 显示修改题组类型弹窗
        showModalChangeType: false,
        // 小格子宽度
        cellWidth: 30,
        // 小格子高度
        cellHeight: 30,
        // 行间距
        gapHeight: 8,
        // 网格线粗
        lineWidth: 1,
        // 网格外边距
        gridMargin: 4,
        // 外框
        borderWidth: 1,
        // 内容框上边距
        contentBasePaddingTop: 14,
      }
    },
    computed: {
      enableEditQuestion() {
        return this.$store.getters['answerSheet/enableEditQuestion']
      },
      contentBorderStyle() {
        return {
          left: true,
          right: true,
          top: true,
          bottom: this.block.words == 0,
        }
      },
      contentPaddingTop() {
        let paddingTop = this.contentBasePaddingTop
        if (this.showScorePanelBar) {
          paddingTop += this.block.questionGroup.scoreType.height
        }
        return paddingTop
      },

      /**
       * 打分栏相关
       */
      showScorePanel() {
        let isPostScan = this.$store.getters['answerSheet/isPostScan']
        return isPostScan && this.block.part == 0
      },
      showScorePanelBar() {
        return this.showScorePanel && this.block.questionGroup.scoreType.name == ScoreTypeEnum.Bar.id
      },

      /**
       * 网格相关
       */
      pageBodyWidth() {
        let pageSize = this.$store.getters['answerSheet/pageSize'](this.block.pageIndex)
        return pageSize.width - pageSize.left - pageSize.right
      },
      // 列数
      gridColumnCount() {
        return this.getColumnCount(this.pageBodyWidth)
      },
      // 宽度
      gridWidth() {
        return this.getGridWidth(this.gridColumnCount)
      },
      // 行数
      gridRowCount() {
        return this.getRowCount(this.block.words, this.gridColumnCount)
      },
      // 高度
      gridHeight() {
        return this.getGridHeight(this.gridRowCount)
      },
      // 网格各列样式
      gridColumns() {
        let columns = []
        for (let i = 0; i < this.gridColumnCount - 1; i++) {
          let left = (i + 1) * this.cellWidth + i * this.lineWidth
          columns.push({
            id: `column_${i}`,
            style: {
              left: left + 'px',
              width: `${this.lineWidth / 2}px`,
            },
          })
        }
        return columns
      },
      // 网格各行样式
      gridRows() {
        let rows = []
        // 根据作文总字数显示不同的字数标记
        let wordStep = this.block.questionGroup.words >= 500 ? 100 : 50

        for (let i = 0; i < this.gridRowCount - 1; i++) {
          let top = (i + 1) * this.cellHeight + i * this.gapHeight
          let style = {
            top: top + 'px',
            height: `${this.gapHeight}px`,
            borderTop: `${this.lineWidth / 2}px solid var(--base-border-color)`,
            borderBottom: `${this.lineWidth / 2}px solid var(--base-border-color)`,
          }
          // 判断是否显示字数
          let wordsCount = null
          let rowWordStart = this.block.wordsStart + i * this.gridColumnCount
          let wordsCountNumber = (Math.floor(rowWordStart / wordStep) + 1) * wordStep
          let columnIndex = wordsCountNumber - rowWordStart - 1
          if (columnIndex < this.gridColumnCount) {
            wordsCount = {
              number: wordsCountNumber,
              style: {
                left: `${(this.cellWidth + this.lineWidth) * columnIndex}px`,
                width: `${this.cellWidth}px`,
                fontSize: '12px',
                lineHeight: `${this.gapHeight - this.lineWidth * 2}px`,
                transform: 'scale(0.5)',
                transformOrigin: 'center center',
              },
            }
          }
          rows.push({
            id: `row_${i}`,
            style,
            wordsCount,
          })
        }

        return rows
      },
      gridStyle() {
        return {
          width: this.gridWidth + 'px',
          height: this.gridHeight + 'px',
          border: `${this.lineWidth}px solid var(--base-border-color)`,
          margin: `${this.gridMargin}px auto`,
        }
      },
      gridWrapperHeight() {
        return this.getWrapperHeight(this.gridHeight)
      },
      gridWrapperStyle() {
        let style = {
          border: `${this.borderWidth}px solid var(--base-border-color)`,
          height: this.gridWrapperHeight + 'px',
        }
        if (this.block.contentHeight > 0) {
          style.borderTopColor = 'transparent'
        }
        return style
      },
    },
    watch: {
      gridHeight() {
        this.changeHeight({
          height: this.getBlockHeight(),
        })
      },
    },
    created() {
      this.changeInstance()
    },
    mounted() {
      this.changeHeight({
        height: this.getBlockHeight(),
      })
    },
    updated() {
      this.changeInstance()
    },
    methods: {
      ...mapMutations('answerSheet', ['changePageBlockInstance', 'changePageBlockContent', 'changePageBlockHeight']),

      /**
       * 实例、内容、高度
       */
      changeInstance() {
        this.changePageBlockInstance({
          blockId: this.block.id,
          instance: this,
        })
      },
      changeContent(content) {
        this.changePageBlockContent({
          blockId: this.block.id,
          content: content,
        })
      },
      changeHeight({ height, contentHeight }) {
        let heightChanged = height != this.block.height
        this.changePageBlockHeight({
          blockId: this.block.id,
          height,
          contentHeight,
        })
        if (heightChanged) {
          this.$emit('height-change', height)
        }
      },
      getBlockHeight(contentHeight) {
        if (contentHeight == null) {
          contentHeight = this.block.contentHeight
        }
        return contentHeight + (this.block.words > 0 ? this.gridWrapperHeight : 0)
      },
      handleContentHeightChange(contentHeight) {
        this.changeHeight({
          contentHeight,
          height: this.getBlockHeight(contentHeight),
        })
      },

      /**
       * 作文格
       */
      getColumnCount(width) {
        width = width - this.gridMargin * 2 - this.borderWidth * 2
        return Math.floor((width - this.lineWidth) / (this.cellWidth + this.lineWidth))
      },
      getGridWidth(cols) {
        return cols * this.cellWidth + (cols + 1) * this.lineWidth
      },
      getRowCount(words, columnCount) {
        return Math.ceil(words / columnCount)
      },
      getGridHeight(rows) {
        return rows * this.cellHeight + (rows - 1) * this.gapHeight + 2 * this.lineWidth
      },
      getWrapperHeight(gridHeight) {
        return gridHeight + this.borderWidth * 2 + this.gridMargin * 2
      },
      // 指定高度下最多可以多少行
      getMaxRows(height) {
        let gridHeight = height - this.borderWidth * 2 - this.gridMargin * 2
        return Math.floor((gridHeight - 2 * this.lineWidth + this.gapHeight) / (this.cellHeight + this.gapHeight))
      },
      // 指定字数的网格高度
      getGridHeightByWords(words) {
        let rows = this.getRowCount(words, this.gridColumnCount)
        return this.getGridHeight(rows)
      },

      /**
       * 操作
       */
      handleDelete() {
        let questionCodes = Array.from(new Set(this.block.questionGroup.questions.map(q => q.questionCode)))
        this.$Modal.confirm({
          title: '确认删除',
          content: `您将删除第${questionCodes.join('、')}题`,
          onOk: () => {
            this.$store.commit('answerSheet/deleteSubjectiveQuestionGroup', this.block.questionGroup.id)
            this.$store.commit('answerSheet/generatePages')
          },
        })
      },

      /**
       * 拆分
       */
      split(getNextPageAvailableHeightFunc) {
        let blocks
        // 先拆分内容
        let { words, content, contentHeight } = this.block.questionGroup
        try {
          blocks = this.$refs['contentBox'].split(getNextPageAvailableHeightFunc, this.block.questionGroup, {
            notFirstPaddingTop: this.contentBasePaddingTop,
          })
        } catch {
          blocks = [
            {
              content,
              contentHeight,
            },
          ]
        }
        blocks.forEach(block => {
          if (block) {
            block.height = block.contentHeight
            block.words = 0
            block.wordsStart = 0
          }
        })

        // 再拆分网格
        let leftWords = words
        while (leftWords > 0) {
          let lastBlock = blocks[blocks.length - 1]
          // 网格至少有两行
          if (lastBlock.contentHeight) {
            let pageAvailableHeight = getNextPageAvailableHeightFunc(blocks.length - 1)
            let blockRows = this.getMaxRows(pageAvailableHeight - lastBlock.contentHeight)
            if (blockRows >= 2) {
              lastBlock.words = this.gridColumnCount * blockRows
              lastBlock.height = lastBlock.contentHeight + this.getGridHeightByWords(lastBlock.words)
              leftWords -= lastBlock.words
            }
          }
          if (!(leftWords > 0)) {
            break
          }

          let blockRows = this.getMaxRows(getNextPageAvailableHeightFunc(blocks.length))
          let blockWords = Math.min(this.gridColumnCount * blockRows, leftWords)
          blocks.push({
            content: '',
            contentHeight: 0,
            height: this.getGridHeightByWords(blockWords),
            words: blockWords,
            wordsStart: words - leftWords,
          })
          leftWords -= blockWords
        }
        return blocks
      },

      /**
       * 扫描模板
       */
      getScoreAreas(areaLeft, areaTop) {
        if (this.showScorePanel) {
          return this.$refs['scorePanel'].getScoreAreas(areaLeft, areaTop)
        } else {
          return []
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-block-toolbar {
    .tbtn {
      display: block;
    }
  }
</style>
