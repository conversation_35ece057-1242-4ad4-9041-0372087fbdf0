import Enum from '@/enum/enum'

export default new Enum({
  Init: {
    id: 'I',
    name: '初始状态',
  },
  QrcodeNotFound: {
    id: 'QNF',
    name: '未识别到二维码',
  },
  BookNotFound: {
    id: 'BNF',
    name: '找不到对应教辅',
  },
  SchoolNotSubscribe: {
    id: 'SNU',
    name: '学校未使用该教辅',
  },
  SheetNotFound: {
    id: 'CNF',
    name: '找不到错题反馈卡',
  },
  NotCurrentTerm: {
    id: 'NCT',
    name: '非当前学期教辅',
  },
  ExamInit: {
    id: 'EI',
    name: '初始化项目',
  },
  ExamException: {
    id: 'EE',
    name: '项目创建失败',
  },
  ImageException: {
    id: 'IE',
    name: '图像异常',
  },
  ImageNormal: {
    id: 'IN',
    name: '图像正常',
  },
  MatchSuccess: {
    id: 'MS',
    name: '定位成功',
  },
  MatchFail: {
    id: 'MF',
    name: '定位异常',
  },
  SubjectException: {
    id: 'SE',
    name: '科目异常',
  },
  CornerException: {
    id: 'CE',
    name: '折角异常',
  },
  AdmissionNumException: {
    id: 'AE',
    name: '考号异常',
  },
  RecognizeSuccess: {
    id: 'RS',
    name: '识别成功',
  },
})
