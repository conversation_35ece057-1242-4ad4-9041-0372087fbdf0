import { apiDownloadAnswerCal } from '@/api/report'

import Store from '@/store/index'

// 作答统计
export function generateExcelReportAnswerAnalysisBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let fileName = `${subject.subjectName}/${Store.getters['report/examName']}_作答统计_`
  let organizationId
  let schoolId

  if (category && category.categoryName) {
    fileName = category.categoryName + '/' + fileName
  }
  if (isMultipleSchoolLevel) {
    if (isNextLevel) {
      if (school) {
        fileName = `下级机构或学校报表/(学校)${school.schoolName}/${fileName}${school.schoolName}`
        schoolId = school.schoolId
      } else {
        fileName = `下级机构或学校报表/(机构)${institution.name}/${fileName}${institution.name}`
        organizationId = institution.id
      }
    } else {
      if (school) {
        fileName = `学校报表/${school.schoolName}/${fileName}${school.schoolName}`
        schoolId = school.schoolId
      } else {
        fileName += '联考'
        organizationId = Store.getters['report/examCreatedInstitutionId']
      }
    }
  } else {
    fileName += `${Store.getters['report/currentSchoolName']}`
    schoolId = Store.getters['report/currentSchoolId']
  }
  fileName += `_${subject.subjectName}.xlsx`

  // 用户保存考试科目主观题分数分段配置
  const CurrentUserId = Store.getters['user/info'].userId
  const LocalCustomSubjectiveScoreIntervalSettings =
    (window.localStorage.getItem('report_answer') && JSON.parse(window.localStorage.getItem('report_answer'))) || []
  const TargetUserSetting =
    LocalCustomSubjectiveScoreIntervalSettings.find(item => item.userId === CurrentUserId) || null
  const TargetExamSetting =
    (TargetUserSetting &&
      TargetUserSetting.exams &&
      TargetUserSetting.exams.find(item => item.examId === Store.getters['report/examId'])) ||
    null
  const TargetTemplateSetting =
    (TargetExamSetting &&
      TargetExamSetting.templates &&
      TargetExamSetting.templates.find(item => item.templateId === Store.getters['report/templateId'])) ||
    null
  const TargetSubjectSetting = (
    (TargetTemplateSetting &&
      TargetTemplateSetting.settings &&
      TargetTemplateSetting.settings.find(item => item.examSubjectId === subject.examSubjectId)) || {
      ranges: [],
    }
  ).ranges

  return apiDownloadAnswerCal({
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    examSubjectId: subject.examSubjectId,
    organizationId: organizationId,
    schoolId: schoolId,
    customQuestionIntervals: TargetSubjectSetting,
  })
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}
