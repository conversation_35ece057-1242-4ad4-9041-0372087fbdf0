export {
  // 交、并、差、异或
  intersection,
  intersectionBy,
  intersectionWith,
  union,
  unionBy,
  unionWith,
  difference,
  differenceBy,
  differenceWith,
  xor,
  xorBy,
  xorWith,
  // 去重
  uniq,
  uniqBy,
  uniqWith,
  sortedUniq,
  sortedUniqBy,
  // 查找
  findLast,
  findLastIndex,
  sortedIndex,
  sortedIndexBy,
  sortedIndexOf,
  sortedLastIndex,
  sortedLastIndexBy,
  sortedLastIndexOf,
  maxBy as findMaxBy,
  minBy as findMinBy,
  // 排序
  orderBy,
  shuffle,
  // 拆分
  chunk,
} from 'lodash'

import { uniq, uniqBy } from 'lodash'

/**
 * 分组. 与lodash"groupBy"函数不同，返回的是每项均为{key,group}的数组，可直接链式调用数组方法
 * @param {Array} arr 待分组的数组
 * @param {Function | String} groupByFunc 分组依据函数，必须返回 Number, String 或 Boolean 值；或对象字段名
 */
export function groupArray(arr, groupByFunc) {
  let arrWithKey = arr.map(x => ({
    key: typeof groupByFunc === 'string' ? x[groupByFunc] : groupByFunc(x),
    value: x,
  }))

  let result = []
  arrWithKey.forEach(item => {
    let existing = result.find(x => x.key === item.key)
    if (existing) {
      existing.group.push(item.value)
    } else {
      result.push({
        key: item.key,
        group: [item.value],
      })
    }
  })

  return result
}

/**
 * 相邻分组
 * @param {Array} arr 待分组的数组
 * @param {Function | String} groupByFunc 分组依据函数，必须返回 Number, String 或 Boolean 值；或对象字段名
 */
export function groupArrayAdjacent(arr, groupByFunc) {
  let arrWithKey = arr.map(x => ({
    key: typeof groupByFunc === 'string' ? x[groupByFunc] : groupByFunc(x),
    value: x,
  }))

  let result = []
  let lastGroup = {}

  arrWithKey.forEach((item, idx) => {
    if (idx === 0 || item.key !== lastGroup.key) {
      lastGroup = {
        key: item.key,
        group: [item.value],
      }
      result.push(lastGroup)
    } else {
      lastGroup.group.push(item.value)
    }
  })

  return result
}

/**
 * 取消分组flatMap
 * @param {Array} arr 待拆分的已分组的数组
 * @param {Function} unGroupByFunc 拆分每组时调用的函数，返回数组；或数组字段名
 */
export function unGroupArray(arr, unGroupByFunc) {
  return arr.reduce((a, c) => a.concat(typeof unGroupByFunc === 'string' ? c[unGroupByFunc] : unGroupByFunc(c)), [])
}

/**
 * 找数组最大项索引
 * @param {Array} arr
 * @param {Function | String} iteratee
 * @returns
 */
export function findMaxIndexBy(arr, iteratee = x => x) {
  let currentMax = -Number.MAX_VALUE
  let currentMaxIndex = -1
  ;(arr || []).forEach((item, idx) => {
    let value = typeof iteratee === 'string' ? item[iteratee] : iteratee(item)
    if (value > currentMax) {
      currentMax = value
      currentMaxIndex = idx
    }
  })
  return currentMaxIndex
}

/**
 * 找数组最小项索引
 * @param {Array} arr
 * @param {Function | String} iteratee
 * @returns
 */
export function findMinIndexBy(arr, iteratee = x => x) {
  let currentMin = Number.MAX_VALUE
  let currentMinIndex = -1
  ;(arr || []).forEach((item, idx) => {
    let value = typeof iteratee === 'string' ? item[iteratee] : iteratee(item)
    if (value < currentMin) {
      currentMin = value
      currentMinIndex = idx
    }
  })
  return currentMinIndex
}

/**
 * 数组中是否包含重复项
 * @param {Array} arr
 * @returns Boolean
 */
export function isUniq(arr) {
  return uniq(arr).length === arr.length
}

/**
 * 数组中是否包含重复项
 * @param {Array} arr
 * @param {Function | String} iteratee
 * @returns Boolean
 */
export function isUniqBy(arr, iteratee = x => x) {
  return uniqBy(arr, iteratee).length === arr.length
}

/**
 * 数组中的重复项
 * @param {Array} arr
 * @returns Array
 */
export function duplicate(arr) {
  return duplicateBy(arr)
}

/**
 * 数组中的重复项
 * @param {Array} arr
 * @param {Function | String} iteratee
 * @returns Array
 */
export function duplicateBy(arr, iteratee = x => x) {
  let duplicateElements = []
  let elementOccurrenceMap = new Map()
  arr.forEach(item => {
    let key = typeof iteratee === 'string' ? item[iteratee] : iteratee(item)
    if (elementOccurrenceMap.has(key)) {
      let value = elementOccurrenceMap.get(key)
      value.occurence++
      if (value.occurence == 2) {
        duplicateElements.push(value.element)
      }
    } else {
      elementOccurrenceMap.set(key, {
        element: item,
        occurence: 1,
      })
    }
  })
  return duplicateElements
}

/**
 * 相邻拆分
 * @param {Array} arr
 * @param {Function} comparator，比较函数，传入数组上一元素及当前元素，返回true则与上一个元素同组
 * @returns Array
 */
export function chunkAdjacent(arr, comparator) {
  let chunks = []
  let chunk = []
  arr.forEach((item, idx) => {
    let beginNewChunk = idx === 0 || !comparator(arr[idx - 1], item)
    if (beginNewChunk) {
      chunk = [item]
      chunks.push(chunk)
    } else {
      chunk.push(item)
    }
  })
  return chunks
}
