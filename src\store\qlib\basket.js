import {
  apiGetQuestionBasket,
  apiChangeQuestionBasket,
  apiEditBasketQuestion,
  apiAddBasketQuestions,
  apiClearQuestionBasket,
  apiSaveQuestionBasket,
} from '@/api/qlib'
import { Paper } from '@/helpers/qlib/paper'
import { PaperInfo } from '@/helpers/qlib/paper_info'
import { PaperStructure } from '@/helpers/qlib/paper_structure'
import { PaperStyle } from '@/helpers/qlib/paper_style'
import { Question } from '@/helpers/qlib/question'

// 试题篮最大容量
const MAXBASKETSIZE = 100

export default {
  state: {
    basket: new Paper(),
  },

  getters: {
    basketStageSubject(state) {
      let first = state.basket.questionList[0]
      if (first) {
        return {
          stageId: first.stage.id,
          stageName: first.stage.name,
          subjectId: first.subject.id,
          subjectName: first.subject.name,
          fullName: `${first.stage.name}${first.subject.name}`,
        }
      } else {
        return {
          stageId: undefined,
          stageName: '',
          subjectId: undefined,
          subjectName: '',
          fullName: '',
        }
      }
    },
    basketByTopic(state) {
      return state.basket.paperStructure.topics.map(t => ({
        code: t.code,
        name: t.name,
        questionCount: t.questionCount,
        score: t.score,
      }))
    },
    basketQuestionTotalCount(state) {
      return state.basket.paperStructure.topics.reduce((a, c) => a + c.questionCount, 0)
    },
    isQuestionLibrarySystemUser(state, getters, rootState, rootGetters) {
      return rootGetters['user/isQuestionLibrarySystemUser']
    },
  },

  mutations: {
    update(state, paper) {
      state.basket = paper
    },
    add(state, { questions, keepOrder }) {
      state.basket.add(questions, keepOrder)
    },
    remove(state, qids) {
      state.basket.remove(qids)

      // 试题篮清空后附带清除试卷信息
      if (state.basket.questionList.length === 0) {
        state.basket.paperInfo = new PaperInfo()
        state.basket.paperStyle = new PaperStyle()
        state.basket.paperStructure = new PaperStructure()
        state.basket.innerUpdate()
      }
    },
    replace(state, { oldQuestion, newQuestion }) {
      state.basket.replace(oldQuestion, newQuestion)
    },
    changeStyles(state, styles) {
      state.basket.changeStyles(styles)
    },
    changeQuestionOrderInTopic(state) {
      state.basket.changeQuestionOrderInTopic()
    },
    changeTopicOrder(state, { oldIndex, newIndex }) {
      state.basket.changeTopicOrder(oldIndex, newIndex)
    },
    changeTopicShowQuestionScoreState(state, topic) {
      state.basket.changeTopicShowQuestionScoreState(topic)
    },
    changeScores(state, changeList) {
      state.basket.changeScores(changeList)
    },
    expandQuestionBranches(state, question) {
      state.basket.expandQuestionBranches(question)
    },
    collapseQuestionBranches(state, question) {
      state.basket.collapseQuestionBranches(question)
    },
    changeTopicInfo(state, topic) {
      state.basket.changeTopicInfo(topic)
    },
    changeQuestionsTopic(state, questions) {
      state.basket.changeQuestionsTopic(questions)
    },
  },

  actions: {
    getBasket(context) {
      return apiGetQuestionBasket().then(data => {
        context.commit('update', data)
      })
    },
    changeBasket(context) {
      return apiChangeQuestionBasket(context.state.basket).catch(err => {
        context.dispatch('getBasket')
        throw err
      })
    },
    clearBasket(context) {
      return apiClearQuestionBasket().then(() => {
        return context.dispatch('getBasket')
      })
    },
    addBasket(context, payload) {
      let qList = [],
        keepOrder = false
      if (payload instanceof Question) {
        qList = [payload]
      } else if (payload instanceof Array) {
        qList = payload
      } else if (payload && payload.questions instanceof Array) {
        qList = payload.questions
        keepOrder = !!payload.keepOrder
      }

      if (qList.length === 0) {
        return Promise.resolve()
      }

      let basketStageSubject = context.getters.basketStageSubject
      if (basketStageSubject.stageId && basketStageSubject.subjectId) {
        if (
          qList.some(q => q.stage.id !== basketStageSubject.stageId || q.subject.id !== basketStageSubject.subjectId)
        ) {
          return Promise.reject({
            code: -1001,
            msg: `当前试题篮中为${basketStageSubject.stageName || ''}${basketStageSubject.subjectName || ''}题目`,
          })
        }
      } else {
        let firstStageId = qList[0].stage.id
        let firstSubjectId = qList[0].subject.id
        if (qList.some(q => q.stage.id !== firstStageId || q.subject.id !== firstSubjectId)) {
          return Promise.reject({
            code: -1001,
            msg: `不能加入不同学段科目的题目`,
          })
        }
      }

      if (context.state.basket.questionList.some(x => qList.find(q => q.id === x.id))) {
        return Promise.reject({
          code: -1002,
          msg: '试题篮中已存在要添加的题目',
        })
      }

      if (context.state.basket.questionList.length + qList.length > MAXBASKETSIZE) {
        return Promise.reject({
          code: -1003,
          msg: `试题篮最多容纳 ${MAXBASKETSIZE} 道题`,
        })
      }

      context.commit('add', {
        questions: qList,
        keepOrder,
      })
      return context.dispatch('changeBasket')
    },
    addBasketPaper(context, paper) {
      if (paper.questionList.length === 0) {
        return Promise.reject('无题目')
      }
      // 如果试题栏中有题目,则只加试卷题目
      if (context.state.basket.questionList.length > 0) {
        let toAddQuestionList = paper.questionList.filter(q =>
          context.state.basket.questionList.every(x => x.id !== q.id)
        )
        if (toAddQuestionList.length === 0) {
          return Promise.resolve()
        }
        toAddQuestionList.sort(
          (a, b) =>
            (a.code || (a.branches && a.branches[0].code) || 0) - (b.code || (b.branches && b.branches[0].code) || 0)
        )
        return context.dispatch('addBasket', {
          questions: toAddQuestionList,
          keepOrder: true,
        })
      }
      // 如果试题栏中没有题目,则加试卷题目和加试卷结构
      else {
        context.state.basket.questionList = paper.questionList.slice()
        let paperStructureJson = JSON.stringify(paper.paperStructure.export())
        context.state.basket.paperStructure = PaperStructure.import(paperStructureJson, paper.questionList)
        // 如果是编辑员，保留试卷名
        if (context.getters.isQuestionLibrarySystemUser) {
          context.state.basket.changeStyles({
            title: {
              content: paper.paperInfo.name,
            },
          })
        }
        context.state.basket.innerUpdate()
        return context.dispatch('changeBasket')
      }
    },
    removeBasket(context, qList) {
      if (!(qList instanceof Array)) {
        qList = [qList]
      }

      if (qList.length === 0) {
        return Promise.resolve()
      }

      let qIds = qList.map(q => q.id)
      context.commit('remove', qIds)
      if (context.state.basket.questionList.length > 0) {
        return context.dispatch('changeBasket')
      } else {
        return context.dispatch('clearBasket')
      }
    },
    replaceBasketQuestion(context, { oldQuestion, newQuestion }) {
      context.commit('replace', { oldQuestion, newQuestion })
      return context.dispatch('changeBasket')
    },
    editBasketQuestion(context, { oldQuestion, newQuestion }) {
      context.commit('replace', {
        oldQuestion,
        newQuestion,
      })
      return apiEditBasketQuestion({
        questionId: oldQuestion.id,
        question: newQuestion,
        paperStructure: context.state.basket.paperStructure,
      })
        .then(q => {
          context.state.basket.replaceById(q)
        })
        .catch(err => {
          context.dispatch('getBasket')
          throw err
        })
    },
    // 添加新题
    addBasketQuestions(context, newQuestions) {
      context.commit('add', { questions: newQuestions })
      return apiAddBasketQuestions({
        questions: newQuestions,
        paperStructure: context.state.basket.paperStructure,
      })
        .then(resQuestions => {
          resQuestions.forEach(q => context.state.basket.replaceById(q))
        })
        .catch(err => {
          context.dispatch('getBasket')
          throw err
        })
    },
    changeBasketQuestionOrderInTopic(context) {
      context.commit('changeQuestionOrderInTopic')
      return context.dispatch('changeBasket')
    },
    changeBasketTopicOrder(context, { oldIndex, newIndex }) {
      context.commit('changeTopicOrder', { oldIndex, newIndex })
      return context.dispatch('changeBasket')
    },
    changeBasketTopicShowQuestionScoreState(context, topic) {
      context.commit('changeTopicShowQuestionScoreState', topic)
      return context.dispatch('changeBasket')
    },
    changeBasketQuestionScores(context, changeList) {
      context.commit('changeScores', changeList)
      return context.dispatch('changeBasket')
    },
    expandBasketQuestionBranches(context, question) {
      context.commit('expandQuestionBranches', question)
      return context.dispatch('changeBasket')
    },
    collapseBasketQuestionBranches(context, question) {
      context.commit('collapseQuestionBranches', question)
      return context.dispatch('changeBasket')
    },
    changeBasketStyles(context, styles) {
      context.commit('changeStyles', styles)
      return context.dispatch('changeBasket')
    },
    changeBasketTopicInfo(context, topic) {
      context.commit('changeTopicInfo', topic)
      return context.dispatch('changeBasket')
    },
    changeBasketQuestionsTopic(context, questions) {
      context.commit('changeQuestionsTopic', questions)
      return context.dispatch('changeBasket')
    },
    saveQuestionBasket(context) {
      if (context.state.basket.questionList.length === 0) {
        return
      }
      let basketStageSubject = context.getters['basketStageSubject']
      return apiSaveQuestionBasket(basketStageSubject.stageId, basketStageSubject.subjectId).then(
        ({ paperId, warning }) => {
          context.state.basket.paperInfo.id = paperId
          return {
            paperId,
            warning,
          }
        }
      )
    },
    async splitBasketQuestion(context, question) {
      context.commit('remove', [question.id])
      let splitedQuestions = await Question.split(question)
      await context.dispatch('addBasketQuestions', splitedQuestions)
    },
  },
}
