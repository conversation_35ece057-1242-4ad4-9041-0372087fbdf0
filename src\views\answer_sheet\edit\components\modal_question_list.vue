<template>
  <Modal class="modal-question-list" :model-value="modelValue" :width="800" @on-visible-change="handleVisibleChange">
    <div class="full-score">
      <span>满分 {{ paperFullScore }} 分，客观题 {{ objectiveFullScore }} 分，主观题 {{ subjectiveFullScore }} 分</span>
      <!-- <span v-if="selectScore > 0">，选做题 {{ selectScore }} 分</span> -->
      <span v-if="additionalScore > 0">，附加题 {{ additionalScore }} 分</span>
    </div>
    <Tabs v-model="activeTabName" :animated="false">
      <TabPane label="客观题" name="objective">
        <Table
          :columns="tableObjectiveColumns"
          :data="objectives"
          border
          class="table-question"
          :span-method="handleTableObjectiveSpan"
        ></Table>
      </TabPane>
      <TabPane label="主观题" name="subjective">
        <Table
          :columns="tableSubjectiveColumns"
          :data="subjectives"
          border
          class="table-question"
          :span-method="handleTableSubjectiveSpan"
        ></Table>
      </TabPane>
    </Tabs>
    <template #footer>
      <div>
        <Button type="text" @click="handleModalCancel">取消</Button>
        <Button type="primary" @click="handleModalOK">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { InputNumber, Table } from 'view-ui-plus'

  import { mapGetters } from 'vuex'
  import { deepCopy } from '@/utils/object'
  import { showScrollbarAfterHideModal } from '@/utils/iview'
  import { roundScore, sumByDefaultZero } from '@/utils/math'
  import { groupArray } from '@/utils/array'

  import SourceTypeEnum from '@/enum/answer_sheet/source_type'
  import BranchTypeEnum from '@/enum/qlib/branch_type'

  export default {
    props: {
      modelValue: Boolean,
    },
    emits: ['update:modelValue'],
    data() {
      return {
        activeTabName: 'objective',
        objectives: [],
        subjectives: [],
      }
    },
    computed: {
      ...mapGetters('answerSheet', [
        'from',
        'enableEditQuestion',
        'objectiveQuestions',
        'subjectiveQuestions',
        'blocks',
        'isCoachBook',
      ]),
      selectGroups() {
        let selectBlocks = this.blocks.filter(block => block.selectGroupName)
        return groupArray(selectBlocks, block => block.selectGroupName).map(g => ({
          selectGroupName: g.group[0].selectGroupName,
          selectCount: g.group[0].selectCount,
          blocks: g.group,
        }))
      },
      questionCodeSelectGroupMap() {
        let map = new Map()
        this.selectGroups.forEach(g => {
          g.blocks.forEach(block => {
            map.set(block.questions[0].questionCode, g)
          })
        })
        return map
      },

      /**
       * 表格
       */
      canEdit() {
        return this.enableEditQuestion
      },
      cannotEditButIsCoachBook() {
        return !this.canEdit && this.isCoachBook
      },
      hasCustomObjectiveQuestionName() {
        return (
          this.from == SourceTypeEnum.Exam.id &&
          this.objectives.some(q => q.questionName && q.questionName != q.fullCode)
        )
      },
      hasCustomSubjectiveQuestionName() {
        return (
          this.from == SourceTypeEnum.Exam.id &&
          this.subjectives.some(
            q =>
              (q.questionName && q.questionName != String(q.questionCode)) ||
              (q.branchName && q.branchName != q.fullCode)
          )
        )
      },
      tableObjectiveColumns() {
        let columns = [
          {
            title: '大题',
            key: 'topicName',
          },
          {
            title: '题号',
            key: 'questionCode',
            render: (h, params) => {
              let text = String(params.row.questionCode)
              if (params.row.isAdditional) {
                text += '(附加题)'
              }
              return h('span', {}, text)
            },
          },
          {
            title: '题名',
            key: 'questionName',
          },
          {
            title: '题型',
            key: 'branchTypeId',
            render: (h, params) => {
              return h('span', {}, BranchTypeEnum.getNameById(params.row.branchTypeId))
            },
          },
          {
            title: '选项',
            key: 'optionCount',
          },
          {
            title: '分数',
            key: 'fullScore',
            render: (h, params) => {
              if (!this.canEdit) {
                return h('span', {}, params.row.fullScore)
              }
              return h(InputNumber, {
                modelValue: params.row.fullScore,
                min: 0,
                size: 'small',
                onOnChange: newFullScore => {
                  let q = this.objectives.find(x => x.questionId == params.row.questionId)
                  if (q) {
                    q.fullScore = newFullScore
                  }
                },
              })
            },
          },
        ]
        columns.forEach(col => {
          col.align = 'center'
        })
        if (!this.hasCustomObjectiveQuestionName) {
          columns = columns.filter(col => col.title != '题名')
        }
        return columns
      },
      tableSubjectiveColumns() {
        let scoreRender = (h, params) => {
          // 制作教辅答题卡可以改填空题各空分数
          if (params.row.blankScores.length > 0 && (this.canEdit || this.isCoachBook)) {
            let elBlanks = params.row.blankScores.map((score, idx) => {
              return h('div', {}, [
                h('span', {}, params.row.blankScores.length > 1 ? `空${idx + 1}: ` : ''),
                h(InputNumber, {
                  modelValue: score,
                  min: 0,
                  size: 'small',
                  onOnChange: newBlankScore => {
                    this.changeBlankScore(params.row, idx, newBlankScore)
                  },
                }),
              ])
            })
            elBlanks.unshift(h('div', {}, `小题共 ${params.row.fullScore} 分`))
            return h(
              'div',
              {
                style: {
                  lineHeight: '28px',
                  paddingTop: '10px',
                  paddingBottom: '10px',
                },
              },
              elBlanks
            )
          } else if (this.canEdit) {
            return h(InputNumber, {
              modelValue: params.row.fullScore,
              min: 0,
              size: 'small',
              onOnChange: newFullScore => {
                let q = this.subjectives.find(x => x.questionId == params.row.questionId)
                if (q) {
                  q.fullScore = newFullScore
                }
              },
            })
          } else {
            let text = String(params.row.fullScore)
            if (params.row.blankScores.length > 1) {
              text += ` [${params.row.blankScores.join('、')}]`
            }
            return h('span', {}, text)
          }
        }
        let columns = [
          {
            title: '大题',
            key: 'topicName',
          },
          {
            title: '题号',
            key: 'questionCode',
            render: (h, params) => {
              let text = `${params.row.questionCode}`
              let selectGroup = this.questionCodeSelectGroupMap.get(params.row.questionCode)
              if (selectGroup) {
                text += `(选做${selectGroup.selectGroupName}, ${selectGroup.blocks.length}选${selectGroup.selectCount})`
              }
              return h('span', {}, text)
            },
          },
          {
            title: '题名',
            key: 'questionName',
          },
          {
            title: '小题号',
            key: 'fullCode',
            render: (h, params) => {
              let text = params.row.fullCode
              if (params.row.isAdditional) {
                text += '(附加题)'
              }
              return h('span', {}, text)
            },
          },
          {
            title: '小题名',
            key: 'branchName',
          },
          {
            title: '分数',
            key: 'fullScore',
            render: scoreRender,
            width: 140,
          },
        ]
        columns.forEach(col => {
          col.align = 'center'
        })
        if (!this.hasCustomSubjectiveQuestionName) {
          columns = columns.filter(col => col.title != '题名' && col.title != '小题名')
        }
        return columns
      },

      /**
       * 修改的分数
       */
      changedObjectives() {
        let list = []
        this.objectives.forEach(obj => {
          let q = this.objectiveQuestions.find(x => x.questionId == obj.questionId)
          if (q.fullScore != obj.fullScore) {
            let changedQuestion = deepCopy(obj)
            changedQuestion.oldFullScore = q.fullScore
            list.push(changedQuestion)
          }
        })
        return list
      },
      changedSubjectives() {
        let list = []
        this.subjectives.forEach(subj => {
          let q = this.subjectiveQuestions.find(x => x.questionId == subj.questionId)
          if (q.fullScore != subj.fullScore || q.blankScores.some((s, idx) => s != subj.blankScores[idx])) {
            let changedQuestion = deepCopy(subj)
            changedQuestion.oldFullScore = q.fullScore
            changedQuestion.oldBlankScores = q.blankScores.slice()
            list.push(changedQuestion)
          }
        })
        return list
      },
      changedScoreTableRows() {
        if (this.changedObjectives.length == 0 && this.changedSubjectives.length == 0) {
          return []
        }
        let list = []
        this.changedObjectives.forEach(q => {
          list.push({
            fullCode: q.fullCode,
            oldScoreStr: String(q.oldFullScore),
            newScoreStr: String(q.fullScore),
          })
        })
        this.changedSubjectives.forEach(q => {
          list.push({
            fullCode: q.fullCode,
            oldScoreStr:
              String(q.oldFullScore) + (q.oldBlankScores.length > 0 ? ` [${q.oldBlankScores.join('、')}]` : ''),
            newScoreStr: String(q.fullScore) + (q.blankScores.length > 0 ? ` [${q.blankScores.join('、')}]` : ''),
          })
        })
        return list
      },

      /**
       * 总分
       */
      selectGroupTotalScore() {
        let score = 0
        this.selectGroups.forEach(g => {
          g.blocks.forEach(block => {
            block.questions.forEach(q => {
              score += q.fullScore
            })
          })
        })
        return score
      },
      selectScore() {
        let score = 0
        this.selectGroups.forEach(g => {
          let firstBlockScore = 0
          g.blocks[0].questions.forEach(q => {
            firstBlockScore += q.fullScore
          })
          score += firstBlockScore * g.selectCount
        })
        return score
      },
      objectiveFullScore() {
        return sumByDefaultZero(
          this.objectives.filter(q => !q.isAdditional),
          q => q.fullScore
        )
      },
      subjectiveFullScore() {
        return (
          sumByDefaultZero(
            this.subjectives.filter(q => !q.isAdditional),
            q => q.fullScore
          ) +
          this.selectScore -
          this.selectGroupTotalScore
        )
      },
      additionalScore() {
        return sumByDefaultZero(
          [...this.objectives, ...this.subjectives].filter(q => q.isAdditional),
          q => q.fullScore
        )
      },
      paperFullScore() {
        return this.objectiveFullScore + this.subjectiveFullScore
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.objectives = this.objectiveQuestions.map(q => {
            let newQuestion = deepCopy(q)
            newQuestion.fullCode = q.fullCode
            return newQuestion
          })
          this.subjectives = this.subjectiveQuestions.map(q => {
            let newQuestion = deepCopy(q)
            newQuestion.fullCode = q.fullCode
            return newQuestion
          })
        }
      },
    },
    methods: {
      /**
       * 表格合并单元格
       */
      handleTableObjectiveSpan({ rowIndex, columnIndex }) {
        if (columnIndex != 0) {
          return undefined
        }
        let rowTopicCode = this.objectives[rowIndex].topicCode
        let previousRowTopicCode = rowIndex >= 1 ? this.objectives[rowIndex - 1].topicCode : null
        if (previousRowTopicCode == rowTopicCode) {
          return [0, 0]
        }
        let rowspan = 1
        for (let i = rowIndex + 1; i < this.objectives.length; i++) {
          if (this.objectives[i].topicCode == rowTopicCode) {
            rowspan++
          } else {
            break
          }
        }
        return [rowspan, 1]
      },
      handleTableSubjectiveSpan({ rowIndex, columnIndex }) {
        if (columnIndex == 0) {
          let rowTopicCode = this.subjectives[rowIndex].topicCode
          let previousRowTopicCode = rowIndex >= 1 ? this.subjectives[rowIndex - 1].topicCode : null
          if (previousRowTopicCode == rowTopicCode) {
            return [0, 0]
          }
          let rowspan = 1
          for (let i = rowIndex + 1; i < this.subjectives.length; i++) {
            if (this.subjectives[i].topicCode == rowTopicCode) {
              rowspan++
            } else {
              break
            }
          }
          return [rowspan, 1]
        } else if (columnIndex == 1 || (this.hasCustomSubjectiveQuestionName && columnIndex == 2)) {
          let rowQuestionCode = this.subjectives[rowIndex].questionCode
          let previousRowQuestionCode = rowIndex >= 1 ? this.subjectives[rowIndex - 1].questionCode : null
          if (previousRowQuestionCode == rowQuestionCode) {
            return [0, 0]
          }
          let rowspan = 1
          for (let i = rowIndex + 1; i < this.subjectives.length; i++) {
            if (this.subjectives[i].questionCode == rowQuestionCode) {
              rowspan++
            } else {
              break
            }
          }
          return [rowspan, 1]
        } else {
          return undefined
        }
      },
      changeBlankScore(row, idx, newBlankScore) {
        let q = this.subjectives.find(x => x.questionId == row.questionId)
        if (!q) {
          return
        }
        q.blankScores.splice(idx, 1, newBlankScore)
        if (!this.cannotEditButIsCoachBook) {
          q.fullScore = sumByDefaultZero(q.blankScores, x => x)
        }
      },
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.handleModalCancel()
        }
      },
      handleModalCancel() {
        this.$emit('update:modelValue', false)
        showScrollbarAfterHideModal()
      },
      handleModalOK() {
        let checkMessage = this.check()
        if (checkMessage) {
          this.$Message.warning({
            content: checkMessage,
            duration: 5,
            closable: true,
          })
          return
        }
        if (this.changedScoreTableRows.length == 0) {
          if (this.canEdit) {
            this.$Message.info({
              content: '未作修改',
            })
          }
          this.handleModalCancel()
          return
        }
        this.$Modal.confirm({
          title: '修改分数',
          render: h =>
            h(Table, {
              size: 'small',
              columns: [
                {
                  title: '小题号',
                  key: 'fullCode',
                  align: 'center',
                },
                {
                  title: '原分数',
                  key: 'oldScoreStr',
                  align: 'center',
                },
                {
                  title: '新分数',
                  key: 'newScoreStr',
                  align: 'center',
                },
              ],
              data: this.changedScoreTableRows,
              border: true,
              style: {
                marginTop: '10px',
                marginBottom: '10px',
              },
            }),
          onOk: () => {
            this.$store.commit('answerSheet/changeQuestionScores', {
              objectives: this.changedObjectives,
              subjectives: this.changedSubjectives,
            })
            this.handleModalCancel()
          },
        })
      },
      check() {
        let invalidScoreFullCodes = []
        this.objectives.forEach(obj => {
          obj.fullScore = roundScore(obj.fullScore)
          if (!(obj.fullScore > 0)) {
            invalidScoreFullCodes.push(obj.fullCode)
          }
        })
        this.subjectives.forEach(subj => {
          if (subj.blankScores.length > 0) {
            subj.blankScores = subj.blankScores.map(s => roundScore(s))
            if (subj.blankScores.some(s => !(s > 0))) {
              invalidScoreFullCodes.push(subj.fullCode)
            }
            let sum = roundScore(sumByDefaultZero(subj.blankScores, x => x))
            if (this.cannotEditButIsCoachBook) {
              if (subj.fullScore != sum) {
                invalidScoreFullCodes.push(subj.fullCode)
              }
            } else {
              subj.fullScore = sum
            }
          } else {
            subj.fullScore = roundScore(subj.fullScore)
            if (!(subj.fullScore > 0)) {
              invalidScoreFullCodes.push(subj.fullCode)
            }
          }
        })
        if (invalidScoreFullCodes.length > 0) {
          return `以下小题分数错误：${invalidScoreFullCodes.join('、')}`
        }
        for (let select of this.selectGroups) {
          let scores = select.blocks.map(block => {
            let subjs = this.subjectives.filter(subj =>
              block.questions.some(q => q.questionCode == subj.questionCode && q.branchCode == subj.branchCode)
            )
            return sumByDefaultZero(subjs, subj => subj.fullScore)
          })
          if (scores.some(score => score != scores[0])) {
            return `选做题组【${select.selectGroupName}】下各题满分须相同`
          }
        }
        return ''
      },
    },
  }
</script>

<style lang="scss" scoped>
  :deep(.ivu-modal-body) {
    padding-right: 0;
    padding-left: 0;
  }

  :deep(.ivu-modal-close) {
    top: 10px;
    right: 10px;
  }

  .full-score {
    position: absolute;
    top: 16px;
    left: 188px;
    padding: 8px;
  }

  .table-question {
    margin-right: 16px;
    margin-left: 16px;
  }
</style>
