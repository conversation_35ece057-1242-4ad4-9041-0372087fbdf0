import ajax from '@/api/ajax'
import { Question } from '@/helpers/qlib/question'
import STORE from '@/store/index'

function transformSchoolPaper(p) {
  let stages = STORE.state.qlib.stages || []
  let stage = stages.find(x => x.id === Number(p.gradeLevel || p.stage)) || {}
  let subject = (stage.subjects || []).find(x => x.id === Number(p.subjectId || p.subject)) || {}
  let grade = (stage.grades || []).find(x => x.id === Number(p.gradeId)) || {}
  let term = (stage.terms || []).find(x => x.id === Number(p.term)) || {}
  let paperType = (stage.paperTypes || []).find(x => x.id === Number(p.paperTypeId || p.type)) || {}
  let region = STORE.getters['common/regionsById'](Number(p.regionId || p.region)) || []

  return {
    id: p.id,
    name: p.paperName || p.name,
    stage: {
      id: stage.id || 0,
      name: stage.name || '',
    },
    subject: {
      id: subject.id || 0,
      name: subject.name || '',
    },
    grade: {
      id: grade.id || 0,
      name: grade.name || '',
    },
    term: {
      id: term.id || 0,
      name: term.name || '',
    },
    paperType,
    region,
    year: Number(p.year) || new Date().getFullYear(),
    paperBank: p.paperBank,
    schoolPaperId: p.schoolPaperId,
    schoolId: p.schoolId,
    schoolName: p.schoolName,
    createUserId: p.createUserId,
    createUserName: p.createUserName,
    createTime: new Date(p.createTime),
    viewTimes: p.browseCount,
    downloadTimes: p.downloadCount,
    source: p.source,
    isPublic: p.isPublic,
    isDelete: p.isDelete,
  }
}

// 学校教师分享到校本库
export function apiTeacherSharePaperToSchoolBank(paperId) {
  return ajax.put({
    url: 'ques/schoolBank/teacherSharePaper',
    params: {
      paperId,
    },
    requestName: '分享到校本库',
  })
}

// 学校教师分享到校本库记录
export function apiGetTeacherSharePaperToSchoolBankRecords(params) {
  return ajax
    .get({
      url: 'ques/schoolBank/teacherShares',
      params: {
        stage: params.stageId,
        subject: params.subjectId,
        grade: params.grade || '',
        term: params.term || '',
        year: params.year || '',
        region: (params.region && params.region[params.region.length - 1]) || '',
        type: params.paperType || '',
        page: params.currentPage,
        size: params.pageSize,
        keyword: params.keyword || undefined,
        bookType: params.coachbookType || undefined,
        flagCoachBookId: params.flagCoachBookId || undefined,
      },
      requestName: '获取校本卷库分享记录',
    })
    .then(data => {
      return {
        total: data.total || 0,
        papers: (data.records || []).map(transformSchoolPaper),
      }
    })
}

// 学校教师撤回分享到校本库
export function apiTeacherWithdrawSharePaperToSchoolBank(paperId) {
  return ajax.put({
    url: 'ques/schoolBank/teacherWithdrawPaper',
    params: {
      paperId,
    },
    requestName: '撤回分享',
  })
}

// 管理员分享到校本库
export function apiAdministratorSharePaperToSchoolBank(paperId, schoolId) {
  return ajax.put({
    url: 'ques/schoolBank/administratorSharePaper',
    params: {
      paperId,
      schoolId,
    },
    requestName: '分享到校本库',
  })
}

// 管理员分享到校本库记录
export function apiGetAdministratorSharePaperToSchoolBankRecords(params) {
  return ajax
    .get({
      url: 'ques/schoolBank/administratorShares',
      params: {
        stage: params.stageId,
        subject: params.subjectId,
        grade: params.grade || '',
        term: params.term || '',
        year: params.year || '',
        region: (params.region && params.region[params.region.length - 1]) || '',
        type: params.paperType || '',
        page: params.currentPage,
        size: params.pageSize,
        keyword: params.keyword || undefined,
        bookType: params.coachbookType || undefined,
        flagCoachBookId: params.flagCoachBookId || undefined,
      },
      requestName: '获取校本卷库分享记录',
    })
    .then(data => {
      return {
        total: data.total || 0,
        papers: (data.records || []).map(transformSchoolPaper),
      }
    })
}

// 管理员撤回分享到校本库
export function apiAdministratorWithdrawSharePaperToSchoolBank(paperId, schoolId) {
  return ajax.put({
    url: 'ques/schoolBank/administratorWithdrawPaper',
    params: {
      paperId,
      schoolId,
    },
    requestName: '撤回分享',
  })
}

// 删除校本库试卷
export function apiDeleteSchoolBankPapers(paperIds) {
  return ajax.delete({
    url: 'ques/schoolBank/deletePaperBatch',
    data: paperIds,
    requestName: '删除校本试卷',
  })
}

// 恢复校本库试卷
export function apiRecoverSchoolBankPapers(paperIds) {
  return ajax.put({
    url: 'ques/schoolBank/recoverPaperBatch',
    data: paperIds,
    requestName: '恢复校本试卷',
  })
}

// 公开校本库试卷
export function apiMakePublicSchoolBankPapers(paperIds) {
  return ajax.put({
    url: 'ques/schoolBank/makePapersPublic',
    data: paperIds,
    requestName: '公开校本试卷',
  })
}

// 隐藏校本库试卷
export function apiMakeUnPublicSchoolBankPapers(paperIds) {
  return ajax.put({
    url: 'ques/schoolBank/makePapersUnPublic',
    data: paperIds,
    requestName: '隐藏校本试卷',
  })
}

// 校本库试卷
export function apiGetSchoolBankPapers(params) {
  return ajax
    .get({
      url: 'ques/schoolBank/papers',
      params: {
        stage: params.stageId,
        subject: params.subjectId,
        grade: params.grade || '',
        term: params.term || '',
        year: params.year || '',
        region: (params.region && params.region[params.region.length - 1]) || '',
        type: params.paperType || '',
        page: params.currentPage,
        size: params.pageSize,
      },
      requestName: '获取校本试卷',
    })
    .then(data => {
      return {
        total: data.total || 0,
        papers: (data.records || []).map(transformSchoolPaper),
      }
    })
}

// 校本库章节查题
export function apiGetSchoolBankQuestionsByChapter(params) {
  return ajax
    .get({
      url: 'ques/schoolBank/quesByChapter',
      params: {
        stage: params.stage,
        subject: params.subject,
        bookId: params.bookId || '',
        chapter: params.chapter || '',
        ques_type: params.questionType || '',
        source: params.paperType || '',
        difficulty: params.difficulty || '',
        year: params.year || '',
        region: (params.region && params.region[params.region.length - 1]) || '',
        sort_by: params.sortBy || '',
        size: params.pageSize,
        page: params.currentPage,
      },
      requestName: '加载试题',
    })
    .then(data => {
      return {
        total: data.total || 0,
        questionList: (data.records || []).map(item => {
          return Question.import(item)
        }),
      }
    })
}

// 校本库知识点查题
export function apiGetSchoolBankQuestionsByKnowledge(params) {
  return ajax
    .get({
      url: 'ques/schoolBank/quesByKnows',
      params: {
        stage: params.stage,
        subject: params.subject,
        knowledge: params.knowledge || '',
        ques_type: params.questionType || '',
        source: params.paperType || '',
        difficulty: params.difficulty || '',
        year: params.year || '',
        region: params.region[params.region.length - 1] || '',
        sort_by: params.sortBy || '',
        size: params.pageSize,
        page: params.currentPage,
      },
      requestName: '加载试题',
    })
    .then(data => {
      return {
        total: data.total || 0,
        questionList: (data.records || []).map(item => {
          return Question.import(item)
        }),
      }
    })
}
