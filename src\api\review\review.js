import ajax from '@/api/ajax'

export function apiGetReviewTaskList(params) {
  return ajax.get({
    url: 'review/review/listTask',
    params: {
      page: params.page,
      size: params.size,
      minCreateTime: params.minCreateTime || undefined,
      keyword: params.keyword || undefined,
    },
    requestName: '查询评审任务列表',
  })
}

export function apiGetReviewTask(params) {
  return ajax.get({
    url: 'review/review/getTask',
    params: {
      categoryId: params.categoryId,
    },
    requestName: '查询评审任务',
  })
}

export function apiGetScoreSettings(params) {
  return ajax.get({
    url: 'review/review/getScoreConfig',
    params: {
      categoryId: params.categoryId,
    },
    requestName: '查询评分设置',
  })
}

export function apiGetJudgeReview(params) {
  return ajax.get({
    url: 'review/review/judgeReview',
    params: {
      categoryId: params.categoryId,
    },
  })
}

export function apiSubmitScore(data) {
  return ajax.post({
    url: 'review/review/score',
    data,
    requestName: '提交评分',
  })
}

export function apiReSubmitScore(data) {
  return ajax.post({
    url: 'review/review/reScore',
    data,
    requestName: '提交回评评分',
  })
}

export function apiGetJudgeHistory(params) {
  return ajax.get({
    url: 'review/review/listReviewed',
    params: {
      minScore: params.minScore || undefined,
      maxScore: params.maxScore || undefined,
      beginTime: params.beginTime || undefined,
      endTime: params.endTime || undefined,
      categoryId: params.categoryId,
      page: params.page || 1,
      size: params.size || 15,
    },
    requestName: '获取评审历史',
  })
}

export function apiGetOneReviewed(params) {
  return ajax.get({
    url: 'review/review/getReviewed',
    params: {
      categoryId: params.categoryId,
      judgeAssignOrder: params.judgeAssignOrder,
    },
    requestName: '获取已评作品数据',
  })
}

export function apiGetActivityProgress(params) {
  return ajax.get({
    url: 'review/review/activityProgress',
    params: {
      activityId: params.activityId,
    },
    requestName: '查询评审活动整体进度',
  })
}

export function apiGetCategoryProgress(params) {
  return ajax.get({
    url: 'review/review/categoryProgress',
    params: {
      categoryId: params.categoryId,
    },
    requestName: '查询评审活动类别进度',
  })
}

export function apiGetJudgeProgress(params) {
  return ajax.get({
    url: 'review/review/judgesProgress',
    params: {
      activityId: params.activityId,
      categoryId: params.categoryId || undefined,
    },
    requestName: '查询评审员进度',
  })
}

export function apiResetJudgeReview(params) {
  return ajax.put({
    url: 'review/review/resetJudgeReview',
    params: {
      categoryId: params.categoryId,
      judgeId: params.judgeId,
    },
    requestName: '重置评审员评审记录',
  })
}

export function apiReleaseJudgeReview(params) {
  return ajax.put({
    url: 'review/review/releaseJudgeReview',
    params: {
      activityId: params.activityId,
      categoryId: params.categoryId,
      judgeId: params.judgeId,
    },
    requestName: '回收评审员评审',
  })
}

export function apiGetReviewedHistory(params) {
  return ajax.get({
    url: 'review/review/listReviewed4Admin',
    params: {
      activityId: params.activityId,
      categoryId: params.categoryId || undefined,
      judgeId: params.judgeId || undefined,
      minScore: params.minScore || undefined,
      maxScore: params.maxScore || undefined,
      beginTime: params.beginTime || undefined,
      endTime: params.endTime || undefined,
      page: params.page || 1,
      size: params.size || 15,
    },
    requestName: '管理员查询评审记录',
  })
}
