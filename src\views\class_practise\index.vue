<template>
  <div class="container-emarking">
    <router-view></router-view>
  </div>
</template>

<script>
  import iView from '@/iview'
  import store from '@/store/index'

  export default {
    beforeRouteEnter(to, from, next) {
      iView.LoadingBar.start()

      Promise.all([
        store.dispatch('emarking/getBasicData'),
        store.dispatch('school/getSchoolStageGradeSubjectsParameters'),
      ])
        .then(() => {
          next()
        })
        .catch(() => {
          iView.LoadingBar.error()
        })
        .finally(() => {
          iView.LoadingBar.finish()
        })
    },
    watch: {
      $route() {
        this.navToHome()
      },
    },
    created() {
      this.navToHome()
    },
    methods: {
      navToHome() {
        if (this.$route.name === 'class-practise') {
          this.$router.replace({
            name: 'class-practise-home',
          })
        }
      },
    },
  }
</script>
