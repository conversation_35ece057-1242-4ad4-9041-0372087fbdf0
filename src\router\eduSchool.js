import Role from '@/enum/user/role'

export default {
  name: 'eduSchool',
  path: 'eduschool',
  meta: {
    title: '人员管理',
    menu: '管理',
    name: '人员管理',
    roles: user => {
      return (
        user.schoolType == 1 &&
        user.roles.some(
          r =>
            Role.SchoolAdministrator.id === r ||
            (Role.ResearcherLeader.id === r &&
              user.schoolId &&
              user.schoolId !== '00000000-0000-0000-0000-001000000010') // 李文龙：英德市教师发展中心不开放管理  2024年6月21日
        )
      )
    },
  },
  component: () => import('@/views/eduSchool/index.vue'),
  children: [
    {
      name: 'eduSchool-teacher',
      path: 'teacher',
      meta: {
        menu: '人员管理',
      },
      component: () => import('@/views/eduSchool/teacher/index.vue'),
      children: [
        {
          name: 'eduSchool-teacher-list',
          path: 'list',
          meta: {
            menu: '人员列表',
          },
          component: () => import('@/views/eduSchool/teacher/list/index.vue'),
        },
        {
          name: 'eduSchool-teacher-researcher',
          path: 'researcher',
          meta: {
            menu: '教研员',
          },
          component: () => import('@/views/eduSchool/teacher/researcher/index.vue'),
        },
        {
          name: 'eduSchool-teacher-researcherLeader',
          path: 'researcherLeader',
          meta: {
            menu: '教研领导',
          },
          component: () => import('@/views/eduSchool/teacher/researcher_leader/index.vue'),
        },
        {
          name: 'eduSchool-teacher-administrator',
          path: 'admin',
          meta: {
            menu: '管理员',
          },
          component: () => import('@/views/eduSchool/teacher/admin/index.vue'),
        },
      ],
    },
    {
      name: 'eduSchool-class',
      path: 'class',
      meta: {
        menu: '班级管理',
      },
      component: () => import('@/views/school/class/index.vue'),
    },
    {
      name: 'eduSchool-school',
      path: 'school',
      meta: {
        menu: '学校管理',
        roles: user => {
          return (
            user.roles.some(r => [Role.ResearcherLeader.id, Role.SchoolAdministrator.id].includes(r)) || user.isSystem
          )
        },
      },
      component: () => import('@/views/eduSchool/school/index.vue'),
    },
    {
      name: 'eduSchool-affiliated-manager',
      path: 'affiliated',
      meta: {
        menu: '机构学校管理员',
        roles: user => {
          return user.schoolType == 1 && user.isSystem
        },
      },
      component: () => import('@/views/eduSchool/affiliated_manager/index.vue'),
    },
    {
      name: 'eduSchool-config',
      path: 'config',
      meta: {
        menu: '配置管理',
      },
      component: () => import('@/views/school/config/index.vue'),
      children: [
        {
          name: 'school-config-grade',
          path: 'grade',
          meta: {
            menu: '年级学科',
          },
          component: () => import('@/views/school/config/grade/index.vue'),
        },
        {
          name: 'school-config-semester',
          path: 'semester',
          meta: {
            menu: '学年学期',
          },
          component: () => import('@/views/school/config/semester/index.vue'),
        },
        {
          name: 'school-config-scan-device-lisence',
          path: 'scanDeviceLisence',
          meta: {
            menu: '扫描仪授权',
          },
          component: () => import('@/views/school/config/scan_device_lisence/index.vue'),
        },
      ],
    },
    {
      name: 'system-log',
      path: 'log',
      meta: {
        menu: '日志管理',
        roles: user =>
          user.roles.some(r => r === Role.SystemAdministrator.id || r === Role.SchoolAdministrator.id) || user.isSystem,
      },
      component: () => import('@/views/school/log/index.vue'),
    },
  ],
}
