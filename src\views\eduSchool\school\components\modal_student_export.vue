<template>
  <Modal
    :model-value="modelValue"
    class="modal-export-student"
    title="导出学生"
    @on-visible-change="handleVisibilityChange"
  >
    <Form :label-width="60">
      <Form-item label="年级">
        <Select :model-value="exportGradeId" @on-change="changeExportGrade">
          <Option v-for="g in exportGradeClasses" :key="g.gradeId" :value="g.gradeId">{{ g.gradeName }}</Option>
        </Select>
      </Form-item>
      <Form-item label="班级">
        <Select v-model="exportClassId">
          <Option v-for="c in exportClasses" :key="c.classId" :value="c.classId">{{ c.className }}</Option>
        </Select>
      </Form-item>
    </Form>
    <template #footer>
      <div class="footer">
        <Button type="text" @click="handleCancel">取消</Button>
        <Button type="primary" :loading="exporting" @click="handleBtnExportStudentsClick">{{
          exporting ? '正在导出' : '导出'
        }}</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { apiExportStudentsByGradeAndClass } from '@/api/user/school'

  import { downloadBlob } from '@/utils/download'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      school: {
        type: Object,
        default: () => {},
      },
      gradeList: {
        type: Array,
        default: () => [],
      },
    },
    emits: ['update:modelValue'],
    data() {
      return {
        exportGradeId: 0,
        exportClassId: 0,
        exporting: false,
      }
    },
    computed: {
      exportGradeClasses() {
        let gradeClasses = [
          {
            gradeId: 0,
            gradeName: '全部年级',
            classes: [
              {
                classId: 0,
                className: '全部班级',
              },
            ],
          },
        ]

        this.gradeList.forEach(g => {
          let classes = g.classes.map(c => ({
            classId: c.classId,
            className: c.className,
          }))
          classes.unshift({
            classId: 0,
            className: '全部班级',
          })
          gradeClasses.push({
            gradeId: g.gradeId,
            gradeName: g.gradeName,
            classes,
          })
        })

        return gradeClasses
      },
      exportClasses() {
        let grade = this.exportGradeClasses.find(g => g.gradeId === this.exportGradeId)
        return (grade && grade.classes) || []
      },
      exportGradeClassName() {
        let grade = this.exportGradeClasses.find(g => g.gradeId === this.exportGradeId)
        if (grade.gradeId === 0) {
          return grade.gradeName
        } else {
          if (this.exportClassId === 0) {
            return grade.gradeName
          } else {
            let cls = this.exportClasses.find(c => c.classId === this.exportClassId)
            return cls.className
          }
        }
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.exportGradeId = 0
          this.exportClassId = 0
          this.exporting = false
        }
      },
    },
    methods: {
      handleCancel() {
        this.$emit('update:modelValue', false)
      },
      handleVisibilityChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
      changeExportGrade(gradeId) {
        let grade = this.exportGradeClasses.find(g => g.gradeId === gradeId)
        if (!grade) {
          return
        }

        this.exportGradeId = gradeId
        this.exportClassId = 0
      },
      handleBtnExportStudentsClick() {
        this.exporting = true
        apiExportStudentsByGradeAndClass({
          schoolId: this.school.value,
          gradeId: this.exportGradeId || null,
          classId: this.exportClassId || null,
        })
          .then(blob => {
            downloadBlob(blob, `${this.school.title}${this.exportGradeClassName}学生名单.xlsx`)
          })
          .then(() => {
            this.showModalExportStudent = false
          })
          .finally(() => {
            this.exporting = false
          })
      },
    },
  }
</script>
