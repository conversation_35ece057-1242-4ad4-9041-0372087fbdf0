<template>
  <Card class="filter-section">
    <div class="filter-section-header">
      <div class="filter-form">
        <div class="filter-item">
          <div class="filter-item-label">年级</div>
          <div class="filter-item-content">
            <Select :model-value="selectedGradeId" placeholder="请选择年级" @on-change="handleGradeChange">
              <Option
                v-for="grade in gradeClassSubjectList"
                :key="grade.gradeId"
                :value="grade.gradeId"
                :label="grade.gradeName"
              />
            </Select>
          </div>
        </div>
        <div class="filter-item">
          <div class="filter-item-label">班级</div>
          <div class="filter-item-content">
            <Select :model-value="selectedClassId" placeholder="请选择班级" @on-change="handleClassChange">
              <Option
                v-for="classItem in classList"
                :key="classItem.classId"
                :value="classItem.classId"
                :label="classItem.className"
              />
            </Select>
          </div>
        </div>
        <div class="filter-item">
          <div class="filter-item-label">科目</div>
          <div class="filter-item-content">
            <Select :model-value="selectedSubjectId" placeholder="请选择科目" @on-change="handleSubjectChange">
              <Option
                v-for="subject in subjectList"
                :key="subject.subjectId"
                :value="subject.subjectId"
                :label="subject.subjectName"
              />
            </Select>
          </div>
        </div>
      </div>
    </div>
    <!-- 考试选择区域 -->
    <div class="filter-section-exam">
      <div class="section-exam-header">
        <span class="title">选择考试</span>
      </div>

      <div class="section-exam-filter">
        <SearchInput
          v-model="examKeyword"
          class="search"
          placeholder="搜索考试名称"
          clearable
          prefix="ios-search"
          @on-change="loadExams"
        ></SearchInput>

        <div class="filter-actions">
          <DatePicker
            v-model="examDateRange"
            type="daterange"
            placeholder="选择考试日期范围"
            style="width: 240px"
            split-panels
            clearable
            @on-change="loadExams"
          ></DatePicker>
        </div>
      </div>

      <div class="section-exam-list">
        <Spin v-if="isLoading" fix class="loading-overlay"></Spin>

        <div v-if="examList.length == 0" class="no-exam-tip">
          <Icon type="ios-alert-outline" size="20" /><span class="tip-text">未找到符合条件的考试</span>
        </div>

        <div v-else class="exam-table">
          <Table :data="pageExamList" :columns="examColumns" border></Table>
        </div>
        <div v-if="total > pageSize" class="exam-pagination">
          <Page v-model="currentPage" :total="total" show-total :page-size="pageSize">共 {{ total }} 次考试</Page>
        </div>
      </div>
    </div>
  </Card>
</template>

<script setup>
  import { ref, computed, onBeforeMount, onBeforeUnmount } from 'vue'
  import store from '@/store'

  import { Button, Message } from 'view-ui-plus'
  import SearchInput from '@/components/searchInput'

  import { apiGetClassList, apiGetListSituationExam } from '@/api/report/situation'

  import { formatDateTime, formatDate } from '@/utils/date'
  import { localStore } from '@/utils/storage'

  const emit = defineEmits(['analyze'])

  // 年级班级科目列表
  const gradeClassSubjectList = ref([])
  async function loadGradeClassSubjectList() {
    gradeClassSubjectList.value = await apiGetClassList()
    handleGradeChange()
  }

  // 选中年级
  const selectedGradeId = ref(0)
  const selectedGrade = computed(() => {
    return gradeClassSubjectList.value.find(g => g.gradeId == selectedGradeId.value) || null
  })
  // 切换年级
  function handleGradeChange(value) {
    if (value == null) {
      value = selectedGradeId.value
    }
    if (gradeClassSubjectList.value.every(g => g.gradeId != value)) {
      value = gradeClassSubjectList.value[0]?.gradeId || 0
    }
    selectedGradeId.value = value
    handleClassChange()
  }

  // 班级列表
  const classList = computed(() => {
    return selectedGrade.value?.classes?.filter(cls => Boolean(cls.classId)) || []
  })
  // 选中班级
  const selectedClassId = ref('')
  const selectedClass = computed(() => {
    return classList.value.find(cls => cls.classId == selectedClassId.value) || null
  })
  // 切换班级
  function handleClassChange(value) {
    if (value == null) {
      value = selectedClassId.value
    }
    if (classList.value.every(cls => cls.classId != value)) {
      value = classList.value[0]?.classId || ''
    }
    selectedClassId.value = value
    handleSubjectChange()
  }

  // 科目列表
  const subjectList = computed(() => {
    return selectedClass.value?.subjects?.filter(s => Boolean(s.subjectId)) || []
  })
  // 选中科目
  const selectedSubjectId = ref(0)
  const selectedSubject = computed(() => {
    return subjectList.value.find(s => s.subjectId == selectedSubjectId.value) || null
  })
  // 切换科目
  function handleSubjectChange(value) {
    if (value == null) {
      value = selectedSubjectId.value
    }
    if (subjectList.value.every(s => s.subjectId != value)) {
      value = subjectList.value[0]?.subjectId || 0
    }
    selectedSubjectId.value = value
    loadExams()
  }

  // 考试相关数据和筛选
  const examKeyword = ref('')
  const examDateRange = ref([])
  const examList = ref([])
  const total = computed(() => examList.value.length)
  const isLoading = ref(false)

  // 分页
  const currentPage = ref(1)
  const pageSize = ref(5)
  const pageExamList = computed(() =>
    examList.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
  )

  // 当前请求id，忽略过期请求
  let currentRequestId = 0
  // 加载考试数据
  async function loadExams() {
    if (!selectedGrade.value || !selectedClass.value || !selectedSubject.value) {
      examList.value = []
      return
    }
    isLoading.value = true
    let requestId = ++currentRequestId
    try {
      let res = await apiGetListSituationExam({
        gradeId: selectedGrade.value.gradeId,
        gradeName: selectedGrade.value.gradeName,
        classId: selectedClass.value.classId,
        subjectId: selectedSubject.value.subjectId,
        examName: (examKeyword.value || '').trim(),
        beginTime: examDateRange.value[0] ? formatDateTime(examDateRange.value[0]) : null,
        endTime: examDateRange.value[1] ? formatDateTime(examDateRange.value[1]) : null,
      })
      if (requestId == currentRequestId) {
        examList.value = res
        currentPage.value = 1
      }
    } catch (ex) {
      if (requestId == currentRequestId) {
        examList.value = []
      }
      throw ex
    } finally {
      if (requestId == currentRequestId) {
        isLoading.value = false
      }
    }
  }

  // 表格列定义
  const examColumns = [
    { title: '考试名称', key: 'examName', minWidth: 300 },
    { title: '考试年级', key: 'gradeName', align: 'center', width: 100 },
    {
      title: '学年学期',
      align: 'center',
      width: 180,
      render: (h, params) => {
        let { semesterId, term } = params.row
        let text = ''
        if (semesterId && term) {
          text = `${semesterId}-${semesterId + 1}学年第${term == 1 ? '一' : term == 2 ? '二' : term == 3 ? '三' : '四'}学期`
        } else {
          text = '--'
        }
        return h('span', {}, text)
      },
    },
    {
      title: '考试日期',
      key: 'beginTime',
      align: 'center',
      width: 120,
      render: (h, params) => {
        return h('span', {}, params.row.beginTime ? formatDate(new Date(params.row.beginTime)) : '')
      },
    },
    {
      title: '操作',
      width: 120,
      align: 'center',
      render: (h, params) => {
        return h(
          'div',
          {
            style: {
              display: 'flex',
              justifyContent: 'center',
              gap: '8px',
            },
          },
          [
            h(
              Button,
              {
                type: 'primary',
                size: 'small',
                onClick(e) {
                  e.stopPropagation()
                  analyze(params.row)
                },
              },
              () => '开始分析'
            ),
          ]
        )
      },
    },
  ]

  // 触发分析
  function analyze(exam) {
    if (isLoading.value) {
      return
    }
    if (!selectedGrade.value) {
      Message.warning('请先选择年级')
      return
    }
    if (!selectedClass.value) {
      Message.warning('请先选择班级')
      return
    }
    if (!selectedSubject.value) {
      Message.warning('请先选择科目')
      return
    }
    if (!exam) {
      Message.warning('请选择考试')
      return
    }
    emit('analyze', {
      grade: selectedGrade.value,
      class: selectedClass.value,
      subject: selectedSubject.value,
      exam: exam,
    })
  }

  // 创建时加载年级班级科目列表
  onBeforeMount(() => {
    loadFilterData()
    loadGradeClassSubjectList()
  })
  onBeforeUnmount(() => {
    saveFilterData()
  })

  const storageKey = 'ai_score_analysis_filter'
  function saveFilterData() {
    let userId = store.getters['user/info'].userId
    if (selectedGradeId.value && selectedClassId.value && selectedSubjectId.value) {
      localStore.setField(storageKey, userId, {
        gradeId: selectedGradeId.value,
        classId: selectedClassId.value,
        subjectId: selectedSubjectId.value,
      })
    } else {
      localStore.removeField(storageKey, userId)
    }
  }
  function loadFilterData() {
    let userId = store.getters['user/info'].userId
    let filterData = localStore.getField(storageKey, userId)
    if (filterData) {
      selectedGradeId.value = filterData.gradeId
      selectedClassId.value = filterData.classId
      selectedSubjectId.value = filterData.subjectId
    }
  }
</script>

<style lang="scss" scoped>
  .filter-form {
    @include flex(row, flex-start, flex-start);
    flex-wrap: wrap;
    gap: 24px;
    margin-top: 8px;
    margin-bottom: 16px;

    .filter-item {
      @include flex(row, flex-start, center);

      .filter-item-label {
        margin-right: 10px;
      }

      .filter-item-content {
        width: 160px;
      }
    }
  }

  .section-exam-header {
    margin-bottom: 12px;
  }

  .section-exam-filter {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    .search {
      flex: 1;
      min-width: 240px;
    }

    .filter-actions {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }

  .section-exam-list {
    position: relative;

    .loading-overlay {
      @include flex(row, center, center);
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 10;
      background: rgba(255, 255, 255, 0.7);
    }

    .no-exam-tip {
      @include flex(row, center, center);
      height: 100px;

      border-radius: 6px;
      color: $color-icon;
      background: #f9f9f9;

      .tip-text {
        margin-left: 10px;
      }
    }

    .exam-pagination {
      margin-top: 16px;
      text-align: right;
    }
  }
</style>
