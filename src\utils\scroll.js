import { animate } from '@/utils/animation'

export function scrollToHeight(to, duration = 0) {
  let from = window.pageYOffset
  animate(duration, 'linear', progress => {
    window.scrollTo(0, from + (to - from) * progress)
  })
}

export function scrollToTop(duration = 0) {
  scrollToHeight(0, duration)
}

export function scrollToBottom(duration = 0) {
  let top = document.documentElement.scrollHeight - document.documentElement.clientHeight
  if (top > 0) {
    scrollToHeight(top, duration)
  }
}

export function scrollInto(el, offset = 0, duration = 0) {
  let top = window.pageYOffset + el.getBoundingClientRect().top + offset
  let maxTop = Math.max(0, document.documentElement.scrollHeight - document.documentElement.clientHeight)
  if (top > 0) {
    scrollToHeight(Math.min(top, maxTop), duration)
  }
}
