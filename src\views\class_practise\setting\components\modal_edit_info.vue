<template>
  <Modal :model-value="modelValue" title="修改测练名称" @on-visible-change="handleVisibilityChange">
    <Form class="form" :label-width="80" label-position="left">
      <FormItem label="考试名称">
        <Input v-model="examName" type="text" maxlength="45"></Input>
      </FormItem>
    </Form>
    <template #footer>
      <div>
        <Button type="text" @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleOK">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { apiEditExamNameAndDate } from '@/api/emarking'

  export default {
    props: {
      modelValue: Boolean,
      exam: Object,
    },
    emits: ['update:modelValue', 'on-refresh'],
    data() {
      return {
        examName: '',
      }
    },
    computed: {},
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.examName = this.exam.examName
        }
      },
    },
    methods: {
      handleOK() {
        this.examName = this.examName.trim()
        if (!this.examName) {
          this.$Message.info({
            content: '请输入考试名称',
          })
          return
        }

        apiEditExamNameAndDate({
          examId: this.exam.examId,
          examName: this.examName,
        })
          .then(() => {
            this.$Message.success({
              content: '已修改',
            })
            this.handleCancel()
            this.$emit('on-refresh')
          })
          .finally(() => {})
      },
      handleCancel() {
        this.$emit('update:modelValue', false)
      },
      handleVisibilityChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
    },
  }
</script>
