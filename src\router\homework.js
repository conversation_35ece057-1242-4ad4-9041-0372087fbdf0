import Role from '@/enum/user/role'

export default {
  name: 'homework',
  path: 'homework',
  meta: {
    title: '在线作业',
    menu: '在线作业',
    name: '在线作业',
    roles: user => {
      return (
        user.schoolType == 0 &&
        user.roles.some(r =>
          [
            Role.SchoolAdministrator.id,
            Role.SchoolLeader.id,
            Role.GradeLeader.id,
            Role.SubjectLeader.id,
            Role.GradeSubjectLeader.id,
            Role.ClassLeader.id,
            Role.Teacher.id,
            Role.SystemAdministrator.id,
          ].includes(r)
        )
      )
    },
  },
  component: () => import('@/views/homework/homework_container.vue'),
  redirect: { name: 'homework_list' },
  children: [
    {
      name: 'homework_list',
      path: 'homework_list',
      meta: {
        menu: '作业列表',
      },
      component: () => import('@/views/homework/index.vue'),
    },
    {
      name: 'learning_task_create',
      path: 'learning_task_create',
      meta: {
        menu: '学习任务',
      },
      component: () => import('@/views/homework/learning_task_create.vue'),
    },
    {
      name: 'learning_task_assign',
      path: 'learning_task_assign',
      meta: {
        menu: '学习任务',
      },
      component: () => import('@/views/homework/learning_task_assign.vue'),
    },
    {
      name: 'group_list',
      path: 'group_list',
      meta: {
        menu: '分组管理',
      },
      component: () => import('@/views/homework/group_list.vue'),
    },
    {
      name: 'create_group',
      path: 'create_group',
      meta: {
        menu: '新建分组',
      },
      component: () => import('@/views/homework/create_group.vue'),
    },
    {
      name: 'learning_task_setting',
      path: 'learning_task_setting',
      meta: {
        menu: '练习设置',
      },
      component: () => import('@/views/homework/learning_task_setting.vue'),
    },
    {
      name: 'free_topic_create',
      path: 'free_topic_create',
      meta: {
        menu: '练习设置',
      },
      component: () => import('@/views/homework/free_topic_create.vue'),
    },
    {
      name: 'homework_mark',
      path: 'homework_mark',
      meta: {
        menu: '作业批改',
      },
      component: () => import('@/views/homework/homework_mark.vue'),
    },
    {
      name: 'free_topic_assign',
      path: 'free_topic_assign',
      meta: {
        menu: '自由出题',
      },
      component: () => import('@/views/homework/free_topic_assign.vue'),
    },
    {
      name: 'homework_analysis',
      path: 'homework_analysis',
      meta: {
        menu: '作业报告',
      },
      component: () => import('@/views/homework/homework_analysis.vue'),
    },
    {
      name: 'student_summary',
      path: 'student_summary',
      meta: {
        menu: '学生成绩',
      },
      component: () => import('@/views/homework/student_summary.vue'),
    },
    {
      name: 'answer_modify',
      path: 'answer_modify',
      meta: {
        menu: '修改答案',
      },
      component: () => import('@/views/homework/answer_modify.vue'),
    },
  ],
}
