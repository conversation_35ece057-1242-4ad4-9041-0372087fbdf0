<template>
  <div class="container-coach-book-ingestion">
    <PageHeader class="section-header" @back="back"></PageHeader>
    <component :is="currentComponent" class="section-content"></component>
  </div>
</template>

<script setup>
  import { computed, onBeforeMount, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useCoachBookIngestionStore } from '@/store/qlib/coach_book_ingestion'
  import iView from '@/iview'

  import PageHeader from './components/page_header.vue'
  import StepUpload from './steps/upload/index.vue'
  import StepStructure from './steps/structure/index.vue'
  import StepQuestion from './steps/question/index.vue'

  import { apiGetCoachBookInfo } from '@/api/qlib/coach_book'
  import { apiCoachBookIngestionGetInfo, apiCoachBookIngestionCreate } from '@/api/qlib/coach_book_ingestion'

  import { backOrDefault } from '@/utils/router'

  import IngestionStepEnum from '@/enum/qlib/ingestion/step'

  const route = useRoute()
  const router = useRouter()
  const ingestionStore = useCoachBookIngestionStore()

  const initialized = ref(false)

  /**
   * 当前步骤组件
   */
  const currentComponent = computed(() => {
    if (!initialized.value) {
      return null
    }
    if (ingestionStore.currentStep == IngestionStepEnum.Upload.id) {
      return StepUpload
    } else if (ingestionStore.currentStep == IngestionStepEnum.Structure.id) {
      return StepStructure
    } else if (ingestionStore.currentStep == IngestionStepEnum.Question.id) {
      return StepQuestion
    } else {
      return null
    }
  })

  /**
   * 获取教辅信息和录入信息
   */
  onBeforeMount(async () => {
    try {
      iView.LoadingBar.start()
      await init()
    } catch (err) {
      iView.LoadingBar.error()
      setTimeout(() => {
        back()
      }, 1000)
      throw err
    } finally {
      iView.LoadingBar.finish()
    }
  })

  async function init() {
    let coachBookId = route.params.coachBookId
    ingestionStore.reset()
    let [coachBookInfo, ingestionInfo] = await Promise.all([
      apiGetCoachBookInfo(coachBookId),
      apiCoachBookIngestionGetInfo(coachBookId),
    ])

    if (ingestionInfo == null) {
      ingestionInfo = await apiCoachBookIngestionCreate({
        coachBookId,
        fileType: 'pdf',
      })
    }

    ingestionStore.setCoachBookInfo(coachBookInfo)
    ingestionStore.setIngestionInfo(ingestionInfo)
    ingestionStore.initCurrentStep()
    initialized.value = true
  }

  function back() {
    backOrDefault(router, {
      name: 'qlib-management-supplementaryBook',
    })
  }
</script>

<style lang="scss" scoped>
  .container-coach-book-ingestion {
    @include flex(column, flex-start, stretch);
    height: 100vh;
    background-color: white;

    .section-header {
      flex-grow: 0;
      flex-shrink: 0;
    }

    .section-content {
      flex-grow: 1;
      flex-shrink: 1;
      overflow: hidden;
    }
  }
</style>
