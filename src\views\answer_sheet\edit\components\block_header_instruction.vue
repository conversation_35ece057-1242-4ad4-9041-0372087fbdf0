<template>
  <div class="instruction"><span :style="labelStyle">操作说明</span><img :style="imgStyle" :src="imgSrc" /></div>
</template>

<script>
  import { Instruction } from '@/helpers/answer_sheet/header'

  import ImgYueShanHai from '@/assets/images/answer_sheet/qrcode_coach_book_sheet_instruction_yueshanhai.png'
  import ImgIgrade from '@/assets/images/answer_sheet/qrcode_coach_book_sheet_instruction_igrade.png'
  import ImgIgradePrimary from '@/assets/images/answer_sheet/qrcode_coach_book_sheet_instruction_igrade_primary.png'

  export default {
    computed: {
      labelStyle() {
        return {
          width: Instruction.labelWidth + 'px',
          height: Instruction.height + 'px',
          display: 'inline-block',
          fontSize: '12px',
          paddingTop: '4px',
          lineHeight: '1.5',
        }
      },
      imgStyle() {
        return {
          width: Instruction.width - Instruction.labelWidth + 'px',
          height: Instruction.height + 'px',
          display: 'inline-block',
          verticalAlign: 'top',
        }
      },
      imgSrc() {
        let isCoachBookIgrade = this.$store.getters['answerSheet/isCoachBookIgrade']
        let isCoachBookYSH = this.$store.getters['answerSheet/isCoachBookYSH']
        if (isCoachBookYSH) {
          return ImgYueShanHai
        } else if (isCoachBookIgrade) {
          let stage = this.$store.getters['answerSheet/stage']
          if (stage.id == 1) {
            return ImgIgradePrimary
          } else {
            return ImgIgrade
          }
        } else {
          return ''
        }
      },
    },
  }
</script>
