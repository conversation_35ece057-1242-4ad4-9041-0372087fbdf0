import { apiGetKnowledge, apiGetExpandKnowledge } from '@/api/report'

import { generateExcelBlob } from '@/utils/excel_export'
import { getInstitutionName } from '../../tools/tools'

import { UUID_ZERO } from '@/const/string'

import Store from '@/store/index'

// 知识点分析
export function generateExcelReportKnowledgeAnalysisBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let fileName = `${subject.subjectName}/${Store.getters['report/examName']}_知识点分析_`
  let requestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    examSubjectId: (subject && subject.examSubjectId) || undefined,
    reportName: 'sch_knowAnalyse',
  }

  if (category && category.categoryName) {
    fileName = category.categoryName + '/' + fileName
  }
  if (isMultipleSchoolLevel) {
    if (isNextLevel) {
      fileName = `${Store.getters['report/examName']}_知识点分析_`
      if (school) {
        fileName = `下级机构或学校报表/(学校)${school.schoolName}/${fileName}_${school.schoolName}`
        requestParams.schoolId = school.schoolId
      } else {
        fileName += `下级机构或学校报表/(机构)${institution.name}/${fileName}_${institution.name}`
        requestParams.organizationId = institution.id
        requestParams.reportName = 'union_knowAnalyse'
      }
    } else {
      if (school) {
        fileName = `学校报表/${school.schoolName}/${fileName}_${school.schoolName}`
        requestParams.schoolId = school.schoolId
      } else {
        fileName += '联考'
        requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']
        requestParams.reportName = 'union_knowAnalyse'
      }
    }
  } else {
    fileName += `${Store.getters['report/currentSchoolName']}`
    requestParams.schoolId = Store.getters['report/currentSchoolId']
  }
  fileName += `_${subject.subjectName}.xlsx`

  return fetchSheets(requestParams)
    .then(sheets => generateExcelBlob(sheets))
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}

async function fetchSheets(requestParams) {
  const tableColumns = [
    {
      title: '知识点',
      key: 'knowledge',
      width: 'auto',
    },
    {
      title: requestParams.organizationId ? '小题' : '满分',
      key: requestParams.organizationId ? 'questions' : 'fullScore',
    },
    {
      title: requestParams.organizationId ? '满分' : '单位名称',
      key: requestParams.organizationId ? 'fullScore' : 'instituName',
      width: requestParams.organizationId ? undefined : 'auto',
    },
    {
      title: '统计人数',
      key: 'examedStudentCount',
    },
    {
      title: '最高分',
      key: 'maxScore',
    },
    {
      title: '最低分',
      key: 'minScore',
    },
    {
      title: '平均分',
      key: 'avgScore',
    },
    {
      title: '得分率',
      key: 'scoreRate',
    },
    {
      title: '标准差',
      key: 'standardDeviation',
    },
    {
      title: '难度',
      key: 'difficult',
    },
    {
      title: '差异系数',
      key: 'differentCoefficient',
    },
  ].map(x => {
    x.width = ['知识点', '小题', '单位名称'].includes(x.title) ? 'auto' : 12
    if (requestParams.organizationId) {
      if (x.title === '小题') {
        x.key = row => row.newBranchName
      }
    } else {
      if (['知识点', '满分'].includes(x.title)) {
        x.group = true
      }
    }

    const TempKey = x.key
    if (['满分', '最高分', '最低分', '平均分', '标准差', '难度', '差异系数', '得分率'].includes(x.title)) {
      x.key = row => {
        let value = Number(row[TempKey])
        return isNaN(value) ? 0 : value
      }
    }

    if (x.title === '得分率') {
      x.cellNumberFormat = value => (isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%')
    } else if (['平均分', '标准差', '难度', '差异系数'].includes(x.title)) {
      x.cellNumberFormat = value => (Number.isInteger(value) ? undefined : '0.##')
    }

    return x
  })

  let tableData
  if (requestParams.organizationId) {
    tableData = transKnowledgeResponse(await apiGetKnowledge(requestParams))
  } else {
    tableData = transExpandKnowledgeResponse(await apiGetExpandKnowledge(requestParams))
  }

  if (tableData && tableData.length) {
    return [
      {
        sheetName: '知识点分析',
        rows: tableData,
        columns: tableColumns,
      },
    ]
  } else {
    throw {
      code: 9998,
      // msg: '暂无数据',
      msg: '[ignore]',
    }
  }
}

function transKnowledgeResponse(response) {
  let result = []

  if (response && response.length) {
    result = response
      .sort((a, b) => a.sortCode - b.sortCode)
      .map(x => ({
        knowledge: x.knowledgeName || '-',
        subQuestionCode: x.codeStr,
        fullScore: x.totalFullScore,
        examedStudentCount: x.countNum,
        questionName: x.questionName,
        newBranchName: x.branchName || x.questionName || x.subQuestionCode || '-',
        branchName: x.branchName,
        maxScore: Number(x.max),
        minScore: Number(x.min),
        avgScore: Number(x.avg),
        scoreRate: Number((x.scoreRate && x.scoreRate.slice(0, -1)) || '0') / 100,
        standardDeviation: Number(x.sqrt),
        difficult: Number(x.diff),
        differentCoefficient: Number(x.cv),
        questions: x.newBranchName,
      }))
  }

  return result
}

function transExpandKnowledgeResponse(response) {
  let result = []

  if (response && response.length) {
    response
      .sort((a, b) => a.sortCode - b.sortCode)
      .forEach(x => {
        if (!Store.getters['report/isMultipleSchool']) {
          x.childVos = x.childVos.filter(cv => cv.schoolId !== UUID_ZERO)
        }
        x.childVos
          .sort((a, b) => b.sortCode - a.sortCode)
          .forEach(y => {
            result.push({
              knowledge: x.knowName || '-',
              instituId: y.classId,
              instituName: getInstitutionName(y.schoolId, y.classId, y.schoolName, y.className),
              fullScore: x.totalFullScore,
              examedStudentCount: y.countNum,
              maxScore: Number(x.max),
              minScore: Number(x.min),
              avgScore: Number(y.avg),
              scoreRate: Number((x.socreRate && x.scoreRate.slice(0, -1)) || '0') / 100,
              standardDeviation: Number(x.sqrt),
              difficult: Number(y.diff),
              differentCoefficient: Number(x.cv),
            })
          })
      })
  }

  return result
}
