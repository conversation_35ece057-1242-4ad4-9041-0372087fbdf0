import ajax from '@/api/ajax'

export function apiGetPersonalBatchRoomStatsPage({ examSubjectId, currentPage, pageSize }) {
  return ajax.get({
    url: '/scan/scanBatch/personalBatchRoomStatsPage',
    params: {
      examSubjectId,
      page: currentPage,
      size: pageSize,
    },
    requestName: '获取个人扫描批次考场统计',
  })
}

export function apiGetScanUserBatchRoomStatsPage({ examSubjectId, scanUserId, currentPage, pageSize }) {
  return ajax.get({
    url: '/scan/scanBatch/scanUserBatchRoomStatsPage',
    params: {
      examSubjectId,
      scanUserId,
      page: currentPage,
      size: pageSize,
    },
    requestName: '查看指定扫描员批次考场统计',
  })
}

export function apiGetScanStationBatchRoomStatsPage({ examSubjectId, scanStationId, currentPage, pageSize }) {
  return ajax.get({
    url: '/scan/scanBatch/scanStationBatchRoomStatsPage',
    params: {
      examSubjectId,
      scanStationId,
      page: currentPage,
      size: pageSize,
    },
    requestName: '查看指定扫描点批次考场统计',
  })
}

export function apiGetSubjectBatchRoomStatsPage({ examSubjectId, currentPage, pageSize }) {
  return ajax.get({
    url: '/scan/scanBatch/subjectBatchRoomStatsPage',
    params: {
      examSubjectId,
      page: currentPage,
      size: pageSize,
    },
    requestName: '查看科目扫描批次考场统计',
  })
}

export function apiGetRoomStudents({ examSubjectId, roomNo }) {
  return ajax.get({
    url: '/scan/scanBatch/roomStudents',
    params: {
      examSubjectId,
      roomNo,
    },
    requestName: '获取考场考生',
  })
}

export function apiDeleteScanBatch({ examSubjectId, batches }) {
  return ajax.delete({
    url: '/scan/scanBatch/deleteBatch',
    params: {
      examSubjectId,
    },
    data: batches,
    requestName: '删除扫描批次',
  })
}

export function apiSetStudentsMarkAbsent({ examSubjectId, studentIds }) {
  return ajax.put({
    url: '/scan/scanBatch/setStudentsMarkAbsent',
    params: {
      examSubjectId,
    },
    data: studentIds,
    requestName: '标记缺考',
  })
}

export function apiUnsetStudentMarkAbsent({ examSubjectId, studentId }) {
  return ajax.put({
    url: '/scan/scanBatch/unsetStudentMarkAbsent',
    params: {
      examSubjectId,
      studentId,
    },
    requestName: '取消标记缺考',
  })
}

export function apiGetPersonalScanBatchPage({ examSubjectId, roomNo, scanClient, batchNo, currentPage, pageSize }) {
  return ajax.get({
    url: '/scan/scanBatch/personalScanBatchPage',
    params: {
      examSubjectId,
      roomNo,
      scanClient,
      batchNo,
      page: currentPage,
      size: pageSize,
    },
    requestName: '获取个人扫描批次',
  })
}

export function apiGetScanUserScanBatchPage({
  examSubjectId,
  scanUserId,
  roomNo,
  scanClient,
  batchNo,
  currentPage,
  pageSize,
}) {
  return ajax.get({
    url: '/scan/scanBatch/scanUserScanBatchPage',
    params: {
      examSubjectId,
      scanUserId,
      roomNo,
      scanClient,
      batchNo,
      page: currentPage,
      size: pageSize,
    },
    requestName: '获取扫描员扫描批次',
  })
}

export function apiGetScanStationScanBatchPage({
  examSubjectId,
  scanStationId,
  roomNo,
  scanClient,
  batchNo,
  currentPage,
  pageSize,
}) {
  return ajax.get({
    url: '/scan/scanBatch/scanStationScanBatchPage',
    params: {
      examSubjectId,
      scanStationId,
      roomNo,
      scanClient,
      batchNo,
      page: currentPage,
      size: pageSize,
    },
    requestName: '获取扫描点扫描批次',
  })
}

export function apiGetBatchPaperPage({ examSubjectId, batchId, currentPage, pageSize }) {
  return ajax.get({
    url: '/scan/scanBatch/scanPaperPage',
    params: {
      examSubjectId,
      batchId,
      page: currentPage,
      size: pageSize,
    },
    requestName: '获取批次纸张',
  })
}

export function apiGetScanBatchInfo(batchId) {
  return ajax.get({
    url: '/scan/scanBatch/scanBatchInfo',
    params: {
      batchId,
    },
    requestName: '获取扫描批次信息',
  })
}
