<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame 1000006580">
<g clip-path="url(#clip0_2295_792)">
<rect width="80" height="80" rx="20" fill="#EBFFE0"/>
<g id="Group 427318830">
<rect id="Rectangle 346240783" x="14" y="8" width="53" height="64" rx="3.73064" fill="#17ADF7"/>
<rect id="Rectangle 346240788" x="14" y="8" width="53" height="64" rx="3.73064" fill="#17ADF7"/>
<rect id="Rectangle 346240785" x="20.6279" y="37.8813" width="22.6748" height="2.79798" rx="1.39899" fill="#B7E7FF"/>
<rect id="Rectangle 346240786" x="20.6279" y="50.0089" width="34.4657" height="2.79798" rx="1.39899" fill="#B7E7FF"/>
<rect id="Rectangle 346240787" x="20.6279" y="60.2679" width="34.4657" height="2.79798" rx="1.39899" fill="#B7E7FF"/>
<path id="Vector 105" d="M47.793 38.7732L49.6211 42.0233C49.9239 42.5616 50.6568 42.6623 51.0935 42.2256L56.7068 36.6123" stroke="#B7E7FF" stroke-width="1.86532" stroke-linecap="round"/>
<g id="&#229;&#136;&#157;&#228;&#184;&#173;&#230;&#149;&#176;&#229;&#173;&#166;" filter="url(#filter0_d_2295_792)">
<path d="M23.59 20.4H22.18V19.49H27.27C27.27 22.8767 27.2167 24.9967 27.11 25.85C26.99 27.1967 26.4433 27.87 25.47 27.87C25.31 27.87 24.92 27.8533 24.3 27.82L24.07 26.95C24.6033 26.9833 25.0167 27 25.31 27C25.8167 27 26.11 26.4733 26.19 25.42C26.2433 24.98 26.28 23.3067 26.3 20.4H24.53V21.17C24.49 24.3167 23.6233 26.6067 21.93 28.04L21.2 27.4C22.7667 26.1 23.5633 24.0233 23.59 21.17V20.4ZM19.74 24.24C19.3733 24.6133 19.0033 24.97 18.63 25.31L18.35 24.35C19.57 23.25 20.39 22.18 20.81 21.14H18.58V20.28H19.82C19.66 19.8467 19.48 19.4267 19.28 19.02L20.2 18.78C20.4467 19.3533 20.6333 19.8533 20.76 20.28H21.74V21.05C21.5 21.75 21.1367 22.4433 20.65 23.13V23.15L21.06 23.44C21.36 23.2067 21.6633 22.8667 21.97 22.42L22.54 22.99C22.2533 23.3767 21.9533 23.6833 21.64 23.91C21.94 24.17 22.1767 24.3967 22.35 24.59L21.84 25.39C21.42 24.8433 21.0233 24.3933 20.65 24.04V28.02H19.74V24.24ZM32.51 18.76H33.49V20.49H37.05V25.32H36.1V24.75H33.49V28.1H32.51V24.75H29.91V25.32H28.96V20.49H32.51V18.76ZM29.91 23.82H32.51V21.42H29.91V23.82ZM33.49 23.82H36.1V21.42H33.49V23.82ZM44.67 21.16L44.55 21.61C44.7833 22.6367 45.0733 23.5367 45.42 24.31C45.7333 23.4233 45.91 22.3733 45.95 21.16H44.67ZM44.99 25.29C44.6433 24.6033 44.3433 23.8267 44.09 22.96C43.91 23.38 43.7433 23.7167 43.59 23.97L42.99 23.23C43.55 22.13 43.93 20.65 44.13 18.79L45.06 18.95C44.9867 19.5033 44.92 19.9367 44.86 20.25H47.4V21.16H46.83C46.77 22.7867 46.4767 24.17 45.95 25.31C46.4567 26.13 47.0433 26.76 47.71 27.2L47.16 28C46.5267 27.54 45.97 26.9333 45.49 26.18C45.0033 26.9333 44.41 27.56 43.71 28.06L43.21 27.25C43.9567 26.75 44.55 26.0967 44.99 25.29ZM40.23 23.44L41.15 23.54L40.98 24H43.01V24.8C42.83 25.4 42.5133 25.9267 42.06 26.38C42.5467 26.6 42.9367 26.7867 43.23 26.94L42.72 27.7C42.34 27.48 41.8733 27.24 41.32 26.98C40.6733 27.4 39.8467 27.7433 38.84 28.01L38.38 27.2C39.1267 27.02 39.77 26.8 40.31 26.54C39.9967 26.4067 39.56 26.23 39 26.01C39.2533 25.59 39.47 25.2033 39.65 24.85H38.61V24H40.03L40.23 23.44ZM41.16 26.01C41.5933 25.6767 41.9167 25.29 42.13 24.85H40.61C40.4767 25.1167 40.3267 25.38 40.16 25.64C40.54 25.7733 40.8733 25.8967 41.16 26.01ZM39.53 19C39.7433 19.3467 39.9467 19.7433 40.14 20.19L39.4 20.53C39.1933 20.03 38.9733 19.6067 38.74 19.26L39.53 19ZM43.12 19.31C42.9133 19.75 42.66 20.1667 42.36 20.56L41.66 20.19C41.9333 19.8033 42.17 19.39 42.37 18.95L43.12 19.31ZM38.64 20.65H40.53V18.81H41.4V20.65H43.14V21.48H41.4V21.53C41.9 21.7367 42.44 21.9933 43.02 22.3L42.52 23.05C42.0467 22.67 41.6733 22.3933 41.4 22.22V23.34H40.53V21.65C40.2033 22.37 39.6533 23 38.88 23.54L38.41 22.77C39.0967 22.3767 39.5933 21.9467 39.9 21.48H38.64V20.65ZM57.33 20.76V23.18H56.38V21.67H49.61V23.18H48.67V20.76H50.47C50.15 20.22 49.82 19.74 49.48 19.32L50.34 18.9C50.6933 19.38 51.0267 19.8933 51.34 20.44L50.7 20.76H54.45C54.85 20.1533 55.2033 19.4833 55.51 18.75L56.49 19.11C56.15 19.7767 55.8167 20.3267 55.49 20.76H57.33ZM50.3 22.3H55.71V23.14C55.09 23.54 54.3867 23.9333 53.6 24.32V24.67H57.33V25.57H53.6V27.1C53.6 27.6933 53.2833 27.99 52.65 27.99H51.38L51.15 27.13C51.63 27.1433 52.0233 27.15 52.33 27.15C52.5433 27.15 52.65 27.0233 52.65 26.77V25.57H48.7V24.67H52.65V23.91C53.35 23.61 53.9133 23.3533 54.34 23.14H50.3V22.3ZM52.73 18.7C53.0767 19.2133 53.3933 19.7533 53.68 20.32L52.83 20.75C52.5367 20.15 52.2167 19.6067 51.87 19.12L52.73 18.7Z" fill="white"/>
</g>
</g>
<g id="Group 427318831">
<rect id="Rectangle 346240783_2" x="14" y="8" width="53" height="64" rx="3.73064" fill="#509C26"/>
<path id="Rectangle 346240788_2" d="M14 11.7306C14 9.67027 15.6703 8 17.7306 8H41C55.3594 8 67 19.6406 67 34V68.2694C67 70.3297 65.3297 72 63.2694 72H17.7306C15.6703 72 14 70.3297 14 68.2694V11.7306Z" fill="url(#paint0_linear_2295_792)"/>
<g id="&#233;&#171;&#152;&#228;&#184;&#173;&#228;&#189;&#147;&#232;&#130;&#178;" filter="url(#filter1_d_2295_792)">
<path d="M23.28 18.74C23.3733 19.0067 23.4633 19.28 23.55 19.56H27.44V20.44H18.55V19.56H22.52C22.4333 19.34 22.3333 19.1267 22.22 18.92L23.28 18.74ZM24.99 28L24.77 27.15L25.54 27.19C25.9133 27.19 26.1 27.0567 26.1 26.79V24.26H19.9V28.03H19V23.44H27V26.99C27 27.6633 26.6067 28 25.82 28H24.99ZM20.12 20.91H25.89V22.99H20.12V20.91ZM24.98 22.27V21.63H21.03V22.27H24.98ZM20.97 24.74H25.04V26.84H20.97V24.74ZM24.2 26.14V25.44H21.82V26.14H24.2ZM32.71 18.76H33.69V20.49H37.25V25.32H36.3V24.75H33.69V28.1H32.71V24.75H30.11V25.32H29.16V20.49H32.71V18.76ZM30.11 23.82H32.71V21.42H30.11V23.82ZM33.69 23.82H36.3V21.42H33.69V23.82ZM39.9 22.95C39.6133 23.33 39.3267 23.6833 39.04 24.01L38.76 23.06C39.7467 21.82 40.47 20.3833 40.93 18.75L41.78 19.15C41.5467 19.9367 41.23 20.71 40.83 21.47V28.04H39.9V22.95ZM41.53 20.41H44.06V18.79H44.99V20.41H47.64V21.32H45.67C46.1367 22.9 46.9233 24.2933 48.03 25.5L47.42 26.27C46.26 24.81 45.45 23.1733 44.99 21.36V25.14H46.42V26.05H44.99V28.03H44.06V26.05H42.66V25.14H44.06V21.32C43.56 23.38 42.72 25.07 41.54 26.39L41.05 25.58C42.1233 24.4533 42.9 23.0333 43.38 21.32H41.53V20.41ZM49.08 19.61H53.06C52.9733 19.3967 52.86 19.1667 52.72 18.92L53.83 18.75C53.9367 18.9967 54.04 19.2833 54.14 19.61H58.14V20.51H56.06C56.8933 21.17 57.5267 21.7367 57.96 22.21L57.16 22.76C56.9733 22.5467 56.7367 22.2967 56.45 22.01C54.8567 22.1433 52.7167 22.2567 50.03 22.35L49.82 21.54C50.0067 21.52 50.16 21.48 50.28 21.42C50.64 21.1733 51.04 20.87 51.48 20.51H49.08V19.61ZM52.68 20.51C52.1533 20.9567 51.7133 21.3 51.36 21.54C52.7867 21.4867 54.2367 21.4133 55.71 21.32L55.09 20.79L55.52 20.51H52.68ZM51.45 25.02V25.63H55.74V25.02H51.45ZM55.74 24.22V23.57H51.45V24.22H55.74ZM51.45 26.43V28.03H50.5V22.76H56.71V27.03C56.71 27.69 56.3567 28.02 55.65 28.02H54.63L54.38 27.1L55.34 27.14C55.6067 27.14 55.74 27.0367 55.74 26.83V26.43H51.45Z" fill="white"/>
</g>
</g>
<g id="Frame">
<path id="Vector" d="M49.1508 34.2617C51.7307 34.2029 53.8708 36.2723 53.9296 38.8838C53.9873 41.4953 51.9419 43.6593 49.3609 43.7181C46.781 43.7769 44.6408 41.7086 44.5821 39.0971V38.9915C44.5516 36.4095 46.597 34.2933 49.1497 34.2639V34.2606L49.1508 34.2617ZM20 47.7273C22.1445 47.6555 24.2911 47.5869 26.4356 47.5216L27.3674 48.547L35.8692 55.3473C35.9471 55.5517 35.9648 55.7743 35.92 55.9885C35.8753 56.2027 35.7699 56.3995 35.6166 56.5556L32.6089 58.5422C32.2959 58.6505 31.9616 58.6821 31.6339 58.6344C31.3062 58.5867 30.9947 58.461 30.7257 58.2679C27.2723 55.4192 23.8223 52.5664 20.3756 49.7096C20 49.3939 20.0523 48.2204 20 47.7273ZM29.6447 37.3424C28.5104 37.3511 29.0427 38.3395 29.6012 38.7978C32.3117 41.0566 35.0222 43.3121 37.736 45.5643L33.5059 48.6439C32.7754 49.1718 33.4025 50.6719 33.9511 51.1138L50.1882 64.2583H56.6216C56.4311 63.4822 56.5182 62.4589 55.8727 61.9473C51.2768 58.2679 46.6852 54.5886 42.0958 50.9092C43.6045 49.7096 45.1111 48.51 46.6122 47.3104C46.8154 47.0445 46.9113 46.7119 46.8809 46.3786C46.8504 46.0454 46.6959 45.7357 46.4479 45.511C43.1604 42.7471 39.8751 39.9919 36.593 37.2444C34.282 37.2716 31.9666 37.3032 29.6436 37.3424H29.6447Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_2295_792" x="16.55" y="17.9" width="40.78" height="11" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1"/>
<feGaussianBlur stdDeviation="0.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.33 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2295_792"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2295_792" result="shape"/>
</filter>
<filter id="filter1_d_2295_792" x="16.75" y="17.94" width="41.39" height="10.96" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1"/>
<feGaussianBlur stdDeviation="0.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.33 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2295_792"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2295_792" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2295_792" x1="78.5" y1="-3" x2="16" y2="75.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#F1FFEA" stop-opacity="0.75"/>
<stop offset="0.874591" stop-color="#509C26"/>
</linearGradient>
<clipPath id="clip0_2295_792">
<rect width="80" height="80" rx="20" fill="white"/>
</clipPath>
</defs>
</svg>
