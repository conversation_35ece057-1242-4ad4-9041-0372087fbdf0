<template>
  <div class="com-user-info">
    <div class="panel-avatar">
      <div class="avatar">
        <img v-if="user.avatarUrl" :src="user.avatarUrl" />
        <span v-else>头像</span>
      </div>
      <Button type="primary" @click="showModalChangeAvatar = true">更换头像</Button>
    </div>
    <div class="panel-info">
      <Form class="form-info" :label-width="100" label-position="left">
        <FormItem class="form-item" label="姓名">
          <Input v-model="user.realName" type="text" style="width: 200px" maxlength="20" clearable></Input>
        </FormItem>
        <FormItem class="form-item" label="性别">
          <RadioGroup v-model="user.sex">
            <Radio label="男">男</Radio>
            <Radio label="女">女</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem class="form-item" label="手机号码">
          <span>{{ user.mobile }}</span>
        </FormItem>
        <FormItem class="form-item" label="电子邮箱">
          <span>{{ user.email }}</span>
        </FormItem>
        <FormItem class="form-item" label="当前学校">
          <span>{{ user.schoolName }}</span>
        </FormItem>
        <FormItem class="form-item" label="任教班级">
          <span v-html="teachingText"></span>
        </FormItem>
        <FormItem class="form-item" label="担任职务">
          <span v-html="positionText"></span>
        </FormItem>
        <FormItem class="form-item" label="第三方账号">
          <div class="form-item-content">
            <div class="third-account-item">
              <div class="icon-box">
                <img class="icon" src="@/assets/images/common/logo_wechat.svg" />
                <!-- <span class="third-name">微信</span> -->
              </div>
              <div class="info-box">
                <div class="binding-box">
                  <template v-if="bindingPlatform.includes('weixin')">
                    <span class="binding-status status-bound">已绑定微信</span>
                    <span class="btn-binding" @click="unbind('WeChat')">解除绑定</span>
                  </template>
                  <template v-else>
                    <span class="binding-status status-not-bound">未绑定微信</span>
                    <span class="btn-binding btn-add-binding" @click="bind('WeChat')">添加绑定</span>
                  </template>
                </div>
              </div>
            </div>
            <div class="third-account-item">
              <div class="icon-box">
                <img class="icon" src="@/assets/images/common/logo_qq.svg" />
                <!-- <span class="third-name name-qq">QQ</span> -->
              </div>
              <div class="info-box">
                <div class="binding-box">
                  <template v-if="bindingPlatform.includes('qq')">
                    <span class="binding-status status-bound">已绑定QQ</span>
                    <span class="btn-binding" @click="unbind('QQ')">解除绑定</span>
                  </template>
                  <template v-else>
                    <span class="binding-status status-not-bound">未绑定QQ</span>
                    <span class="btn-binding btn-add-binding" @click="bind('QQ')">添加绑定</span>
                  </template>
                </div>
              </div>
            </div>
            <div class="third-account-item">
              <div class="icon-box">
                <img class="icon" src="@/assets/images/common/logo_dingding.svg" />
                <!-- <span class="third-name">钉钉</span> -->
              </div>
              <div class="info-box">
                <div class="binding-box">
                  <template v-if="bindingPlatform.includes('dingding')">
                    <span class="binding-status">已绑定钉钉</span>
                    <span class="btn-binding" @click="unbind('DingTalk')">解除绑定</span>
                  </template>
                  <template v-else>
                    <span class="binding-status status-not-bound">未绑定钉钉</span>
                    <span class="btn-binding btn-add-binding" @click="bind('DingTalk')">添加绑定</span>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </FormItem>
        <FormItem class="form-item">
          <Button type="primary" @click="handleBtnChangeInfoClick">保存更改</Button>
        </FormItem>
      </Form>
    </div>

    <Modal v-model="showModalChangeAvatar" class="modal-change-avatar" title="修改头像" :mask-closable="false">
      <div class="modal-content">
        <div class="section-file">
          <com-upload
            class="com-upload"
            :accepts="['.jpg', '.png', '.jpeg', '.gif']"
            :max-size="0.5"
            :file-name="newAvatarFile && newAvatarFile.name"
            @on-selected="handleAvatarFileSelected"
          ></com-upload>
        </div>
        <div class="modal-tips">图片支持JPG，JPEG，PNG，GIF格式，大小限制在500KB内</div>
        <div class="section-preview">
          <img v-if="newAvatarSrc" :src="newAvatarSrc" />
          <span v-else>新头像预览</span>
        </div>
      </div>
      <template #footer>
        <div>
          <Button type="text" @click="showModalChangeAvatar = false">取消</Button>
          <Button type="primary" @click="handleModalChangeAvatarOk">确定</Button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
  import ComUpload from '@/components/upload.vue'

  import {
    apiChangeUserInfo,
    apiGetBindingPlatform,
    apiUnbindAccountToThird,
    apiGetBindingAuthByWeChat,
    apiGetBindingAuthByDingTalk,
    apiGetBindingAuthByQQ,
    apiUpdateAvatar,
  } from '@/api/user'

  import { deepCopy } from '@/utils/object'
  import { groupArray } from '@/utils/array'
  import Role, { SchoolPositions, EduSchoolPositions } from '@/enum/user/role'
  import SchoolTypeEnum from '@/enum/school/school_type'

  export default {
    components: {
      ComUpload,
    },
    data() {
      return {
        user: {},
        showModalChangeAvatar: false,
        newAvatarSrc: null,
        newAvatarFile: null,
        bindingPlatform: [],
      }
    },
    computed: {
      stageGradeSubjects() {
        return this.$store.state.school.stageGradeSubjects
      },
      subjects() {
        let list = []
        this.stageGradeSubjects.forEach(stage => {
          stage.grades.forEach(grade => {
            grade.subjects.forEach(s => {
              let subject = list.find(x => x.id === s.id)
              if (!subject) {
                list.push({
                  id: s.id,
                  name: s.name,
                })
              }
            })
          })
        })

        return list
      },
      grades() {
        let list = []
        this.stageGradeSubjects.forEach(stage => {
          stage.grades.forEach(grade => {
            list.push({
              id: grade.id,
              name: grade.name,
            })
          })
        })

        return list
      },
      positions() {
        let list = {
          ...SchoolPositions,
          ...EduSchoolPositions,
        }
        list.SchoolAdministrator = {
          id: Role.SchoolAdministrator.id,
          name: '管理员',
        }
        return list
      },
      teachingText() {
        let teachings = (this.user.teachings || []).slice()
        if (teachings.length === 0) {
          return ''
        }

        teachings.sort((a, b) => a.subjectId - b.subjectId)
        let teachingsBySubjects = groupArray(teachings, t => t.subjectId).map(g => {
          let subject = this.subjects.find(x => x.id === g.key)
          return {
            subjectId: g.key,
            subjectName: (subject && subject.name) || '',
            classes: g.group.map(c => ({
              classId: c.classId,
              className: c.className,
            })),
            classNames: g.group.map(c => c.className).join('，'),
          }
        })

        return teachingsBySubjects.map(x => `${x.subjectName}（${x.classNames}）`).join('<br>')
      },
      positionText() {
        let positions = (this.user.schoolPositions || []).slice()
        if (positions.length === 0) {
          return ''
        }

        let positionNames = positions.map(p => {
          // 学校职务
          if (this.user.schoolType === SchoolTypeEnum.School.id) {
            if (p.roleId === this.positions.SchoolAdministrator.id) {
              return this.positions.SchoolAdministrator.name
            } else if (p.roleId === this.positions.SchoolLeader.id) {
              return this.positions.SchoolLeader.name
            } else if (p.roleId === this.positions.GradeLeader.id) {
              let grade = this.grades.find(g => g.id === p.gradeId)
              return `${(grade && grade.name) || ''}${this.positions.GradeLeader.name}`
            } else if (p.roleId === this.positions.SubjectLeader.id) {
              let stage = this.stageGradeSubjects.find(s => s.id === p.stageId)
              let subject = this.subjects.find(s => s.id === p.subjectId)
              return `${(stage && stage.name) || ''}${(subject && subject.name) || ''}${
                this.positions.SubjectLeader.name
              }`
            } else if (p.roleId === this.positions.GradeSubjectLeader.id) {
              let grade = this.grades.find(g => g.id === p.gradeId)
              let subject = this.subjects.find(s => s.id === p.subjectId)
              return `${(grade && grade.name) || ''}${(subject && subject.name) || ''}${
                this.positions.GradeSubjectLeader.name
              }`
            } else if (p.roleId === this.positions.ClassLeader.id) {
              return `${p.className}${this.positions.ClassLeader.name}`
            }
          }

          // 教育局职务
          else if (this.user.schoolType === SchoolTypeEnum.Bureau.id) {
            if (p.roleId === this.positions.SchoolAdministrator.id) {
              return '管理员'
            }
            if (p.roleId === this.positions.ResearcherLeader.id) {
              return this.positions.ResearcherLeader.name
            } else if (p.roleId === this.positions.Researcher.id) {
              let stage = this.stageGradeSubjects.find(s => s.id === p.stageId)
              let subject = this.subjects.find(s => s.id === p.subjectId)
              return `${(stage && stage.name) || ''}${(subject && subject.name) || ''}${this.positions.Researcher.name}`
            }
          }

          return ''
        })

        return positionNames.join('<br>')
      },
    },
    created() {
      this.loadUser()
      this.getBindingPlatform()
    },
    methods: {
      loadUser() {
        this.$store
          .dispatch('user/refreshInfo')
          .then(() => {
            this.user = deepCopy(this.$store.state.user)
          })
          .catch(err => {
            this.user = {}
            throw err
          })
      },
      getBindingPlatform() {
        apiGetBindingPlatform().then(res => {
          this.bindingPlatform = res || []
        })
      },
      handleBtnChangeInfoClick() {
        let info = this.$store.getters['user/info']
        if (this.user.realName === info.realName && this.user.sex === info.sex) {
          this.$Message.info({
            content: '未作修改',
          })
          return
        }

        if (!this.user.realName) {
          this.$Message.info({
            content: '请输入姓名',
          })
          return
        }

        apiChangeUserInfo(this.user)
          .then(() => {
            this.$Message.success({
              content: '已更新个人信息，重新登录后生效',
              duration: 3,
            })
          })
          .finally(() => {
            this.loadUser()
          })
      },
      handleAvatarFileSelected(file) {
        this.newAvatarFile = file
        if (file) {
          this.newAvatarSrc = window.URL.createObjectURL(file)
        } else {
          if (this.newAvatarSrc) {
            window.URL.revokeObjectURL(this.newAvatarSrc)
          }
          this.newAvatarSrc = null
        }
      },
      handleModalChangeAvatarOk() {
        if (!this.newAvatarFile) {
          this.$Message.info({
            content: '请选择要更改的头像',
          })
          return
        }
        apiUpdateAvatar(this.newAvatarFile).then(() => {
          this.loadUser()
          this.showModalChangeAvatar = false
        })
      },
      bind(type) {
        switch (type) {
          case 'QQ':
            apiGetBindingAuthByQQ().then(res => {
              if (res) {
                window.location.assign(res)
              }
            })
            break
          case 'WeChat':
            apiGetBindingAuthByWeChat().then(res => {
              if (res) {
                window.location.assign(res)
              }
            })
            break
          case 'DingTalk':
            apiGetBindingAuthByDingTalk().then(res => {
              if (res) {
                window.location.assign(res)
              }
            })
            break
          default:
            break
        }
      },
      unbind(type) {
        switch (type) {
          case 'QQ':
            this.$Modal.confirm({
              title: '解绑QQ',
              content: '您确定要解除绑定QQ吗？',
              onOk: () => {
                apiUnbindAccountToThird({
                  platform: 'qq',
                }).then(() => {
                  this.$Message.success({
                    content: '解除绑定QQ成功',
                  })
                  this.getBindingPlatform()
                })
              },
            })
            break
          case 'WeChat':
            this.$Modal.confirm({
              title: '解绑微信',
              content: '您确定要解除绑定微信吗？',
              onOk: () => {
                apiUnbindAccountToThird({
                  platform: 'weixin',
                }).then(() => {
                  this.$Message.success({
                    content: '解除绑定微信成功',
                  })
                  this.getBindingPlatform()
                })
              },
            })
            break
          case 'DingTalk':
            this.$Modal.confirm({
              title: '解绑钉钉',
              content: '您确定要解除绑定钉钉吗？',
              onOk: () => {
                apiUnbindAccountToThird({
                  platform: 'dingding',
                }).then(() => {
                  this.$Message.success({
                    content: '解除绑定钉钉成功',
                  })
                  this.getBindingPlatform()
                })
              },
            })
            break
          default:
            break
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .com-user-info {
    @include flex(row, flex-start, flex-start);
    margin-right: 50px;
  }

  .panel-avatar {
    flex-grow: 0;
    flex-shrink: 0;
    margin-right: 50px;
    text-align: center;

    .avatar {
      width: 150px;
      height: 150px;
      margin-bottom: 20px;
      border: 1px solid $color-border;

      img {
        width: 100%;
        height: 100%;
      }

      span {
        line-height: 150px;
      }
    }
  }

  .panel-info {
    .form-item {
      margin-bottom: 10px;
    }

    .form-item-content {
      @include flex(row, flex-start, center);
      padding: 10px 0;

      .third-account-item {
        @include flex(row, flex-start, center);

        &:not(:last-child) {
          margin-right: 30px;
        }

        .icon-box {
          @include flex(column, flex-start, center);
          margin-right: 8px;

          .icon {
            width: 40px;
            height: 40px;
          }
        }

        .third-name {
          margin-top: 4px;
          font-weight: 600;
          font-size: 12px;
          line-height: 18px;
        }

        .name-qq {
          font-size: 13px;
        }

        .info-box {
          @include flex(column, flex-start, flex-start);
        }

        .binding-box {
          @include flex(column, flex-start, flex-start);
        }

        .binding-status {
          padding-left: 6px;
          color: #999;
          font-size: 12px;
          line-height: 20px;
        }

        .status-not-bound {
          color: $color-error;
        }

        .btn-binding {
          display: inline-block;
          width: 70px;
          height: 20px;
          margin-top: 4px;
          border: 1px solid #666;
          border-radius: 10px;
          font-size: 12px;
          line-height: 18px;
          text-align: center;
          cursor: pointer;
        }

        .btn-add-binding {
          border-color: $color-primary;
          color: $color-primary;
        }
      }
    }
  }

  .modal-change-avatar {
    .section-preview {
      width: 200px;
      height: 200px;
      margin: 20px auto;
      border: 1px solid $color-border;
      text-align: center;

      img {
        width: 100%;
        height: 100%;
      }

      span {
        line-height: 200px;
      }
    }

    .modal-tips {
      margin-top: 15px;
      font-size: 13px;
      text-align: center;
    }
  }
</style>
