import { round as roundNumber, sumBy } from 'lodash'

export { sum, sumBy, mean, meanBy, round as roundNumber } from 'lodash'

/**
 * 数组求和，每项默认为0
 * @param {Array} arr
 * @param {Function | String} iteratee
 * @returns Number
 */
export function sumByDefaultZero(arr, iteratee = x => x) {
  let wrappedIteratee = typeof iteratee === 'string' ? x => Number(x[iteratee]) || 0 : x => Number(iteratee(x)) || 0
  return sumBy(arr, wrappedIteratee)
}

/**
 * 四舍五入分数：系统内题目分数一般保留1位小数
 * @param {*} score
 */
export function roundScore(score) {
  return roundNumber(score, 1)
}

/**
 * 小数转为百分数
 * @param {Number} number
 * @param {Number} precision 保留小数位数
 * @returns String
 */
export function roundPercent(number, precision = 1) {
  if (Number.isNaN(number)) {
    return '0%'
  }
  return `${roundNumber(number * 100, precision)}%`
}

/**
 * 修正数字，使其位于[min, max]区间且小数位数不超过digit
 * @param {Number} num
 * @param {Number} min
 * @param {Number} max
 * @param {Number} digit 小数位数
 * @returns Number
 */
export function normalizeNumber(num, min, max, digit = 0) {
  let n = Number(num) || min
  n = Number(n.toFixed(digit))
  n = Math.max(min, Math.min(max, n))
  return n
}

/**
 * 转化保留2位小数（末位为0不保留） 返回字符串版本
 * @param {Number} ({String}) num
 * @returns string
 */
export function retainTwoDecimalPlaces(number) {
  const ToNumber = Number(number)
  if (isNaN(ToNumber)) {
    return ''
  } else if (Number.isInteger(ToNumber)) {
    return ToNumber.toString()
  } else {
    return Number((Math.round(ToNumber * 100) / 100).toString()).toString()
  }
}

/**
 * 区间
 */
export class Interval {
  constructor(start, end, startInclusive, endInclusive) {
    this.start = Number(start) || 0
    this.end = Number(end) || 0
    this.startInclusive = Boolean(startInclusive) || false
    this.endInclusive = Boolean(endInclusive) || false
  }

  get expression() {
    return `${this.startInclusive ? '[' : '('}${this.start},${this.end}${this.endInclusive ? ']' : ')'}`
  }

  containsValue(value) {
    let isWithinStart = this.startInclusive ? value >= this.start : value > this.start
    let isWithinEnd = this.endInclusive ? value <= this.end : value < this.end
    return isWithinStart && isWithinEnd
  }

  static parse(expression) {
    if (typeof expression != 'string' || !expression) {
      return null
    }
    expression = expression.trim()
    if (
      !(expression.startsWith('(') || expression.startsWith('[')) ||
      !(expression.endsWith(')') || expression.endsWith(']'))
    ) {
      return null
    }
    let startInclusive = expression.startsWith('[')
    let endInclusive = expression.endsWith(']')
    expression = expression.substring(1, expression.length() - 1)
    let parts = expression.split(',')
    if (parts.length != 2) {
      return null
    }
    let start = Number(parts[0].trim())
    let end = Number(parts[1].trim())
    if (Number.isNaN(start) || Number.isNaN(end)) {
      return null
    }
    return new Interval(start, end, startInclusive, endInclusive)
  }
}
