import ajax from '@/api/ajax'

// 个人查看统计
export function apiGetPersonalScanStats({ examSubjectId }) {
  return ajax.get({
    url: '/scan/scanstat/personalStat',
    params: {
      examSubjectId,
    },
    requestName: '获取扫描统计',
  })
}

// 管理员查看扫描点统计
export function apiGetStationScanStats({ examSubjectId, scanStationId }) {
  return ajax.get({
    url: '/scan/scanstat/scanStationStat',
    params: {
      examSubjectId,
      scanStationId,
    },
    requestName: '获取扫描统计',
  })
}

// 管理员查看扫描员统计
export function apiGetUserScanStats({ examSubjectId, scanUserId }) {
  return ajax.get({
    url: '/scan/scanstat/scanUserStat',
    params: {
      examSubjectId,
      scanUserId,
    },
    requestName: '获取扫描统计',
  })
}

// 获取扫描考生统计
export function apiGetScanStudentStats({ examSubjectId, scanStationId }) {
  return ajax.get({
    url: '/scan/scanstat/studentStat',
    params: {
      examSubjectId,
      scanStationId,
    },
    requestName: '获取扫描考生统计',
  })
}

// 考生分页列表
export function apiGetScanStudentPage({
  examSubjectId,
  scanStationId,
  schoolId,
  roomNo,
  status,
  key,
  currentPage,
  pageSize,
}) {
  return ajax.get({
    url: '/scan/scanstat/studentPage',
    params: {
      examSubjectId,
      scanStationId,
      schoolId,
      roomNo,
      status,
      key,
      page: currentPage,
      size: pageSize,
    },
    requestName: '获取考生分页列表',
  })
}

export function apiDownloadStudentSignExcel({ examId, examSubjectIds, scanStationId, signs }) {
  return ajax.download({
    url: '/scan/scanstat/studentSignExcel',
    params: {
      examId,
      examSubjectIds: (examSubjectIds || []).join(','),
      scanStationId,
      signs: (signs || []).join(','),
    },
    requestName: '下载考生扫描状态名单',
  })
}

export function apiDownloadExamNotUploadedStudentListExcel(requestParams) {
  return ajax.download({
    url: '/scan/scanstat/exportStudentUploadMissingToExcel',
    params: {
      examId: requestParams.examId, // *String
      examSubjectIds: requestParams.examSubjectIds, // String 考试科目Id集合 用逗号隔开， 空则导出全部科目
    },
    requestName: '导出漏传到阅卷系统的学生名单',
  })
}
