export default {
  name: 'answerSheet',
  path: 'answersheet',
  meta: {
    title: '答题卡',
    name: '答题卡',
  },
  component: () => import('@/views/answer_sheet/index.vue'),
  children: [
    {
      name: 'answerSheet-list',
      path: 'list',
      component: () => import('@/views/answer_sheet/list/index.vue'),
    },
    {
      name: 'answerSheet-editPaper',
      path: 'editpaper/:paperId',
      component: () => import('@/views/answer_sheet/edit/paper.vue'),
    },
    {
      name: 'answerSheet-editExam',
      path: 'editexam/:examId/:examSubjectId/:answerSheetId',
      component: () => import('@/views/answer_sheet/edit/exam.vue'),
    },
    {
      name: 'answerSheet-editManual',
      path: 'editmanual/:sheetId',
      component: () => import('@/views/answer_sheet/edit/manual.vue'),
    },
    {
      name: 'answerSheet-coachBookEditPaper',
      path: 'coachbookeditpaper/:paperId/:coachBookId',
      component: () => import('@/views/answer_sheet/edit/coach_book_paper.vue'),
    },
    {
      name: 'answerSheet-coachBookEditManual',
      path: 'coachbookeditmanual/:sheetId/:coachBookId',
      component: () => import('@/views/answer_sheet/edit/coach_book_manual.vue'),
    },
    {
      name: 'answerSheet-changeTemplate',
      path: 'changetemplate/:id',
      meta: {
        showHeader: false,
        showFooter: false,
        pageWidth: 'full',
      },
      component: () => import('@/views/scan_template/change_answer_sheet_block.vue'),
    },
  ],
}
