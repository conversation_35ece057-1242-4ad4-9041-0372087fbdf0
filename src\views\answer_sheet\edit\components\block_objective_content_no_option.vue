<template>
  <div class="block-objective-content">
    <com-box-content
      v-if="showContent"
      ref="contentBox"
      :content="block.content"
      :width="pageBodyWidth"
      :height="block.contentHeight"
      :border="contentBorderStyle"
      :resizable="true"
      :padding-top="5"
      @height-change="handleContentHeightChange"
      @resize="handleResize"
      @resize-end="handleResizeEnd"
      @content-change="changeContent"
    ></com-box-content>

    <div class="page-block-toolbar no-print">
      <div v-if="canSplit" class="btn btn-primary btn-split" title="拆分" @click="handleSplit"></div>
    </div>

    <div v-if="canMerge" class="merge-area no-print" title="合并" @click="handleMerge"></div>
  </div>
</template>

<script>
  import comBoxContent from './box_content.vue'

  import { mapGetters, mapMutations } from 'vuex'

  export default {
    components: {
      comBoxContent,
    },
    props: {
      block: {
        type: Object,
        required: true,
      },
    },
    emits: ['height-change'],
    data() {
      return {
        // 选项总高度
        optionsHeight: 0,
        // 选项左边题号宽度
        questionCodeWidth: 31,
        // 选项每行排几个
        optionsPerRow: 1,
        // 选项标签及填涂宽度
        optionLabelOMRWidth: 26,
        optionLabelLetterWidth: 20,
        // 修改选项内容
        showModalChangeOptionContent: false,
        optionInModal: null,
      }
    },
    computed: {
      ...mapGetters('answerSheet', ['pageContentWidth', 'subject', 'objectivesSeparate', 'baseStyle']),
      pageSize() {
        return this.$store.getters['answerSheet/pageSize'](this.block.pageIndex)
      },
      baseStyle() {
        return this.$store.getters['answerSheet/baseStyle']
      },

      /**
       * 样式
       */
      pageBodyWidth() {
        return this.pageSize.width - this.pageSize.left - this.pageSize.right
      },
      contentBorderStyle() {
        return {
          left: false,
          right: false,
          top: false,
          bottom: false,
        }
      },

      hasStem() {
        let stem = this.block.questionGroup.questions[0].originalBranch?.stem
        if (!stem) {
          return false
        }
        // 全空白字符
        if (!stem.trim()) {
          return false
        }
        // 只有括号
        stem = stem.replace(/&nbsp;/g, ' ')
        if (/^\s*(<p>)?\s*[(（]\s*[）)]\s*(<\/p>)?\s*$/.test(stem)) {
          return false
        }
        return true
      },
      showContent() {
        if (!this.hasStem) {
          return false
        }
        // 最后一块没有内容则不显示
        if (
          this.block.part == this.block.questionGroup.blocks.length - 1 &&
          !this.block.content &&
          this.block.contentHeight == 0
        ) {
          return false
        }
        return true
      },

      /**
       * 合并拆分
       */
      canMerge() {
        return (
          this.block.part == 0 &&
          this.$store.getters['answerSheet/getCanMergeQuestionGroup'](this.block.questionGroup.id)
        )
      },
      canSplit() {
        return (
          this.block.part == 0 &&
          this.$store.getters['answerSheet/getCanSplitQuestionGroup'](this.block.questionGroup.id)
        )
      },
    },
    created() {
      this.changeInstance()
    },
    updated() {
      this.changeInstance()
    },
    methods: {
      ...mapMutations('answerSheet', ['changePageBlockInstance', 'changePageBlockContent', 'changePageBlockHeight']),
      changeInstance() {
        this.changePageBlockInstance({
          blockId: this.block.id,
          instance: this,
        })
      },
      changeContent(content) {
        this.changePageBlockContent({
          blockId: this.block.id,
          content: content,
        })
      },
      changeHeight(contentHeight = this.block.contentHeight, emitEvent) {
        let height = contentHeight
        let heightChanged = height != this.block.height
        this.changePageBlockHeight({
          blockId: this.block.id,
          height,
          contentHeight,
        })
        if (emitEvent || (emitEvent == null && heightChanged)) {
          this.$emit('height-change', height)
        }
      },
      handleContentHeightChange(contentHeight) {
        this.changeHeight(contentHeight)
      },
      // 调整高度过程中不重排
      handleResize(contentHeight) {
        this.changeHeight(contentHeight, false)
      },
      handleResizeEnd() {
        this.changeHeight(this.contentHeight, true)
      },

      handleMerge() {
        this.$store.commit('answerSheet/mergeQuestionGroup', this.block.questionGroup.id)
        this.$store.commit('answerSheet/generatePages')
      },
      handleSplit() {
        this.$store.commit('answerSheet/splitQuestionGroup', this.block.questionGroup.id)
        this.$store.commit('answerSheet/generatePages')
      },

      /**
       * 不支持分成多块
       */
      split(getNextPageAvailableHeightFunc) {
        let questionGroupHeight = this.block.questionGroup.height
        let questionGroupContent = this.block.questionGroup.content
        let availableHeight = getNextPageAvailableHeightFunc(0)
        if (questionGroupHeight <= availableHeight) {
          return [
            {
              content: questionGroupContent,
              height: questionGroupHeight,
              contentHeight: questionGroupHeight,
            },
          ]
        } else {
          return [
            null,
            {
              content: questionGroupContent,
              height: questionGroupHeight,
              contentHeight: questionGroupHeight,
            },
          ]
        }
      },

      getTemplateElements() {
        return []
      },
    },
  }
</script>
