import {
  apiFetchScoreInterval,
  apiFetchUnionScoreInterval,
  apiFetchScoreIntervalOfSeniorHigh,
  apiFetchUnionScoreIntervalForSeniorHigh,
} from '@/api/report'

import { generateExcelBlob } from '@/utils/excel_export'
import { getInstitutionName } from '../../tools/tools'

import { UUID_ZERO } from '@/const/string'

import Store from '@/store/index'

// 分数段统计
export function generateExcelReportScoreIntervalBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let fileName = Store.getters['report/examName'] + '_'
  let requestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    subjectId: (subject && subject.subjectId) || undefined,
    examSubjectId: (subject && subject.examSubjectId) || undefined,
    ranges: [],
  }

  if (category) {
    const CombinationName = (combination && combination.subjectName) || ''
    requestParams.categoryId = category.categoryId
    requestParams.subjectCode = combination && combination.subjectCode

    if (isMultipleSchoolLevel) {
      if (school) {
        requestParams.schoolId = school.schoolId

        fileName = `学校报表/${school.schoolName}/${category.categoryName}/${
          CombinationName ? CombinationName + '/' : ''
        }${fileName}分数段统计_${school.schoolName}_${category.categoryName}${
          CombinationName ? '_' + CombinationName : ''
        }`
      } else {
        requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']

        fileName = `${category.categoryName}/${
          CombinationName ? CombinationName + '/' : ''
        }${fileName}分数段统计_联考_${category.categoryName}${CombinationName ? '_' + CombinationName : ''}`
      }
    } else {
      requestParams.schoolId = Store.getters['report/currentSchoolId']

      fileName = `${category.categoryName}/${CombinationName ? CombinationName + '/' : ''}${fileName}分数段统计_${
        Store.getters['report/currentSchoolName']
      }_${category.categoryName}${CombinationName ? '_' + CombinationName : ''}`
    }
  } else {
    if (isMultipleSchoolLevel) {
      if (isNextLevel) {
        if (school) {
          fileName = subject
            ? `下级机构或学校报表/(学校)${school.schoolName}/${subject.subjectName}/${fileName}分数段统计_${subject.subjectName}`
            : `下级机构或学校报表/(学校)${school.schoolName}/${fileName}分数段统计_${school.schoolName}`
          requestParams.schoolId = school.schoolId
        } else {
          fileName = subject
            ? `下级机构或学校报表/(机构)${institution.name}/${subject.subjectName}/${fileName}分数段统计_${institution.name}_${subject.subjectName}`
            : `下级机构或学校报表/(机构)${institution.name}/${fileName}分数段统计_${institution.name}`
          requestParams.organizationId = institution.id
        }
      } else {
        if (school) {
          fileName = subject
            ? `学校报表/${school.schoolName}/${subject.subjectName}/${fileName}分数段统计_${subject.subjectName}`
            : `学校报表/${school.schoolName}/${fileName}分数段统计_${school.schoolName}`
          requestParams.schoolId = school.schoolId
        } else {
          fileName = subject
            ? `${subject.subjectName}/${fileName}分数段统计_联考_${subject.subjectName}`
            : `${fileName}分数段统计_联考`
          requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']
        }
      }
    } else {
      fileName = subject
        ? `${subject.subjectName}/${fileName}分数段统计_${Store.getters['report/currentSchoolName']}_${subject.subjectName}`
        : `${fileName}分数段统计_${Store.getters['report/currentSchoolName']}`
      requestParams.schoolId = Store.getters['report/currentSchoolId']
    }
  }

  fileName += '.xlsx'

  let examAttributes = Store.getters['report/examAttributes']
  let subjectFullScore =
    examAttributes && examAttributes.fullScoreMap && examAttributes.fullScoreMap[requestParams.subjectId || 0]

  let subjectSetting
  let localScoreIntervalSetting = JSON.parse(window.localStorage.getItem('report_score_interval'))
  if (localScoreIntervalSetting) {
    let userSetting = localScoreIntervalSetting.find(x => x.userId === Store.getters['user/info'].userId)
    if (userSetting) {
      let examSetting = userSetting.exams.find(x => x.examId === requestParams.examId)
      if (examSetting) {
        let templateSetting = examSetting.templates.find(x => x.templateId === requestParams.templateId)
        if (templateSetting) {
          subjectSetting = templateSetting.examSubjects.find(
            x => x.examSubjectId === (requestParams.examSubjectId || UUID_ZERO)
          )
          if (subjectSetting) {
            subjectSetting = subjectSetting.setting
            subjectSetting.isFixedInterval = subjectSetting.mode !== '自定义分段'
            subjectSetting.isCumulativeInterval = subjectSetting.mode === '固定值分段（累积）'
          }
        }
      }
    }
  }

  if (!subjectSetting) {
    subjectSetting = {
      mode: '固定值分段',
      isFixedInterval: true,
      isCumulativeInterval: false,
      fixedInterval: subjectFullScore && subjectFullScore > 100 ? Math.round(subjectFullScore / 100) * 10 : 10,
    }
  }

  // generate range
  const ReportTemplateSettingScoreIntervals = Store.getters['report/templateSettingScoreIntervals']
  const SubjectTemplateSetting = ReportTemplateSettingScoreIntervals.find(
    x => x.subjectId === ((subject && subject.subjectId) || 0)
  )
  const SubjectTemplateSettingIntervals = (SubjectTemplateSetting && SubjectTemplateSetting.intervals) || []

  if (SubjectTemplateSettingIntervals.length) {
    requestParams.ranges = SubjectTemplateSettingIntervals.map((x, idx) => ({
      name: x.name,
      range: x.range,
      sortCode: idx + 1,
    }))
  } else if (subjectSetting.mode === '自定义分段') {
    requestParams.ranges = subjectSetting.customedRanges
  } else {
    let i = 0
    let j = 0
    let range = ''

    if (subjectSetting.isCumulativeInterval) {
      for (i = subjectSetting.fixedInterval; i < subjectFullScore; i += subjectSetting.fixedInterval, j++) {
        range = `[${i - subjectSetting.fixedInterval}, ${subjectFullScore})`
        requestParams.ranges.push({
          sortCode: j,
          name: range,
          range: range,
        })
      }
    } else {
      for (i = subjectSetting.fixedInterval; i < subjectFullScore; i += subjectSetting.fixedInterval, j++) {
        range = `[${i - subjectSetting.fixedInterval}, ${i})`
        requestParams.ranges.push({
          sortCode: j,
          name: range,
          range: range,
        })
      }
    }

    range = `[${i - subjectSetting.fixedInterval}, ${subjectFullScore}]`
    requestParams.ranges.push({
      sortCode: j,
      name: range,
      range: range,
    })
  }

  return fetchSheets(requestParams)
    .then(sheets => generateExcelBlob(sheets))
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}

async function fetchSheets(requestParams) {
  let reqeustAPI
  if ('categoryId' in requestParams) {
    reqeustAPI = requestParams.organizationId
      ? apiFetchUnionScoreIntervalForSeniorHigh
      : apiFetchScoreIntervalOfSeniorHigh
  } else {
    reqeustAPI = requestParams.organizationId ? apiFetchUnionScoreInterval : apiFetchScoreInterval
  }

  let tableData = await reqeustAPI(requestParams)

  if (tableData && tableData.length) {
    tableData.sort((a, b) => a.sortCode - b.sortCode)
    tableData = tableData.map(x => {
      x.cells.forEach(cell => {
        if (cell.rate == null) {
          cell.rate = cell.countNum > 0 ? cell.num / cell.countNum : 0
        }
      })

      return {
        itemName: getInstitutionName(
          x.cells[0].schoolId,
          x.cells[0].classId,
          x.cells[0].schoolName,
          x.cells[0].className
        ),
        present: x.present || (x.cells[0] && x.cells[0].countNum),
        cells: x.cells
          .sort((a, b) => b.sortCode - a.sortCode)
          .map((y, yIndex) => {
            if (!yIndex) {
              y.range = y.range.replace(')', ']')
            }

            return {
              count: y.num,
              rate: y.rate || 0,
              range: y.range.replace(/[.]0/g, ''),
            }
          }),
      }
    })

    // columns
    let columns = [
      {
        title: '',
        key: 'itemName',
        width: 'auto',
      },
      {
        title: '实考人数',
        key: 'present',
        width: 'auto',
      },
    ]

    tableData[0].cells.forEach(x => {
      columns.push({
        title: x.range,
        children: [
          {
            title: '人数',
            width: 12,
            key: row => {
              const Range = row.cells.find(cell => cell.range === x.range)
              if (Range) {
                const Value = Number(Range.count)
                return isNaN(Value) ? '-' : Value
              } else {
                return '-'
              }
            },
          },
          {
            title: '占比',
            width: 12,
            key: row => {
              const Range = row.cells.find(cell => cell.range === x.range)
              if (Range) {
                const Value = Number(Range.rate)
                return isNaN(Value) ? '-' : Value
              } else {
                return '-'
              }
            },
            cellNumberFormat: value => (isNaN(value) ? undefined : Number.isInteger(value) ? '0%' : '0.##%'),
          },
        ],
      })
    })

    return [
      {
        sheetName: '分数段统计',
        rows: tableData,
        columns: columns,
      },
    ]
  } else {
    throw {
      code: 9998,
      msg: '暂无数据',
    }
  }
}
