import { apiGetScoreGrades } from '@/api/report'

import { groupArray } from '@/utils/array'
import { generateExcelBlob } from '@/utils/excel_export'
import { getScoreRanges } from '../../tools/tools'

import { UUID_ZERO } from '@/const/string'
import statsWays from '@/enum/report/stats_way'

import Store from '@/store/index'

// 科目等级结构分析
export function generateExcelReportScoreGradeBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let fileName = Store.getters['report/examName'] + '_'
  let requestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
  }

  if (category) {
    requestParams.categoryId = category.categoryId
    if (isMultipleSchoolLevel) {
      if (school) {
        requestParams.schoolId = school.schoolId
        fileName = `学校报表/${school.schoolName}/${category.categoryName}/${fileName}科目等级结构分析_${school.schoolName}_${category.categoryName}`
      } else {
        requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']
        fileName = `${category.categoryName}/${fileName}科目等级结构分析_联考_${category.categoryName}`
      }
    } else {
      requestParams.schoolId = Store.getters['report/currentSchoolId']
      fileName = `${category.categoryName}/${fileName}科目等级结构分析_${Store.getters['report/currentSchoolName']}_${category.categoryName}`
    }
  } else {
    if (isMultipleSchoolLevel) {
      if (isNextLevel) {
        if (school) {
          fileName = `下级机构或学校报表/(学校)${school.schoolName}/${fileName}科目等级结构分析_${school.schoolName}`
          requestParams.schoolId = school.schoolId
        } else {
          fileName = `下级机构或学校报表/(机构)${institution.name}/${fileName}科目等级结构分析_${institution.name}`
          requestParams.organizationId = institution.id
        }
      } else {
        if (school) {
          fileName = `学校报表/${school.schoolName}/${fileName}科目等级结构分析_${school.schoolName}`
          requestParams.schoolId = school.schoolId
        } else {
          fileName = `${fileName}科目等级结构分析_联考`
          requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']
        }
      }
    } else {
      fileName = `${fileName}科目等级结构分析_${Store.getters['report/currentSchoolName']}`
      requestParams.schoolId = Store.getters['report/currentSchoolId']
    }
  }

  fileName += '.xlsx'

  return fetchSheets(requestParams)
    .then(sheets => generateExcelBlob(sheets))
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}

async function fetchSheets(requestParams) {
  let scoreRanges = getScoreRanges() || []
  let adaptedRange = scoreRanges.find(range => range.subjectId === 0) || scoreRanges[0]

  let tableColumns = [
    {
      title: '科目',
      key: 'subjectName',
      width: 'auto',
    },
  ]
  let tableData = await apiGetScoreGrades(requestParams)

  if (tableData && tableData.length && adaptedRange && adaptedRange.range) {
    let mapStatsWays = statsWays.getIdNames()
    let subjectList = [
      {
        examSubjectId: UUID_ZERO,
        subjectId: 0,
        subjectName: '总分',
      },
    ].concat(Store.getters['report/templateSubjects'])

    tableData = groupArray(tableData, x => x.examSubjectId)
      .map(x => {
        let scoreGrades = []
        let targetSubject = subjectList.find(subject => subject.examSubjectId === x.key)
        let targetRange = scoreRanges.find(range => range.subjectId === targetSubject.subjectId)
        if (targetRange) {
          targetRange.range.forEach(r => {
            let scoreGradeItems = x.group.filter(g => g.scoreGradeName === r.name)
            let scoreGrade = scoreGradeItems.find(
              item => item.schoolId === (requestParams.organizationId || requestParams.schoolId)
            )
            if (scoreGrade) {
              scoreGrade = {
                scoreRange: r.value,
                scoreGradeNum: scoreGrade.scoreGradeNum,
                scoreGradeName: r.name,
                scoreGradeRate: scoreGrade.scoreGradeRate,
              }
            } else {
              scoreGrade = {
                scoreRange: r.value,
                scoreGradeNum: 0,
                scoreGradeRate: 0,
                scoreGradeName: r.name,
              }
            }
            scoreGrades.push(scoreGrade)
          })
        } else {
          scoreGrades = x.group.map(g => ({
            scoreRange: '(-, -)',
            scoreGradeNum: g.scoreGradeNum,
            scoreGradeRate: g.scoreGradeRate,
            scoreGradeName: g.scoreGradeName,
          }))
        }

        if (scoreGrades.length < adaptedRange.range.length) {
          adaptedRange.rage.forEach(r => {
            if (!scoreGrades.find(g => g.scoreGradeName === r.name)) {
              scoreGrades.push({
                scoreRange: '(-, -)',
                scoreGradeNum: 0,
                scoreGradeRate: 0,
                scoreGradeName: r.name,
              })
            }
          })
        }

        return {
          subjectId: targetSubject.subjectId,
          subjectName: targetSubject.subjectName,
          examSubjectId: targetSubject.examSubjectId,
          scoreGrades: scoreGrades,
        }
      })
      .sort((a, b) => a.subjectId - b.subjectId)

    let statsWayName = mapStatsWays.find(msw => msw.id === adaptedRange.statsWay).name
    if (scoreRanges && scoreRanges.length) {
      tableColumns.push(
        ...adaptedRange.range.map(r => ({
          title: r.name,
          children: [
            {
              title: statsWayName,
              width: 14,
              key: row =>
                (row.scoreGrades.find(x => x.scoreGradeName === r.name) || { scoreRange: '(-, -)' }).scoreRange,
            },
            {
              title: '学生人数',
              width: 14,
              key: row =>
                (row.scoreGrades.find(x => x.scoreGradeName === r.name) || { scoreGradeNum: 0 }).scoreGradeNum,
            },
            {
              title: '学生比例',
              width: 14,
              key: row => {
                const ScoreGrade = row.scoreGrades.find(sg => sg.scoreGradeName === r.name)
                let value = '-'
                if (ScoreGrade) {
                  value = (ScoreGrade.scoreGradeRate && Number(ScoreGrade.scoreGradeRate)) || 0
                }
                return value
              },
              cellNumberFormat: value =>
                isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%',
            },
          ],
        }))
      )
    }

    return [
      {
        sheetName: '科目等级对比',
        rows: tableData,
        columns: tableColumns,
      },
    ]
  } else {
    throw {
      code: 9998,
      msg: '暂无数据',
    }
  }
}
