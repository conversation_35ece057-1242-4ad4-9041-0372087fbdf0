<script>
  import { apiDownloadInstitutionGradeNumber } from '@/api/user/school'

  import { downloadBlob } from '@/utils/download'
  import { formatDateHourMinutesByChinese } from '@/utils/date'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      school: {
        type: Object,
        default: () => {},
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        isExporting: false,
        selectedStageId: 0,
        selectedGradeId: 0,
      }
    },

    computed: {
      schoolStageGradeClassSubjects() {
        return this.$store.state.school?.schoolStageGradeClassSubjects || []
      },
      stages() {
        const Stages = [
          {
            id: 0,
            name: '全部学段',
          },
        ]

        if (this.schoolStageGradeClassSubjects.length) {
          Stages.push(
            ...this.schoolStageGradeClassSubjects.map(s => ({
              id: s.stageId,
              name: s.stageName,
            }))
          )
        }

        return Stages
      },
      grades() {
        const Grades = [
          {
            id: 0,
            name: '全部年级',
          },
        ]

        if (this.selectedStageId) {
          const TargetStage = this.schoolStageGradeClassSubjects.find(s => s.stageId === this.selectedStageId)
          if (TargetStage) {
            Grades.push(
              ...(TargetStage.grades || []).map(g => ({
                id: g.gradeId,
                name: g.gradeName,
              }))
            )
          }
        } else {
          this.schoolStageGradeClassSubjects.forEach(s => {
            ;(s.grades || []).forEach(g => {
              if (!Grades.some(ag => ag.id === g.gradeId)) {
                Grades.push({
                  id: g.gradeId,
                  name: g.gradeName,
                })
              }
            })
          })
        }

        return Grades
      },
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(isVisible) {
        if (isVisible) {
          this.selectedStageId = 0
        } else {
          this.closeModal()
        }
      },

      handleStageIdChange(stageId) {
        this.selectedStageId = stageId
        if (stageId) {
          const TargetStage = this.schoolStageGradeClassSubjects.find(s => s.stageId === this.selectedStageId)
          if (TargetStage) {
            if (!(TargetStage.grades || []).some(g => g.gradeId === this.selectedGradeId)) {
              this.selectedGradeId = 0
            }
          }
        }
      },

      downloadFile() {
        if (this.isExporting) {
          return
        }
        if (!this.school.value) {
          this.$Message.warning({
            duration: 4,
            content: '请求信息加载失败，请刷新重试',
          })
          return
        }

        this.isExporting = true
        apiDownloadInstitutionGradeNumber({
          institutionId: this.school.value,
          gradeLevel: this.selectedStageId || undefined,
          gradeId: this.selectedGradeId || undefined,
        })
          .then(responseBlob =>
            downloadBlob(
              responseBlob,
              `${this.school.title}_下属学校年级学生人数${formatDateHourMinutesByChinese(new Date())}）.xlsx`
            )
          )
          .then(() => this.closeModal())
          .finally(() => (this.isExporting = false))
      },
    },
  }
</script>

<template>
  <Modal :model-value="modelValue" title="导出下属学校年级学生人数" @on-visible-change="handleModalVisibleChange">
    <div class="modal-export-institution-grade-student-number">
      <Form :label-width="60">
        <Form-item label="学段">
          <Select :model-value="selectedStageId" transfer @on-change="handleStageIdChange">
            <Option v-for="s of stages" :key="'stage' + s.id" :value="s.id">{{ s.name }}</Option>
          </Select>
        </Form-item>
        <Form-item label="年级">
          <Select v-model="selectedGradeId" transfer>
            <Option v-for="g of grades" :key="'grade' + g.id" :value="g.id">{{ g.name }}</Option>
          </Select>
        </Form-item>
      </Form>
    </div>

    <template #footer>
      <Button type="text" @click="closeModal">取消</Button>
      <Button type="primary" :loading="isExporting" @click="downloadFile">{{
        isExporting ? '正在导出' : '导出'
      }}</Button>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-export-institution-grade-student-number {
    padding-right: 16px;
  }
</style>
