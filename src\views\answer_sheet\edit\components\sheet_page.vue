<template>
  <div class="sheet-page" :style="pageStyle">
    <com-page-header :page="page"></com-page-header>

    <div class="page-body" :style="pageBodyStyle">
      <component
        :is="componentMap[block.type]"
        v-for="block in page.blocks"
        :key="block.id"
        class="page-block"
        :block="block"
        style="position: relative"
        :style="{ height: block.height + 'px' }"
        @height-change="handleHeightChange"
      ></component>
    </div>

    <com-page-footer :page="page"></com-page-footer>
  </div>
</template>

<script>
  import comPageHeader from './page_header.vue'
  import comPageFooter from './page_footer.vue'
  import comBlockExamInfo from './block_header.vue'
  import comBlockDescription from './block_description.vue'
  import comBlockObjective from './block_objective.vue'
  import comBlockFillBlank from './block_fill_blank.vue'
  import comBlockEssay from './block_essay.vue'
  import comBlockChineseWriting from './block_chinese_writing.vue'
  import comBlockEnglishWriting from './block_english_writing.vue'
  import comBlockTrunk from './block_trunk.vue'
  import comBlockContentObjective from './block_objective_content.vue'
  import comBlockContentObjectiveNoOption from './block_objective_content_no_option.vue'

  export default {
    components: {
      comPageHeader,
      comPageFooter,
      comBlockExamInfo,
      comBlockDescription,
      comBlockObjective,
      comBlockFillBlank,
      comBlockEssay,
      comBlockChineseWriting,
      comBlockEnglishWriting,
      comBlockTrunk,
      comBlockContentObjective,
      comBlockContentObjectiveNoOption,
    },
    props: {
      page: {
        type: Object,
        required: true,
      },
    },
    emits: ['height-change'],
    data() {
      return {
        componentMap: {
          ExamInfo: 'comBlockExamInfo',
          Description: 'comBlockDescription',
          Objective: 'comBlockObjective',
          FillBlank: 'comBlockFillBlank',
          Essay: 'comBlockEssay',
          ChineseWriting: 'comBlockChineseWriting',
          EnglishWriting: 'comBlockEnglishWriting',
          Trunk: 'comBlockTrunk',
          ContentObjective: 'comBlockContentObjective',
          ContentObjectiveNoOption: 'comBlockContentObjectiveNoOption',
        },
      }
    },
    computed: {
      pageSize() {
        return this.$store.getters['answerSheet/pageSize'](this.page.pageIndex)
      },
      baseStyle() {
        return this.$store.getters['answerSheet/baseStyle']
      },
      pageStyle() {
        return {
          ...this.baseStyle,
          width: this.pageSize.width + 'px',
          height: this.pageSize.height + 'px',
          position: 'relative',
          userSelect: 'none',
          backgroundColor: 'white',
          boxSizing: 'border-box',
        }
      },
      pageBodyStyle() {
        return {
          position: 'absolute',
          left: this.pageSize.left + 'px',
          top: this.pageSize.top + 'px',
          right: this.pageSize.right + 'px',
          bottom: this.pageSize.bottom + 'px',
        }
      },
    },
    methods: {
      handleHeightChange() {
        this.$emit('height-change')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .sheet-page {
    margin: 20px auto;
    background-color: white;

    :deep(*) {
      box-sizing: border-box;
    }

    :deep(.page-block-toolbar) {
      position: absolute;
      top: -1px;
      right: -24px;
      display: none;
      width: 24px;
      height: calc(100% + 2px);

      .btn {
        box-sizing: content-box;
        height: 24px;
        color: $color-icon;
        font-size: 20px;
        line-height: 24px;
        text-align: center;
        background-color: #f0f0f0;
        cursor: pointer;

        &:not(:first-child) {
          border-top: 1px solid $color-border;
        }
      }

      .btn-primary:hover {
        color: $color-primary;
      }

      .btn-warning:hover {
        color: $color-warning;
      }

      .btn-error:hover {
        color: $color-error;
      }

      .btn-split {
        background-image: url('~@/assets/images/answer_sheet/split_gray.svg');
        background-repeat: no-repeat;
        background-position: top 2px left 2px;
        background-size: 20px 20px;

        &:hover {
          background-image: url('~@/assets/images/answer_sheet/split_primary.svg');
        }
      }
    }

    :deep(.page-block:hover .page-block-toolbar) {
      display: block;
    }

    :deep(.merge-area) {
      position: absolute;
      top: -5px;
      right: 50px;
      left: 50px;
      height: 10px;
      text-align: center;
      cursor:
        url('~@/assets/images/answer_sheet/merge_primary.svg') 15 15,
        pointer;
    }
  }
</style>
