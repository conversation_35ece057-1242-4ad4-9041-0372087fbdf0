/**
 * OMR区域
 */

import TemplateRect from '@/helpers/scan_template_define/template_rect'
export default class TemplateOmrArea {
  constructor() {
    // omr区域id
    this.id = ''
    // 选项方向: H-水平，V-垂直
    this.direction = ''
    // 选项个数
    this.optionCount = 0
    // 选项列表
    // Array[Rect]
    this.options = []
  }

  static import(outer) {
    let omr = new TemplateOmrArea()
    omr.id = outer.id
    omr.direction = outer.direction
    omr.optionCount = outer.optionCount
    omr.options = outer.options.map(opt => new TemplateRect(opt))
    return omr
  }
}
