import ajax from '@/api/ajax'

export function apiCreateStagedReport(params) {
  return ajax
    .post({
      url: 'report/stageReport/create',
      data: params,
    })
    .then(data => {
      return data
    })
}

export function apiGetStagedReportList(params) {
  return ajax
    .get({
      url: 'report/stageReport/list',
      params: params,
      requestName: '获取阶段报告列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetStagedReportExamList(params) {
  return ajax
    .get({
      url: 'report/stageReport/listExam',
      params: params,
      requestName: '获取阶段报告可选项目',
    })
    .then(data => {
      return data
    })
}

export function apiDeleteStagedReport(params) {
  return ajax
    .delete({
      url: 'report/stageReport/delete',
      params: params,
      requestName: '删除阶段报告可选项目',
    })
    .then(data => {
      return data
    })
}

export function apiGenerateStagedReport(params) {
  return ajax
    .put({
      url: 'report/stageReport/generate',
      params: params,
      requestName: '生成阶段报告可选项目',
    })
    .then(data => {
      return data
    })
}

export function apiUpdateStagedReport(params) {
  return ajax
    .put({
      url: 'report/stageReport/updateName',
      params: params,
      requestName: '修改阶段报告可选项目名称',
    })
    .then(data => {
      return data
    })
}

export function apiGetStagedReportDetails(params) {
  return ajax
    .get({
      url: 'report/stageReport/get',
      params: params,
      requestName: '获取阶段报告详情',
    })
    .then(data => {
      return data
    })
}

export function apiGetStagedReportStudent(params) {
  return ajax
    .get({
      url: 'report/stageReport/listStudent',
      params: params,
      requestName: '获取阶段报告学生',
    })
    .then(data => {
      return data
    })
}

export function apiGetStagedReportClass(params) {
  return ajax
    .get({
      url: 'report/stageReport/listClass',
      params: params,
      requestName: '获取阶段报告班级',
    })
    .then(data => {
      return data
    })
}

export function apiGetStagedReportStudentPdf(params) {
  return ajax
    .get({
      url: 'report/stageReport/student/pdf',
      params: params,
      requestName: '获取阶段报告学生pdf链接',
    })
    .then(data => {
      return data
    })
}

export function apiGetStagedReportClassPdf(params) {
  return ajax
    .get({
      url: 'report/stageReport/class/pdf',
      params: params,
      requestName: '获取阶段报告班级pdf链接',
    })
    .then(data => {
      return data
    })
}

export function apiGetStagedReportClasses(params) {
  return ajax
    .get({
      url: 'report/stageReport/classList',
      params: params,
      requestName: '获取阶段报告查询班级',
    })
    .then(data => {
      return data
    })
}

export function apiGetStagedReportZip(params) {
  return ajax
    .post({
      url: 'report/stageReport/zip',
      data: params.classIds,
      params: {
        stageId: params.stageId,
      },
      requestName: '按班级导出阶段报告',
    })
    .then(data => {
      return data
    })
}

export function apiGetStagedReportClassZip(params) {
  return ajax
    .get({
      url: 'report/stage/lectureNotes/zip',
      params: {
        stageId: params.stageId,
      },
      requestName: '按班级导出阶段讲义',
    })
    .then(data => {
      return data
    })
}

export function apiStagedReportUpdate(params) {
  return ajax
    .post({
      url: 'report/stageReport/update',
      data: params,
    })
    .then(data => {
      return data
    })
}

export function apiGetReportSchool(params) {
  return ajax
    .get({
      url: 'report/customReport/listSchool',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiGetReportSchoolNormalExam(params) {
  return ajax
    .get({
      url: 'report/customReport/listExamForSchool',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiGetReportSubjectList(params) {
  return ajax
    .get({
      url: 'report/customReport/listSubject',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiGetReportSchoolUnionExam(params) {
  return ajax
    .get({
      url: 'report/customReport/listUnionExam',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiSingleReportCreate(params) {
  return ajax
    .post({
      url: 'report/customReport/create',
      data: params,
    })
    .then(data => {
      return data
    })
}

export function apiGetReportListSingle(params) {
  return ajax
    .get({
      url: 'report/customReport/list',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiGetUnionExamSchool(params) {
  return ajax
    .get({
      url: 'report/customReport/listSchool',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiGetSingleReportConfig(params) {
  return ajax
    .get({
      url: 'report/customReport/detail',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiSingleReportAddSubject(params) {
  return ajax
    .post({
      url: 'report/customReport/addSubject',
      params: {
        reportId: params.reportId,
      },
      data: params.subjects,
    })
    .then(data => {
      return data
    })
}

export function apiSingleReportUpdateName(params) {
  return ajax
    .post({
      url: 'report/customReport/updateName',
      params: {
        id: params.id,
        name: params.name,
      },
    })
    .then(data => {
      return data
    })
}

export function apiGetSingleReportDelete(params) {
  return ajax
    .delete({
      url: 'report/customReport/delete',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiGetSingleReportSubjectDelete(params) {
  return ajax
    .delete({
      url: 'report/customReport/deleteSubject',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiGetSingleReportSchools(params) {
  return ajax
    .get({
      url: 'report/customReport/listReportSchool',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiGetSingleReportSchoolDelete(params) {
  return ajax
    .delete({
      url: 'report/customReport/deleteSchool',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiSingleReportAddSchool(params) {
  return ajax
    .post({
      url: 'report/customReport/addSchool',
      params: {
        reportId: params.reportId,
      },
      data: params.schoolIds,
    })
    .then(data => {
      return data
    })
}

export function apiSingleReportGenerate(params) {
  return ajax
    .post({
      url: 'report/customReport/generatePdf',
      params: {
        reportId: params.reportId,
        subjectId: params.subjectId,
      },
    })
    .then(data => {
      return data
    })
}

export function apiSingleReportGenerateReset(params) {
  return ajax
    .post({
      url: 'report/customReport/reset',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiGetSingleReportStudent(params) {
  return ajax
    .get({
      url: 'report/customReport/listStudent',
      params: params,
      requestName: '获取单次报告学生',
    })
    .then(data => {
      return data
    })
}

export function apiGetSingleReportQueryClasses(params) {
  return ajax
    .get({
      url: 'report/customReport/listQueryClass',
      params: params,
    })
    .then(data => {
      return data
    })
}

export function apiGetSingleReportQuerySchools(params) {
  return ajax
    .get({
      url: 'report/customReport/listQuerySchool',
      params: params,
    })
    .then(data => {
      return data
    })
}

export function apiGetSingleReportClasses(params) {
  return ajax
    .get({
      url: 'report/customReport/listClass',
      params: params,
      requestName: '获取单次报告班级',
    })
    .then(data => {
      return data
    })
}

export function apiGetSingleReportStudentPdfZip(params) {
  return ajax
    .get({
      url: 'report/customReport/studentPdfZip',
      params: params,
    })
    .then(data => {
      return data
    })
}

export function apiGetSingleReportClassPdfZip(params) {
  return ajax
    .get({
      url: 'report/customReport/classPdfZip',
      params: params,
    })
    .then(data => {
      return data
    })
}

export function apiGetSingleReportRoleList(params) {
  return ajax
    .get({
      url: 'report/customReport/listRole',
      params: params,
    })
    .then(data => {
      return data
    })
}

export function apiSingleReportOpenToStudentUpdate(params) {
  return ajax
    .post({
      url: 'report/customReport/updateOpenToStudent',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiSingleReportRoleAdd(params) {
  return ajax
    .post({
      url: 'report/customReport/addRoles',
      params: {
        id: params.id,
      },
      data: params.roles,
    })
    .then(data => {
      return data
    })
}

export function apiSingleReportUpdateSubjectSetting(params) {
  return ajax
    .post({
      url: 'report/customReport/updateSubjectShowOriginalPaper',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiSingleReportUpdateIncludeStudentReport(params) {
  return ajax
    .post({
      url: 'report/customReport/updateSubjectIncludeStudentReport',
      params,
    })
    .then(data => {
      return data
    })
}

export function apiPublishCustomReport(params) {
  return ajax
    .put({
      url: 'report/customReport/publish',
      params: params,
      requestName: '发布报告',
    })
    .then(data => {
      return data
    })
}

export function apiUnPublishCustomReport(params) {
  return ajax
    .put({
      url: 'report/customReport/unpublish',
      params: params,
      requestName: '取消发布报告',
    })
    .then(data => {
      return data
    })
}

export function apiPublishStageReport(params) {
  return ajax
    .put({
      url: 'report/stageReport/publish',
      params: params,
      requestName: '发布报告',
    })
    .then(data => {
      return data
    })
}

export function apiUnPublishStageReport(params) {
  return ajax
    .put({
      url: 'report/stageReport/unpublish',
      params: params,
      requestName: '取消发布报告',
    })
    .then(data => {
      return data
    })
}
