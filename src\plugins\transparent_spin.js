import TransparentSpin from '@/components/transparent_spin.vue'
import { createApp } from 'vue'

let spinInstance
let timer = 0

function create() {
  if (timer) {
    clearTimeout(timer)
    timer = 0
  }

  if (spinInstance) {
    return
  }

  let elSpinContainer = document.createElement('div')
  document.body.appendChild(elSpinContainer)

  spinInstance = createApp(TransparentSpin)
  spinInstance.mount(elSpinContainer)
}

function createDelay(delay) {
  if (timer) {
    return
  }
  timer = setTimeout(() => {
    create()
  }, delay)
}

function destroy() {
  if (timer) {
    clearTimeout(timer)
    timer = 0
  }

  if (!spinInstance) {
    return
  }

  let el = spinInstance.$el

  spinInstance.unmount()
  spinInstance = null

  if (el && el.parentNode === document.body) {
    document.body.removeChild(el)
  }
}

export default {
  install(app) {
    app.config.globalProperties.$TransparentSpin = {
      show: function () {
        create()
      },
      showDelay: function (delay = 500) {
        createDelay(delay)
      },
      hide: function () {
        destroy()
      },
    }
  },
}
