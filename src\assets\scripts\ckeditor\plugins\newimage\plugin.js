﻿;(function () {
  window.CKEDITOR.plugins.add('newimage', {
    icons: 'newimage',
    init: function (editor) {
      editor.addCommand('insertNewImage', {
        exec: function (editor) {
          selectLocalImage(function (html) {
            editor.insertHtml(html)
          })
        },
      })
      editor.ui.addButton('NewImage', {
        label: '插入图片',
        command: 'insertNewImage',
        toolbar: 'insert',
      })
      editor.on('paste', function (evt) {
        var data = evt.data
        // 一、粘贴图片文件
        if (!data.dataValue) {
          if (data.dataTransfer._.files[0]) {
            convertImageBlobToHtml(data.dataTransfer._.files[0]).then(function (html) {
              editor.insertHtml(html)
            })
          }
          return
        }
        // 二、粘贴html
        var html = data.dataValue
        var transferData = data.dataTransfer._.data
        // 二、1 从editor复制，删除开头的ckeditor添加的span标签
        if ('cke/id' in transferData) {
          var match = html.match(/^<span[^>]+display\s*:\s*none[^>]+>[^<]*<\/span>/)
          if (match) {
            data.dataValue = html.substring(match[0].length)
          }
          return
        }
        // 二、2 其他来源，处理图片，清理html
        html = handleImage(html)
        html = cleanHTML(html)
        data.dataValue = html || ''
      }, null, null, 9)
      // 粘贴事件后续处理会请求图片，故将优先级设为9
    },
  })

  // 选择本地图片
  function selectLocalImage(callback) {
    var elInput = document.createElement('input')
    elInput.type = 'file'
    elInput.accept = '.png,.jpg'
    elInput.click()
    elInput.onchange = function (e) {
      var files = e.target.files
      var file = files && files[0]
      var maxImageFileSizeInKB = 500
      var maxImageFileSize = 1024 * maxImageFileSizeInKB
      if (!file) {
        alert('未选择文件')
      } else if (!/image/i.test(file.type)) {
        alert('请选择图片文件')
      } else if (file.size > maxImageFileSize) {
        alert('图片文件不要超过' + maxImageFileSizeInKB + 'K')
      } else {
        convertImageBlobToHtml(file).then(callback)
      }
    }
  }

  // 图片blob转为html
  function convertImageBlobToHtml(blob) {
    return blob2DataUrl(blob)
      .then(loadImage)
      .then(initImageSize)
      .then(function (img) {
        return img.outerHTML
      })
  }

  // blob转为dataurl
  function blob2DataUrl(blob) {
    return new Promise(function (resolve, reject) {
      var reader = new FileReader()
      reader.onload = function () {
        resolve(reader.result)
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  // 加载图片
  function loadImage(src, anonymous) {
    return new Promise(function (resolve, reject) {
      var img = document.createElement('img')
      if (anonymous) {
        img.crossOrigin = 'anonymous'
      }
      img.onload = function () {
        resolve(img)
      }
      img.onerror = reject
      img.src = src
    })
  }

  // 设置图片初始大小
  function initImageSize(img) {
    return new Promise(function (resolve) {
      var maxWidth = 600,
        maxHeight = 100
      var scaleX = img.naturalWidth / maxWidth
      var scaleY = img.naturalHeight / maxHeight
      if (scaleX <= 1 && scaleY <= 1) {
        img.width = img.naturalWidth
        img.height = img.naturalHeight
      } else if (scaleX < scaleY) {
        img.width = img.naturalWidth / scaleY
        img.height = maxHeight
      } else {
        img.width = maxWidth
        img.height = img.naturalHeight / scaleX
      }
      resolve(img)
    })
  }

  // 清理粘贴的html
  function cleanHTML(html) {
    if (!window._qdomClone) {
      return ''
    }
    var div = document.createElement('div')
    div.innerHTML = html
    // 公式当作文本
    var formulas = div.querySelectorAll('span.math-formula')
    formulas.forEach(function (node) {
      node.before(document.createTextNode(node.outerHTML))
      node.remove()
    })
    var frag = new DocumentFragment()
    Array.from(div.childNodes).forEach(function (node) {
      frag.append(node)
    })
    var qdom = window._qdomClone(frag)
    return qdom.innerHTML
  }

  // 处理图片
  function handleImage(html) {
    var fragment = CKEDITOR.htmlParser.fragment.fromHtml(html)
    var toRemoveImgs = []
    fragment.children.forEach(function removeOutSrcImage(node) {
      if (node.name == 'img') {
        // 跳过本网站图片
        var src = node.attributes.src || ''
        if (src.startsWith('http://') || src.startsWith('https://')) {
          var url = new URL(src)
          if (url.hostname.includes('igrade.cn')) {
            return
          }
        }
        toRemoveImgs.push(node)
      } else {
        if (node.children && node.children.length > 0) {
          node.children.forEach(removeOutSrcImage)
        }
      }
    })
    toRemoveImgs.forEach(img => {
      img.remove()
    })
    var writer = new CKEDITOR.htmlParser.basicWriter()
    fragment.writeHtml(writer)
    return writer.getHtml()
  }
})()
