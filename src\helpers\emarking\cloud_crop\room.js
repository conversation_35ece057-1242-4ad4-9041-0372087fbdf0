import JSZip from 'jszip'
import { loadImageBlob } from '@/utils/promise'

export function fetchRoomImagesZipBlob(paperInfos) {
  let zip = new JSZip()

  // function
  function generateBlobInPaperInfo(info) {
    let fileNamePrefix = `${info.admissionNum}(${info.studentName})`
    if (info.serialNo) {
      fileNamePrefix += `-${info.serialNo}`
    }
    return Promise.all(info.pagerUrls.map(loadImageBlob))
      .then(blobs => {
        blobs.forEach((b, bIndex) => {
          zip.file(fileNamePrefix + '-' + (bIndex + 1) + '.png', b)
        })
        return {
          info: fileNamePrefix,
        }
      })
      .catch(err => {
        throw {
          info: fileNamePrefix,
          err: err,
        }
      })
  }

  return Promise.allSettled(paperInfos.map(info => generateBlobInPaperInfo(info)))
    .then(async result => {
      let blob = await zip.generateAsync({ type: 'blob' })
      return {
        blob: blob,
        successCount: (result || []).filter(x => x.status === 'fulfilled').length,
        failedList: (result || [])
          .filter(x => x.status === 'rejected')
          .map(x => ({
            error: x.reason.err,
            fileName: x.reason.info,
          })),
      }
    })
    .catch(err => {
      throw '生成zipBlob失败' + (err ? '：' + err : '')
    })
}
