import ajax from '@/api/ajax'

export function apiGetSystemLog(data) {
  return ajax.post({
    url: 'user/log/all/page',
    params: {
      pageNo: data.currentPage,
      pageSize: data.pageSize,
    },
    data: {
      beginTime: data.beginTime,
      endTime: data.endTime,
      identity: data.identity,
      key: data.keyword,
      logType: data.logType,
      operateType: data.operationType,
      responseOk: data.responseOk,
      schoolId: data.schoolId,
    },
    requestName: '获取系统日志',
  })
}

export function apiGetSchoolLog(data) {
  return ajax.post({
    url: 'user/log/page',
    params: {
      pageNo: data.currentPage,
      pageSize: data.pageSize,
    },
    data: {
      beginTime: data.beginTime,
      endTime: data.endTime,
      identity: data.identity,
      key: data.keyword,
      logType: data.logType,
      operateType: data.operationType,
      responseOk: data.responseOk,
      schoolId: data.schoolId,
    },
    requestName: '获取系统日志',
  })
}

export function apiDeleteLog(ids) {
  return ajax.delete({
    url: 'user/log/deleteBatch',
    data: ids,
    requestName: '删除系统日志',
  })
}
