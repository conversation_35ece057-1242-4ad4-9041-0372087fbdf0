import {
  apiGetSchoolCompare,
  apiGetClassCompare,
  apiOrgGetSameLevelSubjCompare,
  apiOrgGetNextLevelSubjCompare,
  apiOrgGetNextLevelSubjCombinationCompare,
  apiGetSchoolSubjCombinationCompare,
  apiGetClassSubjCombinationCompare,
} from '@/api/report'

import Store from '@/store'

export function generateExcelNextLevelCompareSheetsStruct(pageData = []) {
  const Sheets = []

  if (Store.getters['report/isSeniorHighSchoolSubjectCombinationSelectableProject']) {
  } else {
    const CurrentExamSubject = Store.getters['report/currentExamSubject']
    console.log('current exam subject: ', CurrentExamSubject)
  }

  return Sheets
}
