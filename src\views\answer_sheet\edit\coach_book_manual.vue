<template>
  <SheetEdit
    :answer-sheet="answerSheet"
    :source-type="sourceType"
    :source-id="''"
    @save-sheet="saveSheet"
    @download-sheet="downloadSheet"
    @page-back="backToList"
  ></SheetEdit>
</template>

<script>
  import SheetEdit from './edit'

  import {
    apiGetAnswerSheetNewId,
    apiGetAnswerSheetDetail,
    apiGetAnswerSheetPDFUrl,
    apiGetAnswerSheetInfo,
    apiSaveCoachBookAnswerSheet,
  } from '@/api/emarking/answer_sheet'
  import { apiGetCoachBookInfo } from '@/api/qlib/coach_book'

  import { mapGetters, mapMutations } from 'vuex'
  import { downloadUrl } from '@/utils/download'
  import { parseQuestionsAndBlocks } from '@/helpers/answer_sheet/util'

  import SourceTypeEnum from '@/enum/answer_sheet/source_type'
  import { SheetVersion } from '@/const/answer_sheet'

  export default {
    components: {
      SheetEdit,
    },
    data() {
      return {
        // 当前答题卡
        answerSheet: null,
        // 教辅
        coachBookInfo: null,
      }
    },
    computed: {
      ...mapGetters('answerSheet', ['sheetName']),
      sheetId() {
        let id = this.$route.params.sheetId
        if (id == 'new') {
          id = ''
        }
        return id
      },
      coachBookId() {
        return this.$route.params.coachBookId
      },
      isNewAnswerSheet() {
        return !this.sheetId
      },
      sourceType() {
        return SourceTypeEnum.Manual.id
      },
    },
    created() {
      this.resetSheet()
      this.init()
    },
    methods: {
      ...mapMutations('answerSheet', [
        'resetSheet',
        'createFromManual',
        'setCoachBookInfo',
        'initCoachBookStyle',
        'extractCoachBookInfo',
        'generatePages',
      ]),
      async init() {
        this.showLoadingSpin()
        try {
          await this.loadCoachBook()
          if (this.isNewAnswerSheet) {
            await this.createNewSheet()
          } else {
            await this.loadSheet()
            await this.checkAndImportSheet()
          }
        } finally {
          this.$Spin.hide()
        }
      },
      showLoadingSpin() {
        this.$Spin.show({
          render: h =>
            h(
              'div',
              {
                style: {
                  fontSize: '24px',
                },
              },
              '正在加载答题卡，请稍候'
            ),
        })
      },

      /**
       * 创建新卡
       */
      createNewSheet() {
        return apiGetAnswerSheetNewId().then(newId => {
          this.createFromManual({ newId, coachBookInfo: this.coachBookInfo })
          this.setCoachBookInfo(this.coachBookInfo)
          this.initCoachBookStyle()
          this.generatePages()
        })
      },

      /**
       * 加载数据
       */
      loadCoachBook() {
        return apiGetCoachBookInfo(this.coachBookId).then(coachBookInfo => {
          let stageSubjects = this.$store.getters['qlib/stageSubjects']()
          let stage = stageSubjects.find(s => s.id == coachBookInfo.stageId)
          if (stage) {
            let subject = stage.subjects.find(s => s.id == coachBookInfo.subjectId)
            coachBookInfo.stageName = (stage && stage.name) || ''
            coachBookInfo.subjectName = (subject && subject.name) || ''
          }
          this.coachBookInfo = coachBookInfo
        })
      },
      loadSheet() {
        return apiGetAnswerSheetDetail(this.sheetId).then(res => {
          this.answerSheet = res
        })
      },

      /**
       * 导入与生成
       */
      async checkAndImportSheet() {
        // 检查答题卡
        let sheet
        try {
          sheet = JSON.parse(this.answerSheet.sheetJson)
        } catch (err) {
          this.handleSheetError('答题卡结构损坏', '抱歉！由于未知原因，答题卡结构已被损坏')
          return
        }
        if (sheet.version !== SheetVersion) {
          // 上一个版本可选导入，更旧版本不可导入
          if (sheet.version + 1 == SheetVersion) {
            let back = await this.handlePreviousVersion()
            if (back) {
              this.backToList()
              return
            }
          } else {
            this.handleSheetError('答题卡版本不一致', '本答题卡不是由当前版本的答题卡制作工具制作的，无法编辑。')
            return
          }
        }

        let { objectiveQuestions, subjectiveQuestions, blocks } = parseQuestionsAndBlocks({
          objectivesJson: this.answerSheet.objectivesJson,
          blocksJson: this.answerSheet.blocksJson,
          sheet,
        })

        // 导入答题卡
        try {
          await this.$store.dispatch('answerSheet/importSheet', {
            sheet,
            objectiveQuestions,
            subjectiveQuestions,
            blocks,
            enableEditQuestion: true,
            from: this.sourceType,
          })
        } catch (err) {
          this.$Message.error({
            content: '导入答题卡失败',
          })
          throw err
        }
        this.setCoachBookInfo(this.coachBookInfo)
        this.extractCoachBookInfo()
        this.initCoachBookStyle()
        this.generatePages()
      },
      handleSheetError(title, content) {
        this.$Modal.error({
          title: `${title}`,
          content: `<strong>${content}</strong>`,
          okText: '退出编辑',
          onOk: () => {
            this.backToList()
          },
        })
      },
      handlePreviousVersion() {
        this.$Spin.hide()
        return new Promise(resolve => {
          this.$Modal.confirm({
            title: '答题卡版本不一致',
            width: 430,
            content: `本答题卡不是由当前版本的答题卡制作工具制作的。
            <br><br>您可以：
            <br>1. 退出编辑，或：
            <br>2. 继续编辑答题卡，<strong>原答题卡版式将发生变化</strong>，需要您检查调整`,
            okText: '退出编辑',
            cancelText: '继续编辑',
            onOk: () => {
              resolve(true)
            },
            onCancel: () => {
              this.showLoadingSpin()
              resolve(false)
            },
          })
        })
      },

      /**
       * 按钮事件
       */
      async saveSheet(data) {
        data.coachBookId = this.$route.params.coachBookId
        data.sheetType = 3
        try {
          let newSheetId = await apiSaveCoachBookAnswerSheet(data)
          this.$Message.success({
            content: '保存教辅答题卡成功',
          })
          // 新答题卡需更改路由
          if (this.isNewAnswerSheet) {
            await this.$router.replace({
              name: 'answerSheet-coachBookEditManual',
              params: {
                sheetId: newSheetId,
                coachBookId: this.coachBookId,
              },
            })
            // 重新加载答题卡
            await this.loadSheet()
          } else {
            this.answerSheet.sheetJson = data.sheetJson
            this.answerSheet.objectivesJson = data.objectivesJson
            this.answerSheet.blocksJson = data.blocksJson
          }
        } finally {
          this.$Spin.hide()
        }
      },
      async downloadSheet() {
        this.$Spin.show({
          render: h =>
            h(
              'span',
              {
                style: {
                  fontSize: '24px',
                },
              },
              '下载中，请稍候'
            ),
        })
        let sheetInfo = await apiGetAnswerSheetInfo(this.sheetId)
        let pdfUrl = await apiGetAnswerSheetPDFUrl(this.sheetId)
        let fileName = `${sheetInfo.name}.pdf`
        if (sheetInfo && pdfUrl) {
          downloadUrl(pdfUrl, fileName, () => {
            this.$Spin.hide()
          })
        } else {
          this.$Spin.hide()
        }
      },
      backToList() {
        this.$router.back()
        // this.$router.replace({
        //   name: 'answerSheet-list',
        // })
      },
    },
  }
</script>
