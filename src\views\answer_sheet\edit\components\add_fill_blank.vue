<template>
  <div class="add-fill-blank">
    <Form :label-width="80">
      <FormItem label="大题号" class="ivu-form-item-required">
        <Select :model-value="topicCode" class="input-short" @on-change="changeTopicCode">
          <Option v-for="t in topicChineseCodes" :key="t.code" :value="t.code">{{ t.codeInChinese }}</Option>
        </Select>
      </FormItem>
      <FormItem label="大题名" class="ivu-form-item-required">
        <Input v-model="topicName" :disabled="isExistTopic" maxlength="20"></Input>
      </FormItem>
      <FormItem label="题号" class="ivu-form-item-required">
        <InputNumber
          v-model="questionCodeFrom"
          class="input-short"
          :min="1"
          :precision="0"
          @on-change="generateQuestions"
        ></InputNumber>
        <span>——</span>
        <InputNumber
          v-model="questionCodeTo"
          class="input-short"
          :min="1"
          :precision="0"
          @on-change="generateQuestions"
        ></InputNumber>
      </FormItem>
      <FormItem label="每行空数" class="ivu-form-item-required">
        <InputNumber v-model="blanksPerRow" :min="1" :max="5" :precision="0" class="input-short"></InputNumber>
      </FormItem>
      <FormItem label="分数" class="ivu-form-item-required">
        <InputNumber v-model="score" :min="0" class="input-short" @on-change="changeScore"></InputNumber>
      </FormItem>
    </Form>
    <Table :columns="tableColumns" :data="questions" border></Table>
  </div>
</template>

<script>
  import { Input } from 'view-ui-plus'
  import TextButton from '@/components/text_button'

  import { mapGetters } from 'vuex'
  import SubjectiveQuestion from '@/helpers/emarking/subjective_question'
  import { intersection, groupArray } from '@/utils/array'
  import { roundScore, sumByDefaultZero } from '@/utils/math'
  import { randomId } from '@/utils/string'
  import { isPositiveInteger, isPositiveFinite, numberToChinese } from '@/utils/number'
  import BranchTypeEnum from '@/enum/qlib/branch_type'
  import { Max_Question_Code } from '@/const/emarking'

  export default {
    props: {
      branchTypeId: Number,
      topicChineseCodes: Array,
    },
    data() {
      return {
        topicCode: 1,
        topicName: '',
        questionCodeFrom: 1,
        questionCodeTo: null,
        score: null,
        blanksPerRow: 3,
        questions: [],
      }
    },
    computed: {
      ...mapGetters('answerSheet', ['questionCodes', 'maxQuestionCode', 'topicCodeNames', 'maxTopicCode']),
      isExistTopic() {
        return this.topicCodeNames.some(t => t.topicCode == this.topicCode)
      },
      questionsGroupByCode() {
        return groupArray(this.questions, q => q.questionCode).map(g => ({
          questionCode: g.key,
          branches: g.group,
        }))
      },
      tableColumns() {
        let scoreRender = (h, params) => {
          let rows = []
          params.row.blankScores.forEach((item, idx) => {
            let children = []
            children.push(
              h(
                'span',
                {
                  style: {
                    display: 'inline-block',
                    width: '2.2em',
                  },
                },
                params.row.blankScores.length > 1 ? `空${idx + 1}:` : ''
              )
            )
            children.push(
              h(Input, {
                size: 'small',
                type: 'number',
                modelValue: item.score,
                min: 0,
                style: {
                  width: '80px',
                },
                onOnChange: e => {
                  this.handleChangeBlankScore(params.row, idx, e)
                },
              })
            )
            rows.push(
              h(
                'div',
                {
                  style: {
                    lineHeight: '28px',
                  },
                },
                children
              )
            )
          })
          return h('div', {}, rows)
        }

        let actionRender = (h, params) => {
          let btnRow1 = [
            h(
              TextButton,
              {
                type: 'primary',
                onClick: () => {
                  this.handleAddBranch(params.row)
                },
              },
              () => '加小题'
            ),
            h(
              TextButton,
              {
                type: 'warning',
                disabled: this.questions.filter(q => q.questionCode == params.row.questionCode).length <= 1,
                onClick: () => {
                  this.handleDeleteBranch(params.row)
                },
              },
              () => '删小题'
            ),
          ]
          let btnRow2 = [
            h(
              TextButton,
              {
                type: 'primary',
                onClick: () => {
                  this.handleAddBlank(params.row)
                },
              },
              () => '加小空'
            ),
            h(
              TextButton,
              {
                type: 'warning',
                disabled: params.row.blankScores.length <= 1,
                onClick: () => {
                  this.handleDeleteBlank(params.row)
                },
              },
              () => '删小空'
            ),
          ]
          return h('div', {}, [h('div', {}, btnRow1), h('div', {}, btnRow2)])
        }

        let columns = [
          {
            title: '题号',
            align: 'center',
            key: 'fullCode',
          },
          {
            title: '分数',
            align: 'center',
            render: scoreRender,
          },
          {
            title: '操作',
            align: 'center',
            render: actionRender,
          },
        ]

        return columns
      },
    },
    created() {
      this.topicCode = this.maxTopicCode + 1
      this.topicName = numberToChinese(this.topicCode) + '、' + BranchTypeEnum.getNameById(this.branchTypeId)
      this.questionCodeFrom = this.maxQuestionCode + 1
    },
    methods: {
      changeTopicCode(newTopicCode) {
        let oldTopicCode = this.topicCode
        this.topicCode = newTopicCode
        if (this.isExistTopic) {
          this.topicName = this.topicCodeNames.find(t => t.topicCode == this.topicCode).topicName
        } else {
          let oldTopicCodeChinese = numberToChinese(oldTopicCode)
          let newTopicCodeChinese = numberToChinese(newTopicCode)
          this.topicName = this.topicName.replace(new RegExp(`^${oldTopicCodeChinese}、`), newTopicCodeChinese + '、')
        }
      },
      generateQuestions() {
        let questions = []
        if (
          isPositiveInteger(this.questionCodeFrom) &&
          this.questionCodeFrom <= Max_Question_Code &&
          isPositiveInteger(this.questionCodeTo) &&
          this.questionCodeTo <= Max_Question_Code &&
          this.questionCodeFrom <= this.questionCodeTo
        ) {
          for (let code = this.questionCodeFrom; code <= this.questionCodeTo; code++) {
            questions.push({
              fullCode: '',
              questionCode: code,
              branchCode: 1,
              blankScores: [
                {
                  score: this.score,
                },
              ],
            })
          }
        }
        this.questions = questions
        this.updateFullCode()
      },
      updateFullCode() {
        this.questionsGroupByCode.forEach(({ questionCode, branches }) => {
          if (branches.length == 1) {
            branches[0].fullCode = `${questionCode}`
            branches[0].branchCode = 1
          } else {
            branches.forEach((b, idx) => {
              b.branchCode = idx + 1
              b.fullCode = `${b.questionCode}.${b.branchCode}`
            })
          }
        })
      },
      changeScore() {
        this.questionsGroupByCode.forEach(({ branches }) => {
          if (branches.length == 1 && branches[0].blankScores.length == 1) {
            branches[0].blankScores[0].score = this.score
          }
        })
      },
      handleChangeBlankScore(row, idx, e) {
        this.questions.find(q => q.fullCode == row.fullCode).blankScores[idx].score = Number(e.target.value) || 0
      },
      handleAddBranch(row) {
        let index = this.questions.findIndex(q => q.fullCode == row.fullCode)
        let newQuestion = {
          fullCode: '',
          questionCode: row.questionCode,
          branchCode: row.branchCode + 1,
          blankScores: [
            {
              score: row.blankScores[row.blankScores.length - 1].score,
            },
          ],
        }
        this.questions.splice(index + 1, 0, newQuestion)
        this.updateFullCode()
      },
      handleDeleteBranch(row) {
        if (this.questions.filter(q => q.questionCode == row.questionCode).length > 1) {
          let index = this.questions.findIndex(q => q.fullCode == row.fullCode)
          this.questions.splice(index, 1)
          this.updateFullCode()
        }
      },
      handleAddBlank(row) {
        let question = this.questions.find(q => q.fullCode == row.fullCode)
        let lastScore = question.blankScores[row.blankScores.length - 1].score
        question.blankScores.push({
          score: lastScore,
        })
      },
      handleDeleteBlank(row) {
        let question = this.questions.find(q => q.fullCode == row.fullCode)
        if (question.blankScores.length > 1) {
          question.blankScores.pop()
        }
      },
      check() {
        if (!this.topicCode) {
          return {
            message: '请选择大题',
          }
        }
        if (!this.topicName) {
          return {
            message: '请输入大题名',
          }
        }
        if (this.questions.length == 0) {
          return {
            message: '请添加题目',
          }
        }
        let newQuestionCodes = this.questionsGroupByCode.map(g => g.questionCode)
        let intersections = intersection(newQuestionCodes, this.questionCodes)
        if (intersections.length > 0) {
          return {
            message: `以下题号已存在：${intersections.join('，')}`,
          }
        }

        let scoreInvalidBranchNames = []
        let subjectiveQuestions = []
        this.questionsGroupByCode.forEach(({ questionCode, branches }) => {
          branches.forEach(b => {
            let subj = new SubjectiveQuestion()
            subj.questionId = randomId()
            subj.questionCode = questionCode
            subj.questionName = `${questionCode}`
            subj.branchCode = branches.length > 1 ? b.branchCode : 0
            subj.branchName = branches.length > 1 ? `${questionCode}.${b.branchCode}` : `${questionCode}`
            subj.topicCode = this.topicCode
            subj.topicName = this.topicName
            subj.blankScores = b.blankScores.map(x => roundScore(x.score))
            if (subj.blankScores.some(score => !score || !isPositiveFinite(score))) {
              scoreInvalidBranchNames.push(subj.branchName)
            }
            subj.fullScore = sumByDefaultZero(subj.blankScores, x => x)
            subjectiveQuestions.push(subj)
          })
        })

        if (scoreInvalidBranchNames.length > 0) {
          return {
            message: `请输入分数：${scoreInvalidBranchNames.join('、')}`,
          }
        }

        return {
          message: '',
          data: {
            topicCode: this.topicCode,
            topicName: this.topicName,
            blanksPerRow: this.blanksPerRow,
            subjectiveQuestions,
          },
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  :deep(.ivu-form-item) {
    margin-bottom: 10px;
  }

  .input-short {
    width: 100px;
  }
</style>
