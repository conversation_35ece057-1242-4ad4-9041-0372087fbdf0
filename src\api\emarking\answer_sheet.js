import ajax from '@/api/ajax'

// 查个人答题卡
export function apiGetMyAnswerSheetList({ name, mode, status, sourceType, stageId, subjectId, referExam }) {
  return ajax.get({
    url: 'mark/answerSheet/myList',
    params: {
      name,
      mode,
      status,
      sourceType,
      gradeLevel: stageId,
      subjectId,
      referExam,
    },
    requestName: '获取我的答题卡',
  })
}

// 获取新答题卡ID
export function apiGetAnswerSheetNewId() {
  return ajax.get({
    url: 'mark/answerSheet/newId',
    requestName: '获取新答题卡Id',
  })
}

// 查答题卡详情
export function apiGetAnswerSheetDetail(id) {
  return ajax.get({
    url: 'mark/answerSheet',
    params: {
      id,
    },
    requestName: '加载答题卡',
  })
}

// 查答题卡简单信息
export function apiGetAnswerSheetInfo(id) {
  return ajax.get({
    url: 'mark/answerSheet/info',
    params: {
      id,
    },
    requestName: '获取答题卡信息',
  })
}
export function apiGetAnswerSheetPDFUrl(id) {
  return ajax.get({
    url: 'mark/answerSheet/getPdfUrl',
    params: {
      id,
    },
    requestName: '获取答题卡PDF路径',
  })
}

// 查题库试卷关联答题卡Id
export function apiGetAnswerSheetIdByPaperId(paperId) {
  return ajax.get({
    url: 'mark/answerSheet/getIdByPaperId',
    params: {
      paperId,
    },
    requestName: '查询试卷答题卡',
  })
}

// 保存答题卡
export function apiSaveAnswerSheet(answerSheetData, onProgress) {
  return ajax.upload({
    url: `mark/answerSheet/save`,
    data: answerSheetData,
    onProgress,
    requestName: '保存答题卡',
  })
}

// 保存教辅答题卡
export function apiSaveCoachBookAnswerSheet(answerSheetData, onProgress) {
  return ajax.upload({
    url: `mark/answerSheet/coachBookSave`,
    data: answerSheetData,
    onProgress,
    requestName: '保存教辅答题卡',
  })
}

// 修改答题卡扫描模板
export function apiChangeAnswerSheetTemplateJson({ id, templateJson, blocksJson }) {
  return ajax.put({
    url: 'mark/answerSheet/changeTemplateJson',
    params: {
      id,
    },
    data: {
      templateJson,
      blocksJson,
    },
    requestName: '修改答题卡扫描模板',
  })
}

// 复制答题卡
export function apiCopyAnswerSheet({ answerSheetId, name, stageId, subjectId }) {
  return ajax.post({
    url: 'mark/answerSheet/copy',
    params: {
      id: answerSheetId,
      name,
      gradeLevel: stageId,
      subjectId,
    },
    requestName: '复制答题卡',
  })
}

// 更新答题卡状态
export function apiUpdateAnswerSheetStatus({ id, status }) {
  return ajax.put({
    url: 'mark/answerSheet/updateStatus',
    params: {
      id, // [string]
      status, // *required [number] (0 - 正常 | 1 - 回收站 | 2 - 删除)
    },
    requestName: '更新答题卡',
  })
}

export function apiGetSharedAnswerSheetList(answerSheetId) {
  return ajax.get({
    url: 'mark/answerSheet/shareHistory',
    params: {
      answerSheetId: answerSheetId,
    },
    requestName: '获取答题卡分享历史列表',
  })
}

export function apiShareAnswerSheet(requestParams) {
  return ajax.put({
    url: 'mark/answerSheet/share',
    params: {
      answerSheetId: requestParams.answerSheetId,
      teacherId: requestParams.teacherId,
    },
    requestName: '分享答题卡',
  })
}

// 删除试卷答题卡
export function apiPhysicalDeletePaperAnswerSheet(paperId) {
  return ajax.delete({
    url: 'mark/answerSheet/physicalDeleteByPaperId',
    params: {
      paperId,
    },
    requestName: '删除答题卡',
  })
}
