<template>
  <div class="wrapper-view-more-less">
    <div ref="content" class="more-less-content" :style="contentStyle">
      <slot></slot>
    </div>
    <div v-if="overflow" class="more-less-bar" :style="barStyle">
      <span v-if="expand" @click="expand = !expand">{{ showLessText }}<Icon type="md-arrow-dropup"></Icon></span>
      <span v-else @click="expand = !expand">{{ showMoreText }}<Icon type="md-arrow-dropdown"></Icon></span>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      maxHeight: {
        type: Number,
        default: 200,
      },
      showMoreText: {
        type: String,
        default: '展开',
      },
      showLessText: {
        type: String,
        default: '收起',
      },
      barStyle: {
        type: Object,
        default: () => ({}),
      },
    },
    data() {
      return {
        expand: false,
        overflow: false,
        observer: null,
        displayNoneAncestorObservers: [],
      }
    },
    computed: {
      contentStyle() {
        if (!this.overflow || this.expand) {
          return {}
        }
        let maxHeight = typeof this.maxHeight == 'number' ? this.maxHeight + 'px' : this.maxHeight
        return {
          maxHeight,
          overflow: 'hidden',
        }
      },
    },
    mounted() {
      this.createObserver()
      this.$nextTick().then(() => {
        this.checkOverflow()
      })
    },
    beforeUnmount() {
      this.observer.disconnect()
      this.removeDisplayNoneAncestorObservers()
    },
    methods: {
      createObserver() {
        this.observer = new MutationObserver(() => {
          this.checkOverflow()
        })
        this.observer.observe(this.$refs.content, {
          childList: true,
          subtree: true,
          attributes: true,
          characterData: true,
        })
      },
      checkOverflow() {
        this.removeDisplayNoneAncestorObservers()
        let el = this.$refs.content
        let scrollHeight = el.scrollHeight
        // 如果内容高度为0，元素可能是隐藏的，需要添加祖先元素的监听
        if (scrollHeight == 0) {
          this.addAncestorObservers(el)
        }
        this.overflow = scrollHeight > this.maxHeight
      },
      removeDisplayNoneAncestorObservers() {
        this.displayNoneAncestorObservers.forEach(observer => {
          if (observer) {
            observer.disconnect()
          }
        })
        this.displayNoneAncestorObservers = []
      },
      addAncestorObservers(el) {
        let parent = el.parentElement
        while (parent) {
          if (parent.style.display == 'none') {
            let observer = new MutationObserver(() => {
              this.checkOverflow()
            })
            observer.observe(parent, {
              attributes: true,
              attributeFilter: ['style'],
            })
            this.displayNoneAncestorObservers.push(observer)
          }
          parent = parent.parentElement
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .more-less-bar {
    margin-top: 10px;
    color: $color-primary;
    text-align: center;
    cursor: pointer;
  }
</style>
