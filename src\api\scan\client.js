import ajax from '@/api/ajax'

import { ScanClientHost } from '@/config/config'

// 查询扫描端编号
export function apiGetScanClientId() {
  return ajax.get({
    url: `${ScanClientHost}/clientId`,
  })
}
export function apiGetScanClientInfo() {
  return ajax.get({
    url: `${ScanClientHost}/clientInfo`,
  })
}

// 获取扫描端状态
export function apiGetScannerStatus() {
  return ajax.get({
    url: `${ScanClientHost}/status`,
  })
}

// 开始扫描
export function apiStartScan(params) {
  return ajax.post({
    url: `${ScanClientHost}/start`,
    data: params,
  })
}

// 开始扫描错题反馈卡
export function apiStartScanWrongQuestionSheet(params) {
  return ajax.post({
    url: `${ScanClientHost}/startFeedback`,
    params: {
      // 数字签名Id
      secretId: params.secretId,
      // 时间戳（秒，10位）
      timeStamp: params.timeStamp,
      // 随机字符串6位
      nonce: params.nonce,
      // 签名
      signature: params.signature,
      userId: params.userId,
      schoolId: params.schoolId,
      // 图像颜色，Gray | RGB
      pixelType: 'Gray',
    },
  })
}

// 制作扫描模板扫描答题卡，返回base64图像数据
export function apiStartScanAnswerSheet(requestParams) {
  return ajax.post({
    url: `${ScanClientHost}/scanAnswerSheet`,
    data: requestParams,
  })
}
