CKEDITOR.plugins.add('wavy-underline', {
  icons: 'wavy-underline',
  init: function (editor) {
    editor.addCommand('wavy-underline', {
      exec: function (editor) {
        const className = 'wavy-underline'
        const style = new CKEDITOR.style({ element: 'span', attributes: { class: 'wavy-underline' } })

        const selection = editor.getSelection()
        const ranges = selection.getRanges()
        ranges.forEach(range => {
          // 扩展选中区域
          let startContainer = range.startContainer
          let startParent = startContainer.getParent()
          if (startContainer.type == 3 && startParent.hasClass(className) && range.startOffset == 0) {
            range.setStartBefore(startParent)
          }
          let endContainer = range.endContainer
          let endParent = endContainer.getParent()
          if (
            endContainer.type == 3 &&
            endParent.hasClass(className) &&
            endParent.getText().length == range.endOffset
          ) {
            range.setEndAfter(endParent)
          }
          // 无波浪线添加，有则删除
          let cloned = range.cloneContents()
          let hasWave = cloned.find('.' + className).count() > 0
          if (hasWave) {
            editor.removeStyle(style)
            return
          } else if (editor.getSelection().getSelectedText()) {
            editor.applyStyle(style)
          }
        })
      },
    })

    // 添加工具栏按钮
    editor.ui.addButton('wavy-underline', {
      label: '波浪线',
      command: 'wavy-underline',
      toolbar: 'basicstyles,10', // 工具栏组和按钮优先级
      icon: this.path + 'icons/wavy-underline.png', // 按钮图标路径
    })
  },
})
