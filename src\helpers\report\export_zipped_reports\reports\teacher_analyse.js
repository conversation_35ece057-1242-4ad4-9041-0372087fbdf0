import { apiDownloadTeacherAnalyseExcel } from '@/api/report'

import Store from '@/store/index'

// 教师教学质量分析报表
export function generateExcelReportTeacherAnalyseBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  const RequestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    excelShowRank: excelShowRank,
    isMerged: false,
  }

  let fileName = `${Store.getters['report/examName']}_教师教学质量分析报表_`
  if (category && category.categoryName) {
    fileName = category.categoryName + '/' + fileName + '联考.xlsx'
  } else if (isNextLevel) {
    fileName = `下级机构或学校报表/(机构)${institution.name}/${fileName}${institution.name}.xlsx`
    RequestParams.orgSchId = institution.id
  } else {
    fileName += '联考.xlsx'
  }

  return apiDownloadTeacherAnalyseExcel(RequestParams)
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}
