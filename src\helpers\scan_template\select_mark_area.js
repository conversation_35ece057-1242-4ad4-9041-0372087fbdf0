import Rect from './rect'
export default class SelectMarkArea {
  constructor() {
    this.id = ''
    this.pageIndex = 0
    this.blockId = ''
    this.blockName = ''
    this.textRect = null
    this.matchRect = null
  }

  static create({ pageIndex, blockId, blockName, questionCode, textRect, matchRect }) {
    let area = new SelectMarkArea()
    area.id = `SelectMarkArea-${questionCode || ''}`
    area.pageIndex = pageIndex
    area.blockId = blockId
    area.blockName = blockName
    area.textRect = textRect ? new Rect(textRect) : null
    area.matchRect = new Rect(matchRect)
    return area
  }
}
