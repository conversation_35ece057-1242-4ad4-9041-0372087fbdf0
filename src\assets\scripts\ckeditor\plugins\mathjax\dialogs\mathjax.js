﻿CKEDITOR.dialog.add('mathjax', function (editor) {
	var preview = null, theWidget = null, lastValue = '';
	function debouncedShowMathFormula() {
		let duration = 1000
		if (debouncedShowMathFormula.nextTime && Date.now() < debouncedShowMathFormula.nextTime) {
			return
		}

		setTimeout(function() {
			theWidget.showMathFormula(preview, lastValue);
		}, duration)
		debouncedShowMathFormula.nextTime = Date.now() + duration - 100;
	}
	return {
		title: '公式编辑器',
		minWidth: 500,
		minHeight: 200,
		contents: [
			{
				id: 'info',
				elements: [
					{
						id: 'equation',
						type: 'textarea',
						label: '在此编写Tex或MathML代码',

						onLoad: function () {
							var inputElement = this.getInputElement();
							inputElement.on('paste', function(e) {
								var pastedText = e.data.$.clipboardData.getData('text');
								// 替换化学反应方程式中有条件的长等号
								var replaced = false;
								var match;
								while ((match = pastedText.match(/\\xlongequal\s*{/)) != null) {
									// 括号成对
									var braceCount = 1;
									var start = match.index + match[0].length;
									var end = start;
									while (end < pastedText.length) {
										if (pastedText[end] == '{') {
											braceCount++;
										} else if (pastedText[end] == '}') {
											braceCount--;
										}
										if (braceCount == 0) {
											break
										}
										end++;
									}
									if (braceCount == 0) {
										let content = pastedText.substring(start, end);
										pastedText = pastedText.substring(0, match.index) + '\\overset{' + content + '}{=\\!=\\!=\\!=' + pastedText.substring(end);
										replaced = true;
									}
								}
								if (replaced) {
									e.data.preventDefault();
									inputElement.setValue(inputElement.getValue() + pastedText);
								}
							});
							this.getInputElement().on('keyup', function () {
								var value = this.getValue()
								if (!value.startsWith('<math') && !value.startsWith('$')) {
									value = '$' + value + '$'
								}
								if (lastValue !== value) {
									lastValue = value;
									debouncedShowMathFormula();
								}
							});
						},

						setup: function (widget) {
							theWidget = widget
							lastValue = widget.data.math;
							this.setValue(widget.data.math);
						},

						commit: function (widget) {
							var value = this.getValue()
							if (!value.startsWith('<math') && !value.startsWith('$')) {
								value = '$' + value + '$'
							}
							widget.setData('math', value);
							widget.showMathFormula();
						}
					},
					{
						id: 'preview',
						type: 'html',
						html:
							'<div style="width:100%;text-align:center;font-size:16px; scroll: visible">' +
							'<span class="math-formula"></span>' +
							'</div>',

						onLoad: function () {
							preview = CKEDITOR.document.getById(this.domId);
							
							// 清除dialog全局样式
							var el = preview.$
							while(el != window.document.documentElement) {
								if (el.classList.contains('cke_reset_all')) {
									el.classList.remove('cke_reset_all');
									break;
								}
								else {
									el = el.parentElement;
								}
							}
						},

						setup: function (widget) {
							widget.showMathFormula(preview, widget.data.math);
						}
					}
				]
			}
		]
	};
});
