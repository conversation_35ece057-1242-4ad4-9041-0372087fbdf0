import Role from '@/enum/user/role'

export default {
  name: 'emarking',
  path: 'emarking',
  meta: {
    title: '阅卷',
    menu: '阅卷',
    name: '阅卷',
    roles: [
      Role.SchoolAdministrator.id,
      Role.SchoolLeader.id,
      Role.GradeLeader.id,
      Role.SubjectLeader.id,
      Role.GradeSubjectLeader.id,
      Role.ClassLeader.id,
      Role.Teacher.id,
      Role.ResearcherLeader.id,
      Role.Researcher.id,
      Role.SystemAdministrator.id,
    ],
  },
  component: () => import('@/views/emarking/index.vue'),
  children: [
    {
      name: 'emarking-home',
      path: 'home',
      component: () => import('@/views/emarking/home/<USER>'),
    },
    {
      name: 'emarking-topic-feedback',
      path: 'feedback',
      meta: {
        title: '错题采集',
        name: '错题采集',
      },
      component: () => import('@/views/emarking/home/<USER>'),
    },
    {
      name: 'emarking-exam-normal',
      path: 'exam_normal',
      meta: {
        title: '校内检测',
        name: '校内检测',
      },
      component: () => import('@/views/emarking/home/<USER>'),
    },
    {
      name: 'emarking-exam-union',
      path: 'exam_union',
      meta: {
        title: '区域联考',
        name: '区域联考',
      },
      component: () => import('@/views/emarking/home/<USER>'),
    },
    {
      name: 'emarking-exam-monitor',
      path: 'exam_monitor',
      meta: {
        title: '评卷监控',
        name: '评卷监控',
      },
      component: () => import('@/views/emarking/home/<USER>'),
    },
    {
      name: 'emarking-scan-task',
      path: 'scan',
      meta: {
        title: '答卷扫描',
        name: '答卷扫描',
      },
      component: () => import('@/views/emarking/home/<USER>'),
    },
    {
      name: 'emarking-exam-audio',
      path: 'audio',
      meta: {
        title: '考试语音',
        name: '考试语音',
      },
      component: () => import('@/views/emarking/home/<USER>'),
    },
    {
      name: 'emarking-exam-create',
      path: 'exam/create/:isAdministrator/:examScope',
      component: () => import('@/views/emarking/exam/create/index.vue'),
    },
    {
      name: 'emarking-exam',
      path: 'exam/:examId',
      meta: {
        showHeader: false,
        pageWidth: 'no-max',
      },
      component: () => import('@/views/emarking/exam/index.vue'),
      children: [
        {
          name: 'emarking-exam-info',
          path: 'info',
          meta: {
            menu: '考试信息',
          },
          component: () => import('@/views/emarking/exam/info/index.vue'),
        },
        {
          name: 'emarking-exam-venue',
          path: 'venue',
          meta: {
            menu: '考点考场',
          },
          component: () => import('@/views/emarking/exam/venue/index.vue'),
        },
        {
          name: 'emarking-exam-student',
          path: 'student',
          meta: {
            menu: '考生设置',
          },
          component: () => import('@/views/emarking/exam/student/index.vue'),
        },
        {
          name: 'emarking-exam-student2',
          path: 'student2',
          meta: {
            menu: '考生设置',
          },
          component: () => import('@/views/emarking/exam/exam_student/index.vue'),
        },
        {
          name: 'emarking-exam-paper-subscription',
          path: 'papersubscription',
          meta: {
            menu: '试卷报订',
          },
          component: () => import('@/views/emarking/exam/paper_subscription/index.vue'),
        },
        {
          name: 'emarking-exam-teacher',
          path: 'teacher',
          meta: {
            menu: '评卷员设置',
          },
          component: () => import('@/views/emarking/exam/teacher/index.vue'),
        },
        {
          name: 'emarking-exam-paper',
          path: 'paper',
          meta: {
            menu: '试卷设置',
          },
          component: () => import('@/views/emarking/exam/paper/index.vue'),
        },
        {
          name: 'emarking-exam-task',
          path: 'task',
          meta: {
            menu: '评卷任务',
          },
          component: () => import('@/views/emarking/exam/task/index.vue'),
        },
        {
          name: 'emarking-exam-scan-station',
          path: 'scanstation',
          meta: {
            menu: '扫描点',
            menuGroupText: '扫描管理',
            menuGroupName: 'emarking-exam-scan',
          },
          component: () => import('@/views/emarking/exam/scan/station.vue'),
        },
        {
          name: 'emarking-exam-scanner',
          path: 'scanner',
          meta: {
            menu: '扫描员',
            menuGroupText: '扫描管理',
            menuGroupName: 'emarking-exam-scan',
          },
          component: () => import('@/views/emarking/exam/scan/scanner.vue'),
        },
        {
          name: 'emarking-exam-cloud-crop',
          path: 'cloudcrop',
          meta: {
            menu: '云切图',
            menuGroupText: '扫描管理',
            menuGroupName: 'emarking-exam-scan',
          },
          component: () => import('@/views/emarking/exam/scan/cloud_crop/index.vue'),
          children: [
            {
              name: 'emarking-exam-cloud-crop-package',
              path: 'package',
              meta: {
                menu: '扫描包管理',
              },
              component: () => import('@/views/emarking/exam/scan/cloud_crop/package/index.vue'),
            },
            {
              name: 'emarking-exam-cloud-crop-paper',
              path: 'paper',
              meta: {
                menu: '答卷浏览',
              },
              component: () => import('@/views/emarking/exam/scan/cloud_crop/paper/index.vue'),
            },
            {
              name: 'emarking-exam-cloud-crop-abnormal',
              path: 'abnormal',
              meta: {
                menu: '异常处理',
              },
              component: () => import('@/views/emarking/exam/scan/cloud_crop/abnormal/index.vue'),
            },
            {
              name: 'emarking-exam-cloud-crop-cut',
              path: 'cut',
              meta: {
                menu: '子图切割',
              },
              component: () => import('@/views/emarking/exam/scan/cloud_crop/cut/index.vue'),
            },
            {
              name: 'emarking-exam-cloud-crop-upload',
              path: 'upload',
              meta: {
                menu: '答卷上传',
              },
              component: () => import('@/views/emarking/exam/scan/cloud_crop/upload/index.vue'),
            },
          ],
        },
        {
          name: 'emarking-exam-scan-monitor',
          path: 'scanmonitor',
          meta: {
            menu: '扫描统计',
            menuGroupText: '扫描管理',
            menuGroupName: 'emarking-exam-scan',
          },
          component: () => import('@/views/emarking/exam/scan/monitor.vue'),
        },
        {
          name: 'emarking-exam-task-group',
          path: 'taskgroup',
          meta: {},
          component: () => import('@/views/emarking/exam/task/group.vue'),
        },
        {
          name: 'emarking-exam-student-block-image',
          path: 'studentBlockImg',
          meta: {
            menu: '考生题块原图',
            menuGroupText: '导出考生原图',
            menuGroupName: 'emarking-exam-student-image',
          },
          component: () => import('@/views/emarking/exam/image/block_image.vue'),
        },
        {
          name: 'emarking-exam-typical-paper-image',
          path: 'typicalPaperImg',
          meta: {
            menu: '评卷标记典型卷',
            menuGroupText: '导出考生原图',
            menuGroupName: 'emarking-exam-student-image',
          },
          component: () => import('@/views/emarking/exam/image/typical_paper.vue'),
        },
        {
          name: 'emarking-exam-score-review',
          path: 'scorereview',
          meta: {
            menu: '成绩复核',
            menuGroupText: '成绩管理',
            menuGroupName: 'emarking-exam-score',
          },
          component: () => import('@/views/emarking/exam/score/review.vue'),
        },
        {
          name: 'emarking-exam-score-edit',
          path: 'scoreedit',
          meta: {
            menu: '成绩修改',
            menuGroupText: '成绩管理',
            menuGroupName: 'emarking-exam-score',
          },
          component: () => import('@/views/emarking/exam/score/edit.vue'),
        },
        {
          name: 'emarking-exam-score-import',
          path: 'scoreimport',
          meta: {
            menu: '成绩导入',
            menuGroupText: '成绩管理',
            menuGroupName: 'emarking-exam-score',
          },
          component: () => import('@/views/emarking/exam/score/import.vue'),
        },
        {
          name: 'emarking-exam-score-import-by-school',
          path: 'scoreimportbyschool',
          meta: {
            menu: '成绩导入',
          },
          component: () => import('@/views/emarking/exam/score/import_by_school.vue'),
        },
        {
          name: 'emarking-exam-score-swap',
          path: 'scoreswap',
          meta: {
            menu: '成绩调换',
            menuGroupText: '成绩管理',
            menuGroupName: 'emarking-exam-score',
          },
          component: () => import('@/views/emarking/exam/score/swap.vue'),
        },
        {
          name: 'emarking-exam-score-raw',
          path: 'scoreRaw',
          meta: {
            menu: '导出原始分',
            menuGroupText: '成绩管理',
            menuGroupName: 'emarking-exam-score',
          },
          component: () => import('@/views/emarking/exam/score/raw.vue'),
        },
        {
          name: 'emarking-exam-modified-logs',
          path: 'scoremodifiedlogs',
          meta: {
            menu: '成绩修改日志',
            menuGroupText: '成绩管理',
            menuGroupName: 'emarking-exam-score',
            needSystemAccount: true,
          },
          component: () => import('@/views/emarking/exam/score/logs.vue'),
        },
        {
          name: 'emarking-exam-financial',
          path: 'financial',
          meta: {
            menu: '财务信息',
          },
          component: () => import('@/views/emarking/exam/financial/index.vue'),
        },
      ],
    },
    {
      name: 'emarking-subject-index',
      path: 'marking/:examSubjectId/:isFromHomePage',
      component: () => import('@/views/emarking/marking/index.vue'),
    },
    {
      name: 'emarking-mark',
      path: 'marking/:examSubjectId/mark/:blockId',
      component: () => import('@/views/emarking/marking/mark/index.vue'),
      meta: {
        showHeader: false,
        showFooter: false,
        pageWidth: 'full',
      },
    },
    {
      name: 'emarking-mark-batch',
      path: 'marking/:examSubjectId/mark/batch/:blockId',
      component: () => import('@/views/emarking/marking/batch/index.vue'),
      meta: {
        showHeader: false,
        showFooter: false,
        pageWidth: 'full',
      },
    },
    {
      name: 'emarking-abnormal',
      path: 'marking/:examSubjectId/abnormal/:blockId',
      component: () => import('@/views/emarking/marking/abnormal/index.vue'),
      meta: {
        showHeader: false,
        showFooter: false,
        pageWidth: 'full',
      },
    },
    {
      name: 'emarking-arbitrate',
      path: 'marking/:examSubjectId/arbitrate/:blockId',
      component: () => import('@/views/emarking/marking/arbitrate/index.vue'),
      meta: {
        showHeader: false,
        showFooter: false,
        pageWidth: 'full',
      },
    },
    {
      name: 'emarking-repeat',
      path: 'marking/:examSubjectId/repeat/:blockId',
      component: () => import('@/views/emarking/marking/repeat/index.vue'),
      meta: {
        showHeader: false,
        showFooter: false,
        pageWidth: 'full',
      },
    },
    {
      name: 'emarking-monitor',
      path: 'monitor/:examId',
      meta: {
        showHeader: false,
        pageWidth: 'no-max',
      },
      component: () => import('@/views/emarking/monitor/index.vue'),
    },
    {
      name: 'emarking-audio-group-play',
      path: 'audiogroup/play/:audioGroupId',
      meta: {
        showHeader: false,
        showFooter: false,
      },
      component: () => import('@/views/emarking/audio_group/play.vue'),
    },
    {
      name: 'emarking-audio-group-edit',
      path: 'audiogroup/edit/:audioGroupId',
      component: () => import('@/views/emarking/audio_group/edit.vue'),
    },
    {
      name: 'emarking-feedback',
      path: 'feedback/:examId/:examSubjectId',
      meta: {
        showHeader: false,
        showFooter: false,
      },
      component: () => import('@/views/emarking/feedback/index.vue'),
    },
    {
      name: 'emarking-feedback-wrongquestion-download',
      path: 'fb/wrongquestion/download/:from?',
      meta: {
        title: '错题下载',
        name: '错题下载',
      },
      component: () => import('@/views/emarking/feedback/download_wrong_question/index.vue'),
    },
  ],
}
