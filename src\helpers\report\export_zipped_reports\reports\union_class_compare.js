import { apiDownloadUnionClassCompareExcel } from '@/api/report'

import Store from '@/store/index'

// 联考班级对比分析报表
export function generateExcelReportUnionClassCompareBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  const RequestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    excelShowRank: excelShowRank,
  }

  let fileName = `${Store.getters['report/examName']}_班级对比分析报告_`
  if (category && category.categoryName) {
    fileName = category.categoryName + '/' + fileName + '联考.xlsx'
  } else if (isNextLevel) {
    fileName = `下级机构或学校报表/(机构)${institution.name}/${fileName}${institution.name}.xlsx`
    RequestParams.orgSchId = institution.id
  } else {
    fileName += '联考.xlsx'
  }

  return apiDownloadUnionClassCompareExcel(RequestParams)
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}
