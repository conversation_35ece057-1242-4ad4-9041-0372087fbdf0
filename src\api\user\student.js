import ajax from '@/api/ajax'

export function apiCreateStudent(data) {
  return ajax.post({
    url: 'user/student/add',
    data: {
      classId: data.classId,
      realName: data.realName,
      studentNo: data.studentNo || undefined,
      examNo: data.examNo || undefined,
      studentCode: data.studentCode || undefined,
      sex: data.sex || undefined,
      parents: (data.parents || []).map(x => ({
        realName: x.realName,
        mobile: x.mobile,
      })),
      foreignSubjectId: data.foreignSubjectId || null,
      selectSubjectIds: data.selectSubjectIds || null,
      seatNumber: data.seatNumber || undefined,
    },
    requestName: '新增学生',
  })
}

export function apiEditStudent(data) {
  return ajax.put({
    url: 'user/student/edit',
    data: {
      studentId: data.studentId,
      classId: data.classId,
      realName: data.realName,
      studentNo: data.studentNo || undefined,
      examNo: data.examNo || undefined,
      studentCode: data.studentCode || undefined,
      sex: data.sex || undefined,
      parents: (data.parents || []).map(x => ({
        realName: x.realName,
        mobile: x.mobile,
      })),
      foreignSubjectId: data.foreignSubjectId || null,
      selectSubjectIds: data.selectSubjectIds || null,
      seatNumber: data.seatNumber || undefined,
    },
    requestName: '编辑学生',
  })
}

// 删除学生
export function apiDeleteStudents(students) {
  return ajax.delete({
    url: 'user/student/delete',
    data: students,
  })
}

export function apiGetStudents(params) {
  return ajax.get({
    url: 'user/student/list',
    params: {
      classId: params.classId,
      key: params.keyword,
      size: params.pageSize || 10000,
      current: params.currentPage || 1,
    },
    requestName: '获取班级学生',
  })
}

/**
 * 文轩网站教师查看班级学生，返回结果中包含已激活教辅科目
 */
export function apiGetClassStudentsWithActiveCoachBookSubjects(classId) {
  return ajax.get({
    url: 'user/student/listWithSubject',
    params: {
      classId,
      size: 10000,
      current: 1,
    },
    requestName: '获取班级学生',
  })
}

// 导入学生
export function apiImportStudents(excelFile, options) {
  return ajax.upload({
    url: 'user/student/import',
    data: {
      file: excelFile,
      chkRealname: true,
      chkClass: !options.updateClass,
      isClean: options.cleanParentRelate,
    },
  })
}

export function apiExportStudents(params) {
  return ajax.download({
    url: 'user/student/export',
    params: {
      gradeLevel: params.stageId || null,
      gradeId: params.gradeId || null,
      classId: params.classId || null,
    },
    requestName: '导出学生',
  })
}

export function apiStudentsTransferClass(params) {
  return ajax.put({
    url: 'user/student/transferClass',
    data: params.studentsId,
    params: {
      fromClassId: params.fromClassId,
      toClassId: params.toClassId,
    },
    requestName: '学生（批量）转班',
  })
}

export function apiGetDeletedStudents(params) {
  return ajax.get({
    url: 'user/student/delList',
    params: {
      gradeId: params.gradeId,
      classId: params.classId,
      key: params.keyword,
      size: params.pageSize,
      current: params.currentPage,
    },
    requestName: '获取已删除学生',
  })
}

// 恢复已删除学生
export function apiRecoverStudents(studentIds) {
  return ajax.put({
    url: 'user/student/undeleteStu',
    data: studentIds,
  })
}

export function apiChangeStudentNo(params) {
  return ajax.put({
    url: 'user/student/changeStuNo',
    params: {
      studentId: params.studentId,
      studentNo: params.studentNo,
    },
    requestName: '修改学号',
  })
}

export function apiClearExamNo(params) {
  return ajax.put({
    url: 'user/student/clearExamNo',
    params: {
      gradeId: params.gradeId,
      classIds: params.classIds.join(','),
    },
    requestName: '清除校内考试号',
  })
}

export function apiClearExamNoNotInSchool() {
  return ajax.put({
    url: 'user/student/clearExamNoNotInSchool',
    requestName: '清除校内考试号',
  })
}

// 导入更新学籍号
export function apiUpdateStudentNoBatch(excelFile) {
  return ajax.upload({
    url: 'user/student/updateStudentNoBatch',
    data: {
      file: excelFile,
    },
  })
}

// 更改学生所选外语
export function apiChangeStudentForeignLanguage(requestParams) {
  return ajax.put({
    url: 'user/student/updateForeign',
    params: {
      studentId: requestParams.studentId,
      foreignSubjectId: requestParams.foreignSubjectId,
    },
    requestName: '更改学生所选外语',
  })
}

// 批量导入学生外语语种
export function apiImportStudentsForeignLanguage(excelFile) {
  return ajax.upload({
    url: 'user/student/importStudentForeign',
    data: {
      file: excelFile,
    },
  })
}

export function apiAddStudent(data) {
  return ajax.put({
    url: 'user/student/addXzbStudentsToFcb',
    data: data,
    requestName: '新增行政班学生至教学班',
  })
}

export function apiEditDeletedStudent(data) {
  return ajax.put({
    url: '/user/student/editDeletedStu',
    data: {
      studentId: data.studentId,
      realName: data.realName,
      studentNo: data.studentNo,
      examNo: data.examNo,
      sex: data.sex,
    },
    requestName: '编辑学生',
  })
}

export function apiUploadAllStudentPhoto(data) {
  return ajax.upload({
    url: 'user/student/importIdPhotoByClass',
    params: {
      classId: data.classId,
    },
    data: {
      file: data.file,
    },
    requestName: '批量导入学生证件照',
  })
}

export function apiUploadTheStudentPhoto(data) {
  return ajax.upload({
    url: 'user/student/updateStudentIdPhoto',
    params: {
      studentId: data.studentId,
    },
    data: {
      file: data.file,
    },
    requestName: '导入单个学生证件照',
  })
}

// 下载机构下的学生名单
export function apiUserGetInstitutionStudents(requestParams) {
  return ajax.download({
    url: 'user/student/exportByEdu',
    params: {
      schoolId: requestParams.schoolId,
      gradeLevel: requestParams.gradeLevel,
      gradeId: requestParams.gradeId,
      classId: requestParams.classId,
    },
    requestName: '批量导出机构下全部学校学生',
  })
}

export function apiCreateStudentGroup(params) {
  return ajax.post({
    url: 'user/classGroup/create',
    data: params,
    requestName: '创建学生分组',
  })
}

export function apiGetStudentGroup(params) {
  return ajax.get({
    url: 'user/classGroup/listGroupStudent',
    params: {
      subjectId: params.subjectId,
      classId: params.classId,
    },
    requestName: '获取学生分组',
  })
}

export function apiUpdateStudentGroup(params) {
  return ajax.post({
    url: 'user/classGroup/addOrRemoveGroupStudent',
    data: params,
    requestName: '更新学生分组',
  })
}

export function apiDeleteStudentGroup(params) {
  return ajax.delete({
    url: 'user/classGroup/delete',
    params: {
      groupId: params.groupId,
    },
    requestName: '删除学生分组',
  })
}

// 文轩 科任教师调班
export function apiTeacherAdjustStudentClass(requestParams) {
  return ajax.put({
    url: 'user/student/winShareTransferClass',
    params: {
      fromClassId: requestParams.fromClassId, // String*
      toClassId: requestParams.toClassId, // String*
    },
    data: requestParams.studentIds, // Array[String]*
  })
}

export function apiGetSearchStudents(params) {
  return ajax.get({
    url: 'user/student/page',
    params: {
      gradeId: params.gradeId || undefined,
      classId: params.classId || undefined,
      graduateYear: params.graduateYear || undefined,
      key: params.keyword || undefined,
      size: params.pageSize,
      current: params.currentPage,
      isInSchool: params.isInSchool,
      isDelete: params.isDelete,
      showMobile: params.showMobile,
    },
    requestName: '根据条件筛选学生',
  })
}

export function apiUpdateSelectSubjects(requestParams) {
  return ajax.put({
    url: 'user/student/updateSelectSubjects',
    params: {
      studentId: requestParams.studentId,
      selectSubjectIds: requestParams.selectSubjectIds,
    },
    requestName: '修改学生选科',
  })
}
