<template>
  <div class="editor-wrap" :class="{ 'editor-wrap-active': active }">
    <ckeditor
      class="editor-inner"
      :type="editorType"
      :config="editorConfig"
      :value="contentsHtml"
      @change="onChange"
      @focus="handleFocus"
      @blur="handleBlur"
    ></ckeditor>
    <div style="clear: both"></div>
  </div>
</template>

<script>
  import CKEditor from '@/components/editor.vue'

  import { debounce } from '@/utils/function'

  export default {
    components: {
      ckeditor: CKEditor,
    },
    props: {
      modelValue: {
        type: Array,
        default: () => [],
      },
    },
    emits: ['update:modelValue'],
    data() {
      return {
        contentsHtml: '',
        active: false,
        editorType: 'inline',
        editorConfig: {
          toolbar: [
            {
              name: 'basicstyles',
              items: ['Bold', 'Italic', 'Underline', 'Subscript', 'Superscript', 'RemoveFormat'],
            },
            {
              name: 'insert',
              items: ['Image', 'NewImage', 'Table', 'SpecialChar'],
            },
            {
              name: 'mathjax',
              items: ['Mathjax'],
            },
            {
              name: 'save',
              items: ['Undo', 'Redo'],
            },
          ],
          title: false,
        },
      }
    },
    created() {
      this.buildContentsHtml()
    },
    methods: {
      buildContentsHtml() {
        if (!this.modelValue || this.modelValue.length == 0) {
          this.contentsHtml = ''
        }
        let html = ''
        this.modelValue.forEach(item => {
          if (item.type == 'text') {
            html += item.value
          } else if (item.type == 'image') {
            html += `<div><img src="${item.value}"></div>`
          }
        })
        this.contentsHtml = html
      },
      onChange(data) {
        this.contentsHtml = data
        this.changeContents()
      },
      handleFocus() {
        this.active = true
      },
      handleBlur() {
        this.active = false
      },
      changeContents: debounce(function () {
        let segments = this.splitHtmlByImg(this.contentsHtml)
        let contents = []
        segments.forEach(segment => {
          if (segment.nodeType == Node.ELEMENT_NODE && segment.tagName.toLowerCase() === 'img') {
            contents.push({
              type: 'image',
              value: segment.src,
            })
          } else {
            let wrapper = document.createElement('div')
            wrapper.append(segment)
            let innerText = wrapper.innerText.trim()
            if (innerText) {
              contents.push({
                type: 'text',
                value: wrapper.innerHTML,
              })
            }
          }
        })
        this.$emit('update:modelValue', contents)
      }, 200),
      splitHtmlByImg(htmlStr) {
        if (!htmlStr) {
          return []
        }

        const segments = []
        let fragment = new DocumentFragment()
        const stack = [fragment]

        function processNode(node) {
          let parent = stack[stack.length - 1]

          if (node.nodeType === Node.TEXT_NODE) {
            parent.append(document.createTextNode(node.textContent))
          } else if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.tagName.toLowerCase() === 'img') {
              // 保存之前的片段
              if (fragment.childNodes.length > 0) {
                segments.push(fragment)
              }
              // 保存 img 标签
              segments.push(node.cloneNode(false))
              // 祖先节点复制
              for (let i = 0; i < stack.length; i++) {
                stack[i] = stack[i].cloneNode(false)
                if (i > 0) {
                  stack[i - 1].append(stack[i])
                }
              }
              fragment = stack[0]
            } else {
              const cloned = node.cloneNode(false)
              parent.append(cloned)
              stack.push(cloned)
              // 递归处理子节点
              Array.from(node.childNodes).forEach(processNode)
              stack.pop()
            }
          }
        }

        const doc = new DOMParser().parseFromString(htmlStr, 'text/html')
        Array.from(doc.body.childNodes).forEach(processNode)

        // 如果最后 fragment 还有内容，保存它
        if (fragment.childNodes.length > 0) {
          segments.push(fragment)
        }

        return segments
      },
    },
  }
</script>

<style lang="scss" scoped>
  .editor-wrap {
    min-height: 32px;
    padding: 4px 7px;
    border: 1px solid $color-border;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    font-size: $font-size-medium;
    line-height: 1.5;
    outline: none;
    transition:
      border 0.2s ease-in-out,
      box-shadow 0.2s ease-in-out;

    &.editor-wrap-active {
      border-color: #37cdbe;
      box-shadow: 0 0 0 2px rgba(5, 193, 174, 0.2);
    }
  }

  .editor-inner {
    padding: 0 !important;
  }
</style>
