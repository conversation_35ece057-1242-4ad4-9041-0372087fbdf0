import * as echarts from 'echarts/core'

import { CanvasRenderer } from 'echarts/renderers'

import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  RadarComponent,
  Mark<PERSON>ineComponent,
  ToolboxComponent,
} from 'echarts/components'

import { <PERSON><PERSON>hart, <PERSON>hart, <PERSON><PERSON>hart, Radar<PERSON>hart, ScatterChart } from 'echarts/charts'

echarts.use([
  CanvasRenderer,

  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  RadarComponent,
  MarkLineComponent,
  ToolboxComponent,

  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  RadarChart,
  ScatterChart,
])

export default echarts
