<template>
  <Modal
    :model-value="props.modelValue"
    title="添加章节结构"
    width="600"
    :mask-closable="false"
    :closable="!loading"
    @on-visible-change="handleVisibleChange"
  >
    <div class="modal-content">
      <Form :label-width="80" label-position="left">
        <FormItem label="类别">
          <RadioGroup v-model="structureType" type="button" button-style="solid">
            <Radio v-for="t in allowStructureTypes" :key="t.id" :label="t.id">{{ t.name }}</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem v-if="showStructureName" label="名称">
          <Input v-model="structureName" maxlength="50" clearable />
        </FormItem>
        <FormItem label="页面">
          <div class="page-list">{{ selectedPageText }}</div>
        </FormItem>
      </Form>
    </div>
    <template #footer>
      <Button type="text" :disabled="loading" @click="handleCancel">取消</Button>
      <Button type="primary" :loading="loading" @click="handleOK">确定</Button>
    </template>
  </Modal>
</template>

<script setup>
  import { computed, ref, watch } from 'vue'
  import iView from '@/iview'

  import StructureTypeEnum from '@/enum/qlib/ingestion/structure_type'

  const props = defineProps({
    modelValue: Boolean,
    allowStructureTypes: Array,
    selectedPages: Array,
  })

  const emits = defineEmits(['update:modelValue', 'add'])

  const structureType = ref('')
  const structureName = ref('')
  const loading = ref('')

  const showStructureName = computed(() => {
    return structureType.value != StructureTypeEnum.Toc.id
  })

  const selectedPageText = computed(() => {
    let pageOrders = props.selectedPages.map(x => x.index + 1)
    pageOrders.sort((a, b) => a - b)
    if (pageOrders.length == 0) {
      return ''
    } else if (pageOrders.length == 1) {
      return `第${pageOrders[0]}页`
    } else {
      // 是否连续
      let isContinuous = pageOrders.every((x, idx) => idx === 0 || x === pageOrders[idx - 1] + 1)
      if (isContinuous) {
        return `第 ${pageOrders[0]} 页至第 ${pageOrders[pageOrders.length - 1]} 页`
      } else {
        return pageOrders.map(x => `第 ${x} 页`).join('、')
      }
    }
  })

  watch(
    () => props.modelValue,
    () => {
      if (props.modelValue) {
        structureType.value = ''
        structureName.value = ''
        loading.value = false
      }
    }
  )

  function handleVisibleChange(visibility) {
    if (!visibility) {
      handleCancel()
    }
  }

  function handleCancel() {
    emits('update:modelValue', false)
  }

  function handleOK() {
    let message = check()
    if (message) {
      iView.Message.warning(message)
      return
    }
    loading.value = true
    emits('add', {
      structureType: structureType.value,
      structureName: structureType.value == StructureTypeEnum.Toc.id ? StructureTypeEnum.Toc.name : structureName.value,
      pageIndexes: props.selectedPages.map(x => x.index),
    })
  }

  function check() {
    if (!structureType.value) {
      return '请选择类别'
    }
    if (showStructureName.value && !structureName.value) {
      return '请输入名称'
    }
    return null
  }
</script>
