import { apiGetRegions } from '@/api/qlib'
import { deepCopy } from '@/utils/object'

export default {
  namespaced: true,
  state: {
    regions: [],
  },
  getters: {
    regionsByProvinceCityCounty(state) {
      return () => deepCopy(state.regions)
    },
    regionsByProvinceCity(state) {
      return () => {
        let _regions = state.regions.map(p => ({
          value: p.value,
          label: p.label,
          children:
            (p.children &&
              p.children.map(c => ({
                value: c.value,
                label: c.label,
              }))) ||
            [],
        }))
        return deepCopy(_regions)
      }
    },
    regionsByProvince(state) {
      return () => {
        let _regions = state.regions.map(p => ({
          value: p.value,
          label: p.label,
        }))
        return deepCopy(_regions)
      }
    },
    regionsById(state) {
      return regionId => {
        for (let province of state.regions) {
          let result = findRegion(province)
          if (result.length > 0) {
            return result
          }
        }
        return []

        function findRegion(node) {
          if (node.value === regionId) {
            return [
              {
                value: node.value,
                label: node.label,
              },
            ]
          }

          if (node.children) {
            for (let c of node.children) {
              let result = findRegion(c)
              if (result.length > 0) {
                return [
                  {
                    value: node.value,
                    label: node.label,
                  },
                  ...result,
                ]
              }
            }
          }

          return []
        }
      }
    },
  },
  actions: {
    getRegions(context) {
      if (context.state.regions.length > 0) {
        return Promise.resolve(true)
      }
      return apiGetRegions().then(regions => {
        context.state.regions = regions
      })
    },
  },
}
