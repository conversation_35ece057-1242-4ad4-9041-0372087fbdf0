<template>
  <Card class="analysis-ai-card">
    <template #title><span class="card-title">AI分析</span></template>
    <div class="analysis-ai-content">
      <div class="content-icon">
        <img src="@/assets/images/icon.png" class="img-icon" />
      </div>
      <div class="content-body">
        <div v-if="showSectionStart" class="body-section section-start">
          <Button :disabled="disableStartBtn" @click="handleBtnGenerateClick">开始分析</Button>
          <span v-if="showCannotUseMessage" class="cannot-use-message">{{ cannotUseMessage }}</span>
        </div>
        <div v-if="showSectionWaiting" class="body-section section-waiting">
          <span>分析中...</span>
        </div>
        <div v-if="lastAnalysisTime" class="body-section section-last-analysis-time">
          <span>上次分析时间：{{ lastAnalysisTime }}</span>
        </div>
        <div v-if="hasReasoning" class="body-section section-reasoning">
          <div class="header" @click="toggleShowReasoning">
            <span class="label">{{ hasContent ? '已深度思考' : '深度思考中' }}</span>
            <Icon class="arrow-icon" :class="{ close: !showReasoning }" type="ios-arrow-down"></Icon>
          </div>
          <div
            v-show="showReasoning"
            ref="reasoningResponseElement"
            class="response-container"
            v-html="reasoningHtml"
          ></div>
        </div>
        <div v-if="hasContent" class="body-section section-content">
          <div ref="contentResponseElement" class="response-container" v-html="contentHtml"></div>
        </div>
        <Alert v-if="error" class="body-section section-error" type="error" show-icon>{{ error }}</Alert>
        <div v-if="sseStatus != 'closed'" class="body-section section-loading">
          <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
        </div>
        <div v-if="showToolbar" class="body-section section-toolbar">
          <Tooltip class="tooltip-btn" content="重新生成" placement="bottom">
            <div class="btn" @click="handleBtnRegenerateClick">
              <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                <path
                  fill-rule="evenodd"
                  d="M12 4a7.98 7.98 0 0 0-6.02 2.732L4.473 5.415A9.98 9.98 0 0 1 12 2c5.523 0 10 4.477 10 10h1.478a.5.5 0 0 1 .394.807l-2.477 3.186a.5.5 0 0 1-.79 0l-2.477-3.186a.5.5 0 0 1 .394-.807H20a8 8 0 0 0-8-8m0 18c3 0 5.693-1.322 7.526-3.415l-1.505-1.317A8 8 0 0 1 4 12h1.397a.5.5 0 0 0 .376-.83L3.376 8.43a.5.5 0 0 0-.752 0L.226 11.17a.5.5 0 0 0 .376.83H2c0 5.523 4.477 10 10 10"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
          </Tooltip>
          <Tooltip class="tooltip-btn" content="复制内容" placement="bottom">
            <div class="btn" @click="copyContent">
              <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                <path
                  fill-rule="evenodd"
                  d="M21 3.5V17a2 2 0 0 1-2 2h-2v-2h2V3.5H9v2h5.857c1.184 0 2.143.895 2.143 2v13c0 1.105-.96 2-2.143 2H5.143C3.959 22.5 3 21.605 3 20.5v-13c0-1.105.96-2 2.143-2H7v-2a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2m-6.143 4H5.143v13h9.714z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
          </Tooltip>
          <Tooltip class="tooltip-btn" content="导出分析" placement="bottom">
            <div class="btn" @click="exportResult">
              <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M20.207 12.207a1 1 0 0 0-1.414-1.414L13 16.586V2a1 1 0 1 0-2 0v14.586l-5.793-5.793a1 1 0 0 0-1.414 1.414l7.5 7.5c.195.195.45.293.706.293H5a1 1 0 1 0 0 2h14a1 1 0 1 0 0-2h-6.999a1 1 0 0 0 .706-.293z"
                ></path>
              </svg>
            </div>
          </Tooltip>
        </div>
        <div v-if="sseStatus == 'closed' && (hasReasoning || hasContent)" class="body-section section-tip">
          内容由AI生成，仅供参考
        </div>
      </div>
    </div>
  </Card>
</template>

<script setup>
  import { ref, computed, watch, onBeforeMount, onMounted, onBeforeUnmount, useTemplateRef, nextTick } from 'vue'
  import { useAiStore } from '@/store/ai'

  import { Message } from 'view-ui-plus'

  import { apiGetMyAiUsage } from '@/api/ai/score_analysis'
  import { apiHtmlToWord } from '@/api/qlib/export'

  import { fetchEventSource } from '@microsoft/fetch-event-source'
  import MarkdownIt from 'markdown-it'
  import DOMPurify from 'dompurify'
  import { throttle } from '@/utils/function'
  import { downloadBlob } from '@/utils/download'
  import { createHiddenContainer, removeHiddenContainer } from '@/utils/dom'
  import MathJaxUtil from '@/utils/mathjax'
  import { handleMathFormula } from '@/helpers/qlib/export_html'

  const props = defineProps({
    loadLastAnalysisFunc: Function,
    sseMethod: {
      type: String,
      default: 'POST',
    },
    sseUrl: String,
    sseParams: Object,
    autoStartSse: {
      type: Boolean,
      default: true,
    },
    enableStart: Boolean,
    // 分析标题
    analysisTitle: String,
  })

  const aiStore = useAiStore()

  const reasoningResponseElement = useTemplateRef('reasoningResponseElement')
  const contentResponseElement = useTemplateRef('contentResponseElement')

  // 加载上次分析结果中
  const loadingLastAiAnalysis = ref(false)
  // 上次分析时间
  const lastAnalysisTime = ref('')

  // 加载用量中
  const loadingUsage = ref(false)
  // 用量统计
  const usage = ref(null)
  // 是否可用Ai分析
  const canUse = computed(() => usage.value?.canUse)
  // 不可用提示
  const cannotUseMessage = computed(() => usage.value?.cause || 'AI分析次数已用完')

  // SSE状态: connecting-连接中, open-已连接, closed-已关闭
  const sseStatus = ref('closed')
  // SSE错误
  const sseError = ref('')
  // SSE控制器
  let sseController = null
  // reasoning数据
  const reasoningData = ref('')
  // reasoning数据HTML
  const reasoningHtml = ref('')
  // 是否存在reasoning数据
  const hasReasoning = computed(() => reasoningData.value.length > 0)
  // content数据
  const contentData = ref('')
  // content数据HTML
  const contentHtml = ref('')
  // 是否存在content数据
  const hasContent = computed(() => contentData.value.length > 0)
  // 数据中的错误
  const dataError = ref('')
  // 错误，包含SSE错误和数据中的错误
  const error = computed(() => sseError.value || dataError.value)

  // 显示开始分析按钮：非请求中，无分析数据，且允许生成分析
  const showSectionStart = computed(() => {
    return (
      sseStatus.value == 'closed' &&
      !hasReasoning.value &&
      !hasContent.value &&
      !loadingLastAiAnalysis.value &&
      !loadingUsage.value &&
      props.enableStart
    )
  })
  // 禁用开始分析按钮：不可生成Ai分析
  const disableStartBtn = computed(() => !canUse.value)
  // 显示不可用提示
  const showCannotUseMessage = computed(() => usage.value != null && !canUse.value && !loadingUsage.value)
  // 显示分析中：SSE连接中或已连接尚未收到数据
  const showSectionWaiting = computed(
    () => sseStatus.value == 'connecting' || (sseStatus.value == 'open' && !hasReasoning.value && !hasContent.value)
  )
  // 显示推理过程
  const showReasoning = ref(true)
  function toggleShowReasoning() {
    showReasoning.value = !showReasoning.value
  }
  // 显示工具栏：已关闭且存在推理或内容或错误
  const showToolbar = computed(
    () => sseStatus.value == 'closed' && (hasReasoning.value || hasContent.value || error.value)
  )

  // 开始分析
  async function startAiAnalysis() {
    resetAnalysisData()
    await loadLastAnalysis()
    await getAiUsage()
    // 如果已生成分析为空，且自动启动SSE，且有可用次数，则开始SSE
    if (!hasReasoning.value && !hasContent.value && props.autoStartSse && canUse.value) {
      startSSE()
    }
  }

  // 重置分析数据
  function resetAnalysisData() {
    lastAnalysisTime.value = ''
    usage.value = null
    reasoningData.value = ''
    reasoningHtml.value = ''
    contentData.value = ''
    contentHtml.value = ''
    dataError.value = ''
  }

  // 加载上次分析数据
  async function loadLastAnalysis() {
    if (!props.loadLastAnalysisFunc || !props.sseParams) {
      return
    }
    loadingLastAiAnalysis.value = true
    aiStore.isLoadingAiAnalysis = true
    let lastAnalysis = null
    try {
      lastAnalysis = await props.loadLastAnalysisFunc(props.sseParams)
    } catch (err) {
      sseError.value = '获取上次分析结果失败'
      throw err
    } finally {
      loadingLastAiAnalysis.value = false
      aiStore.isLoadingAiAnalysis = false
    }
    if (lastAnalysis) {
      reasoningData.value = lastAnalysis.reasoningContent || ''
      reasoningHtml.value = convertMarkdownToHtml(reasoningData.value)
      contentData.value = lastAnalysis.content || ''
      contentHtml.value = convertMarkdownToHtml(contentData.value)
      lastAnalysisTime.value = lastAnalysis.createTime || ''
      nextTickRenderMath()
    }
  }

  // 获取Ai用量
  async function getAiUsage() {
    loadingUsage.value = true
    aiStore.isLoadingAiAnalysis = true
    try {
      usage.value = await apiGetMyAiUsage()
    } finally {
      loadingUsage.value = false
      aiStore.isLoadingAiAnalysis = false
    }
  }

  // 生成分析
  async function handleBtnGenerateClick() {
    await getAiUsage()
    if (!canUse.value) {
      Message.warning({
        content: cannotUseMessage.value,
        duration: 5,
      })
      return
    }
    startSSE()
  }

  // 开启SSE
  function startSSE() {
    resetAnalysisData()
    enableAutoScroll.value = true

    if (!props.sseParams) {
      return
    }

    sseStatus.value = 'connecting'
    sseError.value = ''
    sseController = new AbortController()

    aiStore.isLoadingAiAnalysis = true

    let url = props.sseUrl + '?' + new URLSearchParams(props.sseParams).toString()
    fetchEventSource(url, {
      openWhenHidden: true,
      method: props.sseMethod,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        Authentication: localStorage.getItem('token') || '',
      },
      signal: sseController.signal,
      onopen(response) {
        if (response.ok) {
          sseStatus.value = 'open'
        } else {
          handleSSEError('连接失败')
          throw new Error('SSE连接失败')
        }
      },
      onmessage(event) {
        try {
          handleSSEMessage(event)
        } catch (err) {
          console.error(err)
        }
      },
      onerror(err) {
        handleSSEError('出错了')
        throw err
      },
      onclose() {
        sseStatus.value = 'closed'
        sseController = null
        aiStore.isLoadingAiAnalysis = false
        nextTickRenderMath()
      },
    })
  }

  // 结束SSE
  function abortSSE(abortReason) {
    sseStatus.value = 'closed'
    if (sseController) {
      sseController.abort(abortReason)
      sseController = null
    }
    aiStore.isLoadingAiAnalysis = false
  }

  // 处理SSE错误
  function handleSSEError(errorMessage) {
    sseError.value = errorMessage
    abortSSE(errorMessage)
  }

  // 处理SSE消息
  function handleSSEMessage(event) {
    let data = null
    if (event.data) {
      try {
        data = JSON.parse(event.data)
      } catch {
        data = null
      }
    }
    if (!data) {
      throw event
    }
    if (data.e) {
      dataError.value += data.e
    }
    if (data.r) {
      reasoningData.value += data.r
      throttleRenderReasoning()
    }
    if (data.c) {
      contentData.value += data.c
      throttleRenderContent()
    }
  }

  // 渲染
  function convertMarkdownToHtml(markdown) {
    if (!markdown) {
      return ''
    }
    const md = MarkdownIt()
    let html = md.render(markdown)
    return DOMPurify.sanitize(html)
  }

  const throttleRenderReasoning = throttle(() => {
    reasoningHtml.value = convertMarkdownToHtml(reasoningData.value)
    autoScrollToBottom()
  }, 200)

  const throttleRenderContent = throttle(() => {
    contentHtml.value = convertMarkdownToHtml(contentData.value)
    autoScrollToBottom()
  }, 200)

  // 自动滚动
  const enableAutoScroll = ref(true)
  onMounted(() => {
    window.addEventListener('scroll', handleUserScroll)
  })
  onBeforeUnmount(() => {
    window.removeEventListener('scroll', handleUserScroll)
  })
  // 用户滚动时，取消自动滚动
  function handleUserScroll() {
    if (sseStatus.value != 'open') {
      return
    }
    const scrollTop = document.documentElement.scrollTop
    const windowHeight = window.innerHeight
    const docHeight = document.documentElement.scrollHeight
    const distanceFromBottom = docHeight - (scrollTop + windowHeight)

    if (distanceFromBottom > 100) {
      // 离底部超过阈值 => 用户在查看历史，不要自动滚
      enableAutoScroll.value = false
    } else {
      // 已滚回底部范围内 => 恢复自动滚
      enableAutoScroll.value = true
    }
  }
  // 自动滚动到底部
  function autoScrollToBottom() {
    if (enableAutoScroll.value) {
      window.scrollTo({ top: document.documentElement.scrollHeight })
    }
  }

  // 渲染数学公式
  function renderMath() {
    MathJaxUtil.clearRenderQueue()
    let containers = [reasoningResponseElement.value, contentResponseElement.value]
    containers.forEach(container => {
      if (!container) {
        return
      }
      let mathCodes = container.querySelectorAll('code.language-math')
      mathCodes.forEach(mathCode => {
        let mathCodeText = mathCode.innerText
        if (!mathCodeText) {
          return
        }
        let hasStartDelimiter = mathCodeText.startsWith('$')
        let hasEndDelimiter = mathCodeText.endsWith('$')
        if (!hasStartDelimiter) {
          mathCodeText = '$' + mathCodeText.trim()
        }
        if (!hasEndDelimiter) {
          mathCodeText = mathCodeText.trim() + '$'
        }
        // pre标签中无法渲染公式，替换为div
        let parent = mathCode.parentElement
        if (parent?.tagName == 'PRE') {
          let div = document.createElement('div')
          div.innerHTML = mathCodeText
          parent.replaceWith(div)
        } else {
          if (!hasStartDelimiter || !hasEndDelimiter) {
            mathCode.innerHTML = mathCodeText
          }
        }
      })
      MathJaxUtil.render(container)
    })
  }
  function nextTickRenderMath() {
    nextTick(() => {
      renderMath()
    })
  }

  // 重新生成
  function handleBtnRegenerateClick() {
    abortSSE('重新生成')
    handleBtnGenerateClick()
  }

  // 复制内容
  function copyContent() {
    if (contentData.value) {
      navigator.clipboard.writeText(contentData.value)
      Message.success('已复制')
    } else {
      Message.warning('暂无内容')
    }
  }

  // 导出分析
  async function exportResult() {
    let html = await getExportAnalysisContentHtml()
    if (!html) {
      Message.warning('暂无内容')
      return
    }
    let blob = await apiHtmlToWord({
      html: html,
      size: 'A4',
    })
    downloadBlob(blob, `${props.analysisTitle}.docx`)
  }

  // 获取导出分析内容HTML
  async function getExportAnalysisContentHtml() {
    let content = contentResponseElement.value.innerHTML
    if (!content) {
      return ''
    }
    let hiddenContainer = createHiddenContainer()
    hiddenContainer.innerHTML = content

    // 处理数学公式
    handleMathFormula(hiddenContainer)

    // 设置标题样式
    hiddenContainer.querySelectorAll('h1').forEach(h1 => {
      h1.style.fontSize = '2em'
      h1.style.textAlign = 'center'
      h1.style.fontWeight = 'bold'
      h1.style.marginTop = '1em'
      h1.style.marginBottom = '1em'
      h1.style.lineHeight = '1'
    })
    hiddenContainer.querySelectorAll('h2').forEach(h2 => {
      h2.style.fontSize = '1.5em'
      h2.style.fontWeight = 'bold'
      h2.style.marginTop = '1em'
      h2.style.marginBottom = '0.5em'
      h2.style.lineHeight = '1'
    })
    hiddenContainer.querySelectorAll('h3').forEach(h3 => {
      h3.style.fontSize = '1.25em'
      h3.style.fontWeight = 'bold'
      h3.style.marginTop = '1em'
      h3.style.marginBottom = '0.5em'
      h3.style.lineHeight = '1'
    })
    hiddenContainer.querySelectorAll('h4').forEach(h4 => {
      h4.style.fontSize = '1.1em'
      h4.style.fontWeight = 'bold'
      h4.style.marginTop = '1em'
      h4.style.marginBottom = '0.5em'
      h4.style.lineHeight = '1'
    })
    hiddenContainer.querySelectorAll('h5').forEach(h5 => {
      h5.style.fontSize = '1em'
      h5.style.fontWeight = 'bold'
      h5.style.marginTop = '0.5em'
      h5.style.marginBottom = '0.5em'
      h5.style.lineHeight = '1'
    })
    hiddenContainer.querySelectorAll('h6').forEach(h6 => {
      h6.style.fontSize = '1em'
      h6.style.fontWeight = 'bold'
      h6.style.marginTop = '0.5em'
      h6.style.marginBottom = '0.5em'
      h6.style.lineHeight = '1'
    })

    // 设置表格样式
    hiddenContainer.querySelectorAll('table').forEach(table => {
      table.style.width = '90%'
      table.style.marginBottom = '0.5em'
      table.style.borderCollapse = 'collapse'
      table.style.borderSpacing = '0'

      table.querySelectorAll('td, th').forEach(cell => {
        cell.style.padding = '5px 10px'
        cell.style.border = '1px solid #e8eaec'
        cell.style.width = cell.offsetWidth + 'px'

        if (cell.tagName == 'TH') {
          cell.style.fontWeight = 'bold'
          cell.style.backgroundColor = '#f5f5f5'
        }
      })
    })

    // 设置列表样式
    hiddenContainer.querySelectorAll('ul, ol').forEach(list => {
      list.style.paddingLeft = '2em'

      list.querySelectorAll('li').forEach(li => {
        li.style.marginTop = '0.25em'
      })
    })

    // 设置段落样式
    hiddenContainer.querySelectorAll('p').forEach(p => {
      p.style.marginBottom = '0.5em'
    })

    // 设置水平线样式
    hiddenContainer.querySelectorAll('hr').forEach(hr => {
      hr.style.marginTop = '0.5em'
      hr.style.marginBottom = '0.5em'
    })

    // 设置代码块样式
    hiddenContainer.querySelectorAll('pre').forEach(pre => {
      pre.style.marginBottom = '0.5em'
      pre.style.padding = '8px'
      pre.style.borderRadius = '4px'
      pre.style.whiteSpace = 'pre-wrap'
      pre.style.background = '#f8f8f9'
    })

    // 设置说明样式
    let footer = document.createElement('div')
    footer.style.textAlign = 'center'
    footer.style.fontSize = '12px'
    footer.style.color = '#999'
    footer.style.marginTop = '16px'
    footer.innerHTML = '内容由AI生成，仅供参考'
    hiddenContainer.appendChild(footer)

    content = hiddenContainer.innerHTML
    removeHiddenContainer(hiddenContainer)

    return `<!DOCTYPE html><html lang="zh-CN"><body style="line-height: 1.5;">${content}</body></html>`
  }

  /**
   * 参数变化，结束当前请求，重新请求
   */
  watch(
    () => [props.sseMethod, props.sseUrl, props.sseParams],
    () => {
      abortSSE('参数变化')
      startAiAnalysis()
    },
    { deep: true }
  )

  // 组件挂载时开始分析
  onBeforeMount(() => {
    startAiAnalysis()
  })

  // 组件卸载时，结束当前请求
  onBeforeUnmount(() => {
    abortSSE('组件卸载')
  })
</script>

<style lang="scss" scoped>
  .analysis-ai-card {
    flex-grow: 1;
    width: 0;
  }

  .card-title {
    color: $color-title;
    font-weight: bold;
    font-size: $font-size-medium-x;
  }

  .analysis-ai-content {
    @include flex(row, flex-start, flex-start);
  }

  .content-icon {
    flex-grow: 0;
    flex-shrink: 0;
    margin-right: 16px;

    .img-icon {
      width: 32px;
      height: 32px;
      transform: scale(1.25);
      transform-origin: center center;
    }
  }

  .content-body {
    flex-grow: 1;
    flex-shrink: 1;

    .body-section:not(:first-child) {
      margin-top: 16px;
    }
  }

  .section-start {
    @include flex(row, flex-start, center);
    height: 32px;

    .cannot-use-message {
      margin-left: 16px;
      color: $color-warning;
    }
  }

  .section-waiting {
    @include flex(row, flex-start, center);
    height: 32px;
    color: $color-icon;
  }

  .section-last-analysis-time {
    @include flex(row, flex-start, center);
    height: 32px;
  }

  :deep(.response-container) {
    line-height: 1.8;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 1em;
      margin-bottom: 0.5em;
      font-weight: bold;
      line-height: 1;
    }

    h1 {
      margin-bottom: 1em;
      font-size: 2em;
      text-align: center;
    }

    h2 {
      font-size: 1.5em;
    }

    h3 {
      font-size: 1.25em;
    }

    h4 {
      font-size: 1.1em;
    }

    h5,
    h6 {
      margin-top: 0.5em;
      font-size: 1em;
    }

    ul,
    ol {
      padding-left: 2em;

      li {
        margin-top: 0.25em;
      }
    }

    hr {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }

    p {
      margin-bottom: 0.5em;
    }

    table {
      width: 100%;
      margin-bottom: 0.5em;
      border-collapse: collapse;
      border-spacing: 0;

      td,
      th {
        padding: 5px 10px;
        border: 1px solid #e8eaec;
      }

      th {
        font-weight: bold;
        background-color: #f5f5f5;
      }
    }

    pre {
      margin-bottom: 0.5em;
      padding: 8px;
      border-radius: 4px;
      white-space: pre-wrap;
      background: $color-background-light;
    }
  }

  .section-reasoning {
    color: $color-icon;

    .header {
      @include flex(row, flex-start, center);
      height: 32px;
      cursor: pointer;

      .arrow-icon {
        margin-left: 4px;
        font-size: 16px;
        transform-origin: center center;
        cursor: pointer;
        transition: transform 0.2s;

        &.close {
          transform: rotate(180deg);
        }
      }
    }

    .response-container {
      margin-top: 16px;
      padding-left: 16px;
      border-left: 2px solid $color-border;
    }
  }

  .section-loading {
    @include flex(row, flex-start, center);
    height: 32px;

    .spin-icon-load {
      color: $color-primary;
      animation: ani-spin 1s linear infinite;
    }

    @keyframes ani-spin {
      from {
        transform: rotate(0deg);
      }

      50% {
        transform: rotate(180deg);
      }

      to {
        transform: rotate(360deg);
      }
    }
  }

  .section-toolbar {
    @include flex(row, flex-start, center);

    .btn {
      @include flex(row, center, center);
      width: 32px;
      height: 32px;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background-color: $color-background;
      }

      img,
      svg {
        width: 16px;
        height: 16px;
        color: $color-icon;
      }
    }

    .tooltip-btn:not(:first-child) .btn {
      margin-left: 8px;
    }
  }

  .section-tip {
    @include flex(row, center, center);
    height: 32px;
    color: $color-icon;
    font-size: $font-size-small;
  }
</style>
