import ajax from '@/api/ajax'
import { Question } from '@/helpers/qlib/question'
import { Paper } from '@/helpers/qlib/paper'
import { PaperInfo } from '@/helpers/qlib/paper_info'

/**
 * 获取个人上传记录
 */
export function apiGetSelfUploadedPapers(params) {
  let sendParams = {
    stage: params.stage,
    subject: params.subject,
    size: params.pageSize,
    page: params.currentPage,
    keyword: params.keyword || '',
    bookType: params.coachbookType || undefined,
    flagCoachBookId: params.coachbookId || undefined,
    paperGroupId: params.paperGroupId || undefined,
  }
  return ajax
    .get({
      url: `ques/paperUpload/records`,
      params: sendParams,
      requestName: '获取上传记录',
    })
    .then(data => {
      return {
        total: data.total || 0,
        papers: (data.records || []).map(p => PaperInfo.import(p)),
      }
    })
}

/**
 * 上传试卷word
 */
export function apiUploadPaper(data, onProgress) {
  return ajax.upload({
    url: `ques/paperUpload/upload`,
    data,
    onProgress,
    requestName: '上传试卷',
  })
}

/**
 * 上传试卷pdf
 */
export function apiUploadPaperPdf(data) {
  return ajax.upload({
    url: `ques/paperUpload/uploadPdf`,
    data,
    requestName: '上传试卷Pdf',
  })
}

/**
 * 获取划题试卷内容
 */
export function apiGetDividePaperContent({ paperId, addRawHtml }) {
  return ajax
    .get({
      url: `ques/paperUpload/dividePaperContent`,
      params: {
        paperId,
        addRawHtml,
      },
      requestName: '获取划题试卷内容',
    })
    .then(data => {
      let questions = (data.questions || []).map(q => Question.import(q))
      let paper = new Paper(data.info, null, data.structureJson, questions)
      return {
        rawHtml: data.rawHtml,
        paper,
      }
    })
}

/**
 * 划题添加题目
 */
export function apiAddDividePaperQuestions({ paperId, paperStructure, questions }) {
  return Promise.all(questions.map(q => q.export())).then(exportedQuestions => {
    return ajax
      .post({
        url: `ques/paperUpload/dividePaperQuestions`,
        params: { paperId },
        data: {
          paperStructure: JSON.stringify(paperStructure.export()),
          questions: exportedQuestions,
        },
        requestName: '划题添加题目',
      })
      .then(resQuestions => resQuestions.map(q => Question.import(q)))
  })
}

/**
 * 划题修改题目
 */
export function apiChangeDividePaperQuestions({ paperId, paperStructure, questions }) {
  return Promise.all(questions.map(q => q.export())).then(exportedQuestions => {
    return ajax
      .put({
        url: `ques/paperUpload/dividePaperQuestions`,
        params: { paperId },
        data: {
          paperStructure: JSON.stringify(paperStructure.export()),
          questions: exportedQuestions,
        },
        requestName: '划题修改题目',
      })
      .then(resQuestions => resQuestions.map(q => Question.import(q)))
  })
}

/**
 * 划题删除题目
 */
export function apiDeleteDividePaperQuestions({ paperId, paperStructure, questionIds }) {
  return ajax.delete({
    url: `ques/paperUpload/dividePaperQuestions`,
    params: {
      paperId,
    },
    data: {
      paperStructure: JSON.stringify(paperStructure.export()),
      questionIds,
    },
    requestName: '划题删除题目',
  })
}

/**
 * 划题修改试卷结构
 */
export function apiChangeDividePaperStructure(paper) {
  let paperId = paper.paperInfo.id
  let paperStructure = JSON.stringify(paper.paperStructure.export())
  return ajax.request({
    url: `ques/paperUpload/dividePaperStructure`,
    method: 'put',
    params: {
      paperId,
    },
    data: {
      paperStructure,
    },
    useFormData: true,
    requestName: '划题修改试卷结构',
  })
}

/**
 * 完成划题
 */
export function apiFinishDividePaper({ paperId, allowNoScore }) {
  return ajax.put({
    url: `ques/paperUpload/finishDivide`,
    params: {
      paperId,
      allowNoScore,
    },
  })
}

/**
 * 重启划题
 */
export function apiRestartDividePaper(paperId) {
  return ajax.put({
    url: `ques/paperUpload/restartDivide`,
    params: {
      paperId,
    },
  })
}
