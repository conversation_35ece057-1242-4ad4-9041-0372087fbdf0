import ajax from '@/api/ajax'

export function apiGetPaperCttParam({ examId, templateId, examSubjectId }) {
  return ajax.get({
    url: 'report/paperQuality/paperCttParam',
    params: {
      examId,
      templateId,
      examSubjectId,
    },
    requestName: '获取试卷基本指标',
  })
}

export function apiGetQuestionsIrtParams({ examId, templateId, examSubjectId }) {
  return ajax.get({
    url: 'report/paperQuality/questionsIrtParams',
    params: {
      examId,
      templateId,
      examSubjectId,
    },
    requestName: '获取题目特征曲线参数',
  })
}

export function apiGetPaperGtParam({ examId, templateId, examSubjectId }) {
  return ajax.get({
    url: 'report/paperQuality/paperGtParam',
    params: {
      examId,
      templateId,
      examSubjectId,
    },
    requestName: '获取试卷概化参数',
  })
}
