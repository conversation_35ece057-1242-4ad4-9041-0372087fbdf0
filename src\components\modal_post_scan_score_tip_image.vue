<template>
  <div class="img-wrapper" :class="wrapperClass">
    <div class="img-caption">{{ caption }}</div>
    <img :src="src" />
  </div>
</template>

<script>
  export default {
    props: {
      src: String,
      caption: String,
      type: String,
    },
    computed: {
      wrapperClass() {
        return {
          primary: this.type == 'primary',
          warning: this.type == 'warning',
          error: this.type == 'error',
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .img-wrapper {
    @include flex(row, flex-start, center);
    margin-top: 8px;
    margin-bottom: 18px;

    .img-caption {
      width: 80px;
    }

    img {
      height: 44px;
    }
  }

  .img-wrapper.primary {
    .img-caption {
      color: $color-primary;
    }
  }

  .img-wrapper.warning {
    .img-caption {
      color: $color-warning;
    }
  }

  .img-wrapper.error {
    .img-caption {
      color: $color-error;
    }
  }
</style>
