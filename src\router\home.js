import Role from '@/enum/user/role'
import SchoolType from '@/enum/school/school_type'

export default {
  name: 'home',
  path: 'home',
  meta: {
    menu: '首页',
    roles: user => {
      return (
        user.schoolType == SchoolType.School.id ||
        (user.schoolType == SchoolType.Bureau.id &&
          user.roles.some(r => [Role.SchoolAdministrator.id, Role.ResearcherLeader.id].includes(r)))
      )
    },
  },
  component: () => import('@/views/home/<USER>'),
}
