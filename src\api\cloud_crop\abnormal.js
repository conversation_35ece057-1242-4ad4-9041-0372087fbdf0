import ajax from '@/api/ajax'

export function apiGetAbnormalStats(examSubjectId) {
  return ajax.get({
    url: 'imgproc/abnormal/stats',
    params: {
      examSubjectId,
    },
    requestName: '获取异常统计',
  })
}

export function apiGetOneDuplicate(examSubjectId, roomId) {
  return ajax.get({
    url: 'imgproc/abnormal/oneDuplicate',
    params: {
      examSubjectId,
      roomId,
    },
    requestName: '获取重号异常卷',
  })
}

export function apiGetOneCorner(examSubjectId, roomId) {
  return ajax.get({
    url: 'imgproc/abnormal/oneCorner',
    params: {
      examSubjectId,
      roomId,
    },
    requestName: '获取折角异常卷',
  })
}

export function apiVerifyCorner(examSubjectId, scanInfoSerialNo) {
  return ajax.put({
    url: 'imgproc/abnormal/verifyCorner',
    params: {
      examSubjectId,
      scanInfoSerialNo,
    },
    requestName: '忽略折角异常',
  })
}

export function apiGetOneAbsent(examSubjectId, roomId) {
  return ajax.get({
    url: 'imgproc/abnormal/oneQk',
    params: {
      examSubjectId,
      roomId,
    },
    requestName: '获取缺考异常卷',
  })
}

export function apiChangeToAbsent(examSubjectId, scanInfoSerialNo) {
  return ajax.put({
    url: 'imgproc/abnormal/changeToQk',
    params: {
      examSubjectId,
      scanInfoSerialNo: scanInfoSerialNo,
    },
    requestName: '修改为缺考',
  })
}

export function apiChangeToNotAbsent(examSubjectId, scanInfoSerialNo) {
  return ajax.put({
    url: 'imgproc/abnormal/changeToUnQk',
    params: {
      examSubjectId,
      scanInfoSerialNo: scanInfoSerialNo,
    },
    requestName: '修改为非缺考',
  })
}

export function apiVerifyAbsent(examSubjectId, scanInfoSerialNo) {
  return ajax.put({
    url: 'imgproc/abnormal/verifyQk',
    params: {
      examSubjectId,
      scanInfoSerialNo,
    },
    requestName: '确认缺考',
  })
}

export function apiGetOneObjective(examSubjectId, roomId) {
  return ajax.get({
    url: 'imgproc/abnormal/oneObjective',
    params: {
      examSubjectId,
      roomId,
    },
    requestName: '获取客观题异常卷',
  })
}

export function apiGetNextObjective(examSubjectId, scanInfoSerialNo, roomId) {
  return ajax.get({
    url: 'imgproc/abnormal/nextObjective',
    params: {
      examSubjectId,
      scanInfoSerialNo,
      roomId,
    },
    requestName: '获取下一份客观题异常卷',
  })
}

export function apiGetPreviousObjective(examSubjectId, scanInfoSerialNo, roomId) {
  return ajax.get({
    url: 'imgproc/abnormal/previousObjective',
    params: {
      examSubjectId,
      scanInfoSerialNo,
      roomId,
    },
    requestName: '获取上一份客观题异常卷',
  })
}

export function apiChangeObjective(data) {
  return ajax.put({
    url: 'imgproc/abnormal/changeObjective',
    params: {
      examSubjectId: data.examSubjectId,
      scanInfoSerialNo: data.scanInfoSerialNo,
    },
    data: data.objectives || [],
    requestName: '修改客观题',
  })
}

export function apiChangeAndVerifyObjective(data) {
  return ajax.put({
    url: 'imgproc/abnormal/changeAndVerifyObjective',
    params: {
      examSubjectId: data.examSubjectId,
      scanInfoSerialNo: data.scanInfoSerialNo,
    },
    data: data.objectives || [],
    requestName: '修改客观题',
  })
}

export function apiVerifyAllObjectives(examSubjectId) {
  return ajax.put({
    url: 'imgproc/abnormal/verifyAllObjectives',
    params: {
      examSubjectId,
    },
    requestName: '忽略所有客观题异常',
  })
}

export function apiVerifyPreviousObjectives(params) {
  return ajax.put({
    url: 'imgproc/abnormal/verifyPreviousObjectives',
    params: {
      examSubjectId: params.examSubjectId,
      scanInfoSerialNo: params.scanInfoSerialNo,
    },
    requestName: '忽略已浏览客观题异常',
  })
}

export function apiVerifyObjective(examSubjectId, scanInfoSerialNo) {
  return ajax.put({
    url: 'imgproc/abnormal/verifyObjective',
    params: {
      examSubjectId,
      scanInfoSerialNo,
    },
    requestName: '忽略客观题异常',
  })
}

export function apiGetOneSelect(examSubjectId, roomId) {
  return ajax.get({
    url: 'imgproc/abnormal/oneSelects',
    params: {
      examSubjectId,
      roomId,
    },
    requestName: '获取选做题异常卷',
  })
}

export function apiGetNextSelect(examSubjectId, scanInfoSerialNo, roomId) {
  return ajax.get({
    url: 'imgproc/abnormal/nextSelect',
    params: {
      examSubjectId,
      scanInfoSerialNo,
      roomId,
    },
    requestName: '获取下一份选做题异常卷',
  })
}

export function apiGetPreviousSelect(examSubjectId, scanInfoSerialNo, roomId) {
  return ajax.get({
    url: 'imgproc/abnormal/previousSelect',
    params: {
      examSubjectId,
      scanInfoSerialNo,
      roomId,
    },
    requestName: '获取上一份选做题异常卷',
  })
}

export function apiChangeSelect(data) {
  return ajax.put({
    url: 'imgproc/abnormal/changeSelect',
    params: {
      examSubjectId: data.examSubjectId,
      scanInfoSerialNo: data.scanInfoSerialNo,
    },
    data: data.selects || [],
    requestName: '修改选做题',
  })
}

export function apiChangeAndVerifySelect(data) {
  return ajax.put({
    url: 'imgproc/abnormal/changeAndVerifySelect',
    params: {
      examSubjectId: data.examSubjectId,
      scanInfoSerialNo: data.scanInfoSerialNo,
    },
    data: data.selects || [],
    requestName: '修改选做题',
  })
}

export function apiVerifyAllSelects(examSubjectId) {
  return ajax.put({
    url: 'imgproc/abnormal/verifyAllSelects',
    params: {
      examSubjectId,
    },
    requestName: '忽略所有选做题异常',
  })
}

export function apiVerifySelect(examSubjectId, scanInfoSerialNo) {
  return ajax.put({
    url: 'imgproc/abnormal/verifySelect',
    params: {
      examSubjectId,
      scanInfoSerialNo,
    },
    requestName: '忽略选做题异常',
  })
}

export function apiChangeAdmissionNum(params) {
  return ajax.put({
    url: 'imgproc/abnormal/changeZkzh',
    params: {
      examSubjectId: params.examSubjectId,
      scanInfoSerialNo: params.scanInfoSerialNo,
      newStudentSerialNo: params.newStudentId,
    },
    requestName: '修改准考号',
  })
}

export function apiDeletePaper(examSubjectId, scanInfoSerialNo) {
  return ajax.delete({
    url: 'imgproc/abnormal/deleteBySerialNo',
    params: {
      examSubjectId,
      scanInfoSerialNo: scanInfoSerialNo,
    },
    requestName: '删除卷',
  })
}

export function apiRecoverPaper(examSubjectId, scanInfoSerialNo) {
  return ajax.put({
    url: 'imgproc/abnormal/recoverBySerialNo',
    params: {
      examSubjectId,
      scanInfoSerialNo,
    },
    requestName: '恢复删除卷',
  })
}

export function apiGetOperationHistory(params) {
  return ajax.get({
    url: 'imgproc/abnormal/operationHistory',
    params: {
      examSubjectId: params.examSubjectId,
      operationType: params.operationType || undefined,
      beginTime: params.beginTimeStr || undefined,
      endTime: params.endTimeStr || undefined,
      admissionNum: params.admissionNum || '',
      page: params.currentPage,
      size: params.pageSize,
    },
    requestName: '获取操作历史记录',
  })
}
