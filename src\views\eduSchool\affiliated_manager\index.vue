<template>
  <div class="container-school-affiliated-manager">
    <div v-if="pagePermission" class="content">
      <div class="header-tools-bar">
        <!-- <Select v-model="schoolName" style="width: 200px" placeholder="下属学校（单位）名称">
        <Option v-for="(unit, uIndex) of affiliatedUnits" :key="'unit' + uIndex"></Option>
      </Select> -->
        <div class="bar-left">
          <Button type="default" shape="circle" icon="md-cloud-upload" @click="showModalImportAffiliatedManagers"
            >导入管理员</Button
          >
          <Button
            type="default"
            shape="circle"
            icon="md-cloud-download"
            style="margin-left: 10px"
            @click="exportAffiliatedManagersExcel"
            >导出管理员列表</Button
          >
        </div>

        <div class="bar-right">
          <Input
            v-model="schoolName"
            style="width: 200px; margin-right: 10px"
            placeholder="下属学校（单位）名称"
            icon="md-search"
            clearable
            @on-change="fetchData"
          ></Input>
          <Input
            v-model="mobile"
            style="width: 200px"
            placeholder="手机号码"
            icon="md-search"
            clearable
            @on-change="fetchData"
          ></Input>
        </div>
      </div>

      <div class="table-panel">
        <Table :columns="tableColumns" :data="tableData"></Table>

        <div class="pager">
          <Page
            :total="pageTotal"
            :page-size="pageSize"
            :model-value="currentPage"
            show-elevator
            show-sizer
            show-total
            :page-size-opts="[10, 20, 50, 100]"
            @on-change="changePage"
            @on-page-size-change="changePageSize"
          ></Page>
        </div>
      </div>
    </div>
    <div v-else class="no-permission">对不起，您当前账号没有权限查看此页面，请变更学校或账号后重试</div>

    <ModalAdministratorImport v-model="modalImportMangerVisible" @on-refresh="fetchData" />
  </div>
</template>

<script>
  import ModalAdministratorImport from './components/modal_administrator_import.vue'
  import { apiExportAffiliatedManagersOfInstitution } from '@/api/user'

  import { scrollToTop } from '@/utils/scroll'
  import { exportExcel } from '@/utils/excel_export'

  export default {
    components: {
      ModalAdministratorImport,
    },
    data() {
      return {
        // requestParams
        schoolName: '',
        mobile: '',
        currentPage: 1,
        pageSize: 10,

        pageTotal: 0,

        debounceSearch: () => {},
        tableData: [],
        modalImportMangerVisible: false,
      }
    },

    computed: {
      user() {
        return this.$store.getters['user/info']
      },

      pagePermission() {
        if (this.user) {
          return this.user.isSystem && this.user.schoolType == 1
        } else {
          return false
        }
      },

      tableColumns() {
        let columns = [
          {
            title: '姓名',
            key: 'realName',
            align: 'center',
          },
          {
            title: '单位名称',
            key: 'schoolName',
            align: 'center',
          },
          {
            title: '单位类型',
            key: 'schoolType',
            align: 'center',
            render: (h, params) =>
              h(
                'span',
                {
                  style: {
                    'text-align': 'center',
                  },
                },
                params.row.schoolType ? '机构' : '学校'
              ),
          },
          {
            title: '单位代码',
            key: 'schoolCode',
            align: 'center',
          },
          {
            title: '手机号码',
            key: 'mobile',
            align: 'center',
          },
        ]

        return columns
      },
    },

    created() {
      this.fetchData()
    },

    methods: {
      fetchData() {
        apiExportAffiliatedManagersOfInstitution({
          schoolName: this.schoolName.trim(),
          mobile: this.mobile.trim(),
          currentPage: this.currentPage,
          pageSize: this.pageSize,
        })
          .then(response => {
            this.tableData = response.records
            this.pageTotal = response.total
          })
          .finally(() => scrollToTop())
      },

      changePage(page) {
        this.currentPage = page
        this.fetchData()
      },

      changePageSize(size) {
        this.pageSize = size
        this.changePage(1)
      },

      showModalImportAffiliatedManagers() {
        this.modalImportMangerVisible = true
      },

      importAffiliatedManagersExcel() {},

      exportAffiliatedManagersExcel() {
        this.$Spin.show({
          render: h =>
            h(
              'div',
              {
                style: {
                  fontSize: '24px',
                },
              },
              '正在导出……'
            ),
        })

        apiExportAffiliatedManagersOfInstitution({
          schoolName: this.schoolName,
          mobile: this.mobile,
          currentPage: 1,
          pageSize: this.pageTotal,
        })
          .then(response => {
            if (!response || !response.records || !response.records.length) {
              this.$Message.info({
                content: '暂无数据',
              })
              return
            }

            let tableColumns = [
              {
                title: '姓名',
                key: 'realName',
                width: 'auto',
              },
              {
                title: '单位名称',
                key: 'schoolName',
                width: 'auto',
              },
              {
                title: '单位类型',
                key: row => (row.schoolType ? '机构' : '学校'),
                width: 'auto',
              },
              {
                title: '单位代码',
                key: 'schoolCode',
                width: 'auto',
              },
              {
                title: '手机号码',
                key: 'mobile',
                width: 'auto',
              },
            ]

            exportExcel(
              [
                {
                  sheetName: '管理员',
                  rows: response.records,
                  columns: tableColumns,
                },
              ],
              this.user.schoolName + '机构学校管理员列表.xlsx'
            )
          })
          .finally(() => this.$Spin.hide())
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-school-affiliated-manager {
    min-height: 300px;
    margin-top: 10px;
    margin-bottom: 20px;

    .content {
      .header-tools-bar {
        @include flex(row, space-between, center);
        margin-bottom: 10px;
      }

      .table-panel {
        padding: 20px 20px 10px 20px;
        background-color: white;

        .pager {
          margin: 20px 0 10px 0;
          text-align: right;
        }
      }
    }

    .no-permission {
      width: 100%;
      height: 200px;
      margin-top: 60px;
      font-size: $font-size-large-x;
      text-align: center;
    }
  }
</style>
