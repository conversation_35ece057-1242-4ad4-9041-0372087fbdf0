import router from '@/router/index'

export function getNavs(routeName) {
  let route = findRouteByName(routeName)
  let children = (route && route.children) || []
  return children
    .filter(x => x.meta && x.meta.menu)
    .map(x => ({
      name: x.name,
      path: x.path,
      menu: x.meta.menu,
      menuGroupName: x.meta.menuGroupName,
      menuGroupText: x.meta.menuGroupText,
    }))
}

export function findRouteByName(routeName) {
  return _findRouteByName(router.options.routes, routeName)
}

function _findRouteByName(routes, routeName) {
  let finding = routes.find(x => x.name === routeName)
  if (finding) {
    return finding
  } else {
    for (let i = 0; i < routes.length; i++) {
      let children = routes[i].children
      if (children && children.length) {
        let finding = _findRouteByName(children, routeName)
        if (finding) {
          return finding
        }
      }
    }
  }
}

/**
 * 返回上个页面，如果历史记录中没有上个页面，则跳转到默认页面
 * @param {*} router
 * @param {*} defaultRoute
 */
export function backOrDefault(router, defaultRoute) {
  let hasBack = history && history.state && history.state.back
  if (hasBack) {
    router.go(-1)
  } else {
    router.push(defaultRoute)
  }
}
