<template>
  <div class="container-student">
    <div class="action-box">
      <div class="action-box-left">
        <div class="screen-box">
          <div class="screen-grade-box">
            <span class="label">年级</span>
            <Select v-model="currentGradeId" clearable style="width: 90px" @on-change="changeGrade">
              <Option v-for="item in gradeList" :key="item.gradeId" :value="item.gradeId">{{ item.gradeName }}</Option>
            </Select>
          </div>
          <div class="screen-class-box">
            <span class="label">班级</span>
            <Select v-model="currentClassId" clearable style="width: 140px" @on-change="changeClass">
              <Option v-for="item in classList" :key="item.classId" :value="item.classId">{{ item.className }}</Option>
            </Select>
          </div>
        </div>
        <div class="search-box">
          <SearchInput v-model="keyword" placeholder="姓名/学籍号" @update:model-value="changeKeyword" />
        </div>
      </div>
      <div v-if="isSysAdmin" class="action-box-right">
        <Button type="primary" @click="handleStudentImport">导入本校学生</Button>
        <Button v-if="currentSelectedSchool.schoolType === 1" type="primary" @click="handleMultipleSchoolStudentImport"
          >导入下属学校学生</Button
        >

        <template v-if="isSystem && currentSelectedSchoolIsInstitution">
          <Dropdown transfer trigger="hover" placement="bottom-end" @on-click="handleExportDropdownClick">
            <Button type="default">导出 <Icon type="ios-arrow-down" /></Button>
            <template #list>
              <DropdownMenu>
                <DropdownItem v-if="currentSelectedSchool.schoolType === 0" name="student">导出学生</DropdownItem>
                <DropdownItem name="institutionStudent">导出下属学校学生</DropdownItem>
                <DropdownItem name="institutionGradeStudentNumber">导出下属学校年级学生人数</DropdownItem>
              </DropdownMenu>
            </template>
          </Dropdown>
        </template>
        <Button v-else-if="currentSelectedSchool.schoolType === 0" type="default" @click="handleStudentExport"
          >导出学生</Button
        >

        <Button type="warning" :disabled="!studentList.length" ghost @click="handleStudentDelete">批量删除学生</Button>
      </div>
    </div>
    <div class="block-content">
      <div class="table-box">
        <Table :columns="tableColumns" :data="studentList"></Table>
      </div>
      <div class="pager">
        <Page
          :total="pageTotal"
          :page-size="pageSize"
          :model-value="currentPage"
          show-elevator
          show-sizer
          show-total
          :page-size-opts="[10, 20, 50, 100]"
          @on-change="changePage"
          @on-page-size-change="changePageSize"
        ></Page>
      </div>
    </div>
    <ModalStudentImport
      v-model="modalStudentImportVisible"
      :is-multiple-school="isMultipleSchool"
      :school-id="currentSelectedSchool.value"
      @on-refresh="onRefresh"
    />
    <ModalStudentDelete
      v-model="modalStudentDeleteVisible"
      :school-id="currentSelectedSchool.value"
      :grade-list="gradeList"
      @on-refresh="onRefresh"
    />
    <ModalStudentExport v-model="modalStudentExportVisible" :school="currentSelectedSchool" :grade-list="gradeList" />
    <modal-institution-student-export
      v-model="modalInstitutionStudentExportVisible"
      :school="currentSelectedSchool"
    ></modal-institution-student-export>
    <modal-export-institution-grade-student-number
      v-model="isModalExportInstitutionGradeStudentNumberShowed"
      :school="currentSelectedSchool"
    ></modal-export-institution-grade-student-number>
  </div>
</template>

<script>
  import SearchInput from '@/components/searchInput.vue'
  import ModalStudentImport from './modal_student_import.vue'
  import ModalStudentDelete from './modal_student_delete.vue'
  import ModalStudentExport from './modal_student_export.vue'
  import ModalInstitutionStudentExport from './modal_institution_student_export.vue'
  import ModalExportInstitutionGradeStudentNumber from './modal_export_institution_grade_student_number.vue'

  import { apiGetStudentList, apiGetGradeClassList } from '@/api/user/school'

  export default {
    components: {
      SearchInput,
      ModalStudentImport,
      ModalStudentDelete,
      ModalStudentExport,
      'modal-institution-student-export': ModalInstitutionStudentExport,
      'modal-export-institution-grade-student-number': ModalExportInstitutionGradeStudentNumber,
    },
    props: {
      currentSelectedSchool: {
        type: Object,
        default: () => {},
      },
      isSysAdmin: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        currentPage: 1,
        pageSize: 10,
        keyword: '',
        currentGradeId: '',
        currentClassId: '',
        studentList: [],
        pageTotal: 0,
        gradeList: [],
        classList: [],
        modalStudentImportVisible: false,
        isMultipleSchool: false,
        modalStudentDeleteVisible: false,
        modalStudentExportVisible: false,
        modalInstitutionStudentExportVisible: false,
        isModalExportInstitutionGradeStudentNumberShowed: false,
      }
    },
    computed: {
      isSystem() {
        return this.$store.getters['user/isSystem'] || false
      },
      currentSelectedSchoolIsInstitution() {
        return (
          (this.currentSelectedSchool &&
            this.currentSelectedSchool.schoolType &&
            Boolean(this.currentSelectedSchool.schoolType)) ||
          false
        )
      },
      tableColumns() {
        let columns = [
          {
            title: '姓名',
            key: 'studentName',
            width: 100,
          },
          {
            title: '性别',
            key: 'sex',
            align: 'center',
          },
          {
            title: '年级',
            key: 'gradeName',
            align: 'center',
          },
          {
            title: '班级',
            align: 'center',
            minWidth: 100,
            render: (h, params) => {
              let list = params.row.classes

              return h(
                'div',
                {
                  style: {
                    marginTop: '1px',
                  },
                },
                list.map(c =>
                  h(
                    'p',
                    {
                      style: {
                        textAlign: 'center',
                        lineHeight: '22px',
                        marginBottom: '1px',
                      },
                    },
                    c.className
                  )
                )
              )
            },
          },
          {
            title: '学籍号',
            key: 'studentNo',
            align: 'center',
          },
          {
            title: '校内考试号',
            key: 'examNo',
            align: 'center',
          },
          {
            title: '家长',
            align: 'center',
            minWidth: 100,
            render: (h, params) => {
              let parentStrs = (params.row.parents || []).map(x =>
                x.mobile ? `${x.realName}（${x.mobile}）` : x.realName
              )
              return h(
                'div',
                {},
                parentStrs.map(s => h('p', {}, s))
              )
            },
          },
        ]

        return columns
      },
    },
    watch: {
      currentSelectedSchool: {
        handler: async function (val) {
          if (val && val.value) {
            this.resetData()
            await this.getTheGradeClassList()
            // 如果已选择的年级在切换后的学校不存在就重置，存在则不重置，上级的需求。。
            if (this.gradeList.every(g => g.gradeId !== this.currentGradeId)) {
              this.currentGradeId = ''
            }
            this.getStudentList()
          }
        },
        immediate: true,
        deep: true,
      },
    },
    created() {},
    methods: {
      resetData() {
        this.currentPage = 1
        this.studentList = []
        this.gradeList = []
        this.classList = []
        this.currentClassId = ''
        this.keyword = ''
      },
      onRefresh() {
        this.resetData()
        this.getStudentList()
      },
      changeGrade(value) {
        const { gradeList } = this
        this.currentPage = 1
        const theGrade = gradeList.find(g => g.gradeId === value)
        this.classList = (theGrade && theGrade.classes) || []
        this.currentClassId = ''
        this.getStudentList()
      },
      changeClass() {
        this.currentPage = 1
        this.getStudentList()
      },
      changeKeyword(keyword) {
        this.keyword = keyword
        this.currentPage = 1

        this.getStudentList()
      },
      changePage(page) {
        this.currentPage = page
        this.getStudentList()
      },
      changePageSize(size) {
        this.pageSize = size
        this.currentPage = 1
        this.getStudentList()
      },
      getTheGradeClassList() {
        return apiGetGradeClassList({
          schoolId: this.currentSelectedSchool.value,
        })
          .then(res => {
            this.gradeList = res || []
          })
          .catch(() => {
            this.gradeList = []
          })
      },
      getStudentList() {
        const { currentPage, pageSize, currentClassId, currentGradeId, keyword, currentSelectedSchool } = this
        apiGetStudentList({
          schoolId: currentSelectedSchool.value,
          gradeId: currentGradeId,
          classId: currentClassId,
          keyword,
          currentPage,
          pageSize,
        })
          .then(res => {
            this.studentList = res.list || []
            this.pageTotal = res.total
          })
          .catch(() => {
            this.studentList = []
            this.pageTotal = 0
          })
      },
      handleStudentImport() {
        this.modalStudentImportVisible = true
        this.isMultipleSchool = false
      },
      handleStudentExport() {
        this.modalStudentExportVisible = true
      },
      handleMultipleSchoolStudentImport() {
        this.modalStudentImportVisible = true
        this.isMultipleSchool = true
      },
      handleInstitutionStudentExport() {
        if (this.isSystem) {
          this.modalInstitutionStudentExportVisible = true
        }
      },
      handleStudentDelete() {
        this.modalStudentDeleteVisible = true
      },
      handleExportDropdownClick(name) {
        if (name === 'institutionStudent') {
          this.handleInstitutionStudentExport()
        } else if (name === 'institutionGradeStudentNumber') {
          this.isModalExportInstitutionGradeStudentNumberShowed = true
        } else if (name === 'student') {
          this.handleStudentExport()
        }
      },
    },
  }
</script>

<style scoped lang="scss">
  .container-student {
    .action-box {
      @include flex(row, space-between, center);
      margin-bottom: 20px;

      .action-box-left {
        @include flex(row, flex-start, center);
      }
    }

    .search-box {
      width: 140px;
      margin-right: 10px;
    }

    .screen-box {
      @include flex(row, flex-start, flex-start);
      margin-right: 10px;

      .screen-grade-box {
        margin-right: 6px;
      }

      .label {
        margin-right: 4px;
      }
    }

    .pager {
      @include flex(row, flex-end, center);
      margin-top: 30px;
    }
  }
</style>
