/**
 * 题组类型
 */

import Enum from '@/enum/enum'

export default new Enum({
  Objective: {
    id: 'Objective',
    name: '客观题',
  },
  FillBlank: {
    id: 'FillBlank',
    name: '填空题',
  },
  Essay: {
    id: 'Essay',
    name: '解答题',
  },
  ChineseWriting: {
    id: 'ChineseWriting',
    name: '语文作文',
  },
  EnglishWriting: {
    id: 'EnglishWriting',
    name: '英语作文',
  },
  Trunk: {
    id: 'Trunk',
    name: '材料',
  },
  ContentObjective: {
    id: 'ContentObjective',
    name: '带题干客观题',
  },
  ContentObjectiveNoOption: {
    id: 'ContentObjectiveNoOption',
    name: '带题干无选项客观题',
  },
})
