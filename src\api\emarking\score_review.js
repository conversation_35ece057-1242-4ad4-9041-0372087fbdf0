import ajax from '@/api/ajax'

export function apiGetScoreReviewAbsent(params) {
  return ajax.get({
    url: 'mark/scorereview/absent/page',
    params: {
      examSubjectId: params.examSubjectId,
      sign: params.sign,
      pageNo: params.currentPage,
      pageSize: params.pageSize,
    },
    requestName: '获取缺考考生',
  })
}

export function apiGetScoreReviewZero(params) {
  return ajax.get({
    url: 'mark/scorereview/zero/page',
    params: {
      examSubjectId: params.examSubjectId,
      pageNo: params.currentPage,
      pageSize: params.pageSize,
    },
    requestName: '获取零分考生',
  })
}

export function apiGetScoreReviewSingleSubjectException(params) {
  return ajax.get({
    url: 'mark/scorereview/subjectException/page',
    params: {
      examSubjectId: params.examSubjectId,
      rateOfAnySubject: params.rateSubject,
      rateOfAnyOtherSubject: params.rateOther,
      comparisonSymbolOfAnySubject: params.comparatorSubject,
      comparisonSymbolOfAnyOtherSubject: params.comparatorOther,
      pageNo: params.currentPage,
      pageSize: params.pageSize,
    },
    requestName: '获取偏科考生',
  })
}

export function apiGetScoreReviewAllSubjectException(params) {
  return ajax.get({
    url: 'mark/scorereview/allSubjectException/page',
    params: {
      examId: params.examId,
      rateOfAnySubject: params.rateSubject,
      rateOfAnyOtherSubject: params.rateOther,
      comparisonSymbolOfAnySubject: params.comparatorSubject,
      comparisonSymbolOfAnyOtherSubject: params.comparatorOther,
      pageNo: params.currentPage,
      pageSize: params.pageSize,
    },
    requestName: '获取偏科考生',
  })
}

export function apiGetScoreReviewQuestionException(params) {
  return ajax.get({
    url: 'mark/scorereview/quesException/page',
    params: {
      examSubjectId: params.examSubjectId,
      rateOfObjectiveScore: params.rateObjective,
      rateOfSubjectiveScore: params.rateSubjective,
      comparisonSymbolOfObjectiveScore: params.comparatorObjective,
      comparisonSymbolOfSubjectiveScore: params.comparatorSubjective,
      pageNo: params.currentPage,
      pageSize: params.pageSize,
    },
    requestName: '获取主客观成绩异常考生',
  })
}
