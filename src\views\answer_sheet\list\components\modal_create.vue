<template>
  <Modal :model-value="modelValue" title="创建测练答题卡" :width="700" @on-visible-change="handleVisibleChange">
    <section v-if="step == 'selectType'" class="section-type">
      <div
        v-for="type in createTypes"
        :key="type.id"
        class="create-type-item"
        :class="{ active: type.id == createType }"
        @click="createType = type.id"
      >
        {{ type.name }}
      </div>
    </section>
    <section v-if="step == 'selectPaper'" class="section-paper">
      <div class="filter">
        <Cascader
          :model-value="[stageId, subjectId]"
          :data="stageSubjectCascaderData"
          :clearable="false"
          style="width: 200px"
          @on-change="changeStageSubject"
        ></Cascader>
        <Input
          v-model="keyword"
          clearable
          placeholder="试卷名称"
          maxlength="30"
          suffix="md-search"
          style="width: 200px"
          @on-change="changeKeyword"
        />
      </div>
      <Table
        :columns="tablePaperColumns"
        :data="papers"
        :max-height="tablePaperMaxHeight"
        @on-row-click="selectedPaper = $event"
      ></Table>
    </section>

    <template #footer>
      <div class="footer">
        <div v-if="step == 'selectPaper' && selectedPaper" class="selected-paper">{{ selectedPaper.name }}</div>
        <div class="btns">
          <Button type="text" @click="handleCancel">取消</Button>
          <Button type="primary" :disabled="isBtnOKDisabled" @click="handleOK">确定</Button>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { Checkbox } from 'view-ui-plus'

  import { apiGetComposedPapers } from '@/api/qlib/paper_compose'
  import { apiGetAnswerSheetIdByPaperId } from '@/api/emarking/answer_sheet'
  import { debounce } from '@/utils/function'
  import { formatDateTime } from '@/utils/date'

  export default {
    props: {
      modelValue: Boolean,
      stages: Array,
    },
    emits: ['update:model-value'],
    data() {
      return {
        // 步骤：选择创建方式、选择试卷
        step: 'selectType',
        // 创建方式
        createTypes: [
          {
            id: 'manual',
            name: '手动添加题目创建',
          },
          {
            id: 'paper',
            name: '由题库试卷创建',
          },
        ],
        createType: '',
        // 试卷查询
        stageId: '',
        subjectId: '',
        keyword: '',
        // 试卷列表
        papers: [],
        // 选中试卷
        selectedPaper: null,
        // 表格高度
        tablePaperMaxHeight: window.innerHeight - 300,
      }
    },
    computed: {
      stageSubjectCascaderData() {
        return this.stages.map(stage => ({
          value: stage.id,
          label: stage.name,
          children: stage.subjects.map(s => ({
            value: s.id,
            label: s.name,
          })),
        }))
      },
      tablePaperColumns() {
        return [
          {
            title: ' ',
            width: 50,
            render: (h, params) =>
              h(Checkbox, {
                modelValue: Boolean(this.selectedPaper && this.selectedPaper.id == params.row.id),
                onOnChange: value => {
                  this.selectedPaper = value ? params.row : null
                },
              }),
          },
          {
            title: '试卷名称',
            key: 'name',
          },
          {
            title: '学段学科',
            width: 120,
            align: 'center',
            render: (h, params) => h('span', {}, (params.row.stage.name || '') + (params.row.subject.name || '')),
          },
          {
            title: '创建时间',
            width: 155,
            align: 'center',
            render: (h, params) => h('span', {}, params.row.updateTime ? formatDateTime(params.row.updateTime) : ''),
          },
        ]
      },
      isBtnOKDisabled() {
        if (this.step == 'selectType') {
          return this.createTypes.every(type => type.id != this.createType)
        } else if (this.step == 'selectPaper') {
          return !this.selectedPaper
        } else {
          return true
        }
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.resetData()
        }
      },
    },
    methods: {
      resetData() {
        this.step = 'selectType'
        this.createType = ''
        this.stageId = ''
        this.subjectId = ''
        this.keyword = ''
        this.papers = []
        this.selectedPaper = null
        let teachings = this.$store.getters['user/info'].teachings
        if (teachings.legnth > 0) {
          this.stageId = teachings[0].stageId
          this.subjectId = teachings[0].subjectId
        } else {
          this.stageId = this.stages[0].id
          this.subjectId = this.stages[0].subjects[0].id
        }
      },

      /**
       * 查询试卷
       */
      loadPapers() {
        this.selectedPaper = null
        apiGetComposedPapers({
          stage: this.stageId,
          subject: this.subjectId,
          pageSize: 10000,
          currentPage: 1,
          keyword: this.keyword,
        }).then(({ papers }) => {
          this.papers = papers
        })
      },
      changeStageSubject([stageId, subjectId]) {
        this.stageId = stageId
        this.subjectId = subjectId
        this.loadPapers()
      },
      changeKeyword: debounce(function () {
        this.loadPapers()
      }, 500),

      /**
       * 完成
       */
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
      handleCancel() {
        this.$emit('update:model-value', false)
      },
      handleOK() {
        if (this.step == 'selectType') {
          this.handleSelectTypeOK()
        } else if (this.step == 'selectPaper') {
          this.handleSelectPaperOK()
        }
      },
      handleSelectTypeOK() {
        if (this.createType == 'manual') {
          this.handleCancel()
          this.$router.push({
            name: 'answerSheet-editManual',
            params: {
              sheetId: 'new',
            },
          })
        } else if (this.createType == 'paper') {
          this.step = 'selectPaper'
          this.loadPapers()
        }
      },
      handleSelectPaperOK() {
        if (!this.selectedPaper) {
          this.$Message.info({
            content: '请选择试卷',
          })
          return
        }
        let paperId = this.selectedPaper.id
        let toEditPage = () => {
          this.handleCancel()
          this.$router.push({
            name: 'answerSheet-editPaper',
            params: {
              paperId: this.selectedPaper.id,
            },
          })
        }
        apiGetAnswerSheetIdByPaperId(paperId).then(sheetId => {
          if (sheetId) {
            this.$Modal.confirm({
              title: '您选择的试卷已有测练答题卡',
              content: '是否编辑该测练答题卡？',
              onOk: () => {
                toEditPage()
              },
            })
          } else {
            toEditPage()
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .section-type {
    .create-type-item {
      padding: 16px;
      border: 1px solid $color-border;
      border-radius: 5px;
      line-height: 16px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:not(:first-child) {
        margin-top: 16px;
      }

      &:hover {
        background-color: $color-background-light;
      }

      &.active {
        border-color: $color-primary;
        color: white;
        background-color: $color-primary;
      }
    }
  }

  .section-paper {
    .filter {
      @include flex(row, space-between, center);
      margin-bottom: 16px;
    }
  }

  .footer {
    @include flex(row, flex-start, center);
    text-align: left;

    .selected-paper {
      margin-right: 50px;
    }

    .btns {
      flex-grow: 0;
      flex-shrink: 0;
      margin-left: auto;
    }
  }
</style>
