<template>
  <div class="ai-container">
    <router-view v-if="!isRouteIndex"></router-view>
    <div v-else class="ai-content">
      <div class="ai-header">
        <h1>小同AI智能助手</h1>
        <p class="ai-introduction">通过人工智能技术，为教学提供全方位智能辅助，提升教学效率与学习效果</p>
      </div>

      <div class="module-section">
        <h2>智能工具</h2>
        <div class="module-grid">
          <div v-for="module in activeModules" :key="module.path" class="module-card" @click="navigateTo(module.route)">
            <div class="module-icon">
              <Icon :type="module.icon" size="40" />
            </div>
            <div class="module-name">{{ module.name }}</div>
            <div class="module-desc">{{ module.desc }}</div>
            <div class="card-decoration"></div>
          </div>
        </div>
      </div>

      <div class="coming-soon-section">
        <h2>即将推出</h2>
        <div class="module-grid">
          <div v-for="module in comingSoonModules" :key="module.name" class="module-card coming-soon">
            <div class="module-icon">
              <Icon :type="module.icon" size="40" />
            </div>
            <div class="module-name">{{ module.name }}</div>
            <div class="module-desc">{{ module.desc }}</div>
            <div class="coming-soon-badge">即将推出</div>
            <div class="card-decoration"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { Icon } from 'view-ui-plus'

  const router = useRouter()
  const route = useRoute()

  const activeModules = ref([
    {
      name: 'AI成绩分析',
      route: {
        path: '/ai/score-analysis',
      },
      icon: 'md-analytics',
      desc: '智能分析学生成绩，提供多维度学情报告',
    },
  ])

  const comingSoonModules = ref([
    {
      name: 'AI出题组卷',
      path: '/ai/paper-generation',
      icon: 'md-document',
      desc: '智能出题和组卷，快速生成个性化试卷',
    },
    {
      name: 'AI学习诊断',
      icon: 'md-pulse',
      desc: '智能识别学习问题，提供个性化学习建议',
    },
    {
      name: 'AI教学助手',
      icon: 'md-school',
      desc: '智能备课、课堂互动和教案生成，让教学更高效',
    },
  ])

  const isRouteIndex = computed(() => route.name === 'ai')

  function navigateTo(path) {
    router.push(path)
  }
</script>

<style lang="scss" scoped>
  .ai-container {
    padding: 20px;
    background-color: #f5f7fa;
  }

  .ai-content {
    max-width: 1200px;
    margin: 0 auto;
  }

  .ai-header {
    margin-bottom: 35px;
    text-align: center;

    h1 {
      margin-bottom: 15px;
      color: #303133;
      font-weight: 600;
      font-size: 28px;
    }

    .ai-introduction {
      max-width: 700px;
      margin: 0 auto;
      color: #606266;
      font-size: 16px;
      line-height: 1.6;
    }
  }

  .module-section,
  .coming-soon-section {
    margin-bottom: 40px;

    h2 {
      margin-bottom: 20px;
      padding-left: 12px;
      border-left: 4px solid $color-primary;
      color: #303133;
      font-weight: 500;
      font-size: 20px;
      letter-spacing: 0.5px;
    }
  }

  .module-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }

  .module-card {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    padding: 24px;
    border: 1px solid rgba(5, 193, 174, 0.1);
    border-radius: 10px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fefd 0%, #f0fbf9 100%);
    box-shadow: 0 4px 15px 0 rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(5, 193, 174, 0.25);
      box-shadow: 0 8px 20px 0 rgba(5, 193, 174, 0.15);
      transform: translateY(-6px);
    }

    &.coming-soon {
      border: 1px solid rgba(230, 162, 60, 0.1);
      background: linear-gradient(135deg, #fdfaf5 0%, #faf7f2 100%);
      cursor: default;
      opacity: 0.9;

      &:hover {
        border-color: rgba(230, 162, 60, 0.1);
        box-shadow: 0 4px 15px 0 rgba(0, 0, 0, 0.05);
        transform: none;
      }
    }

    .module-icon {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60px;
      margin-bottom: 18px;
      color: $color-primary;

      .ivu-icon {
        font-size: 48px;
      }
    }

    .module-name {
      position: relative;
      z-index: 2;
      margin-bottom: 12px;
      color: #333;
      font-weight: 500;
      font-size: 18px;
    }

    .module-desc {
      position: relative;
      z-index: 2;
      max-width: 90%;
      color: #666;
      font-size: 14px;
      line-height: 1.5;
      text-align: center;
    }

    .coming-soon-badge {
      position: absolute;
      top: 12px;
      right: 12px;
      z-index: 3;
      padding: 4px 10px;
      border-radius: 4px;
      color: white;
      font-weight: 500;
      font-size: 12px;
      background-color: #e6a23c;
      box-shadow: 0 2px 6px rgba(230, 162, 60, 0.25);
    }

    .card-decoration {
      position: absolute;
      right: 0;
      bottom: 0;
      z-index: 1;
      width: 150px;
      height: 150px;
      border-radius: 100% 0 0 0;
      background: radial-gradient(circle at bottom right, rgba(5, 193, 174, 0.15) 0%, rgba(5, 193, 174, 0) 70%);
      transition: opacity 0.3s ease;
    }

    &:nth-child(odd) .card-decoration {
      top: 0;
      right: auto;
      bottom: auto;
      left: 0;
      border-radius: 0 0 100% 0;
      background: radial-gradient(circle at top left, rgba(5, 193, 174, 0.15) 0%, rgba(5, 193, 174, 0) 70%);
    }

    &:hover .card-decoration {
      opacity: 0.8;
    }

    &:before {
      position: absolute;
      top: -50%;
      left: -50%;
      z-index: 1;
      width: 200%;
      height: 200%;
      background: linear-gradient(
        to bottom right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      transform: rotate(45deg);
      transition: all 0.6s ease;
      content: '';
    }

    &:hover:before {
      left: 150%;
    }

    &.coming-soon .card-decoration {
      background: radial-gradient(circle at bottom right, rgba(230, 162, 60, 0.15) 0%, rgba(230, 162, 60, 0) 70%);
    }

    &.coming-soon:nth-child(odd) .card-decoration {
      background: radial-gradient(circle at top left, rgba(230, 162, 60, 0.15) 0%, rgba(230, 162, 60, 0) 70%);
    }
  }
</style>
