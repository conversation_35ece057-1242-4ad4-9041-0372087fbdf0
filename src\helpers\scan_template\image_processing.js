import { loadScript } from '@/utils/dom'
import { deepCopy } from '@/utils/object'
import { roundNumber, mean } from '@/utils/math'
import { findLastIndex } from '@/utils/array'
import { loadImage } from '@/utils/promise'
import { Degree2Radian, Inch2MM } from '@/const/unit'
import OmrBlock from './omr_block'
import {
  BracketMinWidth,
  BracketMaxWidth,
  BracketMinHeight,
  BracketMaxHeight,
  BracketBorderWidth,
} from '@/const/scan_template'

const Resolution = 150

let cv = null
let loadCVPromise = null

export function loadCV() {
  if (!loadCVPromise) {
    loadCVPromise = loadScript('/lib/opencv-4.9.0/opencv.js')
      .then(() => {
        return window.cv
      })
      .then(loadCVResult => {
        delete window.cv
        cv = loadCVResult
        window.cv = cv
      })
  }
  return loadCVPromise
}

/**
 * 长度转换
 */
export function pixel2MM(pixel) {
  return (pixel / Resolution) * Inch2MM
}

export function MM2Pixel(mm) {
  return (mm / Inch2MM) * Resolution
}

/**
 * 加载canvas
 */
export async function loadCanvas(src) {
  let img = await loadImage(src)
  let canvas = document.createElement('canvas')
  canvas.width = img.naturalWidth
  canvas.height = img.naturalHeight
  let ctx = canvas.getContext('2d')
  ctx.drawImage(img, 0, 0)
  return canvas
}

// 旋转图片，返回对象Url
export async function rotateImage(src, degree) {
  if (degree == 0) {
    return Promise.resolve(src)
  }
  let image = await loadImage(src)
  let canvas = document.createElement('canvas')
  canvas.width = image.naturalWidth
  canvas.height = image.naturalHeight
  let ctx = canvas.getContext('2d')
  ctx.rotate(degree * Degree2Radian)
  ctx.drawImage(image, 0, 0)
  return new Promise(resolve => {
    canvas.toBlob(blob => {
      resolve(URL.createObjectURL(blob))
    })
  })
}

/**
 * 获取像素数据
 */

// canvas某区域像素RGBA数据
function getCanvasRGBAData(canvas, { x, y, width, height }) {
  let ctx = canvas.getContext('2d')
  return ctx.getImageData(x, y, width, height)
}

// canvas某区域像素灰度数据
function getCanvasGrayscaleData(canvas, { x, y, width, height }) {
  let RGBAData = getCanvasRGBAData(canvas, { x, y, width, height })
  let pixels = RGBAData.data
  let count = pixels.length / 4
  let data = new Uint8ClampedArray(count)
  for (let i = 0, index = 0; i < pixels.length; i += 4, index++) {
    data[index] = Math.round(pixels[i] * 0.299 + pixels[i + 1] * 0.587 + pixels[i + 2] * 0.114)
  }
  return {
    width: RGBAData.width,
    height: RGBAData.height,
    data,
  }
}

// canvas某区域像素黑白数据，负片，大津法
function getCanvasBinaryDataOtsu(canvas, { x, y, width, height }) {
  let grayscaleData = getCanvasGrayscaleData(canvas, { x, y, width, height })
  let result = {
    width: grayscaleData.width,
    height: grayscaleData.height,
    data: binarizeOtsu(grayscaleData.data),
  }
  return result
}

// canvas某区域像素黑白数据，负片，固定阈值
function getCanvasBinaryDataThreshold(canvas, { x, y, width, height }, threshold) {
  let grayscaleData = getCanvasGrayscaleData(canvas, { x, y, width, height })
  let result = {
    width: grayscaleData.width,
    height: grayscaleData.height,
    data: binarizeThreshold(grayscaleData.data, threshold),
  }
  return result
}

/**
 * 固定阈值二值化，负片
 * @param pixels 灰度像素数据
 * @param threshold 阈值，小于此值的为前景
 */
function binarizeThreshold(pixels, threshold = 128) {
  let data = new Uint8ClampedArray(pixels.length)
  for (let i = 0; i < pixels.length; i++) {
    data[i] = pixels[i] < threshold ? 1 : 0
  }
  return data
}

/**
 * 大津法二值化，负片
 * @param pixels 灰度像素数据
 */
function binarizeOtsu(pixels) {
  // 灰度级统计
  let stats = Array.from({ length: 256 }).fill(0)
  pixels.forEach(value => stats[value]++)

  // 计算总体
  let total = {
    // 像素个数
    count: pixels.length,
    // 像素值和
    sum: 0,
    // 像素值平均
    mean: 0,
  }
  stats.forEach((count, value) => {
    total.sum += count * value
  })
  total.mean = total.sum / total.count

  // 最大类间方差及对应阈值
  let maxVariance = 0
  let threshold = 0

  // 以每级灰度分割前景(lower)、背景(upper)，计算类间方差
  let lower = {
    count: 0,
    sum: 0,
    mean: 0,
  }
  let upper = {
    count: total.count,
    sum: total.sum,
    mean: total.mean,
  }
  stats.forEach((pxCount, pxValue) => {
    if (pxCount == 0) {
      return
    }
    let pxValueSum = pxCount * pxValue
    lower.count += pxCount
    lower.sum += pxValueSum
    lower.mean = lower.count == 0 ? 0 : lower.sum / lower.count
    upper.count -= pxCount
    upper.sum -= pxValueSum
    upper.mean = upper.count == 0 ? 0 : upper.sum / upper.count

    let variance =
      lower.count * (lower.mean - total.mean) * (lower.mean - total.mean) +
      upper.count * (upper.mean - total.mean) * (upper.mean - total.mean)
    if (variance > maxVariance) {
      maxVariance = variance
      threshold = pxValue
    }
  })

  // 负片
  let data = new Uint8ClampedArray(pixels.length)
  for (let i = 0; i < pixels.length; i++) {
    data[i] = pixels[i] > threshold ? 0 : 1
  }
  return data
}

// 黑白像素区域的子区域
function sliceBinaryData(binaryData, { x, y, width, height }) {
  let data = new Uint8ClampedArray(width * height)
  let idx = 0
  for (let j = 0; j < height; j++) {
    for (let i = 0; i < width; i++) {
      data[idx++] = binaryData.data[(y + j) * binaryData.width + (x + i)]
    }
  }
  return {
    width,
    height,
    data,
  }
}

// canvas区域中有内容的子区域
export function getCanvasContentRect(canvas, { x, y, width, height }, threshold = 4, space = 4) {
  let binaryData = getCanvasBinaryDataOtsu(canvas, { x, y, width, height })
  let contentRect = getBinaryDataContentRect(binaryData, threshold, space)
  return {
    x: x + contentRect.x,
    y: y + contentRect.y,
    width: contentRect.width,
    height: contentRect.height,
  }
}

// 获取黑白区域中有内容的子区域
// threshold：横向或纵向上像素点数阈值，小于此值视作空白
// space: 内容区向外扩展的像素
function getBinaryDataContentRect(binaryData, threshold = 4, space = 4) {
  let { vertical, horizontal } = getProjection(binaryData, true)

  // 分别找横向纵向第一个和最后一个像素点大于阈值的位置
  let predicate = v => v > threshold
  let x1 = vertical.findIndex(predicate)
  let x2 = findLastIndex(vertical, predicate)
  let y1 = horizontal.findIndex(predicate)
  let y2 = findLastIndex(horizontal, predicate)
  x1 = Math.max(0, x1 - space)
  x2 = Math.min(binaryData.width, x2 + space)
  y1 = Math.max(0, y1 - space)
  y2 = Math.min(binaryData.height, y2 + space)

  return {
    x: x1,
    y: y1,
    width: x2 - x1 + 1,
    height: y2 - y1 + 1,
  }
}

// 移除区域中横向、纵向长边界线（常见于准考号填涂区）
function removeBinaryDataLongLines(binaryData) {
  if (binaryData.width <= BracketMaxWidth * 2 && binaryData.height <= BracketMaxHeight * 2) {
    return
  }

  let projection = getProjection(binaryData)

  if (binaryData.height > BracketMaxHeight * 2) {
    removeVerticalLines(binaryData, projection)
  }
  if (binaryData.width > BracketMaxWidth * 2) {
    removeHorizontalLines(binaryData, projection)
  }

  function removeHorizontalLines(binaryData, projection) {
    let meanValue = mean(projection.horizontal)
    let sections = getProjectionSections(projection.horizontal, meanValue)
    let hillSections = sections.map(sec => expandHillSection(projection.horizontal, sec))
    hillSections.forEach(sec => {
      if (isHorizontalLine(binaryData, sec)) {
        cleanHorizontalSection(binaryData, sec)
      }
    })
  }

  function removeVerticalLines(binaryData, projection) {
    let meanValue = mean(projection.vertical)
    let sections = getProjectionSections(projection.vertical, meanValue)
    let hillSections = sections.map(sec => expandHillSection(projection.vertical, sec))
    hillSections.forEach(sec => {
      if (isVerticalLine(binaryData, sec)) {
        cleanVerticalSection(binaryData, sec)
      }
    })
  }

  function isHorizontalLine(binaryData, section) {
    // 将待测水平直线区域在竖直方向上投影，再分段，若存在一段长度超过两个选项宽度，则为直线
    let subBinaryData = sliceBinaryData(binaryData, {
      x: 0,
      y: section.start,
      width: binaryData.width,
      height: section.end - section.start + 1,
    })
    let { vertical } = getProjection(subBinaryData)
    let verticalSections = getProjectionSections(vertical, 0)
    let mergedSections = mergeSections(verticalSections, BracketMinWidth / 3)
    let maxLength = Math.max(...mergedSections.map(sec => sec.end - sec.start + 1))
    return maxLength >= BracketMaxWidth * 2
  }

  function isVerticalLine(binaryData, section) {
    // 将待测数值直线区域在水平方向上投影，再分段，若存在一段长度超过两个选项高度，则为直线
    let subBinaryData = sliceBinaryData(binaryData, {
      x: section.start,
      y: 0,
      width: section.end - section.start + 1,
      height: binaryData.height,
    })
    let { horizontal } = getProjection(subBinaryData)
    let horizontalSections = getProjectionSections(horizontal, 0)
    let mergedSections = mergeSections(horizontalSections, BracketMinHeight / 2)
    let maxLength = Math.max(...mergedSections.map(sec => sec.end - sec.start + 1))
    return maxLength >= BracketMaxHeight * 2
  }

  // 拓展尖峰区域
  function expandHillSection(values, section) {
    // 在左右两侧找数值递减的边界
    let left = section.start
    while (left > 0 && values[left - 1] <= values[left]) {
      left--
    }
    let right = section.end
    while (right < values.length - 1 && values[right + 1] <= values[right]) {
      right++
    }
    return {
      start: left,
      end: right,
    }
  }

  function cleanHorizontalSection(binaryData, section) {
    for (let y = section.start; y <= section.end; y++) {
      for (let x = 0; x < binaryData.width; x++) {
        binaryData.data[y * binaryData.width + x] = 0
      }
    }
  }

  function cleanVerticalSection(binaryData, section) {
    for (let x = section.start; x <= section.end; x++) {
      for (let y = 0; y < binaryData.height; y++) {
        binaryData.data[y * binaryData.width + x] = 0
      }
    }
  }
}

// 识别区域中的填涂框
export function recognizeOmrBlock(canvas, { x, y, width, height }, removeLongLines = true) {
  let binaryData = getCanvasBinaryDataOtsu(canvas, { x, y, width, height })
  let contentRect = getBinaryDataContentRect(binaryData, 4, 0)
  let contentRectBinaryData = sliceBinaryData(binaryData, contentRect)

  // 移除长直线
  if (removeLongLines) {
    removeBinaryDataLongLines(contentRectBinaryData)
    let contentRect2 = getBinaryDataContentRect(contentRectBinaryData, 4, 0)
    contentRectBinaryData = sliceBinaryData(contentRectBinaryData, contentRect2)
    contentRect = {
      x: contentRect.x + contentRect2.x + x,
      y: contentRect.y + contentRect2.y + y,
      width: contentRectBinaryData.width,
      height: contentRectBinaryData.height,
    }
  }

  // 获取投影，按空白横向、纵向拆分成小块
  let pieces = getSmallPieces(contentRectBinaryData)

  // 合并小块，形成选项
  let optionMatrix = extractBrackets(pieces, contentRect)

  return OmrBlock.importOptions({ x, y, width, height }, optionMatrix)
}

// 将区域按空白分成小块
function getSmallPieces(binaryData) {
  let { vertical, horizontal } = getProjection(binaryData)
  let verticalSections = getProjectionSections(vertical)
  let horizontalSections = getProjectionSections(horizontal, 0.02)
  let matrix = []
  horizontalSections.forEach(secH => {
    let row = []
    verticalSections.forEach(secV => {
      let rect = {
        x: secV.start,
        y: secH.start,
        width: secV.end - secV.start + 1,
        height: secH.end - secH.start + 1,
      }
      let pieceBinaryData = sliceBinaryData(binaryData, rect)
      let bracket = getBracket(pieceBinaryData)
      let item = {
        x: secV.start,
        y: secH.start,
        width: secV.end - secV.start + 1,
        height: secH.end - secH.start + 1,
        isBlank: !bracket,
        isLeft: bracket && bracket.left,
        isRight: bracket && bracket.right,
      }
      row.push(item)
    })
    matrix.push(row)
  })
  return matrix
}

// 判断每个小块是否左半选项框或右半选项框，再合并成选项
function extractBrackets(matrix, contentRect) {
  matrix = deepCopy(matrix)
  // 删除空行
  matrix = matrix.filter(row => row.some(cell => cell.isLeft || cell.isRight))
  if (matrix.length == 0) {
    return []
  }

  // 删除空列
  for (let col = matrix[0].length - 1; col >= 0; col--) {
    if (
      matrix.every(row => {
        let cell = row[col]
        return !cell.isLeft && !cell.isRight
      })
    ) {
      matrix.forEach(row => {
        row.splice(col, 1)
      })
    }
  }
  if (matrix[0].length == 0) {
    return []
  }

  // 各列是否左右边框
  let columnCount = matrix[0].length
  let columnBrackets = []
  for (let col = 0; col < columnCount; col++) {
    columnBrackets.push(getColumnBracketLeftRight(matrix, col))
  }
  // 连续的左边框向左合并、连续的右边框向右合并
  columnBrackets.forEach((col, idx) => {
    let pre = columnBrackets[idx - 1]
    let next = columnBrackets[idx + 1]
    if (col.isLeft && pre && pre.isLeft && !pre.isRight) {
      col.isLeft = false
    }
    if (col.isRight && next && next.isRight && !next.isLeft) {
      col.isRight = false
    }
  })

  // 相邻的左右边框配对
  let result = []
  let leftCol = -1
  for (let col = 0; col < columnCount; col++) {
    let { isLeft, isRight } = columnBrackets[col]
    if (leftCol < 0) {
      if (isLeft && isRight) {
        addColumnBrackets(result, matrix, col, col)
      } else if (isLeft) {
        leftCol = col
      }
    } else if (isRight) {
      addColumnBrackets(result, matrix, leftCol, col)
      leftCol = -1
    }
  }
  return result

  function addColumnBrackets(result, matrix, col1, col2) {
    matrix.forEach((row, rowIdx) => {
      let opt = {
        x: row[col1].x + contentRect.x,
        y: row[col1].y + contentRect.y,
        width: row[col2].x + row[col2].width - row[col1].x,
        height: row[col1].height,
      }
      if (
        opt.width >= BracketMinWidth &&
        opt.width <= BracketMaxWidth &&
        opt.height >= BracketMinHeight &&
        opt.height <= BracketMaxHeight
      ) {
        if (!result[rowIdx]) {
          result[rowIdx] = []
        }
        result[rowIdx].push(opt)
      }
    })
  }

  function getColumnBracketLeftRight(matrix, col) {
    return {
      isLeft: matrix.filter(row => row[col].isLeft).length > 0,
      isRight: matrix.filter(row => row[col].isRight).length > 0,
    }
  }
}

// 判断是否选项框左边或右边
function getBracket(binaryData, borderThreshold = 0.8, blankThreshold = 2) {
  // 检测内容区域
  let contentRect = getBinaryDataContentRect(binaryData, 0, 0)
  // 为空返回null
  if (contentRect.height < BracketMinHeight || contentRect.height > BracketMaxHeight) {
    return null
  }
  binaryData = sliceBinaryData(binaryData, contentRect)

  let horizontal, vertical

  // 左边界
  let leftBorderData = sliceBinaryData(binaryData, {
    x: 0,
    y: 0,
    width: BracketBorderWidth,
    height: binaryData.height,
  })
  horizontal = getProjection(leftBorderData).horizontal
  let leftBorderFillRate = horizontal.filter(v => v > 0).length / horizontal.length

  // 右边界
  let rightBorderData = sliceBinaryData(binaryData, {
    x: binaryData.width - BracketBorderWidth,
    y: 0,
    width: BracketBorderWidth,
    height: binaryData.height,
  })
  horizontal = getProjection(rightBorderData).horizontal
  let rightBorderFillRate = horizontal.filter(v => v > 0).length / horizontal.length

  // 上边界
  let topBorderData = sliceBinaryData(binaryData, {
    x: 0,
    y: 0,
    width: binaryData.width,
    height: BracketBorderWidth,
  })
  vertical = getProjection(topBorderData).vertical
  let topBorderFillRate = vertical.filter(v => v > 0).length / vertical.length

  // 下边界
  let bottomBorderData = sliceBinaryData(binaryData, {
    x: 0,
    y: binaryData.height - BracketBorderWidth,
    width: binaryData.width,
    height: BracketBorderWidth,
  })
  vertical = getProjection(bottomBorderData).vertical
  let bottomBorderFillRate = vertical.filter(v => v > 0).length / vertical.length

  // 全框
  if (
    binaryData.width >= BracketMinWidth &&
    [leftBorderFillRate, rightBorderFillRate, topBorderFillRate, bottomBorderFillRate].every(
      rate => rate >= borderThreshold
    )
  ) {
    return {
      left: true,
      right: true,
    }
  }

  // 左边界是实线，且右侧中心为空（剔除字母如B,D,E,F），则是左半框
  if (leftBorderFillRate >= borderThreshold) {
    let x = Math.max(BracketBorderWidth, Math.floor(binaryData.width / 2))
    let y = Math.max(BracketBorderWidth, Math.floor(binaryData.height / 4))
    let width = binaryData.width - x
    let height = binaryData.height - y * 2
    if (width <= 0 || height <= 0) {
      return {
        left: false,
        right: false,
      }
    }
    let rightCenterRectBinaryData = sliceBinaryData(binaryData, { x, y, width, height })
    let rightCenterRectDotCount = rightCenterRectBinaryData.data.filter(v => v > 0).length
    if (rightCenterRectDotCount <= blankThreshold) {
      return {
        left: true,
        right: false,
      }
    }
  }

  // 右边界是实线，且左侧中心为空
  if (rightBorderFillRate >= borderThreshold) {
    let x = 0
    let y = Math.max(BracketBorderWidth, Math.floor(binaryData.height / 4))
    let width = Math.min(binaryData.width - BracketBorderWidth, Math.floor(binaryData.width / 2))
    let height = binaryData.height - y * 2
    if (width <= 0 || height <= 0) {
      return {
        left: false,
        right: false,
      }
    }
    let leftCenterRectBinaryData = sliceBinaryData(binaryData, { x, y, width, height })
    let leftCenterRectDotCount = leftCenterRectBinaryData.data.filter(v => v > 0).length
    if (leftCenterRectDotCount <= blankThreshold) {
      return {
        left: false,
        right: true,
      }
    }
  }

  return {
    left: false,
    right: false,
  }
}

// 黑白区域横向和纵向投影
function getProjection(binaryData, rawValue = false) {
  let vertical = new Array(binaryData.width).fill(0)
  let horizontal = new Array(binaryData.height).fill(0)
  let x = 0
  let y = 0
  binaryData.data.forEach(v => {
    if (v) {
      vertical[x]++
      horizontal[y]++
    }
    x++
    if (x == binaryData.width) {
      x = 0
      y++
    }
  })
  if (rawValue) {
    return {
      vertical,
      horizontal,
    }
  } else {
    return {
      vertical: vertical.map(value => value / binaryData.height),
      horizontal: horizontal.map(value => value / binaryData.width),
    }
  }
}

// 投影分段，占比小于等于threshold的截断
function getProjectionSections(values, threshold = 0.05) {
  let sections = []
  let section = null
  values.forEach((v, idx) => {
    if (v <= threshold) {
      if (section) {
        section = null
      }
    } else {
      if (section) {
        section.end = idx
      } else {
        section = {
          start: idx,
          end: idx,
        }
        sections.push(section)
      }
    }
  })
  return sections
}

// 把间隔不超过gap的两段合并起来
function mergeSections(sections, gap) {
  let merged = []
  let last = null
  sections.forEach(sec => {
    if (last == null || sec.start - last.end > gap) {
      last = sec
      merged.push(last)
    } else {
      last.end = sec.end
    }
  })
  return merged
}

// 获取最长直线
export function getLongestLine(canvas, { x, y, width, height }, maxDegree = 2, deltaTheta = 0.1) {
  if (maxDegree >= 90) {
    maxDegree = 90
  }
  if (maxDegree <= 0) {
    maxDegree = 1
  }
  if (deltaTheta <= 0) {
    deltaTheta = 1
  }
  let binaryData = getCanvasBinaryDataOtsu(canvas, { x, y, width, height })

  // 在两个角度范围内找，比较得到最长的直线
  let result1 = getLongestHoughLine(binaryData, 90 - maxDegree, 90 + maxDegree, deltaTheta)
  let result2 = getLongestHoughLine(binaryData, 270 - maxDegree, 270 + maxDegree, deltaTheta)
  let result = result1.points < result2.points ? result2 : result1
  return {
    degree: roundNumber(result.degree <= 180 ? result.degree - 90 : result.degree - 270, 1),
    distance: result.distance,
    points: result.points,
  }
}

// 用Hough变换方法找最长直线
function getLongestHoughLine(binaryData, minTheta, maxTheta, deltaTheta) {
  // 三角函数值
  let sinCosList = []
  for (let theta = minTheta; theta < maxTheta; theta += deltaTheta) {
    let arc = (Math.PI / 180) * theta
    let sin = Math.sin(arc)
    let cos = Math.cos(arc)
    sinCosList.push({
      sin,
      cos,
    })
  }

  // hough变换结果
  let hough = []
  let x = 0
  let y = 0
  binaryData.data.forEach(pixel => {
    if (x === binaryData.width) {
      y++
      x = 0
    }
    if (pixel) {
      sinCosList.forEach((v, i) => {
        let r = Math.round(v.sin * y + v.cos * x)
        if (r >= 0) {
          let index = r * sinCosList.length + i
          hough[index] = (hough[index] || 0) + 1
        }
      })
    }
    x++
  })

  // hough结果中找最大的点
  let maxIndex = 0
  let max = 0
  hough.forEach((v, i) => {
    if (v > max) {
      max = v
      maxIndex = i
    }
  })

  // 返回直线数据
  let theta = minTheta + (maxIndex % sinCosList.length) * deltaTheta
  let r = Math.floor(maxIndex / sinCosList.length)
  return {
    degree: theta,
    distance: r,
    points: max,
  }
}

/**
 * 判断是否实心黑色矩形
 */
export function isSolidBlackRect(canvas, { x, y, width, height }, threshold = 0.5, dilate = 0) {
  let binaryData = getCanvasBinaryDataThreshold(canvas, { x, y, width, height }, 128)
  // 膨胀
  if (dilate > 0) {
    binaryData = dilateBinaryData(binaryData, dilate)
  }
  let frontCount = binaryData.data.filter(v => v > 0).length
  let proportion = frontCount / width / height
  return proportion > threshold
}

/**
 * 判断是否空心矩形
 */
export function isEmptyRect(canvas, { x, y, width, height }) {
  let binaryData = getCanvasBinaryDataOtsu(canvas, { x, y, width, height })
  let borders = {
    left: {
      rect: {
        x: 0,
        y: 0,
        width: BracketBorderWidth,
        height: binaryData.height,
      },
      horizontal: true,
      threshold: 0.8,
    },
    right: {
      rect: {
        x: binaryData.width - BracketBorderWidth,
        y: 0,
        width: BracketBorderWidth,
        height: binaryData.height,
      },
      horizontal: true,
      threshold: 0.8,
    },
    top: {
      rect: {
        x: 0,
        y: 0,
        width: binaryData.width,
        height: BracketBorderWidth,
      },
      horizontal: false,
      threshold: 0.5,
    },
    bottom: {
      rect: {
        x: 0,
        y: binaryData.height - BracketBorderWidth,
        width: binaryData.width,
        height: BracketBorderWidth,
      },
      horizontal: false,
      threshold: 0.5,
    },
  }
  return Object.keys(borders).every(key => {
    let border = borders[key]
    let borderBinaryData = sliceBinaryData(binaryData, border.rect)
    let projection
    if (border.horizontal) {
      projection = getProjection(borderBinaryData).horizontal
    } else {
      projection = getProjection(borderBinaryData).vertical
    }
    return projection.filter(v => v > 0).length / projection.length >= border.threshold
  })
}

/**
 * 显示图像，方便调试
 */
function showBinaryImageData(imageData) {
  let data = new Uint8ClampedArray(imageData.data.length)
  for (let i = 0; i < imageData.data.length; i++) {
    let value = imageData.data[i]
    data[i] = value == 0 ? 0 : 255
  }
  showGrayscaleImageData({
    width: imageData.width,
    height: imageData.height,
    data,
  })
}

function showGrayscaleImageData(grayscaleImageData) {
  let canvas = document.createElement('canvas')
  canvas.width = grayscaleImageData.width
  canvas.height = grayscaleImageData.height
  let ctx = canvas.getContext('2d')
  let values = []
  grayscaleImageData.data.forEach(value => {
    values.push(value, value, value, 255)
  })
  let pixelArray = Uint8ClampedArray.from(values)
  let canvasImageData = new ImageData(pixelArray, canvas.width, canvas.height)
  ctx.putImageData(canvasImageData, 0, 0)

  let canvasWrapper = document.createElement('div')
  let className = 'canvas-process-preview'
  let oldWrappers = Array.from(document.body.getElementsByClassName(className))
  let left = 100
  if (oldWrappers.length > 0) {
    let lastEl = oldWrappers[oldWrappers.length - 1]
    left = parseFloat(lastEl.style.left) + lastEl.offsetWidth + 20
  }
  canvasWrapper.classList.add('canvas-process-preview')
  canvasWrapper.style.position = 'fixed'
  canvasWrapper.style.left = `${left}px`
  canvasWrapper.style.top = '100px'
  canvasWrapper.style.zIndex = 100
  canvasWrapper.style.padding = '24px'
  canvasWrapper.style.boxShadow = '0 0 10px #ccc'
  canvasWrapper.style.backgroundColor = 'red'

  let btn = document.createElement('button')
  btn.innerHTML = '关闭'
  btn.style.position = 'absolute'
  btn.style.left = '0'
  btn.style.top = '0'
  btn.onclick = () => document.body.removeChild(canvasWrapper)

  canvasWrapper.appendChild(btn)
  canvasWrapper.appendChild(canvas)
  document.body.appendChild(canvasWrapper)
}

export function getOptionSample(canvas, rect) {
  // 二值化，若存在灰底，则使用最大类间法，否则直接使用固定阈值
  let grayscaleData = getCanvasGrayscaleData(canvas, rect)
  let whitePixelsRatio = grayscaleData.data.filter(v => v >= 248).length / grayscaleData.data.length
  let hasGrayBackground = whitePixelsRatio < 0.2
  let pixels
  if (hasGrayBackground) {
    pixels = binarizeOtsu(grayscaleData.data)
  } else {
    pixels = new Uint8ClampedArray(grayscaleData.data.length)
    grayscaleData.data.forEach((v, idx) => {
      pixels[idx] = v >= 224 ? 0 : 1
    })
  }
  let binaryData = {
    width: grayscaleData.width,
    height: grayscaleData.height,
    data: pixels,
  }

  // 内容区域
  let contentRect = getBinaryDataContentRect(binaryData, 4, 0)
  contentRect.x += rect.x
  contentRect.y += rect.y

  // 复制内容区域
  let newCanvas = document.createElement('canvas')
  newCanvas.width = contentRect.width
  newCanvas.height = contentRect.height
  let ctx = newCanvas.getContext('2d')
  ctx.drawImage(
    canvas,
    contentRect.x,
    contentRect.y,
    contentRect.width,
    contentRect.height,
    0,
    0,
    contentRect.width,
    contentRect.height
  )
  let dataUrl = newCanvas.toDataURL()
  return {
    url: dataUrl,
    rect: contentRect,
  }
}

/**
 * 匹配选项
 * @param region 搜索区域
 * @param sample 选项模板
 * @param threshold 阈值
 * @param expand 选项模板拓展
 * @param alignH 匹配结果选项水平对齐
 * @param alignV 匹配结果选项竖直对齐
 * @returns
 */
export function matchOmrBlock(canvas, region, sample, threshold = 0.5, expand = 2, alignH = false, alignV = false) {
  let opt = sample.rect

  // 稍微扩大选项框，但不能超出图片
  if (
    opt.x < expand ||
    opt.y < expand ||
    opt.x + opt.width + expand * 2 > sample.canvas.width ||
    opt.y + opt.height + expand * 2 > sample.canvas.height
  ) {
    expand = 0
  }
  let temRect = {
    x: opt.x - expand,
    y: opt.y - expand,
    width: opt.width + expand * 2,
    height: opt.height + expand * 2,
  }

  // 识别区域比选项小，直接返回空
  if (region.width < temRect.width || region.height < temRect.height) {
    return []
  }

  let tem = getCanvasGrayscaleData(sample.canvas, temRect)
  let image = getCanvasGrayscaleData(canvas, region)
  // 蒙版：取左右两侧各1/4
  let mask = {
    width: temRect.width,
    height: temRect.height,
    data: new Uint8ClampedArray(temRect.width * temRect.height),
  }
  let left = Math.ceil(temRect.width / 4)
  let right = temRect.width - left
  let idx = 0
  for (let i = 0; i < temRect.height; i++) {
    for (let j = 0; j < temRect.width; j++) {
      mask.data[idx++] = j < left || j >= right ? 255 : 0
    }
  }

  let match = matchTemplate(tem, image, mask)

  let result = findAllTargets(match, threshold, temRect.width, temRect.height)
  result.forEach(item => {
    item.x += expand + region.x
    item.y += expand + region.y
    item.width = opt.width
    item.height = opt.height
  })

  let optionMatrix = resultToMatrix(result, opt.width, opt.height, alignH, alignV)
  return OmrBlock.importOptions(region, optionMatrix)
}

function resultToMatrix(result, optWidth, optHeight, alignH, alignV) {
  if (result.length == 0) {
    return []
  }
  // 横向上分段
  let sortByX = result.slice().sort((a, b) => a.x - b.x)
  let xSections = []
  let cur = null
  sortByX.forEach(({ x, value }) => {
    if (!cur || x > cur.left + optWidth) {
      cur = { left: x, right: x, bestMatchValue: value, bestMatchX: x }
      xSections.push(cur)
    } else {
      cur.right = x
      if (value > cur.bestMatchValue) {
        cur.bestMatchValue = value
        cur.bestMatchX = x
      }
    }
  })
  // 纵向上分段
  let sortByY = result.slice().sort((a, b) => a.y - b.y)
  let ySections = []
  cur = null
  sortByY.forEach(({ y, value }) => {
    if (!cur || y > cur.top + optHeight) {
      cur = { top: y, bottom: y, bestMatchValue: value, bestMatchY: y }
      ySections.push(cur)
    } else {
      cur.bottom = y
      if (value > cur.bestMatchValue) {
        cur.bestMatchValue = value
        cur.bestMatchY = y
      }
    }
  })
  // 创建矩阵
  let matrix = []
  for (let row = 0; row < ySections.length; row++) {
    let matrixRow = []
    matrix[row] = matrixRow
    let { top, bottom, bestMatchY } = ySections[row]
    for (let col = 0; col < xSections.length; col++) {
      let { left, right, bestMatchX } = xSections[col]
      let target = result.find(p => p.x >= left && p.x <= right && p.y >= top && p.y <= bottom)
      // 若存在，处理对齐
      if (target) {
        if (alignH && Math.abs(target.y - bestMatchY) < MM2Pixel(0.5)) {
          target.y = bestMatchY
        }
        if (alignV && Math.abs(target.x - bestMatchX) < MM2Pixel(0.5)) {
          target.x = bestMatchX
        }
      }
      // 没找到则补充
      if (!target) {
        target = {
          x: bestMatchX,
          y: bestMatchY,
          width: optWidth,
          height: optHeight,
        }
      }
      matrixRow.push(target)
    }
  }

  return matrix
}

function matchTemplate(tem, img, mask) {
  // 转换为二维数组
  tem = getMatrix(tem)
  img = getMatrix(img)
  if (mask) {
    mask = getMatrix(mask)
  }
  // 模板尺寸
  let temWidth = tem.width
  let temHeight = tem.height
  let temSize = temWidth * temHeight
  if (mask) {
    temSize = 0
    mask.forEach(row => {
      row.forEach(v => {
        if (v != 0) {
          temSize++
        }
      })
    })
  }
  // 结果尺寸
  let resultWidth = img.width - tem.width + 1
  let resultHeight = img.height - tem.height + 1
  let resultSize = resultWidth * resultHeight

  /**
   * 结果中的每个像素（double值）表示模板与原图滑窗的相关系数
   * 相关系数等于两组变量的协方差除以各自标准差的乘积
   * t: 模板像素, i: 滑窗像素, N: 模板像素个数
   * r = (N*Sum(t*i)-Sum(t)*Sum(i))/(Sqrt(N*Sum(t^2)-Sum(t)^2)*Sqrt(N*Sum(i^2)-Sum(i)^2)
   */

  // 计算模板Sum(t),Sum(t^2)
  let { s_t, s_t_2 } = mask ? sum_tem_temS_mask(tem, mask) : sum_tem_temS(tem, mask)
  // 模板标准差
  let sd_t = Math.sqrt(temSize * s_t_2 - s_t * s_t)
  // 计算每个滑窗上的Sum(i),Sum(i^2)
  let s_i_list = new Float64Array(resultSize)
  let s_i_2_list = new Float64Array(resultSize)
  if (mask) {
    sum_img_imgS_mask(img, temWidth, temHeight, resultWidth, resultHeight, s_i_list, s_i_2_list, mask)
  } else {
    sum_img_imgS(img, temWidth, temHeight, resultWidth, resultHeight, s_i_list, s_i_2_list)
  }
  // 计算每个滑窗上的Sum(t*i)
  let s_t_i_list = new Float64Array(resultSize)
  if (mask) {
    sum_tem_img_mask(tem, temWidth, temHeight, img, resultWidth, resultHeight, s_t_i_list, mask)
  } else {
    sum_tem_img(tem, temWidth, temHeight, img, resultWidth, resultHeight, s_t_i_list)
  }

  let resultData = new Float64Array(resultSize)
  for (let i = 0; i < resultSize; i++) {
    let s_i = s_i_list[i]
    let s_i_2 = s_i_2_list[i]
    let s_t_i = s_t_i_list[i]
    resultData[i] = (temSize * s_t_i - s_t * s_i) / (sd_t * Math.sqrt(temSize * s_i_2 - s_i * s_i))
  }

  return {
    width: resultWidth,
    height: resultHeight,
    data: resultData,
  }
}

function getMatrix(grayscaleData) {
  let { width, height, data } = grayscaleData
  let length = data.length
  let rows = []
  let start = 0,
    end = width
  while (start < length) {
    rows.push(data.slice(start, end))
    start += width
    end += width
  }
  rows.width = width
  rows.height = height
  return rows
}

function sum_tem_temS(tem) {
  let s_t = 0
  let s_t_2 = 0

  for (let i = 0; i < tem.height; i++) {
    let row = tem[i]
    for (let j = 0; j < tem.width; j++) {
      let v = row[j]
      s_t += v
      s_t_2 += v * v
    }
  }

  return {
    s_t,
    s_t_2,
  }
}

function sum_tem_temS_mask(tem, mask) {
  let s_t = 0
  let s_t_2 = 0

  for (let i = 0; i < tem.height; i++) {
    let row = tem[i]
    let maskRow = mask[i]
    for (let j = 0; j < tem.width; j++) {
      if (maskRow[j] != 0) {
        let v = row[j]
        s_t += v
        s_t_2 += v * v
      }
    }
  }

  return {
    s_t,
    s_t_2,
  }
}

function sum_img_imgS(img, temWidth, temHeight, resultWidth, resultHeight, s_i_list, s_i_2_list) {
  // 对每一行row, 对每一列col，累加从第一列到col列的像素和、像素平方和
  let imgPixelSum = []
  let imgPixelSquareSum = []
  let imgWidth = img.width
  let imgHeight = img.height
  for (let row = 0; row < imgHeight; row++) {
    imgPixelSum.push(new Int32Array(imgWidth))
    imgPixelSquareSum.push(new Int32Array(imgWidth))
  }
  for (let i = 0; i < imgHeight; i++) {
    let imgRow = img[i]
    let sumRow = imgPixelSum[i]
    let sumSquareRow = imgPixelSquareSum[i]
    for (let j = 0; j < imgWidth; j++) {
      let v = imgRow[j]
      let v2 = v * v
      if (j == 0) {
        sumRow[j] = v
        sumSquareRow[j] = v2
      } else {
        sumRow[j] = sumRow[j - 1] + v
        sumSquareRow[j] = sumSquareRow[j - 1] + v2
      }
    }
  }

  let idx = 0
  for (let row = 0; row < resultHeight; row++) {
    for (let col = 0; col < resultWidth; col++) {
      let s_i = 0
      let s_i_2 = 0
      // 第一行：第一列直接计算；其后各列在上一列结果的基础上，减去滑窗移出的列，加上滑窗移入的列
      if (row == 0) {
        if (col == 0) {
          let endCol = temWidth - 1
          for (let i = 0; i < temHeight; i++) {
            s_i += imgPixelSum[i][endCol]
            s_i_2 += imgPixelSquareSum[i][endCol]
          }
        } else {
          let leftSI = s_i_list[idx - 1]
          let leftSI2 = s_i_2_list[idx - 1]
          s_i = leftSI
          s_i_2 = leftSI2

          let removeCol = col - 1
          let addCol = col + temWidth - 1
          for (let i = 0; i < temHeight; i++) {
            let addV = img[i][addCol]
            let removeV = img[i][removeCol]
            s_i += addV
            s_i -= removeV
            s_i_2 += addV * addV
            s_i_2 -= removeV * removeV
          }
        }
      }
      // 其他行：在上一行结果的基础上，减去滑窗移出的行，加上滑窗移入的行，行的累加和等于两端点之差
      else {
        let upperSI = s_i_list[idx - resultWidth]
        let upperSI2 = s_i_2_list[idx - resultWidth]

        let addRowIdx = row + temHeight - 1
        let removeRowIdx = row - 1
        let startColIdx = col - 1
        let endColIdx = startColIdx + temWidth

        let addRowSI = imgPixelSum[addRowIdx][endColIdx]
        let addRowSI2 = imgPixelSquareSum[addRowIdx][endColIdx]
        let removeRowSI = imgPixelSum[removeRowIdx][endColIdx]
        let removeRowSI2 = imgPixelSquareSum[removeRowIdx][endColIdx]
        if (col != 0) {
          addRowSI -= imgPixelSum[addRowIdx][startColIdx]
          addRowSI2 -= imgPixelSquareSum[addRowIdx][startColIdx]
          removeRowSI -= imgPixelSum[removeRowIdx][startColIdx]
          removeRowSI2 -= imgPixelSquareSum[removeRowIdx][startColIdx]
        }

        s_i = upperSI + addRowSI - removeRowSI
        s_i_2 = upperSI2 + addRowSI2 - removeRowSI2
      }

      s_i_list[idx] = s_i
      s_i_2_list[idx] = s_i_2
      idx++
    }
  }
}

function sum_img_imgS_mask(img, temWidth, temHeight, resultWidth, resultHeight, s_i_list, s_i_2_list, mask) {
  // 有蒙版时直接计算
  let idx = 0
  for (let row = 0; row < resultHeight; row++) {
    for (let col = 0; col < resultWidth; col++) {
      let s_i = 0
      let s_i_2 = 0
      for (let i = 0; i < temHeight; i++) {
        let imgRow = img[row + i]
        let maskRow = mask[i]
        for (let j = 0; j < temWidth; j++) {
          if (maskRow[j] != 0) {
            let v = imgRow[col + j]
            s_i += v
            s_i_2 += v * v
          }
        }
      }
      s_i_list[idx] = s_i
      s_i_2_list[idx] = s_i_2
      idx++
    }
  }
}

function sum_tem_img(tem, temWidth, temHeight, img, resultWidth, resultHeight, s_t_i_list) {
  // 计算卷积
  // TODO: 使用FFT提速
  let idx = 0
  for (let row = 0; row < resultHeight; row++) {
    for (let col = 0; col < resultWidth; col++) {
      let s_t_i = 0
      for (let i = 0; i < temHeight; i++) {
        let rowTem = tem[i]
        let rowImg = img[i + row]
        for (let j = 0; j < temWidth; j++) {
          s_t_i += rowTem[j] * rowImg[j + col]
        }
      }
      s_t_i_list[idx++] = s_t_i
    }
  }
}

function sum_tem_img_mask(tem, temWidth, temHeight, img, resultWidth, resultHeight, s_t_i_list, mask) {
  let idx = 0
  for (let row = 0; row < resultHeight; row++) {
    for (let col = 0; col < resultWidth; col++) {
      let s_t_i = 0
      for (let i = 0; i < temHeight; i++) {
        let rowTem = tem[i]
        let rowImg = img[i + row]
        let rowMask = mask[i]
        for (let j = 0; j < temWidth; j++) {
          if (rowMask[j] != 0) {
            s_t_i += rowTem[j] * rowImg[j + col]
          }
        }
      }
      s_t_i_list[idx++] = s_t_i
    }
  }
}

// 查找匹配结果
function findAllTargets(match, threshold, gapWidth, gapHeight) {
  let targets = []
  let next = null
  while ((next = getNextMaxLoc(match, next, gapWidth, gapHeight))) {
    if (next.value >= threshold) {
      targets.push(next)
    } else {
      break
    }
  }
  return targets
}
// 找下一个最佳匹配位置
function getNextMaxLoc(match, previous, gapWidth, gapHeight) {
  let data = match.data
  let width = match.width
  let height = match.height

  // 清除上一个匹配点附近的值
  if (previous) {
    let startX = previous.x - gapWidth
    if (startX < 0) {
      startX = 0
    }
    let endX = previous.x + gapWidth
    if (endX >= width) {
      endX = width - 1
    }
    let startY = previous.y - gapHeight
    if (startY < 0) {
      startY = 0
    }
    let endY = previous.y + gapHeight
    if (endY >= height) {
      endY = height - 1
    }

    let idx = startY * width + startX
    let newLineIdxIncrease = width - (endX - startX + 1)
    for (let i = startY; i <= endY; i++) {
      for (let j = startX; j <= endX; j++) {
        data[idx] = 0
        idx++
      }
      idx += newLineIdxIncrease
    }
  }

  // 找最大值及位置
  let maxVal = 0
  let maxIdx = 0
  for (let i = 0; i < data.length; i++) {
    let v = data[i]
    if (v > maxVal) {
      maxVal = v
      maxIdx = i
    }
  }

  return {
    value: maxVal,
    x: maxIdx % width,
    y: Math.floor(maxIdx / width),
  }
}

function showMatchTemplateResult(result) {
  let resultData = result.data
  let data = new Uint8ClampedArray(resultData.length)

  for (let i = 0; i < resultData.length; i++) {
    let v = resultData[i]
    data[i] = v <= 0 ? 0 : Math.round(v * 255)
  }
  showGrayscaleImageData({
    width: result.width,
    height: result.height,
    data,
  })
}

// 腐蚀
export function erodeBinaryData(binaryData, kernelSize = 3) {
  let width = binaryData.width
  let height = binaryData.height
  let data = new Uint8ClampedArray(width * height)
  let halfSize = Math.floor(kernelSize / 2)
  for (let i = 0; i < height; i++) {
    for (let j = 0; j < width; j++) {
      // 以当前点为中心，周围kernelSize*kernelSize的区域是否全为白色像素
      let allWhite = true
      for (let m = -halfSize; m <= halfSize; m++) {
        for (let n = -halfSize; n <= halfSize; n++) {
          let x = j + n
          let y = i + m
          if (x < 0 || x >= width || y < 0 || y >= height) {
            allWhite = false
            break
          }
          if (binaryData.data[y * width + x] == 0) {
            allWhite = false
            break
          }
        }
        if (!allWhite) {
          break
        }
      }
      data[i * width + j] = allWhite ? 1 : 0
    }
  }
  return {
    width,
    height,
    data,
  }
}

// 膨胀
export function dilateBinaryData(binaryData, kernelSize = 3) {
  let width = binaryData.width
  let height = binaryData.height
  let data = new Uint8ClampedArray(width * height)
  let halfSize = Math.floor(kernelSize / 2)
  // i行j列
  for (let i = 0; i < height; i++) {
    for (let j = 0; j < width; j++) {
      // 以当前点为中心，周围kernelSize*kernelSize的区域是否存在白色像素
      let hasWhite = false
      for (let m = -halfSize; m <= halfSize; m++) {
        for (let n = -halfSize; n <= halfSize; n++) {
          let x = j + n
          let y = i + m
          if (x < 0 || x >= width || y < 0 || y >= height) {
            continue
          }
          if (binaryData.data[y * width + x] == 1) {
            hasWhite = true
            break
          }
        }
        if (hasWhite) {
          break
        }
      }
      data[i * width + j] = hasWhite ? 1 : 0
    }
  }
  return {
    width,
    height,
    data,
  }
}

export async function isPageMarkRects(canvas, rect) {
  await loadCV()

  let src
  let binary
  let contours
  let hierarchy
  let subMat
  try {
    // 加载图片数据
    src = readImage(canvas, rect, 'grayscale')
    // 二值化
    binary = new cv.Mat()
    cv.threshold(src, binary, 192, 255, cv.THRESH_BINARY)
    cv.bitwise_not(binary, binary)
    // 弥补孔洞
    let M = cv.Mat.ones(3, 3, cv.CV_8U)
    cv.morphologyEx(binary, binary, cv.MORPH_CLOSE, M)
    M.delete()
    // 找出轮廓
    contours = new cv.MatVector()
    hierarchy = new cv.Mat()
    cv.findContours(binary, contours, hierarchy, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)

    // // 画出所有轮廓
    // let contourMat = new cv.Mat()
    // cv.cvtColor(binary, contourMat, cv.COLOR_GRAY2RGB)
    // let contourColor = new cv.Scalar(255, 0, 0)
    // for (let i = 0; i < contours.size(); ++i) {
    //   cv.drawContours(contourMat, contours, i, contourColor, 1, cv.LINE_8, hierarchy, 100)
    // }
    // showImage(contourMat)
    // contourMat.delete()

    // 检查轮廓是否符合要求
    let contourRects = []
    for (let i = 0; i < contours.size(); i++) {
      let cnt = contours.get(i)

      // 检查尺寸
      let boundRect = cv.boundingRect(cnt)
      if (boundRect.width < 8 || boundRect.height < 8 || boundRect.width > 50 || boundRect.height > 50) {
        continue
      }
      // 检查宽高比
      let aspectRatio = boundRect.width / boundRect.height
      if (aspectRatio < 0.5 || aspectRatio > 4) {
        continue
      }
      // // 近似矩形
      // let tmp = new cv.Mat()
      // cv.approxPolyDP(cnt, tmp, 3, true)
      // let sides = tmp.rows
      // tmp.delete()
      // if (sides != 4) {
      //   continue
      // }
      // 检查倾斜角度
      let minAreaRect = cv.minAreaRect(cnt)
      let angle = Math.min(...[0, 90, 180, 270].map(v => Math.abs(v - minAreaRect.angle)))
      if (angle > 5) {
        continue
      }
      contourRects.push(boundRect)
    }

    // 判断矩形轮廓是否构成页码标记
    if (contourRects.length == 0) {
      return false
    } else if (contourRects.length == 1) {
      let cnt = contourRects[0]
      if (cnt.width / rect.width < 0.5 && cnt.height / rect.height < 0.5) {
        return false
      }
    } else {
      // 纵向平齐
      let yList = contourRects.map(r => r.y)
      if (Math.max(...yList) - Math.min(...yList) > 8) {
        return false
      }
      // 横向间距相等
      let xList = contourRects.map(r => r.x).sort((a, b) => a - b)
      let xGapList = xList.map((x, idx) => x - xList[idx - 1]).slice(1)
      if (Math.max(...xGapList) - Math.min(...xGapList) > 8) {
        return false
      }
      // 横向间距不能过小
      if (Math.min(...xGapList) < 5) {
        return false
      }
      // 宽度相等
      let widthList = contourRects.map(r => r.width)
      if (Math.max(...widthList) - Math.min(...widthList) > 8) {
        return false
      }
      // 高度相等
      let heightList = contourRects.map(r => r.height)
      if (Math.max(...heightList) - Math.min(...heightList) > 8) {
        return false
      }
      // 面积相等
      let areaList = contourRects.map(r => r.width * r.height)
      if (Math.min(...areaList) / Math.max(...areaList) < 0.7) {
        return false
      }
      // 是区域主体
      let left = Math.min(...xList)
      let right = Math.max(...contourRects.map(r => r.x + r.width))
      if ((right - left) / rect.width < 0.6) {
        return false
      }
      let top = Math.min(...yList)
      let bottom = Math.max(...contourRects.map(r => r.y + r.height))
      if ((bottom - top) / rect.height < 0.6) {
        return false
      }
    }

    // 至少有一个是黑块
    let existsBlackArea = contourRects.some(rect => {
      subMat = binary.roi(rect)
      let sum = cv.countNonZero(subMat)
      subMat.delete()
      subMat = null
      return sum >= rect.width * rect.height * 0.7
    })
    if (!existsBlackArea) {
      return false
    }

    // // 画出轮廓
    // let dst = new cv.Mat()
    // cv.cvtColor(src, dst, cv.COLOR_GRAY2RGB)
    // let color = new cv.Scalar(255, 0, 0)
    // contourRects.forEach(rectangle => {
    //   let point1 = new cv.Point(rectangle.x, rectangle.y)
    //   let point2 = new cv.Point(rectangle.x + rectangle.width, rectangle.y + rectangle.height)
    //   cv.rectangle(dst, point1, point2, color, 1, cv.LINE_AA, 0)
    // })
    // showImage(dst)
    // dst.delete()

    return true
  } finally {
    ;[src, binary, contours, hierarchy, subMat].forEach(item => {
      if (item) {
        item.delete()
      }
    })
  }
}

function readImage(canvas, rect, color = 'rgb') {
  let imageData = canvas.getContext('2d').getImageData(rect.x, rect.y, rect.width, rect.height)
  let img = cv.matFromImageData(imageData)
  let result = new cv.Mat()
  if (color == 'grayscale') {
    cv.cvtColor(img, result, cv.COLOR_RGBA2GRAY, 0)
  } else {
    cv.cvtColor(img, result, cv.COLOR_RGBA2RGB, 0)
  }
  img.delete()
  return result
}

function showImage(mat) {
  let canvas = document.createElement('canvas')
  canvas.width = mat.cols
  canvas.height = mat.rows
  cv.imshow(canvas, mat)

  let canvasWrapper = document.createElement('div')
  let className = 'canvas-process-preview'
  let oldWrappers = Array.from(document.body.getElementsByClassName(className))
  let left = 100
  if (oldWrappers.length > 0) {
    let lastEl = oldWrappers[oldWrappers.length - 1]
    left = parseFloat(lastEl.style.left) + lastEl.offsetWidth + 20
  }
  canvasWrapper.classList.add('canvas-process-preview')
  canvasWrapper.style.position = 'fixed'
  canvasWrapper.style.left = `${left}px`
  canvasWrapper.style.top = '100px'
  canvasWrapper.style.zIndex = 100
  canvasWrapper.style.padding = '24px'
  canvasWrapper.style.boxShadow = '0 0 10px #ccc'
  canvasWrapper.style.backgroundColor = 'red'

  let btn = document.createElement('button')
  btn.innerHTML = '关闭'
  btn.style.position = 'absolute'
  btn.style.left = '0'
  btn.style.top = '0'
  btn.onclick = () => document.body.removeChild(canvasWrapper)

  canvasWrapper.appendChild(btn)
  canvasWrapper.appendChild(canvas)
  document.body.appendChild(canvasWrapper)
}
