import ajax from '@/api/ajax'

export function apiListPaperTypes({ stageId }) {
  return ajax.get({
    url: 'ques/paperType/list',
    params: {
      gradeLevel: stageId,
    },
    requestName: '查试卷类型',
  })
}

export function apiAddPaperType({ stageId, paperTypeName }) {
  return ajax.post({
    url: 'ques/paperType/add',
    params: {
      gradeLevel: stageId,
      paperTypeName,
    },
    requestName: '新增试卷类型',
  })
}

export function apiChangePaperType({ stageId, paperTypeId, paperTypeName, orderId }) {
  return ajax.put({
    url: 'ques/paperType/update',
    params: {
      gradeLevel: stageId,
      paperTypeId,
      paperTypeName,
      orderId,
    },
    requestName: '修改试卷类型',
  })
}

export function apiDeletePaperType({ stageId, paperTypeId }) {
  return ajax.delete({
    url: `ques/paperType/delete`,
    params: {
      gradeLevel: stageId,
      paperTypeId,
    },
    requestName: '删除试卷类型',
  })
}
