<template>
  <Modal
    class="modal-score-tip"
    :model-value="modelValue"
    :width="730"
    footer-hide
    @on-visible-change="handleVisibleChange"
  >
    <template #header>
      <div class="modal-header">
        <span class="modal-title">先阅后扫打分规范</span>
        <Button
          class="btn-download"
          icon="md-download"
          type="text"
          size="small"
          to="/file/同级生先阅后扫打分规范.pdf"
          target="_blank"
          >下载详细打分规范文档</Button
        >
      </div>
    </template>
    <section class="section-introduction">
      <p>先阅后扫是指老师先在学生答卷上打分，扫描后系统自动识别分数并生成成绩的评卷方式。</p>
      <p>
        先阅后扫有对错框、打分条、手写框三种打分模式，其中：
        <br />（1）对错框适用于填空题；
        <br />（2）打分条或手写框适用于除填空题以外的其他主观题，且在一份答题卡上打分条与手写框不同时出现。
      </p>
      <p>
        采用先阅后扫时请注意：
        <br /><strong style="color: red">（1）打分必须使用红色笔；</strong> <br /><strong style="color: red"
          >（2）请告知学生不要将答案写到对错框、打分条或手写框内。</strong
        >
      </p>
    </section>
    <section class="section-specification">
      <div class="score-type">
        <p class="title">一、对错框打分规范</p>
        <ol>
          <li>
            请仅在错误的空上划线，正确的空无需打分，如示例。
            <ExampleImage :src="imgSrcTrueFalseCorrect1" caption="正确示例" type="primary"></ExampleImage>
          </li>
          <li>若打分有误需修改，请用涂改液或黑色笔涂抹掉红色笔迹。</li>
        </ol>
      </div>
      <div class="score-type">
        <p class="title">二、打分条打分规范</p>
        <ol>
          <li>
            请在分值格上划斜线，如正确示例1。
            <ExampleImage :src="imgSrcBarCorrect1" caption="正确示例 1" type="primary"></ExampleImage>
            需注意斜线不能穿过多个分值格，如错误示例1。
            <ExampleImage :src="imgSrcBarError1" caption="错误示例 1" type="error"></ExampleImage>
          </li>
          <li>
            若打分条分为十位和个位两段，请分别在十位和个位分值格上划线，识别结果为十位和个位划线分值之和，如正确示例2（50分）、正确示例3（45分）。
            <ExampleImage :src="imgSrcBarCorrect2" caption="正确示例 2" type="primary"></ExampleImage>
            <ExampleImage :src="imgSrcBarCorrect3" caption="正确示例 3" type="primary"></ExampleImage>
            十位上或个位上不能划多个分值格，如错误示例2、错误示例3。
            <ExampleImage :src="imgSrcBarError2" caption="错误示例 2" type="error"></ExampleImage>
            <ExampleImage :src="imgSrcBarError3" caption="错误示例 3" type="error"></ExampleImage>
          </li>
          <li>
            需打.5分的，请分别在整数和0.5分值格上划线，识别结果为整数分值加0.5分，如正确示例4（0.5分）、正确示例5（4.5分）、正确示例6（34.5分）。
            <ExampleImage :src="imgSrcBarCorrect4" caption="正确示例 4" type="primary"></ExampleImage>
            <ExampleImage :src="imgSrcBarCorrect5" caption="正确示例 5" type="primary"></ExampleImage>
            <ExampleImage :src="imgSrcBarCorrect6" caption="正确示例 6" type="primary"></ExampleImage>
          </li>
          <li>若打分有误需修改，请用涂改液或黑色笔涂抹掉红色笔迹。</li>
        </ol>
      </div>
      <div class="score-type">
        <p class="title">三、手写框打分规范</p>
        <ol>
          <li>
            请在手写框内工整填写数字，如正确示例1。
            <ExampleImage :src="imgSrcNumberCorrect1" caption="正确示例 1" type="primary"></ExampleImage>
            需注意： <br />（1）数字不要超出边框，如错误示例1； <br />（2）一个手写框内不能写多个数字，如错误示例2。
            <ExampleImage :src="imgSrcNumberError1" caption="错误示例 1" type="error"></ExampleImage>
            <ExampleImage :src="imgSrcNumberError2" caption="错误示例 2" type="error"></ExampleImage>
          </li>
          <li>
            手写框有十位或小数位的，请分别在十位、个位、小数位填写数字，十位或小数位为0的可不填写，如正确示例2（12.5分）、正确示例3（2分）。
            <ExampleImage :src="imgSrcNumberCorrect2" caption="正确示例 2" type="primary"></ExampleImage>
            <ExampleImage :src="imgSrcNumberCorrect3" caption="正确示例 3" type="primary"></ExampleImage>
            需注意整十数分个位不要漏填0，如错误示例3。
            <ExampleImage :src="imgSrcNumberError3" caption="错误示例 3" type="error"></ExampleImage>
          </li>
          <li>若打分有误需修改，请用涂改液将手写框涂白，再重新填写正确数字。</li>
        </ol>
      </div>
    </section>
  </Modal>
</template>

<script>
  import ExampleImage from './modal_post_scan_score_tip_image'

  import imgSrcTrueFalseCorrect1 from '@/assets/images/post_scan/truefalse_correct_1.png'
  import imgSrcBarCorrect1 from '@/assets/images/post_scan/bar_correct_1.png'
  import imgSrcBarCorrect2 from '@/assets/images/post_scan/bar_correct_2.png'
  import imgSrcBarCorrect3 from '@/assets/images/post_scan/bar_correct_3.png'
  import imgSrcBarCorrect4 from '@/assets/images/post_scan/bar_correct_4.png'
  import imgSrcBarCorrect5 from '@/assets/images/post_scan/bar_correct_5.png'
  import imgSrcBarCorrect6 from '@/assets/images/post_scan/bar_correct_6.png'
  import imgSrcBarError1 from '@/assets/images/post_scan/bar_error_1.png'
  import imgSrcBarError2 from '@/assets/images/post_scan/bar_error_2.png'
  import imgSrcBarError3 from '@/assets/images/post_scan/bar_error_3.png'
  import imgSrcNumberCorrect1 from '@/assets/images/post_scan/number_correct_1.png'
  import imgSrcNumberCorrect2 from '@/assets/images/post_scan/number_correct_2.png'
  import imgSrcNumberCorrect3 from '@/assets/images/post_scan/number_correct_3.png'
  import imgSrcNumberError1 from '@/assets/images/post_scan/number_error_1.png'
  import imgSrcNumberError2 from '@/assets/images/post_scan/number_error_2.png'
  import imgSrcNumberError3 from '@/assets/images/post_scan/number_error_3.png'

  export default {
    components: {
      ExampleImage,
    },
    props: {
      modelValue: Boolean,
    },
    emits: ['update:modelValue'],
    data() {
      return {
        imgSrcTrueFalseCorrect1,
        imgSrcBarCorrect1,
        imgSrcBarCorrect2,
        imgSrcBarCorrect3,
        imgSrcBarCorrect4,
        imgSrcBarCorrect5,
        imgSrcBarCorrect6,
        imgSrcBarError1,
        imgSrcBarError2,
        imgSrcBarError3,
        imgSrcNumberCorrect1,
        imgSrcNumberCorrect2,
        imgSrcNumberCorrect3,
        imgSrcNumberError1,
        imgSrcNumberError2,
        imgSrcNumberError3,
      }
    },
    methods: {
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.$emit('update:modelValue', false)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .modal-header {
    .modal-title {
      margin-right: 10px;
      font-size: $font-size-medium-x;
    }

    .btn-download {
      color: $color-primary;
    }
  }

  section {
    line-height: 1.7;
  }

  .section-introduction {
    padding: 12px 16px;
    border-radius: 5px;
    background-color: $color-iview-table-active-row;

    strong {
      color: black;
    }
  }

  .score-type {
    margin-top: 16px;
  }

  .title {
    font-weight: bold;
    font-size: $font-size-medium-x;
  }

  ol {
    margin-left: 32px;
    list-style-position: outside;
    list-style-type: decimal;
  }

  li {
    margin-bottom: 8px;
  }

  .cross {
    font-size: 18px;
  }
</style>
