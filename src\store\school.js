import {
  apiGetGradeClasses as apiGetSchoolGradeClasses,
  apiGetMySchoolGradeSubjects,
  apiGetStageGradeSubjects,
} from '@/api/user'
import { apiGetSchoolTerms } from '@/api/user/school'

import { groupArray, unGroupArray } from '@/utils/array'

export default {
  namespaced: true,
  state: {
    stageGradeSubjects: [], // 系统学段年级学科
    schoolGradeSubjects: [], // 学校开设学段年级学科
    schoolGradeClasses: [], // 学校班级
    schoolStageGradeClassSubjects: [], // 学校学段年级班级学科
    schoolTerms: [], // 学校学年学科
  },
  getters: {
    hasStageHigh(state) {
      return state.schoolStageGradeClassSubjects.some(s => s.stageName == '高中')
    },
    schoolGradeClassSubjects(state) {
      return unGroupArray(state.schoolStageGradeClassSubjects, s =>
        s.grades.map(g => ({
          ...g,
          stageId: s.stageId,
          stageName: s.stageName,
        }))
      )
    },
    schoolSubjectGradeClasses(state) {
      let list = []
      state.schoolStageGradeClassSubjects.forEach(stage => {
        stage.grades.forEach(g => {
          g.subjects.forEach(s => {
            let subject = list.find(x => x.subjectId === s.subjectId)
            if (subject) {
              subject.grades.push({
                gradeId: g.gradeId,
                gradeName: g.gradeName,
                classes: g.classes.map(c => ({
                  classId: c.classId,
                  className: c.className,
                })),
              })
            } else {
              list.push({
                subjectId: s.subjectId,
                subjectName: s.subjectName,
                grades: [
                  {
                    gradeId: g.gradeId,
                    gradeName: g.gradeName,
                    classes: g.classes.map(c => ({
                      classId: c.classId,
                      className: c.className,
                    })),
                  },
                ],
              })
            }
          })
        })
      })

      return list
    },
    subjects(state) {
      let list = []
      state.stageGradeSubjects.forEach(stage => {
        stage.grades.forEach(grade => {
          grade.subjects.forEach(s => {
            if (list.every(x => x.id !== s.id)) {
              list.push({
                id: s.id,
                name: s.name,
              })
            }
          })
        })
      })
      list.sort((a, b) => a.id - b.id)
      return list
    },
    // 学校学段学科
    schoolStageSubjects(state) {
      return state.schoolStageGradeClassSubjects.map(stage => {
        let subjects = []
        stage.grades.forEach(g => {
          g.subjects.forEach(s => {
            if (subjects.every(x => x.subjectId != s.subjectId)) {
              subjects.push({
                subjectId: s.subjectId,
                subjectName: s.subjectName,
              })
            }
          })
        })
        subjects.sort((a, b) => a.subjectId - b.subjectId)
        return {
          stageId: stage.stageId,
          stageName: stage.stageName,
          subjects,
        }
      })
    },
    // 学校学段学科，平铺
    schoolStageSubjectsFlat(state, getters) {
      return unGroupArray(getters.schoolStageSubjects, s =>
        s.subjects.map(subj => ({
          ...subj,
          stageId: s.stageId,
          stageName: s.stageName,
        }))
      )
    },
    schoolTerms(state) {
      return state.schoolTerms
    },
    // 学校（机构）所开通的所有学科
    schoolAllSubjects(state, getters) {
      let _subjects = []

      getters.schoolGradeClassSubjects.forEach(grade =>
        (grade.subjects || []).forEach(subject => {
          if (!_subjects.some(s => s.subjectId === subject.subjectId)) {
            _subjects.push(subject)
          }
        })
      )
      _subjects.sort((a, b) => a.subjectId - b.subjectId)

      return _subjects
    },
  },
  mutations: {
    setSchoolStageGradeSubjects(state) {
      let list = []

      let gradeSubjectsAll = unGroupArray(state.stageGradeSubjects, s =>
        s.grades.map(g => ({
          ...g,
          stageId: s.id,
          stageName: s.name,
        }))
      )

      groupArray(state.schoolGradeSubjects, g => g.gradeId)
        .sort((a, b) => a.key - b.key)
        .forEach(g => {
          let gradeSubject = gradeSubjectsAll.find(x => x.id === g.key)
          if (!gradeSubject) {
            return
          }

          let grade = {
            stageId: gradeSubject.stageId,
            stageName: gradeSubject.stageName,
            gradeId: gradeSubject.id,
            gradeName: gradeSubject.name,
            subjects: [],
            classes: [],
          }

          g.group.forEach(s => {
            let subject = gradeSubject.subjects.find(x => x.id === s.subjectId)
            if (subject) {
              grade.subjects.push({
                subjectId: subject.id,
                subjectName: subject.name,
              })
            }
          })
          grade.subjects.sort((a, b) => a.subjectId - b.subjectId)

          let gradeClasses = state.schoolGradeClasses.filter(x => x.gradeId === g.key)
          if (gradeClasses) {
            grade.classes = gradeClasses.map(c => ({
              classId: c.classId,
              className: c.className,
              classType: c.classType,
              studentCount: c.studentCount,
              teachingSubjects: c.teachingSubjects.every(ts => ts.name)
                ? c.teachingSubjects
                : c.teachingSubjects.map(ts => gradeSubject.subjects.find(x => x.id === ts.id) || ts),
            }))
          }
          list.push(grade)
        })

      state.schoolStageGradeClassSubjects = groupArray(list, x => x.stageId).map(x => ({
        stageId: x.group[0].stageId,
        stageName: x.group[0].stageName,
        grades: x.group,
      }))
    },
  },
  actions: {
    getStageGradeSubjects(context) {
      return apiGetStageGradeSubjects().then(data => {
        context.state.stageGradeSubjects = data
        context.commit('setSchoolStageGradeSubjects')
      })
    },
    getGradeSubjects(context) {
      return apiGetMySchoolGradeSubjects().then(data => {
        context.state.schoolGradeSubjects = data
        context.commit('setSchoolStageGradeSubjects')
      })
    },
    getGradeClasses(context) {
      return apiGetSchoolGradeClasses().then(data => {
        context.state.schoolGradeClasses = data
        context.commit('setSchoolStageGradeSubjects')
      })
    },
    getGradeClassesAndSubjects(context) {
      return Promise.all([apiGetMySchoolGradeSubjects(), apiGetSchoolGradeClasses()]).then(
        ([gradeSubjects, gradeClasses]) => {
          context.state.schoolGradeSubjects = gradeSubjects
          context.state.schoolGradeClasses = gradeClasses
          context.commit('setSchoolStageGradeSubjects')
        }
      )
    },
    getSchoolStageGradeSubjectsParameters(context) {
      return Promise.all([apiGetStageGradeSubjects(), apiGetMySchoolGradeSubjects(), apiGetSchoolGradeClasses()]).then(
        ([stageGradeSubjects, schoolGradeSubjects, schoolGradeClasses]) => {
          context.state.stageGradeSubjects = stageGradeSubjects
          context.state.schoolGradeSubjects = schoolGradeSubjects
          context.state.schoolGradeClasses = schoolGradeClasses
          context.commit('setSchoolStageGradeSubjects')
        }
      )
    },
    getSchoolTerms(context) {
      return apiGetSchoolTerms().then(terms => {
        context.state.schoolTerms = terms
      })
    },
  },
}
