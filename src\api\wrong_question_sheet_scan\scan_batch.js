import ajax from '@/api/ajax'
import { transformScanUnit } from './scan_unit'

function transformBatch(batch) {
  if (!batch) {
    return batch
  }
  batch.batchName = `${batch.scanClient}-${batch.batchNo}`
  batch.scanUnits = (batch.scanUnits || []).map(transformScanUnit)
  return batch
}

// 无scanClientId：查当前用户所扫批次中，存在扫描单元在识别中或存在扫描单元有异常
// 有scanClientId：查当前用户所扫批次中，存在扫描单元在识别中或存在扫描单元有异常，或该批次由scanClientId扫描且答卷上传未完成
export function apiGetUnfinishedBatch(scanClientId) {
  return ajax
    .get({
      url: 'fbscan/scanBatch/unfinished',
      params: {
        scanClientId,
      },
      requestName: '查询未完成批次',
    })
    .then(transformBatch)
}

export function apiGetBatchHistory(params) {
  return ajax
    .get({
      url: 'fbscan/scanBatch/history',
      params: {
        scanUserId: params.scanUserId,
        startTime: params.startTime,
        page: params.currentPage,
        size: params.pageSize,
      },
      requestName: '查询扫描历史',
    })
    .then(data => {
      data.records = data.records.map(transformBatch)
      return data
    })
}

export function apiGetBatchData(batchId) {
  return ajax
    .get({
      url: 'fbscan/scanBatch/data',
      params: {
        batchId,
      },
      requestName: '查询批次数据',
    })
    .then(transformBatch)
}
