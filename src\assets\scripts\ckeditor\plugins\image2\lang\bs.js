/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'bs', {
	alt: 'Te<PERSON><PERSON> na slici',
	btnUpload: '<PERSON><PERSON><PERSON> na server',
	captioned: 'Captioned image', // MISSING
	captionPlaceholder: 'Caption', // MISSING
	infoTab: 'Info slike',
	lockRatio: 'Zakljuèaj odnos',
	menu: 'Svojstva slike',
	pathName: 'image', // MISSING
	pathNameCaption: 'caption', // MISSING
	resetSize: 'Resetuj dimenzije',
	resizer: 'Click and drag to resize', // MISSING
	title: 'Svojstva slike',
	uploadTab: 'Šalji',
	urlMissing: 'Image source URL is missing.', // MISSING
	altMissing: 'Alternative text is missing.' // MISSING
} );
