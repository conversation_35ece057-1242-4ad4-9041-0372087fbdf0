import ajax from '@/api/ajax'

/**
 * 增加浏览次数与下载次数
 */
export function apiIncreasePublicBankPaperViewTimes(paperId) {
  return ajax.put({
    url: 'ques/count/public/view',
    params: {
      paperId,
    },
  })
}

export function apiIncreasePublicBankPaperDownloadTimes(paperId) {
  return ajax.put({
    url: 'ques/count/public/down',
    params: {
      paperId,
    },
  })
}

export function apiIncreaseSchoolBankPaperViewTimes(sharePaperId) {
  return ajax.put({
    url: 'ques/count/school/view',
    params: {
      sharePaperId,
    },
  })
}

export function apiIncreaseSchoolBankPaperDownloadTimes(sharePaperId) {
  return ajax.put({
    url: 'ques/count/school/down',
    params: {
      sharePaperId,
    },
  })
}
