/**
 * 对象深复制
 * @param {Object} obj
 * @returns Object
 */
export function deepCopy(obj) {
  if (obj == null || typeof obj !== 'object') {
    return obj
  }

  let pairs = []
  let newObj = obj instanceof Array ? [] : {}

  _copy(newObj, obj)
  return newObj

  function _copy(objDest, objSrc) {
    let existing = pairs.find(x => x.oldObj === objSrc)
    if (existing) {
      Object.keys(existing.newObj).forEach(key => {
        objDest[key] = existing.newObj[key]
      })
      return
    }

    pairs.push({
      oldObj: objSrc,
      newObj: objDest,
    })

    Object.keys(objSrc).forEach(key => {
      let value = objSrc[key]
      let type = typeof value

      if (value !== null && type === 'object') {
        objDest[key] = value instanceof Array ? [] : {}
        _copy(objDest[key], value)
      } else {
        objDest[key] = objSrc[key]
      }
    })
  }
}

/**
 * 是否空对象
 * @param {Object} value
 * @returns Boolean
 */
export function isEmptyObject(value) {
  return value == null || Object.keys(value).length === 0
}
