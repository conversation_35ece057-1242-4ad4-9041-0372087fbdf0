export default {
  name: 'staged-report',
  path: 'staged_report',
  meta: {
    title: '定制报告',
    name: '定制报告',
    menu: '定制报告',
  },
  component: () => import('@/views/staged_report/index.vue'),
  children: [
    {
      name: 'staged-report-home',
      path: 'home',
      meta: {},
      component: () => import('@/views/staged_report/home.vue'),
    },
    {
      name: 'staged-report-details',
      path: 'staged_report_details/:projectId?',
      meta: {
        showHeader: false,
      },
      component: () => import('@/views/staged_report/staged_report_details.vue'),
    },
    {
      name: 'single-report-details',
      path: 'single_report_details/:projectId?/:subjectId?',
      meta: {
        showHeader: false,
      },
      component: () => import('@/views/staged_report/single_report_details.vue'),
    },
    {
      name: 'single-report-edit',
      path: 'single_report_edit/:projectId?',
      meta: {
        showHeader: false,
      },
      component: () => import('@/views/staged_report/single_report_edit.vue'),
    },
  ],
}
