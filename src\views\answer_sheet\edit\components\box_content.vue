<template>
  <div class="box-content" :class="{ 'editor-active': editorActive }" :style="boxStyle">
    <AnswerSheetEditor
      ref="editor"
      class="editor"
      :value="content"
      :style="editorStyle"
      @ready="checkHeightChangeAndEmitEvent"
      @change="changeBlockContent"
      @focus="handleFocus"
      @blur="handleBlur"
    ></AnswerSheetEditor>
    <div v-if="resizable" class="drag-bar no-print" title="拖动调整高度" @mousedown="startResize">
      <Icon type="md-menu" size="16"></Icon>
    </div>
  </div>
</template>

<script>
  import AnswerSheetEditor from './editor.vue'

  import { sumByDefaultZero } from '@/utils/math'

  export default {
    components: {
      AnswerSheetEditor,
    },
    props: {
      content: {
        type: String,
        required: true,
      },
      height: {
        type: Number,
        required: true,
      },
      width: {
        type: Number,
        required: true,
      },
      paddingTop: {
        type: Number,
        required: false,
      },
      border: {
        type: Object,
        default: () => ({
          left: true,
          right: true,
          top: true,
          bottom: true,
        }),
      },
      resizable: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['content-change', 'height-change', 'resize', 'resize-end'],
    data() {
      return {
        // 边框、边距
        borderWidth: 1,
        padding: {
          top: 14,
          right: 10,
          bottom: 2,
          left: 10,
        },

        // 是否激活
        editorActive: false,

        // 拖动
        startY: 0,
        startHeight: 0,
        minHeight: 0,
      }
    },
    computed: {
      boxStyle() {
        return {
          borderTop: `${this.borderWidth}px solid ${this.border.top ? 'var(--base-border-color)' : 'transparent'}`,
          borderRight: `${this.borderWidth}px solid ${this.border.right ? 'var(--base-border-color)' : 'transparent'}`,
          borderBottom: `${this.borderWidth}px solid ${this.border.bottom ? 'var(--base-border-color)' : 'transparent'}`,
          borderLeft: `${this.borderWidth}px solid ${this.border.left ? 'var(--base-border-color)' : 'transparent'}`,
          height: this.height + 'px',
        }
      },
      editorStyle() {
        let paddingTop = this.paddingTop || this.padding.top
        return {
          paddingTop: `${paddingTop}px`,
          paddingRight: `${this.padding.right}px`,
          paddingBottom: `${this.padding.bottom}px`,
          paddingLeft: `${this.padding.left}px`,
          wordBreak: 'normal',
        }
      },
      baseStyle() {
        return this.$store.getters['answerSheet/baseStyle']
      },
    },
    watch: {
      width() {
        this.$nextTick().then(() => {
          this.checkHeightChangeAndEmitEvent()
        })
      },
      paddingTop(newPaddingTop, oldPaddingTop) {
        this.$emit('height-change', this.height + newPaddingTop - oldPaddingTop)
      },
      baseStyle() {
        this.$nextTick().then(() => {
          this.checkHeightChangeAndEmitEvent()
        })
      },
    },
    methods: {
      handleFocus() {
        this.editorActive = true
      },
      handleBlur() {
        this.editorActive = false
        this.checkHeightChangeAndEmitEvent()
      },
      changeBlockContent(content) {
        this.$emit('content-change', content)
        this.checkHeightChangeAndEmitEvent()
      },
      getBoxMinHeight(elEditor) {
        return elEditor.scrollHeight + this.borderWidth * 2
      },
      checkHeightChangeAndEmitEvent() {
        let boxMinHeight = this.getBoxMinHeight(this.$refs['editor'].$el)
        if (this.resizable) {
          this.$emit('height-change', Math.max(this.height, boxMinHeight))
        } else {
          this.$emit('height-change', boxMinHeight)
        }
      },

      // 拖动调整高度
      startResize(e) {
        // 设置初始位置、初始高度
        this.startY = e.pageY
        this.startHeight = this.height
        this.minHeight = this.getBoxMinHeight(this.$refs['editor'].$el)

        // 监听鼠标移动
        document.addEventListener('mousemove', this.handleMousemove)
        document.addEventListener('mouseup', this.handleMouseup)
        document.addEventListener('mouseleave', this.handleMouseup)
      },
      handleMousemove(e) {
        // 新高度不小于内容高度
        let newHeight = Math.max(this.minHeight, Math.ceil(this.startHeight + e.pageY - this.startY))
        this.$emit('resize', newHeight)
        return newHeight
      },
      handleMouseup(e) {
        document.removeEventListener('mousemove', this.handleMousemove)
        document.removeEventListener('mouseup', this.handleMouseup)
        document.removeEventListener('mouseleave', this.handleMouseup)
        let newHeight = this.handleMousemove(e)
        if (newHeight != this.startHeight) {
          this.$emit('resize-end')
        }
        this.startY = 0
        this.startHeight = 0
        this.minHeight = 0
      },

      // 拆分内容
      split(
        getNextPageAvailableHeightFunc,
        { content: questionGroupContent, height: questionGroupHeight, contentHeight: questionGroupContentHeight },
        options
      ) {
        let { contentTopHeight = 0, contentBottomHeight = 0, notFirstPaddingTop } = options || {}
        let availableHeight = getNextPageAvailableHeightFunc(0)
        if (questionGroupHeight <= availableHeight) {
          return [
            {
              content: questionGroupContent,
              height: questionGroupHeight,
              contentHeight: questionGroupContentHeight,
            },
          ]
        }

        // 无内容直接放下一页
        if (!questionGroupContent) {
          return [
            null,
            {
              content: questionGroupContent,
              height: questionGroupHeight,
              contentHeight: questionGroupContentHeight,
            },
          ]
        }

        // 最终拆分出的结果
        let splitBlocks = []
        // 从当前页开始算起的第几个页面
        let nextPageIndex = 0
        const getHeight = editor => {
          let contentHeight = this.getBoxMinHeight(editor)
          let isFirstBlock = splitBlocks.length == 0 || splitBlocks[0] == null
          let blockHeight = contentHeight + (isFirstBlock ? contentTopHeight : 0)
          return {
            contentHeight,
            blockHeight,
          }
        }

        // 将编辑器内容设为需要拆分的所有内容
        let editor = this.$refs['editor'].$el
        let editorHTML = editor.innerHTML
        let editorPadding = editor.style.padding
        editor.innerHTML = questionGroupContent
        let { contentHeight, blockHeight } = getHeight(editor)

        // 情形一：可容纳全部内容
        if (availableHeight >= blockHeight) {
          splitBlocks = [
            {
              height: blockHeight,
              content: questionGroupContent,
              contentHeight,
            },
          ]
        }
        // 情形二：需拆分
        else {
          // 1. 清空编辑器内容，建立拆分结果数组，初始化数组第一项
          let elements = Array.from(editor.childNodes)
          editor.innerHTML = ''

          const getSplitBlockFromEditor = editor => {
            let { contentHeight, blockHeight } = getHeight(editor)
            return {
              contentHeight,
              height: blockHeight,
              content: editor.innerHTML,
            }
          }

          // 2. 逐个添加元素至编辑器中，检查编辑器高度
          elements.forEach((el, idx) => {
            editor.append(el)
            if (getHeight(editor).blockHeight > availableHeight) {
              editor.removeChild(el)
              if (editor.childNodes.length == 0) {
                splitBlocks.push(null)
              } else {
                splitBlocks.push(getSplitBlockFromEditor(editor))
                if (notFirstPaddingTop != null) {
                  editor.style.paddingTop = notFirstPaddingTop + 'px'
                }
              }
              // (1) 获取下一页可用高度
              availableHeight = getNextPageAvailableHeightFunc(++nextPageIndex)

              // (2) 设置编辑器内容为当前元素
              editor.innerHTML = ''
              editor.append(el)
            }
            if (idx == elements.length - 1) {
              splitBlocks.push(getSplitBlockFromEditor(editor))
            }
          })
        }

        // 补充空白
        let allBlocksContentHeight = sumByDefaultZero(splitBlocks, b => (b && b.contentHeight) || 0)
        let blankHeight =
          questionGroupContentHeight > allBlocksContentHeight ? questionGroupContentHeight - allBlocksContentHeight : 0
        // 首先补充至最后一块
        let lastBlock = splitBlocks[splitBlocks.length - 1]
        availableHeight = Math.max(0, availableHeight - lastBlock.height)
        let addBlankHeight = Math.min(blankHeight, availableHeight)
        lastBlock.contentHeight += addBlankHeight
        lastBlock.height += addBlankHeight
        blankHeight -= addBlankHeight
        // 剩下的逐页添加，小于40像素高的空白舍弃
        while (blankHeight >= 40) {
          availableHeight = getNextPageAvailableHeightFunc(++nextPageIndex)
          addBlankHeight = Math.min(blankHeight, availableHeight)
          splitBlocks.push({
            content: '',
            contentHeight: addBlankHeight,
            height: addBlankHeight,
          })
          blankHeight -= addBlankHeight
        }

        // 添加内容后的部分，如先阅后扫填空题打分框
        if (contentBottomHeight > 0) {
          lastBlock = splitBlocks[splitBlocks.length - 1]
          if (availableHeight - lastBlock.height > contentBottomHeight) {
            lastBlock.height += contentBottomHeight
          } else {
            splitBlocks.push({
              content: '',
              contentHeight: 0,
              height: contentBottomHeight,
            })
          }
        }

        // 恢复编辑器
        editor.innerHTML = editorHTML
        editor.style.paddingTop = null
        editor.style.padding = editorPadding

        return splitBlocks
      },
    },
  }
</script>

<style lang="scss" scoped>
  .box-content {
    position: relative;
  }

  .editor-active {
    border-top-color: $color-info !important;
    border-right-color: $color-info !important;
    border-bottom-color: $color-info !important;
    border-left-color: $color-info !important;
  }

  .drag-bar {
    position: absolute;
    right: 0;
    bottom: 0;
    display: none;
    width: 20px;
    height: 20px;
    padding: 2px;
    line-height: 1;

    &:hover {
      color: $color-primary;
      cursor: ns-resize;
    }
  }

  .box-content:hover {
    // .editor {
    //   background-color: $color-iview-table-active-row;
    // }

    .drag-bar {
      display: block;
    }
  }
</style>
