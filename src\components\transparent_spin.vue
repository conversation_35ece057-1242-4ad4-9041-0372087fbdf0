<template>
  <div class="sure-spin-wrapper">
    <div class="sure-spin-main">
      <div class="sure-spin-dot"></div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .sure-spin-wrapper {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2147483647;
  }

  .sure-spin-main {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .sure-spin-dot {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: $color-primary;
    animation: animation-spin-bounce 1s infinite;
  }

  @keyframes animation-spin-bounce {
    0% {
      transform: scale(0);
    }

    100% {
      transform: scale(1);
      opacity: 0;
    }
  }
</style>
