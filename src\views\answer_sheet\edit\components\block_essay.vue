<template>
  <div class="block-subjective">
    <ScorePanel v-if="showScorePanel" ref="scorePanel" :block="block" :move-top="!border.top"></ScorePanel>
    <SelectPanel
      v-if="showSelectPanel"
      ref="selectPanel"
      :question-group="block.questionGroup"
      :border="selectPanelBorderStyle"
    ></SelectPanel>
    <com-box-content
      ref="contentBox"
      :content="block.content"
      :width="pageBodyWidth"
      :height="block.contentHeight"
      :border="contentBorderStyle"
      :padding-top="contentPaddingTop"
      :resizable="true"
      @height-change="handleContentHeightChange"
      @resize="handleResize"
      @resize-end="handleResizeEnd"
      @content-change="changeContent"
    >
    </com-box-content>

    <div class="page-block-toolbar no-print">
      <div class="btn btn-primary" title="设置" @click="showModalChangeType = true">
        <Icon class="btn-icon" type="ios-settings"></Icon>
      </div>
      <div v-if="canSetSelect" class="btn btn-primary" title="设为选做" @click="handleSetSelect">
        <Icon class="btn-icon" type="md-checkbox-outline"></Icon>
      </div>
      <div v-if="canSplit" class="btn btn-primary btn-split" title="拆分" @click="handleSplit"></div>
      <div v-if="enableEditQuestion" class="btn btn-error" title="删除" @click="handleDelete">
        <Icon class="btn-icon" type="md-close"></Icon>
      </div>
    </div>

    <div v-if="canMerge" class="merge-area no-print" title="合并" @click="handleMerge"></div>

    <ModalChangeType v-model="showModalChangeType" :question-group="block.questionGroup"></ModalChangeType>

    <Modal v-model="showModalSelect" title="设为选做">
      <Form :label-width="80">
        <FormItem label="选做题号">{{ questionsGroupByCode.map(q => q.questionCode).join('、') }}</FormItem>
        <FormItem label="选做规则">
          <span>{{ questionsGroupByCode.length }}选</span>
          <InputNumber v-model="selectCount" :min="1" :max="questionsGroupByCode.length - 1"></InputNumber>
        </FormItem>
      </Form>
      <template #footer>
        <Button type="text" @click="showModalSelect = false">取消</Button>
        <Button type="primary" @click="handleModalSelectOK">确定</Button>
      </template>
    </Modal>
  </div>
</template>
<script>
  import comBoxContent from './box_content.vue'
  import ModalChangeType from './modal_change_type'
  import ScorePanel from './score_panel'
  import SelectPanel from './select_panel'

  import { mapGetters, mapMutations } from 'vuex'
  import { isPositiveInteger } from '@/utils/number'
  import { groupArray } from '@/utils/array'
  import { sumByDefaultZero } from '@/utils/math'
  import ScoreTypeEnum from '@/enum/answer_sheet/score_type'
  import { SelectPanelHeight } from '@/const/answer_sheet'

  export default {
    components: {
      comBoxContent,
      ModalChangeType,
      ScorePanel,
      SelectPanel,
    },
    props: {
      block: {
        type: Object,
        required: true,
      },
    },
    emits: ['height-change'],
    data() {
      return {
        // 修改题型
        showModalChangeType: false,
        // 选做
        showModalSelect: false,
        selectCount: 1,
        contentBasePaddingTop: 14,
      }
    },
    computed: {
      ...mapGetters('answerSheet', ['enableEditQuestion', 'isPostScan']),

      /**
       * 打分栏相关
       */
      showScorePanel() {
        return this.isPostScan && this.block.part == 0
      },
      showScorePanelBar() {
        return this.showScorePanel && this.block.questionGroup.scoreType.name == ScoreTypeEnum.Bar.id
      },

      /**
       * 选做框相关
       */
      showSelectPanel() {
        return this.block.questionGroup.selectGroupName && this.block.part == 0
      },
      selectPanelHeight() {
        return this.showSelectPanel ? SelectPanelHeight : 0
      },

      /**
       * 样式
       */
      pageBodyWidth() {
        let pageSize = this.$store.getters['answerSheet/pageSize'](this.block.pageIndex)
        return pageSize.width - pageSize.left - pageSize.right
      },
      border() {
        return this.$store.getters['answerSheet/getShowBoxContentBorder'](this.block)
      },
      contentBorderStyle() {
        let { left, right, top, bottom } = this.border
        if (this.showSelectPanel) {
          top = false
        }
        return { left, right, top, bottom }
      },
      contentPaddingTop() {
        let paddingTop = this.contentBasePaddingTop
        if (this.showScorePanelBar) {
          paddingTop += this.block.questionGroup.scoreType.height
        }
        return paddingTop
      },
      selectPanelBorderStyle() {
        return {
          left: true,
          right: true,
          top: this.border.top,
          bottom: true,
        }
      },

      /**
       * 合并拆分
       */
      canMerge() {
        return (
          this.block.part == 0 &&
          this.$store.getters['answerSheet/getCanMergeQuestionGroup'](this.block.questionGroup.id)
        )
      },
      canSplit() {
        return (
          this.block.part == 0 &&
          this.$store.getters['answerSheet/getCanSplitQuestionGroup'](this.block.questionGroup.id)
        )
      },

      /**
       * 设为选做
       */
      canSetSelect() {
        return this.block.part == 0 && this.$store.getters['answerSheet/getCanSetSelect'](this.block.questionGroup.id)
      },
      questionsGroupByCode() {
        return groupArray(this.block.questionGroup.questions, q => q.questionCode).map(g => ({
          questionCode: g.group[0].questionCode,
          branches: g.group,
          score: sumByDefaultZero(g.group, q => q.fullScore),
        }))
      },
    },
    watch: {
      selectPanelHeight() {
        this.changeHeight()
      },
    },
    created() {
      this.changeInstance()
    },
    updated() {
      this.changeInstance()
    },
    methods: {
      ...mapMutations('answerSheet', ['changePageBlockInstance', 'changePageBlockContent', 'changePageBlockHeight']),

      /**
       * 实例、内容、高度
       */
      changeInstance() {
        this.changePageBlockInstance({
          blockId: this.block.id,
          instance: this,
        })
      },
      changeContent(content) {
        this.changePageBlockContent({
          blockId: this.block.id,
          content: content,
        })
      },
      changeHeight(contentHeight = this.block.contentHeight, emitEvent) {
        let height = contentHeight + this.selectPanelHeight
        let heightChanged = height != this.block.height
        this.changePageBlockHeight({
          blockId: this.block.id,
          height,
          contentHeight,
        })
        if (emitEvent || (emitEvent == null && heightChanged)) {
          this.$emit('height-change', height)
        }
      },
      handleContentHeightChange(contentHeight) {
        this.changeHeight(contentHeight)
      },
      // 调整高度过程中不重排
      handleResize(contentHeight) {
        this.changeHeight(contentHeight, false)
      },
      // 调整完成后重排
      handleResizeEnd() {
        this.changeHeight(this.contentHeight, true)
      },

      /**
       * 操作
       */
      handleDelete() {
        let questionCodes = Array.from(new Set(this.block.questionGroup.questions.map(q => q.questionCode)))
        this.$Modal.confirm({
          title: '确认删除',
          content: `您将删除第${questionCodes.join('、')}题`,
          onOk: () => {
            this.$store.commit('answerSheet/deleteSubjectiveQuestionGroup', this.block.questionGroup.id)
            this.$store.commit('answerSheet/generatePages')
          },
        })
      },
      handleMerge() {
        let doMerge = () => {
          this.$store.commit('answerSheet/mergeQuestionGroup', this.block.questionGroup.id)
          this.$store.commit('answerSheet/generatePages')
        }
        if (this.isPostScan) {
          this.$Modal.confirm({
            title: '请您确认',
            content: '合并后将成为选做题',
            onOk: doMerge,
          })
        } else {
          doMerge()
        }
      },
      handleSplit() {
        this.$store.commit('answerSheet/splitQuestionGroup', this.block.questionGroup.id)
        this.$store.commit('answerSheet/generatePages')
      },
      handleSetSelect() {
        let score = this.questionsGroupByCode[0].score
        if (!score || this.questionsGroupByCode.some(q => q.score != score)) {
          this.$Message.warning({
            content: `${this.questionsGroupByCode
              .map(q => q.questionCode)
              .join('、')}题分数不相同，请先点击【题目列表】修改分数`,
            duration: 5,
            closable: true,
          })
          return
        }
        this.selectCount = 1
        this.showModalSelect = true
      },
      handleModalSelectOK() {
        if (
          !(
            this.selectCount >= 1 &&
            this.selectCount < this.questionsGroupByCode.length &&
            isPositiveInteger(this.selectCount)
          )
        ) {
          this.$Message.warning({
            content: '选做个数错误',
          })
          return
        }
        this.showModalSelect = false
        this.$store.commit('answerSheet/setQuestionGroupSelect', {
          questionGroupId: this.block.questionGroup.id,
          selectCount: this.selectCount,
        })
        this.$store.commit('answerSheet/generatePages')
      },

      /**
       * 拆分
       */
      split(getNextPageAvailableHeightFunc) {
        let blocks
        try {
          blocks = this.$refs['contentBox'].split(getNextPageAvailableHeightFunc, this.block.questionGroup, {
            contentTopHeight: this.selectPanelHeight,
            notFirstPaddingTop: this.contentBasePaddingTop,
          })
        } catch {
          blocks = []
        }
        return blocks
      },

      /**
       * 扫描模板
       */
      getScoreAreas(areaLeft, areaTop) {
        if (this.showScorePanel) {
          return this.$refs['scorePanel'].getScoreAreas(areaLeft, areaTop)
        } else {
          return []
        }
      },
      getSelectMarkAreas(areaLeft, areaTop) {
        if (this.showSelectPanel) {
          return this.$refs['selectPanel'].getSelectMarkAreas(areaLeft, areaTop)
        } else {
          return []
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-block-toolbar {
    .tbtn {
      display: block;
    }
  }
</style>
