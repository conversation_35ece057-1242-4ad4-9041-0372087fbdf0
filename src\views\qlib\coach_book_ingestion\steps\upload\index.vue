<template>
  <div class="ingestion-step-upload">
    <div v-if="hasUploadedFile" class="section-upload-info">
      <template v-if="isFileTypePdf">
        <div class="pdf-file">
          PDF文件已上传<a :href="ingestionStore.ingestionInfo.filePath[0]" target="_blank" class="open-file"
            >点击查看</a
          >
        </div>
        <Divider class="divider" type="vertical"></Divider>
        <div class="pdf-convert">
          <span v-if="showPdfConvertStatus">{{ pdfConvertStatusText }}</span>
          <TextButton
            v-if="btnConvertPdfToImage"
            class="btn-convert"
            :type="btnConvertPdfToImage.type"
            @click="convertPdfToImage"
            >{{ btnConvertPdfToImage.text }}</TextButton
          >
        </div>
      </template>
      <template v-if="isFileTypeImage">
        <div class="image-file">已上传 {{ ingestionStore.ingestionInfo.filePath.length }} 张图片</div>
      </template>
      <div class="actions">
        <TextButton v-if="showBtnReUploadPdf" class="btn" type="primary" @click="handleBtnReUploadPdfClick"
          >重新上传PDF</TextButton
        >
        <template v-if="hasUploadedImage">
          <TextButton class="btn" type="primary" @click="showModalUploadImage = true">继续上传图片</TextButton>
          <span v-if="imagesOrderChanged" class="change-order">
            <span class="tip">图片顺序已调整，</span
            ><TextButton class="btn" type="warning" @click="saveImageOrder">保存</TextButton
            ><TextButton class="btn" type="warning" @click="cancelImageOrder">撤销</TextButton>
          </span>
          <span v-else class="tip">拖动调整图片顺序</span>
          <TextButton class="btn" type="warning" :disabled="!hasSelectedImages" @click="deleteSelectedImages"
            >删除选中图片
            <Tooltip :max-width="300"
              ><Icon type="ios-information-circle-outline"></Icon>
              <template #content>
                <span>按住 shift 键可连续选择<br />esc 键可取消选择</span>
              </template>
            </Tooltip></TextButton
          >
        </template>
        <TextButton v-if="showBtnDeleteUploadedFile" class="btn" type="error" @click="deleteUploadedFile"
          >删除已上传文件</TextButton
        >
      </div>
    </div>
    <div class="section-upload-content">
      <NoData v-if="!hasUploadedFile">
        <div class="no-data-tip">尚未上传文件</div>
        <div class="no-data-action">
          <Button type="primary" @click="showModalUploadPdf = true">上传PDF文件</Button><span class="label">或</span
          ><Button type="primary" @click="showModalUploadImage = true">上传图片文件</Button>
        </div>
      </NoData>
      <ImageContainer
        v-else
        v-model:images="displayImages"
        :enable-select="hasUploadedImage && !imagesOrderChanged"
        :enable-reorder="hasUploadedImage"
      ></ImageContainer>
    </div>

    <ModalUploadPdf v-model="showModalUploadPdf" @uploaded="handleFileUploaded"></ModalUploadPdf>
    <ModalUploadImage v-model="showModalUploadImage" @uploaded="handleFileUploaded"></ModalUploadImage>
  </div>
</template>

<script setup>
  import { computed, onBeforeMount, onBeforeUnmount, ref, watch } from 'vue'
  import iView from '@/iview'

  import { useCoachBookIngestionStore } from '@/store/qlib/coach_book_ingestion'

  import ModalUploadPdf from './modal_upload_pdf.vue'
  import ModalUploadImage from './modal_upload_image.vue'
  import ImageContainer from '../../components/image_container.vue'
  import NoData from '@/components/no_data.vue'

  import {
    apiCoachBookIngestionConvertPdfToImage,
    apiCoachBookIngestionDeleteUploadedFile,
    apiCoachBookIngestionChangeUploadedImages,
  } from '@/api/qlib/coach_book_ingestion'

  import FileTypeEnum from '@/enum/qlib/ingestion/file_type'
  import CommonProgressEnum from '@/enum/qlib/ingestion/common_progress'

  const ingestionStore = useCoachBookIngestionStore()

  // 显示上传pdf弹窗
  const showModalUploadPdf = ref(false)
  // 显示上传图片弹窗
  const showModalUploadImage = ref(false)
  // 显示的图片
  const displayImages = ref([])
  // 刷新定时器
  let refreshTimer = 0

  // 当前文件类型
  const currentFileType = computed(() => {
    return ingestionStore.ingestionInfo?.fileType
  })
  // 当前文件类型是否为pdf
  const isFileTypePdf = computed(() => {
    return currentFileType.value == FileTypeEnum.Pdf.id
  })
  // 当前文件类型是否为图片
  const isFileTypeImage = computed(() => {
    return currentFileType.value == FileTypeEnum.Image.id
  })
  // 是否已上传文件
  const hasUploadedFile = computed(() => {
    let filePath = ingestionStore.ingestionInfo?.filePath
    return filePath && filePath.length > 0
  })
  // 是否已上传pdf
  const hasUploadedPdf = computed(() => {
    return isFileTypePdf.value && hasUploadedFile.value
  })
  // 是否已上传图片
  const hasUploadedImage = computed(() => {
    return isFileTypeImage.value && hasUploadedFile.value
  })
  // 显示pdf转换图片状态
  const showPdfConvertStatus = computed(() => {
    return hasUploadedPdf.value && ingestionStore.ingestionInfo.pdfToImageStatus != CommonProgressEnum.Init.id
  })
  // pdf转换图片状态文本
  const pdfConvertStatusText = computed(() => {
    if (!showPdfConvertStatus.value) {
      return ''
    }
    let { pdfToImageStatus, pdfPageCount, pageImages } = ingestionStore.ingestionInfo
    let text = ''
    if (pdfToImageStatus == CommonProgressEnum.Running.id) {
      text = `转换图片中`
    } else if (pdfToImageStatus == CommonProgressEnum.Completed.id) {
      text = '转换图片完成'
    } else if (pdfToImageStatus == CommonProgressEnum.Failed.id) {
      text = '转换图片失败'
    }
    if (pdfPageCount > 0) {
      text += `，共 ${pdfPageCount} 页`
    }
    if (pageImages.length > 0 && pdfToImageStatus != CommonProgressEnum.Completed.id) {
      text += `，已转换 ${pageImages.length} 页`
    }
    return text
  })
  // 转换pdf为图片按钮
  const btnConvertPdfToImage = computed(() => {
    if (!hasUploadedPdf.value) {
      return null
    }
    if (ingestionStore.ingestionInfo.pdfToImageStatus == CommonProgressEnum.Init.id) {
      return {
        type: 'primary',
        text: '转为图片',
      }
    } else if (ingestionStore.ingestionInfo.pdfToImageStatus == CommonProgressEnum.Failed.id) {
      return {
        type: 'error',
        text: '重试转换',
      }
    } else {
      return null
    }
  })
  // 显示重新上传pdf按钮
  const showBtnReUploadPdf = computed(() => {
    return hasUploadedPdf.value && ingestionStore.ingestionInfo.pdfToImageStatus != CommonProgressEnum.Running.id
  })
  // 显示删除已上传文件按钮
  const showBtnDeleteUploadedFile = computed(() => {
    return hasUploadedFile.value && ingestionStore.ingestionInfo.pdfToImageStatus != CommonProgressEnum.Running.id
  })

  // 图片顺序是否改变
  const imagesOrderChanged = computed(() => {
    return displayImages.value.some((x, idx) => x.order != idx)
  })

  // 是否有选中的图片
  const hasSelectedImages = computed(() => {
    return displayImages.value.some(x => x.selected)
  })

  // 定时刷新pdf转换状态
  onBeforeMount(startRefreshPdfToImageStatusTimer)
  onBeforeUnmount(() => {
    clearTimeout(refreshTimer)
    refreshTimer = 0
  })

  // 图片变化时设置显示图片列表
  watch(() => ingestionStore.ingestionInfo.pageImages, setDidsplayImages, { immediate: true })

  // 设置显示图片
  function setDidsplayImages() {
    displayImages.value = ingestionStore.ingestionInfo.pageImages.slice().map((x, idx) => ({
      ...x,
      selected: false,
      order: idx,
    }))
  }

  // 转为图片
  async function convertPdfToImage() {
    await apiCoachBookIngestionConvertPdfToImage(ingestionStore.ingestionInfo.id)
    iView.Message.info('已开始转换图片')
    await ingestionStore.refreshIngestionInfo()
    startRefreshPdfToImageStatusTimer()
  }

  // 重新上传pdf
  function handleBtnReUploadPdfClick() {
    iView.Modal.confirm({
      title: '重新上传PDF',
      content: '确定要重新上传PDF吗？已上传的PDF文件会被覆盖且不可恢复！',
      okText: '确定重传',
      onOk: async () => {
        showModalUploadPdf.value = true
      },
    })
  }

  // 删除已上传文件
  function deleteUploadedFile() {
    iView.Modal.confirm({
      title: '删除已上传文件',
      content: '确定要删除已上传的文件吗？此操作不可恢复！',
      okText: '确定删除',
      onOk: async () => {
        await apiCoachBookIngestionDeleteUploadedFile(ingestionStore.ingestionInfo.id)
        iView.Message.success('已删除')
        await ingestionStore.refreshIngestionInfo()
      },
    })
  }

  // 文件上传完成
  async function handleFileUploaded() {
    await ingestionStore.refreshIngestionInfo()
    startRefreshPdfToImageStatusTimer()
  }

  // 开启刷新pdf转图片状态定时器
  function startRefreshPdfToImageStatusTimer() {
    if (
      isFileTypePdf.value &&
      hasUploadedFile.value &&
      ingestionStore.ingestionInfo.pdfToImageStatus == CommonProgressEnum.Running.id
    ) {
      refreshTimer = setTimeout(() => {
        ingestionStore.refreshIngestionInfo().finally(() => {
          startRefreshPdfToImageStatusTimer()
        })
      }, 1000 * 2)
    } else {
      clearTimeout(refreshTimer)
      refreshTimer = 0
    }
  }

  // 保存图片顺序修改
  async function saveImageOrder() {
    if (!imagesOrderChanged.value) {
      return
    }
    iView.Modal.confirm({
      title: '调整图片顺序',
      content: '确定要保存已调整的图片顺序吗？',
      onOk: async () => {
        try {
          let filePath = displayImages.value.map(x => x.url)
          await apiCoachBookIngestionChangeUploadedImages({
            ingestionId: ingestionStore.ingestionInfo.id,
            filePath,
          })
          iView.Message.success('已调整图片顺序')
        } finally {
          await ingestionStore.refreshIngestionInfo()
        }
      },
    })
  }

  // 撤销修改图片顺序
  function cancelImageOrder() {
    setDidsplayImages()
  }

  // 删除选中图片
  function deleteSelectedImages() {
    let selectedImages = displayImages.value.filter(x => x.selected)
    if (selectedImages.length == 0) {
      return
    }
    iView.Modal.confirm({
      title: '删除选中图片',
      content: `确定要删除选中的 ${selectedImages.length} 张图片吗？此操作不可恢复！`,
      okText: '确定删除',
      onOk: async () => {
        let filePath = displayImages.value.filter(x => !x.selected).map(x => x.url)
        try {
          await apiCoachBookIngestionChangeUploadedImages({
            ingestionId: ingestionStore.ingestionInfo.id,
            filePath,
          })
          iView.Message.success('已删除')
        } finally {
          await ingestionStore.refreshIngestionInfo()
        }
      },
    })
  }
</script>

<style lang="scss" scoped>
  .ingestion-step-upload {
    @include flex(column, flex-start, stretch);
  }

  .section-upload-info {
    @include flex(row, flex-start, center);
    flex-grow: 0;
    flex-shrink: 0;
    height: 32px;
    padding-right: 16px;
    padding-left: 16px;
    border-bottom: 1px solid $color-border;

    .pdf-file {
      @include flex(row, flex-start, center);

      .open-file {
        margin-left: 10px;
        color: $color-primary;
      }
    }

    .pdf-convert {
      @include flex(row, flex-start, center);

      .btn-convert:not(:first-child) {
        margin-left: 8px;
      }
    }

    .divider {
      margin-right: 16px;
      margin-left: 16px;
    }

    .actions {
      @include flex(row, flex-start, center);
      flex-grow: 0;
      flex-shrink: 0;
      margin-left: auto;
      column-gap: 16px;
    }
  }

  .section-upload-content {
    flex-grow: 1;
    flex-shrink: 1;
    overflow-y: hidden;

    .no-data-action {
      margin-top: 16px;

      .label {
        margin-right: 16px;
        margin-left: 16px;
      }
    }
  }
</style>
