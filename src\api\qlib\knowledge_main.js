import ajax from '@/api/ajax'

export function apiGetKnowledges(stageId, subjectId) {
  return ajax
    .get({
      url: `ques/knowledge/byLidAndSid`,
      params: {
        lid: stageId,
        sid: subjectId,
      },
      requestName: '获取知识点',
    })
    .then(data => {
      return !data
        ? []
        : data.map(function _transformNode(node) {
            return {
              id: Number(node.id) || 0,
              name: node.knowledgeName || '',
              children: (node.knowledgeVoList || []).map(_transformNode),
            }
          })
    })
}

export function apiExportKnowledgeExcel({ stageId, subjectId }) {
  return ajax.download({
    url: 'ques/knowledge/export',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    requestName: '下载知识点',
  })
}

/**
 * 导入知识点检查
 */
export function apiImportKnowledgeCheck({ stageId, subjectId, file }) {
  return ajax.upload({
    url: 'ques/knowledge/importCheck',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    data: {
      file,
    },
    responseBlob: true,
  })
}

/**
 * 导入知识点
 */
export function apiImportKnowledge({ stageId, subjectId, file }) {
  return ajax.upload({
    url: 'ques/knowledge/import',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    data: {
      file,
    },
    responseBlob: true,
  })
}

/**
 * 是否有导入权限
 */
export function apiGetImportKnowledgeAllowed() {
  return ajax.get({
    url: 'ques/knowledge/allowImport',
  })
}

export function apiDeleteKnowledgeCache({ stageId, subjectId }) {
  return ajax.delete({
    url: 'ques/knowledge/deleteCache',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    requestName: '清除知识点缓存',
  })
}
