import ajax from '@/api/ajax'

// 获取签名
export function apiGetSignature(params) {
  return ajax.get({
    url: '/scan/scanweb/createSignature',
    params: params,
  })
}

// 预检查生成签名和token
export function apiPreScan(requestParams) {
  return ajax.post({
    url: '/scan/scanweb/initScanBatch2',
    data: {
      clientVersion: requestParams.clientVersion, // String
      deviceName: requestParams.deviceName, // String
      deviceNo: requestParams.deviceNo, // String
      deviceManufacturer: requestParams.deviceManufacturer, // String
      examSubjectId: requestParams.examSubjectId, // String
      nonce: requestParams.nonce, // String
      secretId: requestParams.secretId, // String
      timeStamp: requestParams.timeStamp, // String
      isPostScan: requestParams.isPostScan || false, // Boolean
      clientHandwritingSupported: requestParams.clientHandwritingSupported, // <PERSON><PERSON>an
    },
  })
}
