import JSZip from 'jszip'

import { generateZippedFileBlobs } from './generate_blobs'
import { generateSeniorHighZippedFiledBlobs } from './generate_senior_high_blobs'

import { downloadBlob } from '@/utils/download'

import Store from '@/store/index'

export function exportZippedSchoolReports(exportParams) {
  let zip = new JSZip()
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']

  // export situation
  let success = 0
  let failed = 0
  let failedReportList = []

  return (
    'excelShowRank' in exportParams
      ? generateZippedFileBlobs(exportParams)
      : generateSeniorHighZippedFiledBlobs(exportParams)
  )
    .then(results => {
      if (!results || !results.length) {
        throw '无压缩文件'
      }

      results.forEach(r => {
        if (r.status === 'fulfilled') {
          try {
            zip.file(r.value.fileName, r.value.blob)
            success++
          } catch (err) {
            failed++
            failedReportList.push({
              fileName: r.value.fileName,
              explain: '导出失败(打包zip出错)',
            })
          }
        } else if (r.status === 'rejected' && r.value.error !== '[ignore]') {
          failed++
          failedReportList.push({
            fileName: r.value.fileName,
            explain: `导出失败(${r.value.error})`,
          })
        }
      })
    })
    .then(() => zip.generateAsync({ type: 'blob' }))
    .catch(err => {
      throw '生成压缩文件失败：' + err
    })
    .then(zipBlob => {
      let examName = Store.getters['report/examName'] || ''
      let templateName = Store.getters['report/templateName'] || ''
      let schoolName = Store.getters['report/currentSchoolName'] || ''

      downloadBlob(
        zipBlob,
        `${examName}${templateName && examName !== templateName ? '_' + templateName : ''}${
          !isMultipleSchoolLevel && schoolName ? '_【' + schoolName + '】' : ''
        }.zip`
      )

      return {
        generated: true,
        success: success,
        failed: failed,
        failedReportList: failedReportList,
      }
    })
}
