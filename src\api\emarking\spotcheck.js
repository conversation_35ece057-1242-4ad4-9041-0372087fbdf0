import ajax from '@/api/ajax'
import { addImageUrlParam } from '@/utils/url'

export function apiGetScoreHistory(params) {
  return ajax
    .get({
      url: 'mark/spotcheck/list',
      params: {
        subjectiveId: params.blockId,
        groupId: params.groupId,
        schoolId: params.schoolId,
        teacherId: params.userId,
        fromScore: params.minScore,
        toScore: params.maxScore,
        beginTime: params.beginTime, // string: yyyy-MM-dd HH:mm:ss
        endTime: params.endTime, // string: yyyy-MM-dd HH:mm:ss
        admissionNum: params.admissionNum || undefined,
        size: params.pageSize,
        current: params.currentPage,
      },
      requestName: '获取评分记录',
    })
    .then(data => {
      return {
        total: data.total,
        records: data.records.map(x => ({
          blockId: x.subjectiveId,
          userId: x.teacherId,
          realName: x.teacherName,
          schoolId: x.schoolId,
          schoolName: x.schoolName,
          score: x.score,
          subScore: x.subScore,
          No: x.no,
          submitTime: x.submitTime,
          spotScore: x.sportScore,
          subjectiveItemId: x.subjectiveItemId,
        })),
      }
    })
}

export function apiGetAiMarkScoreHistory(params) {
  return ajax
    .get({
      url: 'mark/mark/ai/normalPage',
      params: {
        subjectiveId: params.blockId,
        fromScore: params.minScore || undefined,
        toScore: params.maxScore || undefined,
        beginTime: params.beginTime || undefined, // string: yyyy-MM-dd HH:mm:ss
        endTime: params.endTime || undefined, // string: yyyy-MM-dd HH:mm:ss
        size: params.pageSize,
        page: params.currentPage,
      },
      requestName: '获取智能评分记录',
    })
    .then(data => {
      return {
        total: data.total,
        records: data.records.map(x => ({
          ...x,
          imgUrl: addImageUrlParam(x.imgUrl),
          blockId: x.subjectiveId,
          userId: x.teacherId,
          No: x.no,
        })),
      }
    })
}

export function apiGetAiMarkAbnormalUndoHistory(params) {
  return ajax
    .get({
      url: 'mark/mark/ai/abnormalUndoPage',
      params: {
        subjectiveId: params.blockId,
        fromScore: params.minScore || undefined,
        toScore: params.maxScore || undefined,
        beginTime: params.beginTime || undefined, // string: yyyy-MM-dd HH:mm:ss
        endTime: params.endTime || undefined, // string: yyyy-MM-dd HH:mm:ss
        size: params.pageSize,
        page: params.currentPage,
      },
      requestName: '获取智能评卷疑难卷未处理记录',
    })
    .then(data => {
      return {
        total: data.total,
        records: data.records.map(x => ({
          ...x,
          imgUrl: addImageUrlParam(x.imgUrl),
          blockId: x.subjectiveId,
          userId: x.teacherId,
          No: x.no,
        })),
      }
    })
}

export function apiGetAiMarkAbnormalDoneHistory(params) {
  return ajax
    .get({
      url: 'mark/mark/ai/abnormalDonePage',
      params: {
        subjectiveId: params.blockId,
        fromScore: params.minScore || undefined,
        toScore: params.maxScore || undefined,
        beginTime: params.beginTime || undefined, // string: yyyy-MM-dd HH:mm:ss
        endTime: params.endTime || undefined, // string: yyyy-MM-dd HH:mm:ss
        size: params.pageSize,
        page: params.currentPage,
      },
      requestName: '获取智能评卷疑难卷已处理记录',
    })
    .then(data => {
      return {
        total: data.total,
        records: data.records.map(x => ({
          ...x,
          imgUrl: addImageUrlParam(x.imgUrl),
          blockId: x.subjectiveId,
          userId: x.teacherId,
          No: x.no,
        })),
      }
    })
}

export function apiSubmitAbnormalMarkScore(params) {
  return ajax.post({
    url: 'mark/mark/ai/abnormalMarkScore',
    data: {
      readTime: params.readTime,
      score: params.score,
      subScore: params.subScore,
      subjectiveId: params.subjectiveId,
      subjectiveItemId: params.subjectiveItemId,
    },
    requestName: '提交智能评卷疑难卷判分',
  })
}

export function apiGetHistoryItem(params) {
  return ajax
    .get({
      url: 'mark/spotcheck/sample',
      params: {
        subjectiveId: params.blockId,
        teacherId: params.userId,
        no: params.No,
      },
      requestName: '获取题块图片',
    })
    .then(data => {
      if (data && data.vo) {
        data.vo.imgUrl = addImageUrlParam(data.vo.imgUrl)
      }

      return data
    })
}

export function apiSubmitSpotCheckScore(params) {
  return ajax.put({
    url: 'mark/spotcheck/submit',
    data: {
      score: params.score,
      subScore: params.subScore,
      subjectiveId: params.subjectiveId,
      subjectiveItemId: params.subjectiveItemId,
    },
    requestName: '提交抽查给分',
  })
}

export function apiGetStudentPaperByStudentPaperInfo(requestParams) {
  return ajax.get({
    url: 'mark/spotcheck/studentPaper',
    params: {
      subjectiveId: requestParams.subjectiveId,
      subjectiveItemId: requestParams.subjectiveItemId,
    },
    requestName: '获取考生原图',
  })
}

export function apiGetRepeatScoreHistory(params) {
  return ajax
    .get({
      url: 'mark/mark/undo/allPage',
      params: {
        subjectiveId: params.blockId,
        groupId: params.groupId,
        schoolId: params.schoolId,
        markUserId: params.markUserId,
        markType: params.markType,
        isFinish: params.isFinish,
        size: params.pageSize,
        page: params.currentPage,
      },
      requestName: '获取重评记录',
    })
    .then(data => {
      data.records.forEach(item => {
        item.imgUrl = addImageUrlParam(item.imgUrl)
      })
      return data
    })
}

export function apiGetSpotRecords(requestParams) {
  return ajax.get({
    url: 'mark/spotcheck/listSpotRecords',
    params: {
      examSubjectId: requestParams.examSubjectId, // String
      subjectiveId: requestParams.subjectiveId, // String 题块Id
      spotTeacherId: requestParams.spotTeacherId, // String 抽查员Id
      spotScoreOnly: requestParams.spotScoreOnly, // Boolean 是否只查看抽查给分记录
      includeSysAdminRecord: requestParams.includeSysAdminRecord, // Boolean 是否包含系统管理员抽查记录
      page: requestParams.currentPage || 1, // Number
      size: requestParams.pageSize || 10, // Number
      // [Time format]: yyyy-MM-dd HH:mm:ss
      beginTime: requestParams.beginTime, // String
      endTime: requestParams.endTime, // String
    },
    requestName: '获取抽查记录',
  })
}

export function apiGetSpotRecordStatistic(requestParams) {
  return ajax.get({
    url: 'mark/spotcheck/listMarkerSpotStat',
    params: {
      examSubjectId: requestParams.examSubjectId, // * String
      subjectiveId: requestParams.subjectiveId, // String 题块Id
      spotTeacherId: requestParams.spotTeacherId, // String 抽查员Id
      includeSysAdminRecord: requestParams.includeSysAdminRecord, // Boolean 是否包含系统管理员抽查记录
      // [Time format]: yyyy-MM-dd HH:mm:ss
      beginTime: requestParams.beginTime, // String
      endTime: requestParams.endTime, // String
    },
    requestName: '获取抽查统计',
  })
}
