/**
 * 答题卡
 */

import PaperStructure from './paper_structure'
import Header from './header'
import Topic from './topic'
import QuestionGroup from './question_group'
import Page from './page'
import PageBlock from './page_block'

import Template from '@/helpers/scan_template_define/template'
import TemplateRect from '@/helpers/scan_template_define/template_rect'
import TemplatePage from '@/helpers/scan_template_define/template_page'
import TemplatePageParam from '@/helpers/scan_template_define/template_page_param'
import TemplateStudentInfo from '@/helpers/scan_template_define/template_student_info'
import TemplateObjective from '@/helpers/scan_template_define/template_objective'
import TemplateSubjective from '@/helpers/scan_template_define/template_subjective'
import TemplateQuestionObjective from '@/helpers/scan_template_define/template_question_objective'
import TemplateQuestionSubjective from '@/helpers/scan_template_define/template_question_subjective'

import { sortQuestionFunction } from '@/helpers/emarking/miscellaneous'
import { splitBlankScores } from '@/helpers/qlib/miscellaneous'
import { splitContent, renderTopicsContentMath, resetTopicsContentMath, preProcessPaper } from './util'
import { groupArray, unGroupArray, uniq } from '@/utils/array'
import { sumByDefaultZero } from '@/utils/math'
import { deepCopy } from '@/utils/object'
import { getHTMLWidth } from '@/utils/dom'

import ModeEnum from '@/enum/answer_sheet/mode'
import PageSizeEnum from '@/enum/answer_sheet/page_size'
import SourceTypeEnum from '@/enum/answer_sheet/source_type'
import QuestionGroupTypeEnum from '@/enum/answer_sheet/question_group_type'
import PageBlockTypeEnum from '@/enum/answer_sheet/page_block_type'
import TicketNumberTypeEnum from '@/enum/answer_sheet/ticket_number_type'
import ScoreTypeEnum from '@/enum/answer_sheet/score_type'
import LocateModeEnum from '@/enum/scan_template/locate_mode'
import AdmissionTypeEnum from '@/enum/scan_template/admission_type'
import BranchTypeEnum from '@/enum/qlib/branch_type'
import TrueFalseTypeEnum from '@/enum/answer_sheet/true_false_type'

import { SheetVersion, PixelsPerMillimeter } from '@/const/answer_sheet'
export default class Sheet {
  constructor() {
    // 答题卡版本，为比较方便，以整数表示
    this.version = SheetVersion
    // 答题卡ID
    this.id = ''
    // 答题卡名称
    this.name = ''
    // 来源：由题库试卷生成|由考试试卷结构生成|自主创建
    this.from = SourceTypeEnum.Manual.id
    // 学段
    this.stage = {}
    // 学科
    this.subject = {}
    // 题库组卷Id
    this.paperId = ''
    // 题库组卷试卷内容
    this.paper = null
    // 考试科目Id
    this.examSubjectId = ''
    // 试卷结构（题目、题块）
    this.paperStructure = new PaperStructure()
    // 是否允许增删题目
    this.enableEditQuestion = true
    // 客观题是否分大题排
    this.objectivesSeparate = false
    // 是否显示题目内容
    this.showQuestionContent = false
    // 答题卡模式：线上阅卷|先阅后扫
    this.mode = ModeEnum.Online.id
    // 先阅后扫打分方式：手写框|打分条|对错框
    this.scoreType = ScoreTypeEnum.Bar.id
    // 对错框留空默认值
    this.scoreTypeTrueFalseEmptyDefault = true
    //答题卡尺寸，允许每页有不同边距
    this.size = PageSizeEnum.A4_1
    // 基础样式
    this.baseStyle = {
      fontSize: '14px',
      fontFamily: 'Times New Roman, Times, 宋体, simsun, serif',
      lineHeight: '1.5',
      color: 'black',
      '--base-border-color': 'black',
    }
    // 答题卡头部
    this.header = new Header()
    // 页脚
    this.pageFooterContent = '第 {{page}} 页，共 {{total}} 页'
    // 大题
    this.topics = []
    // 答题卡页（栏）
    this.pages = []
  }

  /**
   * getters ---------------------------------------------------------------------------
   */

  // 获取页面尺寸
  getPageSize(pageIndex) {
    // 正面各栏
    let frontColumns = this.size.frontPages || this.size.pages
    // 背面各栏
    let backColumns = this.size.backPages || this.size.pages

    let frontAndBackColumnCount = frontColumns.length + backColumns.length
    // 纸张序号
    let paperIndex = Math.floor(pageIndex / frontAndBackColumnCount)
    // 栏在纸张中的序号
    let paperColumnIndex = pageIndex % frontAndBackColumnCount
    // 是否正面
    let isFrontSide = paperColumnIndex < frontColumns.length
    // 面序号
    let sideIndex = paperIndex * 2 + (isFrontSide ? 0 : 1)
    // 面内各栏
    let sideColumns = isFrontSide ? frontColumns : backColumns
    // 栏在面中的序号
    let sideColumnIndex = isFrontSide ? paperColumnIndex : paperColumnIndex - frontColumns.length
    // 指定栏
    let page = sideColumns[sideColumnIndex]
    // 每栏宽度平分
    let columnWidth = Math.round((this.size.width / sideColumns.length) * PixelsPerMillimeter)
    // 分栏左边缘与面左边缘的距离
    let pageLeft = sideColumnIndex * columnWidth

    let result = {
      paperIndex,
      sideIndex,
      isFrontSide,
      sideWidth: this.size.width * PixelsPerMillimeter,
      sideHeight: this.size.height * PixelsPerMillimeter,
      sideColumnCount: sideColumns.length,
      sideColumnIndex,
      pageLeft,

      width: columnWidth,
      height: this.size.height * PixelsPerMillimeter,
      left: page.left * PixelsPerMillimeter,
      right: page.right * PixelsPerMillimeter,
      top: page.top * PixelsPerMillimeter,
      bottom: page.top * PixelsPerMillimeter,
    }
    result.contentWidth = result.width - result.left - result.right
    result.contentHeight = result.height - result.top - result.bottom

    return result
  }
  // 获取页面内容宽度（取第一栏内容宽度）
  getPageContentWidth() {
    return this.getPageSize(0).contentWidth
  }
  // 获取页面内容高度（各栏高度相同）
  getPageContentHeight() {
    return this.getPageSize(0).contentHeight
  }
  // 是否显示页面块边框
  getShowBoxContentBorder(pageBlock) {
    let [left, right, top, bottom] = [false, false, false, false]
    const noBorder = _pageBlock => {
      // 这几类不显示边框
      if (
        [
          PageBlockTypeEnum.ExamInfo.id,
          PageBlockTypeEnum.Description.id,
          PageBlockTypeEnum.ContentObjective.id,
          PageBlockTypeEnum.Trunk.id,
        ].includes(_pageBlock.type)
      ) {
        return true
      }
      // 填空题显示题目内容时不显示边框
      if (PageBlockTypeEnum.FillBlank.id == _pageBlock.type && this.showQuestionContent) {
        return true
      }
      return false
    }
    if (!noBorder(pageBlock)) {
      left = true
      right = true
      top = true
      let page = this.pages[pageBlock.pageIndex]
      let blockPageIndex = page.blocks.indexOf(pageBlock)
      let lowerBlock = page.blocks[blockPageIndex + 1]
      let lowerBlockHasBorder = lowerBlock && !noBorder(lowerBlock)
      bottom = !lowerBlockHasBorder
    }
    return { left, right, top, bottom }
  }
  // 是否是评卷题块的唯一题组
  isBlockOnlyQuestionGroup(g) {
    let blocks = []
    g.questions.forEach(q => {
      let block = this.paperStructure.getBlockByQuestionId(q.questionId)
      if (!blocks.includes(block)) {
        blocks.push(block)
      }
    })
    let blockQuestionIds = unGroupArray(blocks, block => block.questions).map(q => q.questionId)
    for (let topic of this.topics) {
      for (let group of topic.questionGroups) {
        if (
          group.id != g.id &&
          group.type != QuestionGroupTypeEnum.Trunk.id &&
          group.questions.some(q => blockQuestionIds.includes(q.questionId))
        ) {
          return false
        }
      }
    }
    return true
  }
  // 是否可与上一块合并
  getCanMergeQuestionGroup(questionGroupId) {
    // 由试卷结构创建且不可增删题目，则不允许合并拆分
    if (this.from == SourceTypeEnum.Exam.id && !this.enableEditQuestion) {
      return false
    }
    // 大题的第一块，不允许合并
    let topic = null
    let theGroup = null
    let preGroup = null
    for (let t of this.topics) {
      for (let gIdx = 0; gIdx < t.questionGroups.length; gIdx++) {
        if (t.questionGroups[gIdx].id == questionGroupId) {
          topic = t
          theGroup = t.questionGroups[gIdx]
          preGroup = t.questionGroups[gIdx - 1]
          break
        }
      }
    }
    if (!topic || !theGroup || !preGroup) {
      return false
    }
    // 智能评分不允许合并
    if (theGroup.aiScoreTypeId != 0) {
      return false
    }
    // 仅填空题、解答题、无选项客观题允许合并
    if (
      ![
        QuestionGroupTypeEnum.FillBlank.id,
        QuestionGroupTypeEnum.Essay.id,
        QuestionGroupTypeEnum.ContentObjectiveNoOption.id,
      ].includes(theGroup.type)
    ) {
      return false
    }
    // 必须与上一块题型相同
    if (theGroup.type != preGroup.type) {
      return false
    }
    // 必须是题块的唯一题组
    if (
      theGroup.type != QuestionGroupTypeEnum.ContentObjectiveNoOption.id &&
      (!this.isBlockOnlyQuestionGroup(theGroup) || !this.isBlockOnlyQuestionGroup(preGroup))
    ) {
      return false
    }
    // 题组各题均包含了所有小题
    let [theGroupByQuestionCode, preGroupByQuestionCode] = [theGroup, preGroup].map(questionGroup => {
      return groupArray(questionGroup.questions, q => q.questionCode).map(g => ({
        questionCode: g.group[0].questionCode,
        fullScore: sumByDefaultZero(g.group, q => q.fullScore),
        branchCount: g.group.length,
        originalQuestionId: g.group[0].originalQuestionId,
      }))
    })
    let questionCodeBranchCountMap = this.paperStructure.getQuestionCodeBranchCountMap()
    let containAllBranches = [theGroupByQuestionCode, preGroupByQuestionCode].every(g =>
      g.every(q => q.branchCount == questionCodeBranchCountMap.get(q.questionCode))
    )
    // 两题组均只包含相同的一题
    let containOnlySameQuestion =
      theGroupByQuestionCode.length == 1 &&
      preGroupByQuestionCode.length == 1 &&
      theGroupByQuestionCode[0].questionCode == preGroupByQuestionCode[0].questionCode

    // 各题均包含所有小题或两题组均只包含相同的一题
    if (!containAllBranches && !containOnlySameQuestion) {
      return false
    }

    // 显示题目解答题要求同一道原题下
    if (
      this.showQuestionContent &&
      [QuestionGroupTypeEnum.Essay.id, QuestionGroupTypeEnum.ContentObjectiveNoOption.id].includes(theGroup.type)
    ) {
      let questionCodes = [...preGroupByQuestionCode, ...theGroupByQuestionCode]
      if (questionCodes.some(({ originalQuestionId }) => originalQuestionId != questionCodes[0].originalQuestionId)) {
        return false
      }
    }

    // 先阅后扫解答题合并为选做
    if (this.mode == ModeEnum.PostScan.id) {
      if (theGroup.type == QuestionGroupTypeEnum.Essay.id) {
        if (this.showQuestionContent) {
          return false
        }
        if (!containAllBranches) {
          return false
        }
        let questionCodes = [...preGroupByQuestionCode, ...theGroupByQuestionCode]
        for (let i = 1; i < questionCodes.length; i++) {
          let cur = questionCodes[i]
          let pre = questionCodes[i - 1]
          if (cur.questionCode != pre.questionCode + 1 || cur.fullScore != pre.fullScore) {
            return false
          }
        }
      }
    }
    // 线上阅卷：非选做题
    else {
      if (theGroup.selectGroupName || preGroup.selectGroupName) {
        return false
      }
    }

    return true
  }
  // 是否可拆分
  getCanSplitQuestionGroup(questionGroupId) {
    // 由试卷结构创建且不可增删题目，则不允许合并拆分
    if (this.from == SourceTypeEnum.Exam.id && !this.enableEditQuestion) {
      return false
    }
    let theGroup = this.findQuestionGroupById(questionGroupId)
    if (!theGroup) {
      return false
    }
    // 智能评分不允许拆分
    if (theGroup.aiScoreTypeId != 0) {
      return false
    }
    // 仅填空题、解答题、无选项客观题允许合并
    if (
      ![
        QuestionGroupTypeEnum.FillBlank.id,
        QuestionGroupTypeEnum.Essay.id,
        QuestionGroupTypeEnum.ContentObjectiveNoOption.id,
      ].includes(theGroup.type)
    ) {
      return false
    }
    // 必须是题块的唯一题组
    if (
      theGroup.type != QuestionGroupTypeEnum.ContentObjectiveNoOption.id &&
      !this.isBlockOnlyQuestionGroup(theGroup)
    ) {
      return false
    }
    // 多题且各题均包含了所有小题
    let questionCodeBranchCountMap = this.paperStructure.getQuestionCodeBranchCountMap()
    let groupByQuestionCode = groupArray(theGroup.questions, q => q.questionCode).map(g => ({
      questionCode: g.group[0].questionCode,
      branchCount: g.group.length,
      originalQuestionId: g.group[0].originalQuestionId,
    }))
    let containAllBranches =
      groupByQuestionCode.length > 1 &&
      groupByQuestionCode.every(g => g.branchCount == questionCodeBranchCountMap.get(g.questionCode))
    // 一题有多个小题
    let containOnlySameQuestion = groupByQuestionCode.length == 1 && groupByQuestionCode[0].branchCount > 1

    // 各题均包含所有小题或两题组均只包含相同的一题
    if (!containAllBranches && !containOnlySameQuestion) {
      return false
    }

    // 显示题目解答题要求同一道原题下
    if (
      this.showQuestionContent &&
      [QuestionGroupTypeEnum.Essay.id, QuestionGroupTypeEnum.ContentObjectiveNoOption.id].includes(theGroup.type)
    ) {
      if (
        groupByQuestionCode.some(
          ({ originalQuestionId }) => originalQuestionId != groupByQuestionCode[0].originalQuestionId
        )
      ) {
        return false
      }
    }

    // 先阅后扫解答题合并为选做
    if (this.mode == ModeEnum.PostScan.id) {
      if (theGroup.type == QuestionGroupTypeEnum.Essay.id) {
        if (!containAllBranches || !theGroup.selectGroupName) {
          return false
        }
      }
    }
    return true
  }
  // 是否可设为选做题
  getCanSetSelect(questionGroupId) {
    // 由试卷结构创建且不可增删题目，则不允许合并拆分
    if (this.from == SourceTypeEnum.Exam.id && !this.enableEditQuestion) {
      return false
    }
    // 显示题目内容不支持选做
    if (this.showQuestionContent) {
      return false
    }
    // 先阅后扫无须手动设置选做
    if (this.mode == ModeEnum.PostScan.id) {
      return false
    }
    let questionGroup = this.findQuestionGroupById(questionGroupId)
    if (!questionGroup) {
      return false
    }
    if (questionGroup.selectGroupName) {
      return false
    }
    // 必须是解答题
    if (questionGroup.type != QuestionGroupTypeEnum.Essay.id) {
      return false
    }
    // 题号连续
    let groupByQuestionCode = groupArray(questionGroup.questions, q => q.questionCode).map(g => ({
      questionCode: g.key,
      branchCount: g.group.length,
    }))
    if (groupByQuestionCode.length < 2) {
      return false
    }
    if (
      groupByQuestionCode.some(
        ({ questionCode }, idx) => idx > 0 && questionCode != groupByQuestionCode[idx - 1].questionCode + 1
      )
    ) {
      return false
    }
    // 包含所有小题
    let questionCodeBranchCountMap = this.paperStructure.getQuestionCodeBranchCountMap()
    if (
      groupByQuestionCode.some(
        ({ questionCode, branchCount }) => branchCount != questionCodeBranchCountMap.get(questionCode)
      )
    ) {
      return false
    }

    // 各题分数相同由页面检查提示
    return true
  }
  // 是否可设置为智能评分
  getCanSetAiScore(questionGroupId) {
    // 目前仅支持小学数学口算
    if (this.stage.id != 1 || this.subject.id != 2 || !this.showQuestionContent || this.mode != ModeEnum.Online.id) {
      return false
    }
    let questionGroup = this.findQuestionGroupById(questionGroupId)
    if (!questionGroup) {
      return false
    }
    return questionGroup.type == QuestionGroupTypeEnum.FillBlank.id
  }
  // 是否有智能评分题目
  hasAiScoreQuestion() {
    return this.topics.some(t => t.questionGroups.some(g => g.aiScoreTypeId))
  }
  // 客观题大题说明是否显示大题名称
  objectiveTopicDescriptionShowTopicName() {
    // 客观题分大题排
    if (this.objectivesSeparate) {
      return true
    }
    // 只有一个客观题大题
    let objectiveQuestions = this.paperStructure.objectiveQuestions
    return objectiveQuestions.every(q => q.topicCode == objectiveQuestions[0].topicCode)
  }
  // 主观题大题说明显示对错打分说明
  showScoreTypeTrueFalseTip(topicCode) {
    let topic = this.topics.find(t => t.code == topicCode)
    if (!topic) {
      return false
    }
    // 先阅后扫
    if (this.mode != ModeEnum.PostScan.id) {
      return false
    }
    // 大题内有对错打分
    let show = topic.questionGroups.some(
      g =>
        [
          QuestionGroupTypeEnum.FillBlank.id,
          QuestionGroupTypeEnum.Essay.id,
          QuestionGroupTypeEnum.ChineseWriting.id,
          QuestionGroupTypeEnum.EnglishWriting.id,
        ].includes(g.type) && g.scoreType.name == ScoreTypeEnum.TrueFalse.id
    )
    return {
      show,
      emptyDefault: this.scoreTypeTrueFalseEmptyDefault,
    }
  }

  /**
   * setters ---------------------------------------------------------------------------
   */
  changeId(id) {
    this.id = id
  }
  changeTitle(title) {
    // 标题中换行改为空格
    this.name = title.replace(/\n/g, ' ')
    this.header.changeTitle(title)
  }
  changeFrom(from) {
    if (SourceTypeEnum.hasId(from)) {
      this.from = from
    }
  }
  changeStage(stage) {
    this.stage = stage
  }
  changeSubject(subject) {
    this.subject = subject
  }
  changePaperId(paperId) {
    this.paperId = paperId
  }
  changePaper(paper) {
    this.paper = paper
  }
  changeExamSubjectId(examSubjectId) {
    this.examSubjectId = examSubjectId
  }
  changePaperStructure({ objectiveQuestions, subjectiveQuestions, blocks, paper }) {
    this.paperStructure = new PaperStructure({ objectiveQuestions, subjectiveQuestions, blocks, paper })
  }
  changeEnableEditQuestion(value) {
    this.enableEditQuestion = Boolean(value)
  }
  changeObjectivesSeparate(value) {
    this.objectivesSeparate = Boolean(value)
  }
  changeShowQuestionContent(value) {
    this.showQuestionContent = Boolean(value)
  }
  changeMode(mode) {
    if (ModeEnum.hasId(mode)) {
      this.mode = mode
    }
  }
  changeScoreType(scoreType) {
    if (ScoreTypeEnum.hasId(scoreType)) {
      this.scoreType = scoreType
    }
  }
  changeSize(sizeId) {
    let size = PageSizeEnum.getEntryById(sizeId)
    if (size) {
      this.size = size
    }
  }
  changeBaseStyle(baseStyle) {
    let newStyle = { ...this.baseStyle }
    Object.keys(newStyle).forEach(key => {
      if (key in baseStyle) {
        newStyle[key] = baseStyle[key]
      }
    })
    this.baseStyle = newStyle
  }
  changePageFooterContent(value) {
    this.pageFooterContent = value
  }
  changePageFooterContentCoachBook(startPage, chapterName) {
    if (!(startPage > 1)) {
      startPage = 1
    }
    let content = `第 {{page+${startPage - 1}}} 页    ${chapterName || ''}`
    this.changePageFooterContent(content)
  }
  extractStartPageFromPageFooter() {
    if (this.pageFooterContent) {
      let match = this.pageFooterContent.match(/{{\s*page\s*\+\s*(\d+)\s*}}/)
      if (match) {
        return Number(match[1]) + 1
      }
    }
    return null
  }
  // 改大题
  changeTopics(topics) {
    this.topics = topics
  }
  // 改页面
  changePages(pages) {
    this.pages = pages
  }

  /**
   * 创建 ---------------------------------------------------------------------------
   */
  // 导入
  static async import({ sheet, objectiveQuestions, subjectiveQuestions, blocks, paper, enableEditQuestion, from }) {
    let s = new Sheet()
    // 兼容旧版
    if (sheet.version !== s.version) {
      sheet.topics.forEach(t => {
        // 大题说明记在Topic中
        if (!t.descriptionContent && !t.descriptionHeight && t.description) {
          t.descriptionContent = t.description.content
          t.descriptionHeight = t.description.height
        }
        t.questionGroups.forEach(g => {
          // 各题平铺
          let hasBranch = g.questions.some(q => q.branches)
          if (hasBranch) {
            let questions = []
            g.questions.forEach(q => {
              // 判断题类型记在题组中
              if (
                q.trueOrFalseType &&
                q.branchTypeId == BranchTypeEnum.TrueOrFalse.id &&
                g.type == QuestionGroupTypeEnum.Objective.id
              ) {
                g.trueOrFalseType = q.trueOrFalseType
              }
              q.branches.forEach(b => {
                let questionCode = q.code
                let branchCode = q.branchCount > 1 ? b.code : 0
                questions.push({
                  questionCode,
                  branchCode,
                })
                // 填空个数记在主观题中
                if (b.blankCount > 0 && g.type == QuestionGroupTypeEnum.FillBlank.id) {
                  let subj = subjectiveQuestions.find(x => x.questionCode == questionCode && x.branchCode == branchCode)
                  if (subj && subj.blankScores.length != b.blankCount) {
                    subj.blankScores = splitBlankScores(subj.fullScore, b.blankCount)
                  }
                }
              })
            })
            g.questions = questions
          }
        })
      })
      if (!sheet.scoreType) {
        sheet.scoreType = ScoreTypeEnum.Number.id
      }
    }

    s.changeId(sheet.id || '')
    s.changeTitle(sheet.name || '')

    if (SourceTypeEnum.hasId(from)) {
      s.changeFrom(from)
    } else {
      throw new Error('答题卡来源错误')
    }

    s.changeStage(sheet.stage || null)
    s.changeSubject(sheet.subject || null)
    s.changePaperId(sheet.paperId || '')
    s.changePaper(paper || null)
    s.changeExamSubjectId(sheet.examSubjectId || '')

    s.changePaperStructure({ objectiveQuestions, subjectiveQuestions, blocks, paper })
    s.changeEnableEditQuestion(enableEditQuestion)
    s.changeObjectivesSeparate(sheet.objectivesSeparate || false)
    s.changeShowQuestionContent(sheet.showQuestionContent)

    if (ModeEnum.hasId(sheet.mode)) {
      s.changeMode(sheet.mode)
    } else {
      throw new Error('答题卡模式错误')
    }
    s.changeScoreType(sheet.scoreType)

    let size = PageSizeEnum.getEntryById((sheet.size && (sheet.size.id || sheet.size.name)) || '')
    if (size) {
      s.changeSize(size.id)
    } else {
      throw new Error('答题卡尺寸错误')
    }

    if (sheet.baseStyle) {
      s.changeBaseStyle(sheet.baseStyle)
    }

    if (!sheet.header) {
      throw new Error('无答题卡头部')
    } else {
      s.header = new Header(sheet.header)
    }

    if (sheet.pageFooterContent) {
      s.pageFooterContent = sheet.pageFooterContent
    }

    let structureQuestions = s.paperStructure.getQuestions()

    await renderTopicsContentMath(sheet.topics, s.baseStyle)

    s.topics = (sheet.topics || []).map(t => new Topic(t, structureQuestions, s.subject.name))

    if (s.showQuestionContent && s.paper) {
      await preProcessPaper(s.paper, s.baseStyle)
    }

    return s
  }
  // 由组卷试卷创建
  static createFromPaper({ newId, paper, title }) {
    let sheet = new Sheet()
    sheet.changeId(newId)
    sheet.header.changeQrcodeText(newId)
    sheet.changeTitle(title || paper.paperInfo.name + '答题卡')
    sheet.changeFrom(SourceTypeEnum.Paper.id)
    sheet.changeStage(paper.paperInfo.stage)
    sheet.changeSubject(paper.paperInfo.subject)
    sheet.changePaperId(paper.paperInfo.id)
    sheet.changePaper(paper)
    sheet.changeEnableEditQuestion(false)
    sheet.changePaperStructure(PaperStructure.extractFromPaper(paper, false))
    sheet.changeTopics(
      Topic.generateTopicsFromPaperStructure(sheet.paperStructure, sheet.subject.name, sheet.stage.name)
    )
    sheet.generateAllQuestionGroupContent()
    return sheet
  }
  // 由试卷结构创建
  static createFromPaperStructure({
    newId,
    examName,
    gradeId,
    examSubjectId,
    subjectId,
    subjectName,
    mode,
    objectiveQuestions,
    subjectiveQuestions,
    blocks,
    paper,
  }) {
    let sheet = new Sheet()
    sheet.changeId(newId)
    sheet.header.changeQrcodeText(newId)
    sheet.changeTitle(examName + subjectName + '答题卡')
    sheet.changeFrom(SourceTypeEnum.Exam.id)
    sheet.changeStage(
      gradeId < 7
        ? {
            id: 1,
            name: '小学',
          }
        : gradeId < 10
          ? {
              id: 2,
              name: '初中',
            }
          : {
              id: 4,
              name: '高中',
            }
    )
    sheet.changeSubject({
      id: subjectId,
      name: subjectName,
    })
    sheet.changePaperId((paper && paper.paperInfo.id) || '')
    sheet.changePaper(paper || null)
    sheet.changeExamSubjectId(examSubjectId)
    sheet.changeMode(mode)
    sheet.changePaperStructure({ objectiveQuestions, subjectiveQuestions, blocks, paper })
    sheet.changeTopics(
      Topic.generateTopicsFromPaperStructure(sheet.paperStructure, sheet.subject.name, sheet.stage.name)
    )
    sheet.generateAllQuestionGroupContent()
    return sheet
  }
  // 手动创建
  static createFromManual({ newId }) {
    let sheet = new Sheet()
    sheet.changeId(newId)
    sheet.header.changeQrcodeText(newId)
    sheet.changeFrom(SourceTypeEnum.Manual.id)
    return sheet
  }

  // 设置教辅答题卡样式
  initCoachBookStyle(coachBookInfo) {
    this.changeStage({
      id: coachBookInfo.stageId,
      name: coachBookInfo.stageName,
    })
    this.changeSubject({
      id: coachBookInfo.subjectId,
      name: coachBookInfo.subjectName,
    })

    this.header.changeShowPageHeader(true)
    this.header.changeTitleAlignLeft(true)
    this.header.changeShowInstruction(true)
    this.header.changeQrcodeText(this.id)
    this.header.changeShowQrcode(true)
    this.header.changeTicketNumberType(TicketNumberTypeEnum.Handwriting.id)
    this.header.changeTicketNumberLength(6)
    this.header.changeTicketNumberOmrHorizontal(true)
    this.header.changeStuInfoLabels(['姓名'])
    this.header.changeStuInfoLabelAlias({ 准考号: '考号' })
    this.header.changeShowCautions(false)
    this.header.changeShowMissingMark(false)

    this.changeModeToPostScan()
    this.changeScoreType(ScoreTypeEnum.Bar.id)
    this.scoreTypeTrueFalseEmptyDefault = true
  }

  // 设置同级生教辅答题卡样式
  initCoachBookStyleIgrade(coachBookInfo) {
    this.changeStage({
      id: coachBookInfo.stageId,
      name: coachBookInfo.stageName,
    })
    this.changeSubject({
      id: coachBookInfo.subjectId,
      name: coachBookInfo.subjectName,
    })

    this.header.changeShowPageHeader(false)
    this.header.changeQrcodeText(this.id)
    this.header.changeShowQrcode(true)
    this.header.changeShowInstruction(false)
    this.header.changeTicketNumberType(TicketNumberTypeEnum.Handwriting.id)
    this.header.changeTicketNumberLength(10)
    this.header.changeTicketNumberOmrHorizontal(true)
    this.header.changeStuInfoLabels(['姓名', '班级'])
    this.header.changeShowCautions(false)
    this.header.changeShowMissingMark(false)

    if (this.mode != ModeEnum.PostScan.id) {
      this.changeModeToPostScan()
    }
    this.changeScoreType(ScoreTypeEnum.Bar.id)
    this.scoreTypeTrueFalseEmptyDefault = true
  }

  generateAllQuestionGroupContent() {
    this.topics.forEach(topic => {
      topic.questionGroups.forEach(g => {
        this.generateQuestionGroupContent(g)
      })
    })
  }
  generateQuestionGroupContent(g) {
    let containerWidth = this.getPageContentWidth() - 20 - 2
    g.generateContent({
      containerWidth,
      baseStyle: this.baseStyle,
      mode: this.mode,
      showQuestionContent: this.showQuestionContent,
    })
  }

  /**
   * 生成页面 ---------------------------------------------------------------------------
   */
  generatePages() {
    // 清空现有页面
    this.pages = []
    // 添加一页
    this.addPage()
    // 每个大题分别添加页面块
    this.topics.forEach(topic => {
      // 重置新页面块数组，新建的block添加到此数组中
      topic.questionGroups.forEach(g => {
        g.newBlocks = []
      })
      this.createTopicPageBlocks(topic)
      // 新块创建完成后，复制到blocks数组中
      topic.questionGroups.forEach(g => {
        g.blocks = g.newBlocks
        g.newBlocks = []
      })
    })
    // 补页，使其填满每面
    let { sideColumnCount, sideColumnIndex } = this.getPageSize(this.pages.length - 1)
    for (let i = sideColumnIndex + 1; i < sideColumnCount; i++) {
      this.addPage()
    }
  }
  // 添加页面
  addPage() {
    let newPage = new Page()
    newPage.pageIndex = this.pages.length
    if (this.isPageShowExamInfo(newPage.pageIndex)) {
      let examInfoBlock = PageBlock.createExamInfoBlock(this.header.height)
      newPage.addBlock(examInfoBlock)
    }
    this.pages.push(newPage)
    return newPage
  }
  // 页面是否显示考试信息
  isPageShowExamInfo(pageIndex) {
    let { isFrontSide, sideColumnIndex } = this.getPageSize(pageIndex)
    return isFrontSide && sideColumnIndex == 0
  }
  // 获取最后一个页面
  getLastPage() {
    return this.pages[this.pages.length - 1]
  }
  // 获取最后一个页面剩余内容高度
  getLastPageAvailableHeight() {
    let lastPage = this.getLastPage()
    if (!lastPage) {
      return 0
    }
    return this.getPageContentHeight() - lastPage.top
  }
  // 获取之后的第几页剩余内容高度
  getNextPageAvailableHeight(idx) {
    if (idx == 0) {
      return this.getLastPageAvailableHeight()
    }
    let pageIndex = this.pages.length - 1 + idx
    let availableHeight = this.getPageContentHeight()
    if (this.isPageShowExamInfo(pageIndex)) {
      availableHeight -= this.header.height
    }
    return availableHeight
  }
  // 检查最后一个页面是否还能容纳某高度的页面块
  checkLastPageHeightAvailable(height) {
    return this.getLastPageAvailableHeight() >= height
  }
  // 最后一个页面是否无内容
  isLastPageEmpty() {
    let lastPage = this.getLastPage()
    if (!lastPage) {
      return false
    }
    return (
      lastPage.blocks.length == 0 ||
      (lastPage.blocks.length == 1 && lastPage.blocks[0].type == PageBlockTypeEnum.ExamInfo.id)
    )
  }
  // 找页面块
  findBlockById(blockId) {
    for (let page of this.pages) {
      for (let block of page.blocks) {
        if (block.id == blockId) {
          return block
        }
      }
    }
    return null
  }
  // 大题生成页面块
  createTopicPageBlocks(topic) {
    // 若不能容纳大题说明块，则新建一页重试
    if (!this.checkLastPageHeightAvailable(topic.descriptionHeight) && !this.isLastPageEmpty()) {
      this.addPage()
    }

    // 大题说明块
    let descriptionBlock = PageBlock.createDescriptionBlock(topic)
    this.getLastPage().addBlock(descriptionBlock)

    // 每个题组分别创建题目块，高度够则直接添加，高度不够则需分拆
    topic.questionGroups.forEach(g => {
      // 不显示材料
      if (g.type == QuestionGroupTypeEnum.Trunk.id && topic.hideTrunkQuestionGroup) {
        return
      }
      if (this.checkLastPageHeightAvailable(g.height)) {
        let questionBlock = PageBlock.createQuestionBlock(topic, g)
        this.getLastPage().addBlock(questionBlock)
        return
      }
      let firstBlock = g.blocks[0]
      let instance = firstBlock && firstBlock.instance

      // 获取不到vue实例或vue实例的拆分方法，则新建一页
      if (!instance || !instance.split) {
        this.addPage()
        let questionBlock = PageBlock.createQuestionBlock(topic, g)
        this.getLastPage().addBlock(questionBlock)
        return
      }

      // 获取拆分结果
      let splitResult = instance.split(this.getNextPageAvailableHeight.bind(this))
      // 整理成题块
      let part = 0
      let height = 0
      let contentHeight = 0
      splitResult.forEach((item, idx) => {
        if (idx > 0) {
          this.addPage()
        }
        if (!item) {
          return
        }
        let questionBlock = PageBlock.createQuestionBlock(topic, g)
        questionBlock.part = part++
        Object.keys(item).forEach(key => {
          questionBlock[key] = item[key]
        })
        this.getLastPage().addBlock(questionBlock)
        height += item.height
        contentHeight += item.contentHeight || 0
      })
      // 更新题组高度
      g.height = height
      g.contentHeight = contentHeight
    })
  }

  /**
   * 页面块内容、高度、实例 ---------------------------------------------------------------------------
   */
  // 设置页头实例
  changePageHeaderInstance(pageIndex, instance) {
    let page = this.pages[pageIndex]
    if (page) {
      page.header = instance
    }
  }
  // 设置页脚实例
  changePageFooterInstance(pageIndex, instance) {
    let page = this.pages[pageIndex]
    if (page) {
      page.footer = instance
    }
  }
  // 设置页面块实例
  changePageBlockInstance(blockId, instance) {
    let block = this.findBlockById(blockId)
    if (block) {
      block.instance = instance
    }
  }
  // 设置页面块内容
  changePageBlockContent(blockId, content) {
    let block = this.findBlockById(blockId)
    if (!block) {
      return
    }
    block.content = content
    if (block.type == PageBlockTypeEnum.Description.id) {
      block.topic.descriptionContent = content
    } else {
      block.questionGroup.content = block.questionGroup.blocks.map(x => x.content).join('')
    }
  }
  // 设置页面块选项内容
  changePageBlockOptionContent(blockId, label, content) {
    let block = this.findBlockById(blockId)
    if (!block) {
      return
    }
    if (block.type != PageBlockTypeEnum.ContentObjective.id) {
      return
    }
    let opt = block.questionGroup.options.find(x => x.label == label)
    if (opt) {
      opt.content = content
    }
  }
  // 设置页面块高度
  changePageBlockHeight(blockId, height, contentHeight) {
    let block = this.findBlockById(blockId)
    if (!block) {
      return
    }
    block.height = height
    if (contentHeight != null) {
      block.contentHeight = contentHeight
    }
    if (block.type == PageBlockTypeEnum.ExamInfo.id) {
      this.header.changeHeight(height, block.pageIndex)
    } else if (block.type == PageBlockTypeEnum.Description.id) {
      block.topic.descriptionHeight = height
    } else {
      block.questionGroup.height = sumByDefaultZero(block.questionGroup.blocks, x => x.height)
      block.questionGroup.contentHeight = sumByDefaultZero(block.questionGroup.blocks, x => x.contentHeight)
    }
  }

  /**
   * 操作 ---------------------------------------------------------------------------
   */
  // 添加客观题
  addObjectiveQuestions({ topicCode, topicName, objectiveQuestions }) {
    // 向paperStructure中添加题目
    this.paperStructure.addObjectiveQuestions(objectiveQuestions)

    if (!this.objectivesSeparate) {
      topicCode = 0
      topicName = '客观题'
    }
    let topic = this.topics.find(t => t.code == topicCode)
    if (!topic) {
      topic = Topic.create(topicCode, topicName, [])
      this.insertTopic(topic)
    }
    topic.addObjectiveQuestions({
      objectiveQuestions,
      objectivesSeparate: this.objectivesSeparate,
      subjectName: this.subject.name,
    })
  }
  // 添加填空题
  addFillBlankQuestions({ topicCode, topicName, blanksPerRow, subjectiveQuestions }) {
    this.addSubjectiveQuestionGroup({
      topicCode,
      topicName,
      subjectiveQuestions,
      questionGroupType: QuestionGroupTypeEnum.FillBlank.id,
      blanksPerRow,
    })
  }
  // 添加解答题
  addEssayQuestions({ topicCode, topicName, subjectiveQuestions, rows, showLine }) {
    let subjectiveQuestionGroups = []
    // 创建新题组，先阅后扫每小题一组，线上阅卷每题一组
    if (this.mode == ModeEnum.PostScan.id) {
      subjectiveQuestionGroups = subjectiveQuestions.map(subj => [subj])
    } else {
      subjectiveQuestionGroups = groupArray(subjectiveQuestions, subj => subj.questionCode).map(g => g.group)
    }
    subjectiveQuestionGroups.forEach(subjs => {
      this.addSubjectiveQuestionGroup({
        topicCode,
        topicName,
        subjectiveQuestions: subjs,
        questionGroupType: QuestionGroupTypeEnum.Essay.id,
        rows,
        showLine,
      })
    })
  }
  // 添加语文作文题
  addChineseWritingQuestion({ topicCode, topicName, subjectiveQuestion, words }) {
    this.addSubjectiveQuestionGroup({
      topicCode,
      topicName,
      subjectiveQuestions: [subjectiveQuestion],
      questionGroupType: QuestionGroupTypeEnum.ChineseWriting.id,
      words,
    })
  }
  // 添加英语作文题
  addEnglishWritingQuestion({ topicCode, topicName, subjectiveQuestion, rows }) {
    this.addSubjectiveQuestionGroup({
      topicCode,
      topicName,
      subjectiveQuestions: [subjectiveQuestion],
      questionGroupType: QuestionGroupTypeEnum.EnglishWriting.id,
      rows,
    })
  }
  // 添加主观题题组
  addSubjectiveQuestionGroup({
    topicCode,
    topicName,
    subjectiveQuestions,
    questionGroupType,
    blanksPerRow,
    rows,
    showLine,
    words,
  }) {
    // 向paperStructure中添加题目
    this.paperStructure.addSubjectiveQuestions(subjectiveQuestions)
    // 创建新题组
    let newQuestionGroup = QuestionGroup.createSubjectiveQuestionGroup({
      subjectiveQuestions,
      questionGroupType,
      blanksPerRow,
      rows,
      showLine,
      words,
      subjectName: this.subject.name,
      stageName: this.stage.name,
    })

    // 设置打分类型
    if (questionGroupType == QuestionGroupTypeEnum.FillBlank.id) {
      newQuestionGroup.changeScoreType({
        name: ScoreTypeEnum.TrueFalse.id,
      })
    } else {
      newQuestionGroup.changeScoreType({
        name: this.scoreType,
      })
    }

    this.insertQuestionGroup(topicCode, topicName, newQuestionGroup)

    this.generateQuestionGroupContent(newQuestionGroup)
    return newQuestionGroup
  }
  // 插入新题组
  insertQuestionGroup(topicCode, topicName, newQuestionGroup) {
    let topic = this.topics.find(t => t.code == topicCode)
    if (topic) {
      topic.insertQuestionGroup(newQuestionGroup)
    } else {
      topic = Topic.create(topicCode, topicName, [newQuestionGroup])
      this.insertTopic(topic)
    }
  }
  // 插入大题
  insertTopic(topic) {
    let insertTopicPosition = this.topics.findIndex(t => t.code >= topic.code)
    if (insertTopicPosition < 0) {
      insertTopicPosition = this.topics.length
    }
    this.topics.splice(insertTopicPosition, 0, topic)
  }
  // 找题组
  findQuestionGroupById(questionGroupId) {
    for (let topic of this.topics) {
      for (let questionGroup of topic.questionGroups) {
        if (questionGroup.id == questionGroupId) {
          return questionGroup
        }
      }
    }
    return null
  }
  // 删除题组
  deleteQuestionGroup(questionGroupId, questionCodes) {
    // 找题组内的题号
    let questionGroup = this.findQuestionGroupById(questionGroupId)
    if (!questionGroup) {
      return
    }
    if (!questionCodes) {
      questionCodes = questionGroup.questions.map(q => q.questionCode)
    }
    // 删包含这些题号的题组
    let deleteQuestionIds = []
    for (let tIdx = this.topics.length - 1; tIdx >= 0; tIdx--) {
      let topic = this.topics[tIdx]
      for (let gIdx = topic.questionGroups.length - 1; gIdx >= 0; gIdx--) {
        let g = topic.questionGroups[gIdx]
        let questionDeleted = false
        for (let qIdx = g.questions.length - 1; qIdx >= 0; qIdx--) {
          let q = g.questions[qIdx]
          if (questionCodes.includes(q.questionCode)) {
            deleteQuestionIds.push(q.questionId)
            questionDeleted = true
            g.questions.splice(qIdx, 1)
          }
        }
        if (g.questions.length == 0) {
          topic.questionGroups.splice(gIdx, 1)
        } else if (questionDeleted) {
          // 先阅后扫填空题题组题目有删除的，要重新生成打分框
          if (g.type == QuestionGroupTypeEnum.FillBlank.id && this.mode == ModeEnum.PostScan.id) {
            this.generateQuestionGroupContent(g)
          }
        }
      }
      if (topic.questionGroups.length == 0) {
        this.topics.splice(tIdx, 1)
      }
    }

    // 删题
    this.paperStructure.deleteQuestions(deleteQuestionIds)
  }
  // 合并题组
  mergeQuestionGroup(questionGroupId) {
    // 找本题组与上个题组
    let topic = null
    let theGroup = null
    let preGroup = null
    let preGroupIndex = 0
    for (let t of this.topics) {
      for (let gIdx = 0; gIdx < t.questionGroups.length; gIdx++) {
        if (t.questionGroups[gIdx].id == questionGroupId) {
          topic = t
          theGroup = t.questionGroups[gIdx]
          preGroup = t.questionGroups[gIdx - 1]
          preGroupIndex = gIdx - 1
          break
        }
      }
    }
    if (!theGroup || !preGroup) {
      return
    }
    let isContentObjectiveNoOption = theGroup.type == QuestionGroupTypeEnum.ContentObjectiveNoOption.id
    // 找对应评卷题块
    let theBlock = this.paperStructure.getBlockByQuestionId(theGroup.questions[0].questionId)
    let preBlock = this.paperStructure.getBlockByQuestionId(preGroup.questions[0].questionId)
    if (!isContentObjectiveNoOption && (!theBlock || !preBlock || theBlock == preBlock)) {
      return
    }
    // 创建新题组，在上个题组的基础上添加内容、题目
    let newGroup = preGroup.cloneAsNoQuestion()
    newGroup.questions = [...preGroup.questions, ...theGroup.questions].sort(sortQuestionFunction)
    // 填空题内容重新生成；解答题内容拼接
    if (newGroup.type == QuestionGroupTypeEnum.FillBlank.id) {
      this.generateQuestionGroupContent(newGroup)
    } else {
      newGroup.content = preGroup.content + theGroup.content
    }
    // 替换题组
    topic.questionGroups.splice(preGroupIndex, 2, newGroup)
    // 先阅后扫解答题设为选做题（选做个数只能为1）
    if (this.mode == ModeEnum.PostScan.id && theGroup.type == QuestionGroupTypeEnum.Essay.id) {
      let questionCodes = newGroup.questions.map(q => q.questionCode)
      let selectGroupName = questionCodes.join('_')
      let selectCount = 1
      newGroup.setSelect(selectGroupName, selectCount)
      this.paperStructure.setSelect(questionCodes, selectGroupName, selectCount)
    }
    // 线上阅卷：合并评卷题块
    else if (!isContentObjectiveNoOption) {
      this.paperStructure.mergeBlock(preBlock.blockId, theBlock.blockId)
    }
  }
  // 拆分题组
  splitQuestionGroup(questionGroupId) {
    // 找本题组
    let topic = null
    let theGroup = null
    let theGroupIndex = 0
    for (let t of this.topics) {
      for (let gIdx = 0; gIdx < t.questionGroups.length; gIdx++) {
        if (t.questionGroups[gIdx].id == questionGroupId) {
          topic = t
          theGroup = t.questionGroups[gIdx]
          theGroupIndex = gIdx
          break
        }
      }
    }
    if (!theGroup) {
      return
    }
    let isContentObjectiveNoOption = theGroup.type == QuestionGroupTypeEnum.ContentObjectiveNoOption.id

    // 取消选做
    let selectGroupName = theGroup.selectGroupName
    theGroup.unsetSelect()
    // 找对应评卷题块
    let theBlock = this.paperStructure.getBlockByQuestionId(theGroup.questions[0].questionId)
    if (!isContentObjectiveNoOption && !theBlock) {
      return
    }
    // 填空题、无内容客观题内容重新生成；解答题内容拼接
    let newContents = []
    // 若只有1题，则按小题拆分
    let groupByQuestionCode = groupArray(theGroup.questions, q => q.questionCode).map(g => g.group)
    let splitIntoBranches = groupByQuestionCode.length == 1
    let questionsList = []
    if (splitIntoBranches) {
      questionsList = theGroup.questions.map(q => [q])
      if (
        ![QuestionGroupTypeEnum.FillBlank.id, QuestionGroupTypeEnum.ContentObjectiveNoOption.id].includes(theGroup.type)
      ) {
        let separators = theGroup.questions.slice(1).map(q => `（${q.branchCode}）`)
        newContents = splitContent(theGroup.content, separators)
      }
    } else {
      questionsList = groupByQuestionCode
      if (
        ![QuestionGroupTypeEnum.FillBlank.id, QuestionGroupTypeEnum.ContentObjectiveNoOption.id].includes(theGroup.type)
      ) {
        let separators = groupByQuestionCode.slice(1).map(questions => `${questions[0].questionCode}.`)
        newContents = splitContent(theGroup.content, separators)
      }
    }

    // 替换题组
    let newGroups = questionsList.map((questions, idx) => {
      let newGroup = theGroup.cloneAsNoQuestion()
      newGroup.content = newContents[idx] || ''
      newGroup.questions = questions
      if (!newGroup.content) {
        this.generateQuestionGroupContent(newGroup)
      }
      return newGroup
    })
    topic.questionGroups.splice(theGroupIndex, 1, ...newGroups)
    // 拆分评卷题块
    if (selectGroupName) {
      this.paperStructure.unsetSelect(selectGroupName)
    } else if (!isContentObjectiveNoOption) {
      this.paperStructure.splitBlock(theBlock.blockId, splitIntoBranches)
    }
  }
  // 改题组类型
  changeQuestionGroupType({
    questionGroupId,
    type,
    groupLength,
    trueOrFalseType,
    words,
    rows,
    showLine,
    branches,
    blanksPerRow,
  }) {
    let questionGroup = this.findQuestionGroupById(questionGroupId)
    if (questionGroup != null) {
      let needResetContent = questionGroup.changeType({
        type,
        groupLength,
        trueOrFalseType,
        words,
        rows,
        showLine,
        branches,
        blanksPerRow,
      })
      if (this.mode == ModeEnum.PostScan.id) {
        questionGroup.changeScoreType({
          name: this.scoreType,
        })
      }
      if (needResetContent) {
        this.generateQuestionGroupContent(questionGroup)
      }
    }
  }
  // 重设题组填空题打分框
  setFillBlankQuestionGroupScoreBox({ questionGroupId, showScoreBox }) {
    let questionGroup = this.findQuestionGroupById(questionGroupId)
    if (questionGroup != null) {
      questionGroup.setFillBlankScoreBox({ showScoreBox })
    }
  }
  // 客观题分大题
  splitObjectiveTopics({ groupLength, trueOrFalseType }) {
    if (this.objectivesSeparate) {
      return
    }
    this.changeObjectivesSeparate(true)

    // 删除所有客观题题组
    this.topics.forEach(topic => {
      topic.questionGroups = topic.questionGroups.filter(g => g.type != QuestionGroupTypeEnum.Objective.id)
    })
    this.topics = this.topics.filter(topic => topic.questionGroups.length > 0)

    if (this.showQuestionContent) {
      this.changeQuestionGroupContentObjectiveNoOption(false)
      return
    }

    // 分大题新增客观题题组
    groupArray(this.paperStructure.objectiveQuestions, q => q.topicCode).forEach(({ key, group }) => {
      let topicCode = key
      let objectiveQuestions = group
      let topicName = objectiveQuestions[0].topicName
      let topic = this.topics.find(t => t.code == topicCode)
      if (!topic) {
        topic = Topic.create(topicCode, topicName, [])
        this.insertTopic(topic)
      }
      topic.addObjectiveQuestions({
        objectiveQuestions,
        objectivesSeparate: this.objectivesSeparate,
        subjectName: this.subject.name,
        groupLength,
        trueOrFalseType,
      })
    })
  }
  // 客观题合并
  mergeObjectiveTopics({ groupLength, trueOrFalseType }) {
    if (!this.objectivesSeparate) {
      return
    }
    this.changeObjectivesSeparate(false)

    if (!this.showQuestionContent) {
      // 删除所有客观题题组
      this.topics.forEach(topic => {
        topic.questionGroups = topic.questionGroups.filter(g => g.type != QuestionGroupTypeEnum.Objective.id)
      })
      this.topics = this.topics.filter(topic => topic.questionGroups.length > 0)
    }

    // 新增客观题大题
    let newTopic = Topic.generateObjectiveTopicFromPaperStructure({
      objectiveQuestions: this.paperStructure.objectiveQuestions,
      topicCode: 0,
      subjectName: this.subject.name,
    })
    newTopic.questionGroups[0].groupLength = groupLength
    newTopic.questionGroups[0].trueOrFalseType = trueOrFalseType
    this.topics.unshift(newTopic)

    if (this.showQuestionContent) {
      this.changeQuestionGroupContentObjectiveNoOption(true)
    }
  }
  // 显示题目内容
  displayQuestionContent() {
    if (this.showQuestionContent) {
      return
    }
    if (!this.paper) {
      return
    }
    this.pages = []
    this.showQuestionContent = true
    this.changePaperStructure(PaperStructure.extractFromPaper(this.paper, true))
    this.changeTopics(Topic.generateQuestionContentTopicsFromPaperStructure(this.paperStructure, this.paper))
    if (this.mode == ModeEnum.PostScan.id) {
      this.changeModeToPostScan()
      this.changeAllScoreType(this.scoreType)
    }
    this.generateAllQuestionGroupContent()

    // 客观题填涂显示在顶部
    if (!this.objectivesSeparate) {
      this.changeObjectivesSeparate(true)
      this.mergeObjectiveTopics({
        groupLength: 5,
        trueOrFalseType: this.subject.name == '英语' ? TrueFalseTypeEnum.TF.id : TrueFalseTypeEnum.TC.id,
      })
    }
  }
  // 转换是否显示选项客观题题组
  changeQuestionGroupContentObjectiveNoOption(noOption) {
    this.topics.forEach(topic => {
      for (let idx = 0; idx < topic.questionGroups.length; idx++) {
        let g = topic.questionGroups[idx]
        if (noOption && g.type == QuestionGroupTypeEnum.ContentObjective.id) {
          // 无题干无需转换
          let stem = g.questions[0].originalBranch?.stem
          if (!stem) {
            return
          }
          // 全空白字符
          if (!stem.trim()) {
            return
          }
          // 只有括号
          stem = stem.replace(/&nbsp;/g, ' ')
          if (/^\s*(<p>)?\s*[(（]\s*[）)]\s*(<\/p>)?\s*$/.test(stem)) {
            return
          }
          // 无选项内容
          let optionsNoContent = g.options.every(opt => {
            if (!opt.content) {
              return true
            }
            let trimContent = opt.content.replace(/&nbsp;/g, '').trim()
            return trimContent.length == 0
          })
          if (optionsNoContent) {
            g.type = QuestionGroupTypeEnum.ContentObjectiveNoOption.id
          }
        } else if (!noOption && g.type == QuestionGroupTypeEnum.ContentObjectiveNoOption.id) {
          let newQuestionGroups = g.questions.map(q => QuestionGroup.createContentObjectiveQuestionGroup(q))
          topic.questionGroups.splice(idx, 1, ...newQuestionGroups)
          idx += newQuestionGroups.length - 1
        }
      }
    })
  }
  // 隐藏题目内容
  hideQuestionContent() {
    if (!this.showQuestionContent) {
      return
    }
    if (!this.paper) {
      return
    }
    this.pages = []
    this.showQuestionContent = false
    this.objectivesSeparate = false
    this.changePaperStructure(PaperStructure.extractFromPaper(this.paper, false))
    this.changeTopics(Topic.generateTopicsFromPaperStructure(this.paperStructure, this.subject.name, this.stage.name))
    if (this.mode == ModeEnum.PostScan.id) {
      this.changeModeToPostScan()
      this.changeAllScoreType(this.scoreType)
    }
    this.generateAllQuestionGroupContent()
  }
  // 切换显示材料
  toggleShowTrunkQuestionGroup(topicCode) {
    let topic = this.topics.find(t => t.code == topicCode)
    if (!topic) {
      return
    }
    topic.hideTrunkQuestionGroup = !topic.hideTrunkQuestionGroup
  }
  // 切换成先阅后扫模式
  changeModeToPostScan() {
    this.changeMode(ModeEnum.PostScan.id)

    // 改内容
    this.topics.forEach(topic => {
      let newQuestionGroups = []
      topic.questionGroups.forEach(g => {
        // 材料、客观题保持不变
        if (
          [
            QuestionGroupTypeEnum.Objective.id,
            QuestionGroupTypeEnum.ContentObjective.id,
            QuestionGroupTypeEnum.Trunk.id,
          ].includes(g.type)
        ) {
          newQuestionGroups.push(g)
          return
        }
        // 填空题重置内容
        if (g.type == QuestionGroupTypeEnum.FillBlank.id) {
          newQuestionGroups.push(g)
          g.setFillBlankScoreBox({ showScoreBox: true })
          // this.generateQuestionGroupContent(g)
          return
        }
        // 只包含一个小题的保持不变
        if (g.questions.length == 1) {
          newQuestionGroups.push(g)
          return
        }
        // 取消选做
        let selectGroupName = g.selectGroupName
        g.unsetSelect()
        // 按小题拆分成题组
        g.questions.forEach(q => {
          let newGroup = g.cloneAsNoQuestion()
          newGroup.questions = [q]
          newQuestionGroups.push(newGroup)
          this.generateQuestionGroupContent(newGroup)
        })
        // 对应评卷题块也拆分成一小题一块
        if (selectGroupName) {
          let blocks = this.paperStructure.getSelectGroupBlocks(selectGroupName)
          this.paperStructure.unsetSelect(selectGroupName)
          blocks.forEach(block => {
            this.paperStructure.splitBlock(block.blockId, true)
          })
        } else {
          let theBlock = this.paperStructure.getBlockByQuestionId(g.questions[0].questionId)
          this.paperStructure.splitBlock(theBlock.blockId, true)
        }
      })
      topic.questionGroups = newQuestionGroups
    })
  }
  // 切换成线上阅卷模式
  changeModeToOnline() {
    this.changeMode(ModeEnum.Online.id)
    this.changeAllScoreType(ScoreTypeEnum.Bar.id)

    this.topics.forEach(topic => {
      topic.questionGroups.forEach(g => {
        // 填空题重置内容
        if (g.type == QuestionGroupTypeEnum.FillBlank.id) {
          // this.generateQuestionGroupContent(g)
          g.setFillBlankScoreBox({ showScoreBox: false })
        }
      })
    })
  }
  // 改题组是否支持0.5分或半对错
  changeQuestionGroupScoreTypeHalfScore(questionGroupId, halfScore) {
    let questionGroup = this.findQuestionGroupById(questionGroupId)
    if (questionGroup != null) {
      questionGroup.changeScoreTypeHalfScore(halfScore)
    }
  }
  // 改所有题组的打分方式
  changeAllScoreType(scoreTypeId) {
    this.changeScoreType(scoreTypeId)
    this.topics.forEach(topic => {
      topic.questionGroups.forEach(g => {
        g.changeScoreType({
          name: this.scoreType,
        })
      })
    })
  }
  // 改题目分数
  changeQuestionScores(objectives, subjectives) {
    this.paperStructure.changeQuestionScores(objectives, subjectives)
  }
  // 设为选做题
  setQuestionGroupSelect(questionGroupId, selectCount) {
    let questionGroup = this.findQuestionGroupById(questionGroupId)
    if (!questionGroup) {
      return
    }
    let questionCodes = questionGroup.questions.map(q => q.questionCode).sort((a, b) => a - b)
    // 题组
    let selectGroupName = questionCodes.join('_')
    questionGroup.setSelect(selectGroupName, selectCount)
    // 题块
    let theBlock = this.paperStructure.getBlockByQuestionId(questionGroup.questions[0].questionId)
    this.paperStructure.splitBlock(theBlock.blockId, false)
    this.paperStructure.setSelect(questionCodes, questionGroup.selectGroupName, selectCount)
  }
  // 改选做说明
  changeSelectDescription(questionGroupId, desc) {
    let questionGroup = this.findQuestionGroupById(questionGroupId)
    if (questionGroup) {
      questionGroup.selectDescription = desc
    }
  }
  // 改智能评分
  changeAiScoreType(questionGroupId, aiScoreTypeId) {
    let questionGroup = this.findQuestionGroupById(questionGroupId)
    if (!questionGroup) {
      return
    }
    questionGroup.changeAiScoreType(aiScoreTypeId)
    this.generateQuestionGroupContent(questionGroup)
  }
  // 设置对错打分说明
  changeShowScoreTypeTrueFalseTip({ topicCode, show, emptyDefault }) {
    this.topics.forEach(topic => {
      if (topic.code == topicCode) {
        topic.changeShowScoreTypeTrueFalseTip({ show, emptyDefault })
      }
    })
  }
  // 设置客观题大题说明
  changeObjectiveTopicDescriptionShowTopicName({ topicCode, showTopicName }) {
    this.topics.forEach(topic => {
      if (topic.code == topicCode) {
        topic.changeObjectiveTopicDescriptionShowTopicName(showTopicName)
      }
    })
  }
  // 添加或移除选择题括号中三角形
  addOrRemoveContentObjectiveTriangle(action) {
    this.topics.forEach(topic => {
      topic.questionGroups.forEach(g => {
        g.addOrRemoveContentObjectiveTriangle(action)
      })
    })
  }

  /**
   * 导出 ---------------------------------------------------------------------------
   */
  // 导出答题卡
  exportSheet() {
    let sheetData = {
      version: this.version,
      id: this.id,
      name: this.name,
      stage: {
        id: this.stage.id,
        name: this.stage.name,
      },
      subject: {
        id: this.subject.id,
        name: this.subject.name,
      },
      paperId: this.paperId,
      examSubjectId: this.examSubjectId,
      objectivesSeparate: this.objectivesSeparate,
      showQuestionContent: this.showQuestionContent,
      mode: this.mode,
      scoreType: this.scoreType,
      scoreTypeTrueFalseEmptyDefault: this.scoreTypeTrueFalseEmptyDefault,
      size: deepCopy(this.size),
      baseStyle: this.baseStyle,
      pageFooterContent: this.pageFooterContent,
      header: this.header.export(),
      topics: this.topics.map(t => t.export()),
    }
    resetTopicsContentMath(sheetData.topics)
    // 智能评分题块
    let aiScoreBlocks = []
    this.topics.forEach(topic => {
      topic.questionGroups.forEach(g => {
        if (g.aiScoreTypeId) {
          let block = g.exportAiScoreBlock()
          if (block) {
            aiScoreBlocks.push(block)
          }
        }
      })
    })
    let { objectives, blocks } = this.paperStructure.export(aiScoreBlocks)
    return {
      sheetJson: JSON.stringify(sheetData),
      objectivesJson: JSON.stringify(objectives),
      blocksJson: JSON.stringify(blocks),
    }
  }
  // 导出扫描模板
  exportScanTemplate() {
    // 提取扫描模板页面
    let templateSides = []
    this.pages.forEach(page => {
      // 确定所属面及尺寸
      let { sideIndex, pageLeft, width, height, left, right, top, bottom } = this.getPageSize(page.pageIndex)
      // 导出扫描模板页面
      let scanTemplatePage = page.exportScanTemplatePage({ width, height, left, right, top, bottom }, pageLeft)
      if (!templateSides[sideIndex]) {
        templateSides[sideIndex] = []
      }
      templateSides[sideIndex].push(scanTemplatePage)
    })

    // 按面合并
    templateSides = templateSides.map((side, sideIndex) =>
      this.mergeTemplatePages(side, sideIndex + 1, templateSides.length)
    )

    // 只有一面则不需要页码标记
    if (templateSides.length == 1) {
      templateSides[0].pageParam.pageMark = null
    }

    // 提取模板题目信息
    let questions = this.getTemplateQuestions(templateSides, this.mode == ModeEnum.PostScan.id)

    // 创建扫描模板
    let template = new Template()
    template.isDoubleSide = templateSides.length > 1
    template.pages = templateSides
    template.questions = questions

    // 检查主观题区域Id重复、打分区域Id重复
    let areaIds = []
    template.questions.subjectives.forEach(subj => areaIds.push(...subj.areaIds))
    if (uniq(areaIds).length != areaIds.length) {
      throw '主观题区域Id重复'
    }
    let scoreAreaIds = []
    template.pages.forEach(page => {
      page.manualScoreAreas.forEach(area => scoreAreaIds.push(area.id))
      page.barScoreAreas.forEach(area => scoreAreaIds.push(area.id))
      page.trueFalseScoreAreas.forEach(area => scoreAreaIds.push(area.id))
    })
    if (uniq(scoreAreaIds).length != scoreAreaIds.length) {
      throw '打分区域Id重复'
    }

    return JSON.stringify(template)
  }
  // 将多栏合并成面
  mergeTemplatePages(sideTemplatePages, sideIndex, sideCount) {
    let side = new TemplatePage()
    side.pageIndex = sideIndex
    side.pageParam = new TemplatePageParam()
    side.pageParam.correctType = LocateModeEnum.Anchor.id
    side.pageParam.anchorMarks = [null, null, null, null]
    side.studentInfo = new TemplateStudentInfo()
    side.studentInfo.admissionType = AdmissionTypeEnum.None.id

    let markAreaIdSuffix = `-${side.pageIndex}`

    sideTemplatePages.forEach(page => {
      page.pageParam.anchorMarks.forEach((anchor, idx) => {
        if (anchor) {
          anchor.id += markAreaIdSuffix
          side.pageParam.anchorMarks[idx] = anchor
        }
      })
      if (page.pageParam.titleArea) {
        side.pageParam.titleArea = page.pageParam.titleArea
      }
      if (page.pageParam.lineArea) {
        side.pageParam.lineArea = page.pageParam.lineArea
      }
      if (page.pageParam.pageMark) {
        side.pageParam.pageMark = page.pageParam.pageMark
        side.pageParam.pageMark.id += markAreaIdSuffix
      }
      if (page.pageParam.qrcodeArea) {
        side.pageParam.qrcodeArea = page.pageParam.qrcodeArea
        side.pageParam.qrcodeText = page.pageParam.qrcodeText || ''
      }
      if (page.studentInfo.admissionOmrArea) {
        side.studentInfo.admissionType = AdmissionTypeEnum.Omr.id
        side.studentInfo.admissionOmrArea = page.studentInfo.admissionOmrArea
        side.studentInfo.admissionOmrArea.id += markAreaIdSuffix
      }
      if (page.studentInfo.admissionBarcodeArea) {
        side.studentInfo.admissionType = AdmissionTypeEnum.Barcode.id
        side.studentInfo.admissionBarcodeArea = page.studentInfo.admissionBarcodeArea
      }
      if (page.studentInfo.admissionHandwritingArea) {
        side.studentInfo.admissionType = AdmissionTypeEnum.Handwriting.id
        side.studentInfo.admissionHandwritingArea = page.studentInfo.admissionHandwritingArea
      }
      if (page.studentInfo.missingMarkArea) {
        side.studentInfo.missingMarkArea = page.studentInfo.missingMarkArea
        side.studentInfo.missingMarkArea.id += markAreaIdSuffix
      }
      side.objectiveAreas.push(...page.objectiveAreas)
      side.subjectiveAreas.push(...page.subjectiveAreas)
      side.selectMarkAreas.push(...page.selectMarkAreas)
      side.manualScoreAreas.push(...page.manualScoreAreas)
      side.barScoreAreas.push(...page.barScoreAreas)
      side.trueFalseScoreAreas.push(...page.trueFalseScoreAreas)
    })

    // 若无标题，则使用页码作为标题
    if (!side.pageParam.titleArea) {
      let { position } = this.generatePageNumber(sideIndex, sideCount)
      side.pageParam.titleArea = TemplateRect.createFromAnswerSheetPageRect(
        position.left,
        position.top,
        position.width,
        position.height
      )
    }

    return side
  }
  // 提取题目
  getTemplateQuestions(sides, checkScoreArea) {
    // 各面所有区域汇总
    let objectiveAreas = unGroupArray(sides, side => side.objectiveAreas)
    let subjectiveAreas = unGroupArray(sides, side => side.subjectiveAreas)
    let selectMarkAreas = unGroupArray(sides, side => side.selectMarkAreas)
    let manualScoreAreas = unGroupArray(sides, side => side.manualScoreAreas)
    let barScoreAreas = unGroupArray(sides, side => side.barScoreAreas)
    let trueFalseScoreAreas = unGroupArray(sides, side => side.trueFalseScoreAreas)

    // 客观题
    let objectives = objectiveAreas.map(area => {
      let objArea = new TemplateObjective()
      objArea.areaId = area.id
      objArea.questions = area._extra.questions.map(q => {
        let obj = new TemplateQuestionObjective()
        obj.questionCode = q.questionCode
        return obj
      })
      return objArea
    })

    // 题块
    let subjectives = []
    let noScoreAreaQuestions = []
    let duplicateScoreAreaQuestions = []
    this.paperStructure.blocks.forEach(block => {
      // 主观题区域包含题块中的题，则该区域属于此题块
      let blockSubjectiveAreas = subjectiveAreas.filter(area =>
        area._extra.questions.some(q =>
          block.questions.some(x => x.questionCode == q.questionCode && x.branchCode == q.branchCode)
        )
      )
      if (blockSubjectiveAreas.length == 0) {
        throw `题块【${block.blockName}】无主观题区域`
      }
      let subjArea = new TemplateSubjective()
      subjArea.subjectiveId = block.blockId
      subjArea.areaIds = blockSubjectiveAreas.map(area => area.id)

      // 选做标记
      if (block.selectGroupName) {
        let subjectiveSelectMark = selectMarkAreas.find(area =>
          block.questions.some(x => x.questionCode == area._extra.questionCode)
        )
        if (subjectiveSelectMark) {
          subjArea.selectMarkId = subjectiveSelectMark.id
        } else {
          throw `选做题块【${block.blockName}】无选做标记`
        }
      }

      subjArea.questions = block.questions.map(q => {
        let subj = new TemplateQuestionSubjective()
        subj.questionCode = q.questionCode
        subj.branchCode = q.branchCode
        // 找打分区域
        let manualScoreArea = manualScoreAreas.filter(
          area => area._extra.questionCode == subj.questionCode && area._extra.branchCode == subj.branchCode
        )
        let barScoreArea = barScoreAreas.filter(
          area => area._extra.questionCode == subj.questionCode && area._extra.branchCode == subj.branchCode
        )
        let trueFalseScoreArea = trueFalseScoreAreas.filter(
          area => area._extra.questionCode == subj.questionCode && area._extra.branchCode == subj.branchCode
        )
        if (manualScoreArea.length > 0) {
          if (manualScoreArea.length > 1) {
            duplicateScoreAreaQuestions.push(q)
          } else {
            subj.scoreType = ScoreTypeEnum.Number.id
            subj.scoreAreaIds = [manualScoreArea[0].id]
          }
        } else if (barScoreArea.length > 0) {
          if (barScoreArea.length > 1) {
            duplicateScoreAreaQuestions.push(q)
          } else {
            subj.scoreType = ScoreTypeEnum.Bar.id
            subj.scoreAreaIds = [barScoreArea[0].id]
          }
        } else if (trueFalseScoreArea.length > 0) {
          if (trueFalseScoreArea.length > q.blankScores.length) {
            duplicateScoreAreaQuestions.push(q)
          } else {
            let scoreAreaIds = []
            let valid = true
            for (let i = 0; i < q.blankScores.length; i++) {
              let blankScoreAreas = trueFalseScoreArea.filter(x => x._extra.blankIndex == i)
              if (blankScoreAreas.length == 0) {
                noScoreAreaQuestions.push(q)
                valid = false
                break
              } else if (blankScoreAreas.length > 1) {
                duplicateScoreAreaQuestions.push(q)
                valid = false
                break
              } else {
                scoreAreaIds.push(blankScoreAreas[0].id)
              }
            }
            if (valid) {
              subj.scoreType = ScoreTypeEnum.TrueFalse.id
              subj.scoreAreaIds = scoreAreaIds
            }
          }
        } else if (checkScoreArea) {
          noScoreAreaQuestions.push(q)
        }
        return subj
      })
      subjectives.push(subjArea)
    })
    if (noScoreAreaQuestions.length > 0) {
      throw `以下题目没有打分区域：${noScoreAreaQuestions.map(q => q.branchName).join('、')}`
    }
    if (duplicateScoreAreaQuestions.length > 0) {
      throw `以下题目打分区域重复：${duplicateScoreAreaQuestions.map(q => q.branchName).join('、')}`
    }
    ;[
      ...objectiveAreas,
      ...subjectiveAreas,
      ...selectMarkAreas,
      ...manualScoreAreas,
      ...barScoreAreas,
      ...trueFalseScoreAreas,
    ].forEach(area => {
      area._extra = undefined
    })

    return {
      objectives,
      subjectives,
    }
  }
  // 生成页码
  generatePageNumber(sideIndex, sideCount) {
    let pageFooterContent = this.pageFooterContent || '第 {{page}} 页, 共 {{total}} 页'
    let text = pageFooterContent.replace(/{{\s*(page|total)\s*(\+\s*(\d+))?\s*}}/g, (match, p1, p2, p3) => {
      let addition = p3 ? Number(p3) : 0
      if (p1 == 'page') {
        return `${sideIndex + 1 + addition}`
      } else {
        return `${sideCount + addition}`
      }
    })
    let height = 14
    let style = {
      fontFamily: `"Times New Roman",Times,楷体,KaiTi,SimKai,serif`,
      fontSize: height + 'px',
      lineHeight: height + 'px',
      height: height + 'px',
      color: 'black',
      whiteSpace: 'pre',
    }
    let width = getHTMLWidth(style, `<div>${text}</div>`)
    let { sideWidth, sideHeight, bottom } = this.getPageSize(0)
    let left = (sideWidth - width) / 2
    let top = sideHeight - bottom / 2 - height / 2
    return {
      text,
      style,
      position: {
        width,
        height,
        left,
        top,
      },
    }
  }
}
