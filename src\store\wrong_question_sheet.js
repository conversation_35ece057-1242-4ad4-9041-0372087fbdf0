import Sheet from '@/helpers/wrong_question_sheet/sheet'
import { PixelsPerMillimeter } from '@/const/wrong_question_sheet'
import { sortQuestionFunction } from '@/helpers/emarking/miscellaneous'
import SourceTypeEnum from '@/enum/wrong_question_sheet/source_type'

export default {
  namespaced: true,

  state: {
    sheet: new Sheet(),
  },

  getters: {
    // Id
    sheetId(state) {
      return state.sheet.id
    },
    // 名称
    sheetName(state) {
      return state.sheet.name
    },
    // 来源
    from(state) {
      return state.sheet.from
    },
    // 是否来自自主创建
    isFromManual(state) {
      return state.sheet.from == SourceTypeEnum.Manual.id
    },
    // 是否来自题库试卷
    isFromPaper(state) {
      return state.sheet.from == SourceTypeEnum.Paper.id
    },
    // 书名
    bookName(state) {
      return state.sheet.bookName
    },
    // 章节名
    chapterName(state) {
      return state.sheet.chapterName
    },
    // 准考号长度
    admissionNumLength(state) {
      return state.sheet.admissionNumLength
    },
    // 学段Id
    stageId(state) {
      return state.sheet.stageId
    },
    // 年级Id
    gradeId(state) {
      return state.sheet.gradeId
    },
    // 科目Id
    subjectId(state) {
      return state.sheet.subjectId
    },
    // 是否校本教辅反馈卡
    isSchoolSheet(state) {
      return state.sheet.from == SourceTypeEnum.Paper.id && !state.sheet.coachBookId
    },
    // 当前尺寸名称
    pageSizeId(state) {
      return state.sheet.size.id
    },
    // 是否分大题
    showTopic(state) {
      return state.sheet.showTopic
    },
    // 是否按题分组
    questionsSeparate(state) {
      return state.sheet.questionsSeparate
    },
    // 每组题数
    questionGroupLength(state) {
      return state.sheet.questionGroupLength
    },
    // 页面尺寸
    pageSize(state) {
      let size = state.sheet.size
      return {
        id: size.id,
        name: size.name,
        width: size.width * PixelsPerMillimeter,
        height: size.height * PixelsPerMillimeter,
        left: size.left * PixelsPerMillimeter,
        right: size.right * PixelsPerMillimeter,
        top: size.top * PixelsPerMillimeter,
        bottom: size.bottom * PixelsPerMillimeter,
      }
    },
    // 页面内容宽度
    pageContentWidth(state) {
      let size = state.sheet.size
      return (size.width - size.left - size.right) * PixelsPerMillimeter
    },
    // 页面内容高度
    pageContentHeight(state) {
      let size = state.sheet.size
      return (size.height - size.top - size.bottom) * PixelsPerMillimeter
    },
    // 页面
    pages(state) {
      return state.sheet.pages
    },
    // 总页数
    totalPages(state) {
      return state.sheet.pages.length
    },
    // 二维码内容
    qrcodeText(state) {
      return state.sheet.qrcodeText
    },
    // 二维码图片
    qrcodeImageSrc(state) {
      return state.sheet.qrcodeImageSrc
    },
    // 客观题
    objectiveQuestions(state) {
      return state.sheet.objectiveQuestions
    },
    // 主观题
    subjectiveQuestions(state) {
      return state.sheet.subjectiveQuestions
    },
    // 试卷题目
    paperStructureQuestions(state) {
      return [...state.sheet.objectiveQuestions, ...state.sheet.subjectiveQuestions].sort(sortQuestionFunction)
    },
    // 是否有题目
    hasQuestion(state) {
      return state.sheet.objectiveQuestions.length > 0 || state.sheet.subjectiveQuestions.length > 0
    },
    // 客观题数量
    objectiveCount(state) {
      return state.sheet.objectiveQuestions.length
    },
    // 主观题数量
    subjectiveCount(state) {
      return state.sheet.subjectiveQuestions.length
    },
  },

  mutations: {
    // 重置
    resetSheet(state) {
      state.sheet = new Sheet()
    },
    // 生成页面
    generatePages(state) {
      state.sheet.generatePages()
    },
    // 改书名
    changeBookName(state, bookName) {
      state.sheet.changeBookName(bookName)
    },
    // 改章节名
    changeChapterName(state, chapterName) {
      state.sheet.changeChapterName(chapterName)
    },
    // 改分大题
    changeShowTopic(state, value) {
      state.sheet.changeShowTopic(value)
    },
    // 改年级
    changeGradeId(state, gradeId) {
      state.sheet.changeGradeId(gradeId)
    },
    // 改科目
    changeSubjectId(state, subjectId) {
      state.sheet.changeSubjectId(subjectId)
    },
    // 改每组题数
    changeQuestionGroupLength(state, groupLength) {
      state.sheet.changeQuestionGroupLength(groupLength)
    },
    // 改题目分组排
    changeQuestionsSeparate(state, questionsSeparate) {
      state.sheet.changeQuestionsSeparate(questionsSeparate)
    },
    // 改页面尺寸
    changeSize(state, sizeId) {
      state.sheet.changeSize(sizeId)
    },
    // 改准考号长度
    changeAdmissionNumLength(state, len) {
      state.sheet.changeAdmissionNumLength(len)
    },
    // 设置页头实例
    changePageHeaderInstance(state, { pageIndex, instance }) {
      state.sheet.changePageHeaderInstance(pageIndex, instance)
    },
    // 设置页脚实例
    changePageFooterInstance(state, { pageIndex, instance }) {
      state.sheet.changePageFooterInstance(pageIndex, instance)
    },
    // 设置页面块实例
    changePageBlockInstance(state, { blockId, instance }) {
      state.sheet.changePageBlockInstance(blockId, instance)
    },
    // 设置页面块高度
    changePageBlockHeight(state, { blockId, height }) {
      state.sheet.changePageBlockHeight(blockId, height)
    },
    // 添加客观题
    addObjectiveQuestions(state, objectives) {
      state.sheet.addObjectiveQuestions(objectives)
    },
    // 添加主观题
    addSubjectiveQuestions(state, subjectives) {
      state.sheet.addSubjectiveQuestions(subjectives)
    },
    // 删除客观题
    deleteObjectiveQuestions(state, questionIds) {
      state.sheet.deleteObjectiveQuestions(questionIds)
    },
    // 删除主观题
    deleteSubjectiveQuestions(state, questionIds) {
      state.sheet.deleteSubjectiveQuestions(questionIds)
    },
    // 更改客观题
    changeObjectiveQuestions(state, objectives) {
      state.sheet.changeObjectiveQuestions(objectives)
    },
    // 更改主观题
    changeSubjectiveQuestions(state, subjectives) {
      state.sheet.changeSubjectiveQuestions(subjectives)
    },
  },
  actions: {
    // 导入
    importSheet(context, { sheet, paper, coachBookInfo }) {
      return Sheet.import({ sheet, paper, coachBookInfo }).then(result => {
        context.state.sheet = result
      })
    },
    // 由组卷试卷创建
    createFromPaper(context, { newId, paper, coachBookInfo }) {
      return Sheet.createFromPaper({ newId, paper, coachBookInfo }).then(sheet => {
        context.state.sheet = sheet
      })
    },
    // 手动创建
    createFromManual(context, { newId, coachBookInfo }) {
      return Sheet.createFromManual({ newId, coachBookInfo }).then(sheet => {
        context.state.sheet = sheet
      })
    },
    // 导出答题卡
    exportSheet(context) {
      return context.state.sheet.exportSheet()
    },
    // 导出扫描模板
    exportScanTemplate(context) {
      return context.state.sheet.exportScanTemplate()
    },
  },
}
