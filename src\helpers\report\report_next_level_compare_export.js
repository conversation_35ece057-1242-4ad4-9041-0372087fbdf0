import {
  apiGetSchoolCompare,
  apiGetClassCompare,
  apiOrgGetSameLevelSubjCompare,
  apiOrgGetNextLevelSubjCompare,
  apiOrgGetNextLevelSubjCombinationCompare,
  apiGetSchoolSubjCombinationCompare,
  apiGetClassSubjCombinationCompare,
} from '@/api/report'

import store from '@/store'

import { UUID_ZERO } from '@/const/string'
import { exportExcel } from '@/utils/excel_export'
import { deepCopy } from '@/utils/object'
import { getInstitutionName } from './tools/tools'

export async function exportReportNextLevelCompareByExcel(
  fileName = 'next_level_compare.xlsx',
  sheetName = '总分',
  tableColumns = [],
  tableData = [],
  // subjectCombinations = [],
  requestParams
) {
  const subjectList = store.getters['report/subjectList'] || []
  const currentSubjectId = store.getters['report/currentSubjectId'] || null

  const ReportModelTypes = store.getters['report/reportModelTypes']
  const HasNextLevelCompare = ReportModelTypes.some(type => type.value === 'under_institutions_compare')
  const IsAdministrator = store.getters['report/isAdministrator']
  const IsExamRootInstitution = store.getters['report/isExamRootInstitution']
  const ExamInstitutionTree = store.getters['report/examInstitutionTree']
  const TiledChildOrgTree = (ExamInstitutionTree.nodes || []).map(node => {
    return {
      institutionId: node.id,
      institutionName: node.name,
      isOrg: node.isOrg,
      level: 0,
      tiledNodes: tiledNodeTree(node),
    }
  })
  const AddBelongOrgColumn =
    requestParams.currentLevelName === 'multipleSchool' &&
    IsAdministrator &&
    HasNextLevelCompare &&
    IsExamRootInstitution

  const TransSheetColumns = transSheetColumns(deepCopy(tableColumns))
  if (AddBelongOrgColumn) {
    TransSheetColumns.splice(1, 0, {
      title: '所属机构',
      width: 'auto',
      key: 'belongOrgName',
    })
  }
  let sheets = [
    {
      sheetName: sheetName,
      rows: tableData.map(r => {
        return {
          ...r,
          belongOrgName: AddBelongOrgColumn ? getBelongOrgName(r.schoolId, TiledChildOrgTree) : '-',
        }
      }),
      columns: TransSheetColumns,
    },
  ]

  if (!currentSubjectId && requestParams) {
    let subjectListWithoutAll = subjectList.filter(s => s.examSubjectId)
    let originRequestList = deepCopy(subjectListWithoutAll)
    // subjectListWithoutAll.concat(subjectCombinations)

    let responses = []
    try {
      responses = await Promise.all(originRequestList.map(r => getSubjectRequest(r, requestParams)))
    } catch {
      responses = []
    }

    responses.forEach((r, rdx) => {
      if (r && r.length) {
        const DynamicColumns = (r[0].rates || []).map((rate, keyIndex) => ({
          key: 'dynamicColumn' + (keyIndex + 1),
          id: rate.id,
        }))
        const SubjectTransSheetColumns = transSheetColumns(
          getSheetColumns(
            originRequestList[rdx],
            DynamicColumns,
            requestParams.currentLevelName,
            requestParams.showSchoolRegionRank,
            requestParams.showClassRegionRank,
            requestParams.showClassSchoolRank,
            requestParams.showRegionRankRaise,
            requestParams.selectReportModelShow,
            requestParams.showSD,
            requestParams.showZScore
          )
        )
        if (AddBelongOrgColumn) {
          SubjectTransSheetColumns.splice(1, 0, {
            title: '所属机构',
            width: 'auto',
            key: 'belongOrgName',
          })
        }

        sheets.push({
          sheetName: originRequestList[rdx].subjectName || originRequestList[rdx].name,
          rows: transData(
            r,
            requestParams.currentLevelName,
            requestParams.selectReportModelShow,
            requestParams.reportModel,
            requestParams.orgTiledInstitutions
          ).map(row => {
            return {
              ...row,
              belongOrgName: AddBelongOrgColumn ? getBelongOrgName(row.schoolId, TiledChildOrgTree) : '-',
            }
          }),
          columns: SubjectTransSheetColumns,
        })
      }
    })

    sheets[0].sheetName = '总分'
  }

  exportExcel(sheets, fileName)
}

function transSheetColumns(columns) {
  return columns.map((originColumn, index) => {
    let excelColumn = {
      title: originColumn.title,
    }

    if (originColumn.key && originColumn.key.includes('dynamicColumn')) {
      excelColumn.children = (originColumn.children || []).map(originChildrenColumn => {
        let excelChildrenColumn = {
          title: originChildrenColumn.title,
          width: 12,
        }
        const DevideColumnKeyArray = originChildrenColumn.key.split('-')
        const ColumnId = (DevideColumnKeyArray && DevideColumnKeyArray[0]) || ''
        const ColumnType = (DevideColumnKeyArray && DevideColumnKeyArray[1]) || ''

        excelChildrenColumn.key = row => {
          const TargetValue = (row.rates || []).find(rate => rate.id === ColumnId)
          if (ColumnType === 'rank') {
            return TargetValue.rank || '-'
          } else {
            return +((TargetValue && TargetValue[ColumnType === 'rate' ? 'name' : ColumnType]) || 0)
          }
        }

        if (ColumnType === 'rate') {
          excelChildrenColumn.cellNumberFormat = value =>
            isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%'
        } else if (originChildrenColumn.title.includes('分')) {
          excelChildrenColumn.cellNumberFormat = value => (Number.isInteger(value) ? undefined : '0.##')
        }

        return excelChildrenColumn
      })
    } else if (index && originColumn.key !== 'teachers') {
      excelColumn.width = 12
      if (originColumn.title.includes('分') && !originColumn.title.includes('率')) {
        excelColumn.cellNumberFormat = value => (Number.isInteger(value) ? undefined : '0.##')
      }
      if (originColumn.key.includes('Rank')) {
        excelColumn.key = row => (row[originColumn.key] && Number(row[originColumn.key])) || '-'
      } else if (originColumn.key !== 'regionRankRaise') {
        excelColumn.key = row => Number(row[originColumn.key])
      } else {
        excelColumn.key = originColumn.key
      }
    } else {
      excelColumn.width = 'auto'
      excelColumn.key = originColumn.key
    }

    return excelColumn
  })
}

function getSubjectRequest(subject, params) {
  let requestParams = deepCopy(params)

  if (!requestParams.selectReportModelShow || requestParams.reportModel === 'under_schools_compare') {
    if (requestParams.currentLevelName === 'multipleSchool') {
      if (subject.id) {
        requestParams.subjectCode = subject.id
        return apiGetSchoolSubjCombinationCompare(requestParams)
      } else {
        requestParams.subjectId = subject.subjectId
        requestParams.examSubjectId = subject.examSubjectId
        requestParams.reportName = 'union_schCompare'
        return apiGetSchoolCompare(requestParams)
      }
    } else {
      if (subject.id) {
        requestParams.subjectCode = subject.id
        return apiGetClassSubjCombinationCompare(requestParams)
      } else {
        requestParams.subjectId = subject.subjectId
        requestParams.examSubjectId = subject.examSubjectId
        requestParams.reportName = 'sch_clsCompare'
        return apiGetClassCompare(requestParams)
      }
    }
  } else if (requestParams.reportModel === 'under_institutions_compare') {
    if (subject.id) {
      requestParams.subjectCode = subject.id
      requestParams.organizationId = requestParams.currentInstitutionId
      return apiOrgGetNextLevelSubjCombinationCompare(requestParams)
    } else {
      requestParams.subjectId = subject.subjectId
      requestParams.organizationId = requestParams.currentInstitutionId
      return apiOrgGetNextLevelSubjCompare(requestParams)
    }
  } else if (requestParams.reportModel === 'same_level_institutions_compare') {
    if (subject.subjectId) {
      requestParams.subjectId = subject.subjectId
      requestParams.organizationId = requestParams.currentInstitutionId
      return apiOrgGetSameLevelSubjCompare(requestParams)
    }
  }

  return null
}

function getSheetColumns(
  subject,
  dynamicColumns = [],
  currentLevelName,
  showSchoolRegionRank = false,
  showClassRegionRank = false,
  showClassSchoolRank = false,
  showRegionRankRaise = false,
  selectReportModelShow = false,
  showSD = false,
  showZScore = false
) {
  let columns = [
    {
      title: '实考人数',
      key: 'present',
    },
    {
      title: '缺考人数',
      key: 'absent',
    },
    {
      title: '统计人数',
      key: 'statCount',
    },
    {
      title: '满分',
      key: 'totalFullScore',
    },
    {
      title: '最高分',
      key: 'maxScore',
    },
    {
      title: '最低分',
      key: 'minScore',
    },
    {
      title: '平均分',
      key: 'avgScore',
    },
  ]

  if (showSD) {
    columns.push({
      title: '标准差',
      key: 'sd',
    })
  }
  if (showZScore) {
    columns.push({
      title: '标准分',
      key: 'zScore',
    })
  }

  dynamicColumns.forEach(item => {
    const ItemChildrenColumns = [
      {
        title: '值',
        key: `${item.id}-rate`,
      },
      {
        title: '人数',
        key: `${item.id}-count`,
      },
      {
        title: '平均分',
        key: `${item.id}-avg`,
      },
    ]

    if (
      (currentLevelName === 'multipleSchool' && showSchoolRegionRank) ||
      (currentLevelName === 'school' && showClassSchoolRank)
    ) {
      ItemChildrenColumns.push({
        title: (currentLevelName === 'school' ? '校' : '联考') + '排名',
        key: item.id + '-rank',
      })
    }

    columns.push({
      title: item.id,
      key: item.key,
      children: ItemChildrenColumns,
    })
  })

  if (currentLevelName === 'school') {
    if (subject.examSubjectId) {
      columns.unshift({
        title: '科任老师',
        key: 'teachers',
      })
    }

    columns.unshift({
      title: '班级',
      key: 'institutionName',
    })

    if (showClassRegionRank) {
      columns.push({
        title: '联考排名',
        key: 'regionRank',
      })
    }
    if (showClassSchoolRank) {
      columns.push({
        title: '校排名',
        key: 'schoolRank',
      })
    }
  } else if (currentLevelName === 'multipleSchool') {
    columns.unshift({
      title: selectReportModelShow ? '单位名称' : '学校',
      key: 'institutionName',
    })
    if (showSchoolRegionRank) {
      columns.push({
        title: '联考排名',
        key: 'regionRank',
      })
    }
    if (showRegionRankRaise) {
      columns.push({
        title: '联考排名变化',
        key: 'regionRankRaise',
      })
    }
  }

  return columns
}

function transData(rawData, currentLevelName, selectReportModelShow, reportModel, orgTiledInstitutions) {
  const ExamCreatedInstitutionId = store.getters['report/examCreatedInstitionId'] || ''
  const Exam = store.getters['report/exam']
  const ExamGradeId = (Exam && Exam.grade && Exam.grade.id) || 0
  let response = (rawData || []).map(x => {
    let institutionName
    if ((ExamCreatedInstitutionId || '') === '00000000-0000-0000-0000-001000000004' && x.schoolId === '1fpfmg0ylbls') {
      institutionName = ExamGradeId < 7 ? '江城区九年一贯制学校' : '江城区公立学校'
    } else {
      institutionName = getInstitutionName(x.schoolId, x.classId, x.schoolName, x.className)
    }

    let row = {
      schoolId: x.schoolId,
      classId: x.classId,
      institutionName: institutionName,
      present: x.present,
      absent: x.absent,
      statCount: x.statCount,
      totalFullScore: x.totalFullScore,
      maxScore: x.maxScore,
      minScore: x.minScore,
      avgScore: x.avgScore,
      regionRank: x.regionRank,
      rates: x.rates,
      sd: x.sd,
      zScore: x.zScore,
      regionRankRaise: x.schoolId === UUID_ZERO || x.regionRankRaise === 0 ? '-' : x.regionRankRaise,
    }

    if (currentLevelName === 'school') {
      row.teachers = (x.teachers || []).map(teacher => teacher.name).join('、')
      if (!row.teachers) {
        row.teachers = '-'
      }
      row.schoolRank = x.schoolRank
    }

    return row
  })

  let headRows = []
  let sortedRows = []
  if (currentLevelName === 'multipleSchool') {
    if (selectReportModelShow && reportModel !== 'under_schools_compare') {
      let headRegular = [UUID_ZERO]
      if (reportModel === 'under_institutions_compare') {
        headRegular.push(store.getters['user/info'].schoolId)
      }
      headRows = response.filter(x => headRegular.some(y => y === x.schoolId))
      sortedRows = response.filter(x => !headRegular.some(y => y === x.schoolId))
    } else {
      headRows = response.filter(x => orgTiledInstitutions.some(y => y.institutionId === x.schoolId))
      sortedRows = response.filter(x => !orgTiledInstitutions.some(y => y.institutionId === x.schoolId))
    }
    sortedRows.sort((a, b) =>
      !Number.isNaN(Number(a.schoolRank)) && !Number.isNaN(Number(b.schoolRank))
        ? Number(a.schoolRank) - Number(b.schoolRank)
        : Number(b.avgScore) - Number(a.avgScore)
    )
  } else {
    headRows = response.filter(x => x.classId === UUID_ZERO)
    sortedRows = response.filter(x => x.classId !== UUID_ZERO)
  }

  return headRows.concat(sortedRows)
}

function tiledNodeTree(n) {
  const Result = []

  const _FillResult = (node, level) => {
    if (level) {
      Result.push({
        institutionId: node.id,
        institutionName: node.name,
        isOrg: node.isOrg,
        level: level,
      })
    }

    if (node.nodes && node.nodes.length) {
      node.nodes.forEach(x => _FillResult(x, level + 1))
    }
  }

  _FillResult(n, 0)

  return Result
}

function getBelongOrgName(schoolId, childNodes = []) {
  return (
    childNodes.find(n => (n.tiledNodes || []).some(x => x.institutionId === schoolId)) || { institutionName: '-' }
  ).institutionName
}
