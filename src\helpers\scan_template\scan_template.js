import Page from './page'
import Template from '@/helpers/scan_template_define/template'
import TemplateObjective from '@/helpers/scan_template_define/template_objective'
import TemplateQuestionObjective from '@/helpers/scan_template_define/template_question_objective'
import TemplateSubjective from '@/helpers/scan_template_define/template_subjective'
import TemplateQuestionSubjective from '@/helpers/scan_template_define/template_question_subjective'
import { sortQuestionFunction, getQuestionFullCode } from '@/helpers/emarking/miscellaneous'
import { loadImage, canvasToBlob } from '@/utils/promise'
import { sumByDefaultZero } from '@/utils/math'
import { unGroupArray } from '@/utils/array'
import { deepCopy } from '@/utils/object'

import StepStatusEnum from '@/enum/scan_template/step_status'
import LocateModeEnum from '@/enum/scan_template/locate_mode'
import AdmissionTypeEnum from '@/enum/scan_template/admission_type'
import AnchorPositionEnum from '@/enum/scan_template/anchor_position'
import ScoreTypeEnum from '@/enum/answer_sheet/score_type'
const AnchorPositions = AnchorPositionEnum.getIds()

export default class ScanTemplate {
  constructor() {
    this.isDoubleSide = true
    this.pages = []
    // 模板题目题块
    // 页面各客观题区域、主观题区域、选做标记均记录了对应题目题块信息，
    // 故模板题目题块可由页面生成
    this.questions = {
      objectives: [],
      blocks: [],
    }

    // 是否可修改科目试卷结构
    this.enableEditQuestion = true
    // 是否只能修改题块
    this.onlyEditBlock = false

    // 科目题目、题块定义
    // 允许修改科目试卷结构：与模板的题目题块一致
    // 不允许修改科目试卷结构：是模板的题目题块的超集
    this.subjectObjectives = []
    this.subjectSubjectives = []
    this.subjectBlocks = []
  }

  /**
   * 导入导出
   */
  static async import({
    templateJson,
    imageUrls,
    subjectObjectives,
    subjectBlocks,
    enableEditQuestion,
    onlyEditBlock,
  }) {
    let scanTemplate = new ScanTemplate()
    scanTemplate.enableEditQuestion = Boolean(enableEditQuestion)
    scanTemplate.onlyEditBlock = Boolean(onlyEditBlock)

    let template = JSON.parse(templateJson)
    // 是否双面
    scanTemplate.isDoubleSide = template.isDoubleSide
    // 题目
    scanTemplate.questions = {
      objectives: template.questions.objectives || [],
      blocks: template.questions.subjectives || [],
    }
    let importSubjectiveQuestions = unGroupArray(template.questions.subjectives || [], block => block.questions)
    scanTemplate.subjectObjectives = subjectObjectives
    scanTemplate.subjectBlocks = subjectBlocks
    scanTemplate.extractBlockSubjectives()
    // 页面图片
    let imgUrlList = (imageUrls || '').split(',').filter(Boolean)
    if (imgUrlList.length != template.pages.length) {
      throw '模板页面数量与图片数量不一致'
    }
    let imgElements
    try {
      imgElements = await Promise.all(imgUrlList.map(url => loadImage(url, true)))
    } catch {
      throw '加载图片失败'
    }
    // 导入页面
    for (let idx = 0; idx < template.pages.length; idx++) {
      template.pages[idx].fileUrl = imgUrlList[idx]
      let page = Page.import(template.pages[idx], imgElements[idx], scanTemplate, importSubjectiveQuestions)
      scanTemplate.pages.push(page)
    }
    // 准考号类型初始化
    let frontPages = this.isDoubleSide ? scanTemplate.pages.filter(p => p.index % 2 == 0) : scanTemplate.pages
    let firstHasAdmissionNumPage = frontPages.find(p =>
      [AdmissionTypeEnum.Omr.id, AdmissionTypeEnum.Barcode.id, AdmissionTypeEnum.Handwriting.id].includes(
        p.admissionNum.type
      )
    )
    let admissionNumType = firstHasAdmissionNumPage
      ? firstHasAdmissionNumPage.admissionNum.type
      : AdmissionTypeEnum.Omr.id
    frontPages.forEach(p => {
      if (
        ![AdmissionTypeEnum.Omr.id, AdmissionTypeEnum.Barcode.id, AdmissionTypeEnum.Handwriting.id].includes(
          p.admissionNum.type
        )
      ) {
        p.admissionNum.type = admissionNumType
      }
    })
    return scanTemplate
  }
  static getEmptyTemplateJson({ isDoubleSide, pageCount }) {
    let scanTemplate = new ScanTemplate()
    scanTemplate.isDoubleSide = isDoubleSide
    for (let idx = 0; idx < pageCount; idx++) {
      let page = new Page()
      page.index = idx
      page.updateName(isDoubleSide)
      scanTemplate.pages.push(page)
    }
    return JSON.stringify(scanTemplate.export())
  }
  export() {
    let template = new Template()
    this.generateQuestionObjectives()
    this.generateQuestionBlocks({ checkExtraScoreArea: true })
    template.questions = {
      objectives: deepCopy(this.questions.objectives),
      subjectives: deepCopy(this.questions.blocks),
    }
    template.questions.objectives.forEach(obj => {
      Object.setPrototypeOf(obj, TemplateObjective.prototype)
      obj.questions.forEach(q => {
        Object.setPrototypeOf(q, TemplateQuestionObjective.prototype)
      })
    })
    template.questions.subjectives.forEach(block => {
      Object.setPrototypeOf(block, TemplateSubjective.prototype)
      block.questions.forEach(q => {
        Object.setPrototypeOf(q, TemplateQuestionSubjective.prototype)
      })
    })

    template.isDoubleSide = this.isDoubleSide
    template.pages = this.pages.map(page => page.export())
    return template
  }
  generateQuestionObjectives() {
    let objectives = []
    this.pages.forEach(page => {
      page.objectiveAreas.forEach(area => {
        let templateObjective = new TemplateObjective()
        templateObjective.areaId = area.id
        templateObjective.questions = area.questionCodes.map(code => {
          let templateQuestionObjective = new TemplateQuestionObjective()
          templateQuestionObjective.questionCode = code
          return templateQuestionObjective
        })
        objectives.push(templateObjective)
      })
    })
    this.questions.objectives = objectives
  }
  generateQuestionBlocks({ checkExtraScoreArea } = {}) {
    let blocks = []
    let trueFalseScoreAreas = []
    let barScoreAreas = []
    let manualScoreAreas = []
    // 题块及对应区域
    this.pages.forEach(page => {
      page.subjectiveAreas.forEach(area => {
        let target = blocks.find(x => x.subjectiveId == area.blockId)
        if (!target) {
          let block = this.subjectBlocks.find(x => x.blockId == area.blockId)
          if (!block) {
            throw `主观题区域找不到对应题块：${area.blockName}`
          }
          target = new TemplateSubjective()
          target.questions = block.questions.map(q => {
            let templateQuestionSubjective = new TemplateQuestionSubjective()
            templateQuestionSubjective.questionCode = q.questionCode
            templateQuestionSubjective.branchCode = q.branchCode
            templateQuestionSubjective.questionId = q.questionId
            return templateQuestionSubjective
          })
          target.subjectiveId = area.blockId
          target.blockName = area.blockName
          blocks.push(target)
        }
        target.areaIds.push(area.id)
      })
      trueFalseScoreAreas.push(...page.trueFalseScoreAreas)
      barScoreAreas.push(...page.barScoreAreas)
      manualScoreAreas.push(...page.manualScoreAreas)
    })
    blocks.sort((a, b) => sortQuestionFunction(a.questions[0], b.questions[0]))
    // 选做标记
    this.pages.forEach(page => {
      page.selectMarkAreas.forEach(area => {
        let target = blocks.find(x => x.subjectiveId == area.blockId)
        if (!target) {
          throw `选做标记找不到对应题块：${area.blockName}`
        }
        target.selectMarkId = area.id
      })
    })
    // 打分区域
    blocks.forEach(block => {
      block.questions.forEach(q => {
        let qTrueFalseScoreAreas = trueFalseScoreAreas
          .filter(area => area.questionCode == q.questionCode && area.branchCode == q.branchCode)
          .sort((a, b) => a.blankIndex - b.blankIndex)
        if (qTrueFalseScoreAreas.length > 0) {
          q.scoreAreaIds = qTrueFalseScoreAreas.map(area => area.id)
          q.scoreType = ScoreTypeEnum.TrueFalse.id
          trueFalseScoreAreas = trueFalseScoreAreas.filter(area => !qTrueFalseScoreAreas.includes(area))
          return
        }

        let qBarScoreAreas = barScoreAreas.filter(
          area => area.questionCode == q.questionCode && area.branchCode == q.branchCode
        )
        if (qBarScoreAreas.length > 0) {
          if (qBarScoreAreas.length > 1) {
            throw `打分条区域重复：${getQuestionFullCode(q)}`
          }
          q.scoreAreaIds = qBarScoreAreas.map(area => area.id)
          q.scoreType = ScoreTypeEnum.Bar.id
          barScoreAreas = barScoreAreas.filter(area => !qBarScoreAreas.includes(area))
          return
        }

        let qManualScoreAreas = manualScoreAreas.filter(
          area => area.questionCode == q.questionCode && area.branchCode == q.branchCode
        )
        if (qManualScoreAreas.length > 0) {
          if (qManualScoreAreas.length > 1) {
            throw `手写数字框区域重复：${getQuestionFullCode(q)}`
          }
          q.scoreAreaIds = qManualScoreAreas.map(area => area.id)
          q.scoreType = ScoreTypeEnum.Number.id
          manualScoreAreas = manualScoreAreas.filter(area => !qManualScoreAreas.includes(area))
          return
        }
      })
    })
    if (checkExtraScoreArea) {
      if (trueFalseScoreAreas.length > 0) {
        throw `存在未关联题目的对错打分框`
      }
      if (barScoreAreas.length > 0) {
        throw `存在未关联题目的打分条`
      }
      if (manualScoreAreas.length > 0) {
        throw `存在未关联题目的手写数字框`
      }
    }
    this.questions.blocks = blocks
  }
  // 将题块中题目提取到主观题列表中
  extractBlockSubjectives() {
    let subjectives = []
    this.subjectBlocks.forEach(block => {
      subjectives.push(...block.questions)
    })
    subjectives.sort(sortQuestionFunction)
    this.subjectSubjectives = subjectives
  }

  /**
   * 清空
   */
  clear() {
    this.pages.forEach(page => page.clear())
    this.questions = {
      objectives: [],
      blocks: [],
    }
    if (this.enableEditQuestion) {
      this.subjectBlocks = []
      if (!this.onlyEditBlock) {
        this.subjectObjectives = []
        this.subjectSubjectives = []
      }
    }
  }

  /**
   * 更改题目开关
   */
  changeEnableEditQuestion(value) {
    this.enableEditQuestion = Boolean(value)
  }

  /**
   * 定位
   */
  changeLocateMode(mode) {
    this.pages.forEach(page => page.changeLocateMode(mode))
  }
  addAnchor({ pageIndex, position, rect }) {
    this.pages[pageIndex].addAnchor(position, rect)
  }
  deleteAnchor({ pageIndex, position }) {
    this.pages[pageIndex].deleteAnchor(position)
  }
  addLine({ pageIndex, rect }) {
    this.pages[pageIndex].addLine(rect)
  }
  deleteLine(pageIndex) {
    this.pages[pageIndex].deleteLine()
  }
  addText({ pageIndex, rect }) {
    this.pages[pageIndex].addText(rect)
  }
  deleteText({ pageIndex, rect }) {
    this.pages[pageIndex].deleteText(rect)
  }

  /**
   * 标题
   */
  addTitle({ pageIndex, rect }) {
    this.pages[pageIndex].addTitle(rect)
  }
  deleteTitle({ pageIndex, rect }) {
    this.pages[pageIndex].deleteTitle(rect)
  }

  /**
   * 准考号
   */
  changeAdmissionNumType(type) {
    this.pages.forEach(page => page.changeAdmissionNumType(type))
  }
  changeAdmissionNumLength(len) {
    this.pages.forEach(page => page.changeAdmissionNumLength(len))
  }
  addAdmissionNumBarcode({ pageIndex, rect }) {
    this.pages[pageIndex].addAdmissionNumBarcode(rect)
  }
  deleteAdmissionNumBarcode(pageIndex) {
    this.pages[pageIndex].deleteAdmissionNumBarcode()
  }
  addAdmissionNumHandwriting({ pageIndex, rect }) {
    this.pages[pageIndex].addAdmissionNumHandwriting(rect)
  }
  deleteAdmissionNumHandwriting(pageIndex) {
    this.pages[pageIndex].deleteAdmissionNumHandwriting()
  }
  addAdmissionNumOmr({ pageIndex, omrBlock }) {
    this.pages[pageIndex].addAdmissionNumOmr(omrBlock)
  }
  replaceAdmissionNumOmr({ pageIndex, omrBlock }) {
    this.pages[pageIndex].replaceAdmissionNumOmr(omrBlock)
  }
  deleteAdmissionNumOmr(pageIndex) {
    this.pages[pageIndex].deleteAdmissionNumOmr()
  }

  /**
   * 客观题
   */
  addObjectiveArea({ pageIndex, omrBlock, newObjectiveQuestions }) {
    this.pages[pageIndex].addObjectiveArea(omrBlock)
    if (this.enableEditQuestion && !this.onlyEditBlock) {
      this.subjectObjectives.push(...newObjectiveQuestions)
      this.deleteNoAreaSubjectObjectives()
    }
    this.generateQuestionObjectives()
  }
  replaceObjectiveArea({ pageIndex, omrBlock }) {
    this.pages[pageIndex].replaceObjectiveArea(omrBlock)
    this.generateQuestionObjectives()
  }
  deleteObjectiveArea({ pageIndex, omrBlock }) {
    this.pages[pageIndex].deleteObjectiveArea(omrBlock)
    if (this.enableEditQuestion && !this.onlyEditBlock) {
      this.deleteNoAreaSubjectObjectives()
    }
    this.generateQuestionObjectives()
  }
  deleteObjectiveAreaBatch(batch) {
    batch.forEach(({ pageIndex, omrBlock }) => {
      this.pages[pageIndex].deleteObjectiveArea(omrBlock)
    })
    if (this.enableEditQuestion && !this.onlyEditBlock) {
      this.deleteNoAreaSubjectObjectives()
    }
    this.generateQuestionObjectives()
  }
  deleteNoAreaSubjectObjectives() {
    let templateObjectiveQuestionCodes = []
    this.pages.forEach(page =>
      page.objectiveAreas.forEach(area => templateObjectiveQuestionCodes.push(...area.questionCodes))
    )
    this.subjectObjectives = this.subjectObjectives
      .filter(obj => templateObjectiveQuestionCodes.includes(obj.questionCode))
      .sort((a, b) => a.questionCode - b.questionCode)
  }

  /**
   * 主观题
   */
  addSubjectiveAreaBatch(batch) {
    let newBlocks = []
    batch.forEach(({ pageIndex, area, newBlock }) => {
      this.pages[pageIndex].addSubjectiveArea(area)
      if (newBlock) {
        newBlocks.push(newBlock)
      }
    })
    if (this.enableEditQuestion) {
      if (newBlocks.length > 0) {
        this.subjectBlocks.push(...newBlocks)
        this.subjectBlocks.sort((a, b) => sortQuestionFunction(a.questions[0], b.questions[0]))
      }
      this.deleteNoAreaSubjectSubjectives()
    }
    this.generateQuestionBlocks()
  }
  changeSubjectiveAreas({ areas, newBlock }) {
    // 1. 区域范围
    areas.forEach(area => {
      this.pages[area.pageIndex].changeSubjectiveAreaRect(area)
    })

    // 2. 题块名称、包含题目
    if (this.enableEditQuestion && newBlock) {
      let block = this.subjectBlocks.find(x => x.blockId == newBlock.blockId)
      if (block) {
        block.blockName = newBlock.blockName
        block.questions = newBlock.questions
      }
      this.pages.forEach(page => {
        page.subjectiveAreas.forEach(area => {
          if (area.blockId == newBlock.blockId) {
            area.blockName = newBlock.blockName
          }
        })
        page.selectMarkAreas.forEach(area => {
          if (area.blockId == newBlock.blockId) {
            area.blockName = newBlock.blockName
          }
        })
      })
      this.deleteNoAreaSubjectSubjectives()
    }
    this.generateQuestionBlocks()
  }
  deleteSubjectiveAreaBatch(batch) {
    batch.forEach(({ pageIndex, area }) => {
      this.pages[pageIndex].deleteSubjectiveArea(area)
    })
    if (this.enableEditQuestion) {
      this.deleteNoAreaSubjectSubjectives()
      // 删除题块后，其选做标记也要删除
      this.deleteNoBlockSelectMarkAreas()
    }
    this.generateQuestionBlocks()
  }
  // 删除科目中没有区域的
  deleteNoAreaSubjectSubjectives() {
    let templateBlockIds = []
    this.pages.forEach(page =>
      page.subjectiveAreas.forEach(area => {
        if (!templateBlockIds.includes(area.blockId)) {
          templateBlockIds.push(area.blockId)
        }
      })
    )
    this.subjectBlocks = this.subjectBlocks
      .filter(block => templateBlockIds.includes(block.blockId))
      .sort((a, b) => sortQuestionFunction(a.questions[0], b.questions[0]))
    if (!this.onlyEditBlock) {
      this.extractBlockSubjectives()
    }
  }

  /**
   * 页码标记
   */
  addPageMark({ pageIndex, rect }) {
    this.pages[pageIndex].addPageMark(rect)
  }
  deletePageMark(pageIndex) {
    this.pages[pageIndex].deletePageMark()
  }
  changeIsPageMarkText({ pageIndex, value }) {
    this.pages[pageIndex].changeIsPageMarkText(value)
  }

  /**
   * 缺考标记
   */
  addMissingMark({ rect }) {
    this.pages[0].addMissingMark(rect)
  }
  deleteMissingMark() {
    this.pages[0].deleteMissingMark()
  }

  /**
   * 选做标记
   */
  addSelectGroup({ selectGroupName, selectCount, blockIds }) {
    this.subjectBlocks.forEach(block => {
      if (blockIds.includes(block.blockId)) {
        block.selectGroupName = selectGroupName
        block.selectCount = selectCount
      }
    })
  }
  deleteSelectGroups(selectGroupNames) {
    this.subjectBlocks.forEach(block => {
      if (selectGroupNames.includes(block.selectGroupName)) {
        block.selectGroupName = null
        block.selectCount = null
      }
    })
    this.deleteNoBlockSelectMarkAreas()
  }
  addSelectMarkArea({ pageIndex, area }) {
    this.pages[pageIndex].addSelectMarkArea(area)
    this.generateQuestionBlocks()
  }
  deleteSelectMarkArea({ pageIndex, area }) {
    this.pages[pageIndex].deleteSelectMarkArea(area)
    this.generateQuestionBlocks()
  }
  // 删除无对应题块的选做标记
  deleteNoBlockSelectMarkAreas() {
    let selectBlockIds = []
    this.subjectBlocks.forEach(block => {
      if (block.selectGroupName) {
        selectBlockIds.push(block.blockId)
      }
    })
    this.pages.forEach(page => {
      page.selectMarkAreas = page.selectMarkAreas.filter(area => selectBlockIds.includes(area.blockId))
    })
    this.generateQuestionBlocks()
  }

  /**
   * 科目标记
   */
  addSubjectMark({ pageIndex, rect }) {
    this.pages[pageIndex].addSubjectMark(rect)
  }
  deleteSubjectMark(pageIndex) {
    this.pages[pageIndex].deleteSubjectMark()
  }

  /**
   * 遮挡检测
   */
  addBlockDetectSkipArea({ pageIndex, rect }) {
    this.pages[pageIndex].addBlockDetectSkipArea(rect)
  }
  deleteBlockDetectSkipArea({ pageIndex, rect }) {
    this.pages[pageIndex].deleteBlockDetectSkipArea(rect)
  }

  /**
   * 学生信息区域
   */
  addStudentInfoArea({ pageIndex, rect }) {
    this.pages[pageIndex].addStudentInfoArea(rect)
  }
  deleteStudentInfoArea({ pageIndex, rect }) {
    this.pages[pageIndex].deleteStudentInfoArea(rect)
  }

  /**
   * 对错打分框
   */
  changeTrueFalseScoreAreaRect({ pageIndex, area }) {
    this.pages[pageIndex].changeTrueFalseScoreAreaRect(area)
  }

  /**
   * 步骤状态
   */
  getLocateStatus() {
    if (this.pages.length == 0) {
      return StepStatusEnum.Wait.id
    }
    let locateMode = this.pages[0].locateMode
    if (locateMode == LocateModeEnum.Anchor.id) {
      let hasAnchor = this.pages.some(p => AnchorPositions.some(position => p.anchor[position]))
      let anchorFinished = this.pages.every(p => AnchorPositions.filter(position => p.anchor[position]).length >= 3)
      return anchorFinished ? StepStatusEnum.Finish.id : hasAnchor ? StepStatusEnum.Process.id : StepStatusEnum.Wait.id
    } else if (locateMode == LocateModeEnum.Line.id) {
      let hasLine = this.pages.some(p => p.line)
      let lineFinished = this.pages.every(p => p.line)
      return lineFinished ? StepStatusEnum.Finish.id : hasLine ? StepStatusEnum.Process.id : StepStatusEnum.Wait.id
    } else if (locateMode == LocateModeEnum.Text.id) {
      let hasText = this.pages.some(p => p.text)
      let textFinished = this.pages.every(p => p.text)
      return textFinished ? StepStatusEnum.Finish.id : hasText ? StepStatusEnum.Process.id : StepStatusEnum.Wait.id
    } else {
      return StepStatusEnum.Wait.id
    }
  }
  getTitleStatus() {
    if (this.pages.length == 0) {
      return StepStatusEnum.Wait.id
    }
    let hasTitle = this.pages.some(p => p.title)
    // 文字定位无需标题
    let titleFinished = this.pages.every(p => p.title || p.locateMode == LocateModeEnum.Text.id)
    return titleFinished ? StepStatusEnum.Finish.id : hasTitle ? StepStatusEnum.Process.id : StepStatusEnum.Wait.id
  }
  getAdmissionNumStatus() {
    let frontPages = this.isDoubleSide ? this.pages.filter(p => p.index % 2 == 0) : this.pages
    if (frontPages.length == 0) {
      return StepStatusEnum.Wait.id
    }
    let admissionNumType = frontPages[0].admissionNum.type
    let key = ''
    if (admissionNumType == AdmissionTypeEnum.Omr.id) {
      key = 'omr'
    } else if (admissionNumType == AdmissionTypeEnum.Barcode.id) {
      key = 'barcode'
    } else if (admissionNumType == AdmissionTypeEnum.Handwriting.id) {
      key = 'handwriting'
    } else {
      return StepStatusEnum.Wait.id
    }
    let hasAdmissionNum = frontPages.some(p => p.admissionNum[key])
    let admissionNumFinished = frontPages.every(p => p.admissionNum[key])
    return admissionNumFinished
      ? StepStatusEnum.Finish.id
      : hasAdmissionNum
        ? StepStatusEnum.Process.id
        : StepStatusEnum.Wait.id
  }
  getObjectiveStatus() {
    if (
      this.enableEditQuestion &&
      this.pages.every(page => page.objectiveAreas.length == 0 && page.subjectiveAreas.length == 0)
    ) {
      return StepStatusEnum.Wait.id
    }

    let blockQuestionCodes = []
    this.pages.forEach(page => {
      page.objectiveAreas.forEach(block => {
        blockQuestionCodes.push(...block.questionCodes)
      })
    })
    let objectiveQuestionCodes = this.subjectObjectives.map(obj => obj.questionCode)
    if (blockQuestionCodes.length == objectiveQuestionCodes.length) {
      return StepStatusEnum.Finish.id
    } else if (blockQuestionCodes.length > 0) {
      return StepStatusEnum.Process.id
    } else {
      return StepStatusEnum.Wait.id
    }
  }
  getSubjectiveStatus() {
    if (
      this.enableEditQuestion &&
      this.pages.every(page => page.objectiveAreas.length == 0 && page.subjectiveAreas.length == 0)
    ) {
      return StepStatusEnum.Wait.id
    }

    if (this.enableEditQuestion && this.onlyEditBlock) {
      let allBlockQuestions = []
      this.subjectBlocks.forEach(block => {
        allBlockQuestions.push(...block.questions)
      })
      let unAddSubjectives = this.subjectSubjectives.filter(q =>
        allBlockQuestions.every(x => x.questionCode != q.questionCode || x.branchCode != q.branchCode)
      )
      if (unAddSubjectives.length > 0) {
        return allBlockQuestions.length > 0 ? StepStatusEnum.Process.id : StepStatusEnum.Wait.id
      }
    }

    let hasAreaBlockIds = []
    this.pages.forEach(page => {
      page.subjectiveAreas.forEach(subj => {
        if (!hasAreaBlockIds.includes(subj.blockId)) {
          hasAreaBlockIds.push(subj.blockId)
        }
      })
    })
    if (hasAreaBlockIds.length == this.subjectBlocks.length) {
      return StepStatusEnum.Finish.id
    } else if (hasAreaBlockIds.length > 0) {
      return StepStatusEnum.Process.id
    } else {
      return StepStatusEnum.Wait.id
    }
  }
  getPageMarkStatus() {
    if (this.pages.length == 0) {
      return StepStatusEnum.Wait.id
    }

    let hasPageMark = this.pages.some(p => p.pageMark)
    let pageMarkFinished = this.pages.every(p => p.pageMark)
    return pageMarkFinished
      ? StepStatusEnum.Finish.id
      : hasPageMark
        ? StepStatusEnum.Process.id
        : StepStatusEnum.Wait.id
  }
  getMissingMarkStatus() {
    let firstPage = this.pages[0]
    if (!firstPage || !firstPage.missingMark) {
      return StepStatusEnum.Wait.id
    } else {
      return StepStatusEnum.Finish.id
    }
  }
  getSubjectMarkStatus() {
    let subjectMarkPages = this.pages.filter(page => this.isDoubleSide || page.index % 2 == 0)
    let hasSubjectMark = subjectMarkPages.some(p => p.subjectMark)
    let subjectMarkFinished = subjectMarkPages.every(p => p.subjectMark)
    return subjectMarkFinished
      ? StepStatusEnum.Finish.id
      : hasSubjectMark
        ? StepStatusEnum.Process.id
        : StepStatusEnum.Wait.id
  }
  getSelectMarkStatus() {
    let selectGroupBlockIds = this.subjectBlocks.filter(block => block.selectGroupName).map(block => block.blockId)
    let selectMarkBlockIds = []
    this.pages.forEach(page => {
      page.selectMarkAreas.forEach(area => {
        selectMarkBlockIds.push(area.blockId)
      })
    })
    if (selectMarkBlockIds.length >= selectGroupBlockIds.length) {
      return StepStatusEnum.Finish.id
    } else if (selectMarkBlockIds.length == 0) {
      return StepStatusEnum.Wait.id
    } else {
      return StepStatusEnum.Process.id
    }
  }
  getScoreAreaStatus() {
    let trueFalseScoreAreas = []
    this.pages.forEach(page => {
      trueFalseScoreAreas.push(...page.trueFalseScoreAreas)
    })
    if (trueFalseScoreAreas.length == 0) {
      return StepStatusEnum.Finish.id
    }
    let hasRectCount = trueFalseScoreAreas.filter(area => area.rect).length
    if (hasRectCount == trueFalseScoreAreas.length) {
      return StepStatusEnum.Finish.id
    } else if (hasRectCount > 0) {
      return StepStatusEnum.Process.id
    } else {
      return StepStatusEnum.Wait.id
    }
  }

  /**
   * 与科目试卷结构比较
   */
  compareTemplateSubjectObjectives() {
    let wrongQuestionCodes = []
    let wrongObjectiveAreas = []

    this.pages.forEach(page =>
      page.objectiveAreas.forEach(omrBlock => {
        let isWrong = false
        omrBlock.questionCodes.forEach(questionCode => {
          let obj = this.subjectObjectives.find(x => x.questionCode == questionCode)
          // 客观题不存在
          if (!obj) {
            wrongQuestionCodes.push(questionCode)
            isWrong = true
          }
          // 选项个数不一致
          else if (obj.optionCount != omrBlock.optionCount) {
            wrongQuestionCodes.push(questionCode)
            isWrong = true
          }
        })
        if (isWrong) {
          wrongObjectiveAreas.push({
            pageIndex: page.index,
            omrBlock,
          })
        }
      })
    )

    wrongQuestionCodes.sort((a, b) => a - b)
    return {
      wrongQuestionCodes,
      wrongObjectiveAreas,
    }
  }
  compareTemplateSubjectBlocks() {
    let wrongBlockNames = []
    let wrongSubjectiveAreas = []

    this.pages.forEach(page => {
      page.subjectiveAreas.forEach(subjArea => {
        let isWrong = false
        let block = this.subjectBlocks.find(x => x.blockId == subjArea.blockId)
        let templateBlock = this.questions.blocks.find(x => x.subjectiveId == subjArea.blockId)
        // 题块不存在
        if (!block) {
          isWrong = true
        } else if (templateBlock) {
          // 包含题目不一致
          let templateBlockQuestionText = templateBlock.questions
            .map(q => `${q.questionCode}.${q.branchCode}`)
            .sort()
            .join(',')
          let blockQuestionText = block.questions
            .map(q => `${q.questionCode}.${q.branchCode}`)
            .sort()
            .join(',')
          if (templateBlockQuestionText != blockQuestionText) {
            isWrong = true
          }
        }
        if (isWrong) {
          wrongBlockNames.push(templateBlock && templateBlock.blockName)
          wrongSubjectiveAreas.push({
            pageIndex: page.index,
            area: subjArea,
          })
        }
      })
    })

    return {
      wrongBlockNames,
      wrongSubjectiveAreas,
    }
  }
  compareTemplateSelectGroups() {
    let notSelectBlockNames = []
    this.pages.forEach(page => {
      page.selectMarkAreas.forEach(area => {
        let block = this.subjectBlocks.find(x => x.blockId == area.blockId)
        if (!block) {
          let templateBlock = this.questions.blocks.find(x => x.subjectiveId == area.blockId)
          notSelectBlockNames.push(templateBlock && templateBlock.blockName)
        } else if (!block.selectGroupName) {
          notSelectBlockNames.push(block.blockName)
        }
      })
    })
    return {
      notSelectBlockNames,
    }
  }

  /**
   * 修改题目分数
   */
  changeQuestionScores({ objectives, subjectives }) {
    if (!this.enableEditQuestion) {
      return
    }
    this.subjectObjectives.forEach(obj => {
      let q = objectives.find(x => x.questionCode == obj.questionCode)
      if (q) {
        obj.fullScore = q.fullScore
        obj.branchTypeId = q.branchTypeId
      }
    })
    this.subjectBlocks.forEach(block => {
      block.questions.forEach(subj => {
        let q = subjectives.find(x => x.questionCode == subj.questionCode && x.branchCode == subj.branchCode)
        if (q) {
          subj.fullScore = q.fullScore
        }
      })
    })
  }

  /**
   * 导出题块子图
   */
  async exportBlockSubImagesAsync() {
    if (this.subjectBlocks.length == 0) {
      return []
    }
    let blockAreas = []
    this.subjectBlocks.forEach(block => {
      let areas = []
      this.pages.forEach(page => {
        page.subjectiveAreas.forEach(area => {
          if (area.blockId == block.blockId) {
            areas.push({
              canvas: page.image.canvas,
              rect: area.rect,
            })
          }
        })
      })
      blockAreas.push({
        blockId: block.blockId,
        blockName: block.blockName,
        areas,
      })
    })

    let canvas = document.createElement('canvas')
    let ctx = canvas.getContext('2d')
    for (let block of blockAreas) {
      let blob = null
      if (block.areas.length > 0) {
        let imageWidth = Math.max(...block.areas.map(area => area.rect.width))
        let imageHeight = sumByDefaultZero(block.areas, area => area.rect.height)
        canvas.width = imageWidth
        canvas.height = imageHeight
        ctx.clearRect(0, 0, imageWidth, imageHeight)
        ctx.fillStyle = 'white'
        ctx.fillRect(0, 0, imageWidth, imageHeight)

        let top = 0
        block.areas.forEach(area => {
          let { x, y, width, height } = area.rect
          ctx.drawImage(area.canvas, x, y, width, height, 0, top, width, height)
          top += height
        })
        blob = await canvasToBlob(canvas)
      }
      delete block.areas
      block.blob = blob
    }
    return blockAreas
  }
}
