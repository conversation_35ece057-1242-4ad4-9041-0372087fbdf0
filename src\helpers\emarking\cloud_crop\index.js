import { downloadUrl } from '@/utils/download'

export * from './scan_template'
export * from './room'

export function downloadPaperImages(scanInfo) {
  if (!scanInfo) {
    return
  }
  let admissionNum = scanInfo.admissionNum || scanInfo.zkzh || ''
  let pages = scanInfo.pages || scanInfo.pagerUrls || []
  pages.forEach((page, idx) => {
    let url = typeof page == 'string' ? page : page.pageUrl
    let pageNo = typeof page == 'string' ? idx + 1 : page.pageNo
    if (!url || !pageNo) {
      return
    }
    let suffix = url.substring(url.lastIndexOf('.'))
    downloadUrl(url, `${admissionNum}-${pageNo}${suffix}`)
  })
}
