<template>
  <div class="add-chinese-writing">
    <Form :label-width="80">
      <FormItem label="大题号" class="ivu-form-item-required">
        <Select :model-value="topicCode" class="input-short" @on-change="changeTopicCode">
          <Option v-for="t in topicChineseCodes" :key="t.code" :value="t.code">{{ t.codeInChinese }}</Option>
        </Select>
      </FormItem>
      <FormItem label="大题名" class="ivu-form-item-required">
        <Input v-model="topicName" :disabled="isExistTopic" maxlength="20"></Input>
      </FormItem>
      <FormItem label="题号" class="ivu-form-item-required">
        <InputNumber v-model="questionCode" class="input-short" :min="1" :precision="0"></InputNumber>
      </FormItem>
      <FormItem label="字数" class="ivu-form-item-required">
        <Select v-model="words" class="input-short" clearable>
          <Option v-for="w in wordsList" :key="w.id" :value="w.id">{{ w.name }}</Option>
        </Select>
      </FormItem>
      <FormItem label="分数" class="ivu-form-item-required">
        <InputNumber v-model="score" :min="0" class="input-short"></InputNumber>
      </FormItem>
    </Form>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import SubjectiveQuestion from '@/helpers/emarking/subjective_question'
  import { roundScore } from '@/utils/math'
  import { randomId } from '@/utils/string'
  import { isPositiveInteger, isPositiveFinite, numberToChinese } from '@/utils/number'
  import BranchTypeEnum from '@/enum/qlib/branch_type'
  import { Max_Question_Code } from '@/const/emarking'

  export default {
    props: {
      branchTypeId: Number,
      topicChineseCodes: Array,
    },
    data() {
      let wordsList = []
      for (let i = 1; i <= 30; i++) {
        wordsList.push({
          id: i * 50,
          name: `${i * 50}字`,
        })
      }

      return {
        topicCode: 1,
        topicName: '',
        questionCode: 1,
        words: 900,
        score: null,
        wordsList: wordsList,
      }
    },
    computed: {
      ...mapGetters('answerSheet', ['questionCodes', 'maxQuestionCode', 'topicCodeNames', 'maxTopicCode']),
      isExistTopic() {
        return this.topicCodeNames.some(t => t.topicCode == this.topicCode)
      },
    },
    created() {
      this.topicCode = this.maxTopicCode + 1
      this.topicName = numberToChinese(this.topicCode) + '、' + BranchTypeEnum.getNameById(this.branchTypeId)
      this.questionCode = this.maxQuestionCode + 1
    },
    methods: {
      changeTopicCode(newTopicCode) {
        let oldTopicCode = this.topicCode
        this.topicCode = newTopicCode
        if (this.isExistTopic) {
          this.topicName = this.topicCodeNames.find(t => t.topicCode == this.topicCode).topicName
        } else {
          let oldTopicCodeChinese = numberToChinese(oldTopicCode)
          let newTopicCodeChinese = numberToChinese(newTopicCode)
          this.topicName = this.topicName.replace(new RegExp(`^${oldTopicCodeChinese}、`), newTopicCodeChinese + '、')
        }
      },
      check() {
        if (!this.topicCode) {
          return {
            message: '请选择大题',
          }
        }
        if (!this.topicName) {
          return {
            message: '请输入大题名',
          }
        }
        if (!isPositiveInteger(this.questionCode)) {
          return {
            message: '请输入正确的题号',
          }
        }
        if (this.questionCode > Max_Question_Code) {
          return {
            message: '题号过大',
          }
        }
        if (!this.words) {
          return {
            message: '请选择字数',
          }
        }
        if (this.questionCodes.includes(this.questionCode)) {
          return {
            message: `题号${this.questionCode}已存在`,
          }
        }
        let score = roundScore(this.score)
        if (!score || !isPositiveFinite(score)) {
          return {
            message: '请输入分数',
          }
        }

        let subj = new SubjectiveQuestion()
        subj.questionId = randomId()
        subj.questionCode = this.questionCode
        subj.questionName = `${this.questionCode}`
        subj.branchCode = 0
        subj.branchName = subj.questionName
        subj.topicCode = this.topicCode
        subj.topicName = this.topicName
        subj.fullScore = score

        return {
          message: '',
          data: {
            topicCode: this.topicCode,
            topicName: this.topicName,
            words: this.words,
            subjectiveQuestion: subj,
          },
        }
      },
      handleCreateWordsSelectItem(query) {
        // iview 有检查query若为空则不会调用此 emit 函数
        let numberQuery = Number(query)
        if (isNaN(numberQuery)) {
          numberQuery = Number(query.slice(0, query.length - 1))
          if (isNaN(numberQuery)) {
            this.$Message.warning({
              duration: 4,
              content: '检测到所输入值非法，请重新输入',
            })
            return
          }
        }

        if (!this.wordsList.some(x => x.id === numberQuery)) {
          this.wordsList.push({
            id: numberQuery,
            name: numberQuery + '字',
          })
        }
        setTimeout(() => (this.words = numberQuery), 50)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .input-short {
    width: 100px;
  }
</style>
