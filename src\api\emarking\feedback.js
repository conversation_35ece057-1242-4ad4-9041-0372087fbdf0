import ajax from '@/api/ajax'

export function apiGetFeedbackList({ semesterId, term, gradeId, subjectId, keyword, currentPage, pageSize }) {
  return ajax.get({
    url: 'mark/feedback/list',
    params: {
      semesterId,
      term,
      gradeId,
      subjectId,
      examName: keyword,
      pageNum: currentPage,
      pageSize,
    },
    requestName: '获取错题反馈项目列表',
  })
}

export function apiGetFeedbackScanStatistics(examSubjectId) {
  return ajax.get({
    url: 'mark/feedback/statistics',
    params: {
      examSubjectId: examSubjectId,
    },
    requestName: '获取错题反馈扫描统计',
  })
}

export function apiGetFeedbackStudentsList(requestParams) {
  return ajax.get({
    url: 'mark/feedback/studentList',
    params: {
      examSubjectId: requestParams.examSubjectId,
      sign: requestParams.sign, // 扫描状态： 1 - 已扫描 | 0 - 未扫描
      pageNum: requestParams.currentPage,
      pageSize: requestParams.pageSize,
      keyword: requestParams.keyword,
      classId: requestParams.classId,
    },
    requestName: '获取错题反馈扫描学生列表',
  })
}

export function apiGetBookPapers(coachBookId, onlyOpen = false) {
  return ajax.get({
    url: 'ques/coachBook/papers',
    params: {
      coachBookId,
      onlyOpen,
    },
    requestName: '获取教辅包含试卷',
  })
}

export function apiSetFeedbackPriority(requestParams) {
  return ajax.put({
    url: 'ques/feedback/setFeedbackScanPriority',
    params: {
      examId: requestParams.examId, // String*
      isFeedbackScanPriority: requestParams.isFeedbackScanPriority, // Boolean* 扫描优先（非则为家长公众号标记错题优先）
    },
    requestName: '设置错题反馈项目是否优先选取扫描数据',
  })
}
