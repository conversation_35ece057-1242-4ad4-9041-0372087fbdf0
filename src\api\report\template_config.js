import ajax from '@/api/ajax'

export function apiGetReportTemplateNonCountingStudents(requestParams) {
  return ajax.get({
    url: 'report/tem/config/skipStudents',
    params: {
      templateId: requestParams.templateId, // *String
      schoolId: requestParams.schoolId, // String
      page: requestParams.currentPage || 1, // *Number
      size: requestParams.pageSize || 10, // *Number
    },
    requestName: '获取报表模板不参与统计学生',
  })
}

export function apiSetReportTemplateNonCountingStudents(requestParams) {
  return ajax.post({
    url: 'report/tem/config/skipStudents',
    params: {
      templateId: requestParams.templateId, // *String
      schoolId: requestParams.schoolId, // *String
    },
    data: requestParams.studentIds, // *Array[String]
    requestName: '添加报表模板不参与统计学生',
  })
}

export function apiRemoveReportTemplateNonCountingStudents(requestParams) {
  return ajax.delete({
    url: 'report/tem/config/skipStudents',
    params: {
      templateId: requestParams.templateId, // *String
    },
    data: requestParams.studentIds, // *Array[String]
    requestName: '移除报表模板不参与统计学生',
  })
}

export function apiGetReportTemplateNonCountingSchools(templateId) {
  return ajax.get({
    url: 'report/tem/config/skipSchools',
    params: {
      templateId: templateId, // *String
    },
    requestName: '获取报表模板不参与统计学校',
  })
}

export function apiSetReportTemplateNonCountingSchools(requestParams) {
  return ajax.post({
    url: 'report/tem/config/skipSchools',
    params: {
      templateId: requestParams.templateId, // *String
    },
    data: requestParams.schoolIds, // *Array[String]
    requestName: '添加报表模板不参与统计学校',
  })
}

export function apiRemoveReportTemplateNonContingStudents(requestParams) {
  return ajax.delete({
    url: 'report/tem/config/skipSchools',
    params: {
      templateId: requestParams.templateId, // *String
    },
    data: requestParams.schoolIds, // *Array[String]
    requestName: '移除报表模板不参与统计学校',
  })
}
