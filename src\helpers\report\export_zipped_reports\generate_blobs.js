import iView from '@/iview'

import { REPORTS as reportList } from './reports_enum'

import Store from '@/store/index'

export function generateZippedFileBlobs(exportParams) {
  let requestListByInstitution = []

  if (Store.getters['report/isMultipleSchoolLevel']) {
    if (exportParams.exportBureauReport) {
      let bRequests = []
      let selectedReports = reportList.filter(r => exportParams.exportBureauReport.reportTypes.includes(r.type))
      let totalRelatedReports = selectedReports.filter(r => r.totalRelated)
      let subjectRelatedReports = selectedReports.filter(r => r.subjectRelated)

      totalRelatedReports.forEach(r => bRequests.push({ function: r.function }))
      exportParams.exportBureauReport.subjectTypes.forEach(s =>
        subjectRelatedReports.forEach(r => bRequests.push({ function: r.function, subject: s }))
      )
      requestListByInstitution.push(bRequests)
    }

    if (exportParams.exportNextLevelReport) {
      const NextLevelOrganizations = getInstitutionUnderOrganizations()

      const SelectedReports = reportList.filter(r => exportParams.exportNextLevelReport.reportTypes.includes(r.type))
      const TotalRelatedReports = SelectedReports.filter(r => r.totalRelated)
      const SubjectRelatedReports = SelectedReports.filter(r => r.subjectRelated)
      const OrgTotalRelatedReports = TotalRelatedReports.filter(r => r.mname)
      const SchTotalRelatedReports = TotalRelatedReports.filter(r => r.name)
      const OrgSubjectRelatedReports = SubjectRelatedReports.filter(r => r.mname)
      const SchSubjectRelatedReports = SubjectRelatedReports.filter(r => r.name)

      // TotalRelatedReport
      NextLevelOrganizations.forEach(item => {
        const NRequests = []

        if (item.isOrg) {
          OrgTotalRelatedReports.forEach(r =>
            NRequests.push({
              function: r.function,
              institution: item,
              isNextLevel: true,
            })
          )
          exportParams.exportNextLevelReport.subjectTypes.forEach(subject =>
            OrgSubjectRelatedReports.forEach(r =>
              NRequests.push({
                function: r.function,
                institution: item,
                isNextLevel: true,
                subject: subject,
              })
            )
          )
        } else {
          SchTotalRelatedReports.forEach(r =>
            NRequests.push({
              function: r.function,
              school: {
                schoolId: item.id,
                schoolName: item.name || '',
              },
              isNextLevel: true,
            })
          )
          exportParams.exportNextLevelReport.subjectTypes.forEach(subject =>
            SchSubjectRelatedReports.forEach(r =>
              NRequests.push({
                function: r.function,
                school: {
                  schoolId: item.id,
                  schoolName: item.name || '',
                },
                isNextLevel: true,
                subject: subject,
              })
            )
          )
        }

        requestListByInstitution.push(NRequests)
      })
    }

    if (exportParams.exportSchoolsReport) {
      const Exam = Store.getters['report/exam'] || null
      const ExamSchoolList = ((Exam && Exam.schools) || []).map(s => ({
        schoolId: s.schoolId,
        schoolName: s.schoolName || '??',
      })) // 若是要导出学校报表 即导出所有考试学校的报表 不予选择

      let sRequests = []
      let selectedReports = reportList.filter(r => exportParams.exportSchoolsReport.reportTypes.includes(r.type))
      let totalRelatedReports = selectedReports.filter(r => r.totalRelated)
      let subjectRelatedReports = selectedReports.filter(r => r.subjectRelated)

      ExamSchoolList.forEach(school => {
        sRequests = []
        totalRelatedReports.forEach(r => sRequests.push({ function: r.function, school: school }))
        exportParams.exportSchoolsReport.subjectTypes.forEach(subject =>
          subjectRelatedReports.forEach(r => sRequests.push({ function: r.function, school: school, subject: subject }))
        )
        requestListByInstitution.push(sRequests)
      })
    }
  } else {
    let iRequests = []
    let selectedReports = reportList.filter(r => exportParams.exportSchoolsReport.reportTypes.includes(r.type))
    let totalRelatedReports = selectedReports.filter(r => r.totalRelated)
    let subjectRelatedReports = selectedReports.filter(r => r.subjectRelated)

    totalRelatedReports.forEach(r => iRequests.push({ function: r.function }))
    exportParams.exportSchoolsReport.subjectTypes.forEach(s =>
      subjectRelatedReports.forEach(r => iRequests.push({ function: r.function, subject: s }))
    )
    requestListByInstitution.push(iRequests)
  }

  return filledResultList()

  async function filledResultList() {
    let resultList = []

    console.log('rbyi: ', requestListByInstitution)
    if (requestListByInstitution.length && requestListByInstitution.some(item => item.length)) {
      for (let requests of requestListByInstitution) {
        let results = await Promise.allSettled(
          requests.map(r =>
            r.function(r.subject, r.school, exportParams.excelShowRank, null, null, r.institution, r.isNextLevel)
          )
        )
        results.forEach(result => {
          if (result.status === 'fulfilled') {
            resultList.push(result)
          } else if (result.status === 'rejected') {
            resultList.push({
              status: result.status,
              value: {
                fileName: result.reason.fileName,
                error: result.reason.error.msg,
              },
            })
          }
        })
      }
    } else {
      iView.Message.warning({
        duration: 4,
        content: '无符合所选条件的报告可导出',
      })
    }

    return resultList
  }
}

function getInstitutionUnderOrganizations() {
  const ExamInstitutionTree = Store.getters['report/examInstitutionTree'] || null

  return (
    ExamInstitutionTree && ExamInstitutionTree.nodes && ExamInstitutionTree.nodes.length
      ? ExamInstitutionTree.nodes
      : []
  ).map(item => ({
    id: item.id,
    name: item.name,
    isOrg: item.isOrg,
  }))
}
