import ajax from '@/api/ajax'

/**
 * 查班级分析数据
 */
export function apiGetAiClassAnalysisData({ classId, examId, templateId, subjectId }) {
  return ajax.get({
    url: 'aireport/ai/scoreAnalysis/classAnalysisData',
    params: { classId, examId, templateId, subjectId },
    requestName: '获取班级分析数据',
  })
}

/**
 * 班级Ai分析接口
 */
export const apiAiClassAnalysisUrl = 'aireport/ai/scoreAnalysis/sse/classAnalysis'

/**
 * 查上次班级Ai分析结果
 */
export function apiGetLastAiClassAnalysis({ classId, examId, templateId, subjectId }) {
  return ajax.get({
    url: 'aireport/ai/scoreAnalysis/lastClassAnalysis',
    params: { classId, examId, templateId, subjectId },
  })
}

/**
 * 查学生分析数据
 */
export function apiGetAiStudentAnalysisData({ classId, studentId, examId, templateId, subjectId }) {
  return ajax.get({
    url: 'aireport/ai/scoreAnalysis/studentAnalysisData',
    params: { classId, studentId, examId, templateId, subjectId },
    requestName: '获取学生分析数据',
  })
}

/**
 * 学生Ai分析接口
 */
export const apiAiStudentAnalysisUrl = 'aireport/ai/scoreAnalysis/sse/studentAnalysis'

/**
 * 查上次学生Ai分析结果
 */
export function apiGetLastAiStudentAnalysis({ classId, studentId, examId, templateId, subjectId }) {
  return ajax.get({
    url: 'aireport/ai/scoreAnalysis/lastStudentAnalysis',
    params: { classId, studentId, examId, templateId, subjectId },
  })
}

/**
 * 获取个人AI用量
 */
export function apiGetMyAiUsage() {
  return ajax.get({
    url: 'aireport/ai/scoreAnalysis/myAiUseStatus',
  })
}
