<template>
  <div class="block-fill-blank">
    <div v-if="aiScoreTypeId" class="ai-score-mask no-print" :style="aiScoreMaskStyle">
      <span v-if="isAiKouSuan" :style="aiScoreLabelStyle">口算题智能评分</span>
    </div>

    <com-box-content
      ref="contentBox"
      :content="block.content"
      :width="pageBodyWidth"
      :height="block.contentHeight"
      :border="contentBorderStyle"
      :padding-top="contentPaddingTop"
      :resizable="true"
      @height-change="handleContentHeightChange"
      @resize="handleResize"
      @resize-end="handleResizeEnd"
      @content-change="changeContent"
    ></com-box-content>

    <div class="page-block-toolbar no-print">
      <div class="btn btn-primary" title="设置" @click="showModalChangeType = true">
        <Icon class="btn-icon" type="ios-settings"></Icon>
      </div>
      <div v-if="canSplit" class="btn btn-primary btn-split" title="拆分" @click="handleSplit"></div>
      <div v-if="enableEditQuestion" class="btn btn-error" title="删除" @click="handleDelete">
        <Icon class="btn-icon" type="md-close"></Icon>
      </div>
      <div
        v-if="aiScoreTypeId != 0 || canSetAiScore"
        class="btn btn-primary"
        title="口算题智能评分"
        @click="changeAiScoreTypeKouSuan"
      >
        <span class="icon-ai">AI</span>
      </div>
      <div v-else-if="isPostScan" class="btn btn-primary" title="刷新填空" @click="handleRefreshScoreBox">
        <Icon class="btn-icon" type="md-refresh"></Icon>
      </div>
    </div>

    <div v-if="canMerge" class="merge-area no-print" title="合并" @click="handleMerge"></div>

    <ModalChangeType v-model="showModalChangeType" :question-group="block.questionGroup"></ModalChangeType>
  </div>
</template>
<script>
  import comBoxContent from './box_content.vue'
  import ModalChangeType from './modal_change_type'

  import { mapGetters, mapMutations } from 'vuex'
  import TemplateTrueFalseScoreArea from '@/helpers/scan_template_define/template_true_false_score_area'
  import TemplateRect from '@/helpers/scan_template_define/template_rect'
  import ScoreTypeEnum from '@/enum/answer_sheet/score_type'
  import AIScoreTypeEnum from '@/enum/emarking/ai_score_type'
  import ModuleEnum from '@/enum/user/module'
  import { TrueFalseBoxCssClass } from '@/const/answer_sheet'

  export default {
    components: {
      comBoxContent,
      ModalChangeType,
    },
    props: {
      block: {
        type: Object,
        required: true,
      },
    },
    emits: ['height-change'],
    data() {
      return {
        // 修改题型
        showModalChangeType: false,
      }
    },
    computed: {
      ...mapGetters('answerSheet', [
        'isPostScan',
        'enableEditQuestion',
        'showQuestionContent',
        'scoreTypeTrueFalseEmptyDefault',
      ]),
      questionGroupId() {
        return this.block.questionGroup.id
      },
      pageBodyWidth() {
        let pageSize = this.$store.getters['answerSheet/pageSize'](this.block.pageIndex)
        return pageSize.width - pageSize.left - pageSize.right
      },
      contentBorderStyle() {
        return this.$store.getters['answerSheet/getShowBoxContentBorder'](this.block)
      },
      contentPaddingTop() {
        if (this.showQuestionContent) {
          return 2
        }
        return 14
      },
      halfScore() {
        return this.block.questionGroup.scoreType.halfScore
      },

      /**
       * 合并拆分
       */
      canMerge() {
        return (
          this.block.part == 0 &&
          this.$store.getters['answerSheet/getCanMergeQuestionGroup'](this.block.questionGroup.id)
        )
      },
      canSplit() {
        return (
          this.block.part == 0 &&
          this.$store.getters['answerSheet/getCanSplitQuestionGroup'](this.block.questionGroup.id)
        )
      },

      /**
       * 智能评分
       */
      isAiScoreModuleNormal() {
        return this.$store.getters['user/isModuleNormal'](ModuleEnum.AIScore.id)
      },
      isAiScoreModuleExpired() {
        return this.$store.getters['user/isModuleExpired'](ModuleEnum.AIScore.id)
      },
      aiScoreTypeId() {
        return this.block.questionGroup.aiScoreTypeId
      },
      isAiKouSuan() {
        return this.aiScoreTypeId == AIScoreTypeEnum.KouSuan.id
      },
      canSetAiScore() {
        return this.$store.getters['answerSheet/getCanSetAiScore'](this.questionGroupId)
      },
      aiScoreMaskStyle() {
        if (this.isAiKouSuan && this.block.questionGroup.questions.length > 1) {
          return {
            zIndex: 1,
          }
        }
        return null
      },
      aiScoreLabelStyle() {
        if (!this.isAiKouSuan) {
          return null
        }
        // 如果有材料，则标记向上移
        let originalQuestion = this.block.questionGroup.questions[0].originalQuestion
        if (originalQuestion && originalQuestion.trunk) {
          return {
            marginTop: '-30px',
          }
        }
        return null
      },
    },
    created() {
      this.changeInstance()
    },
    mounted() {
      this.changeHeight()
    },
    updated() {
      this.changeInstance()
    },
    methods: {
      ...mapMutations('answerSheet', [
        'changePageBlockInstance',
        'changePageBlockContent',
        'changePageBlockHeight',
        'setFillBlankQuestionGroupScoreBox',
      ]),

      /**
       * 实例、内容、高度
       */
      changeInstance() {
        this.changePageBlockInstance({
          blockId: this.block.id,
          instance: this,
        })
      },
      changeContent(content) {
        this.changePageBlockContent({
          blockId: this.block.id,
          content: content,
        })
      },
      changeHeight(contentHeight = this.block.contentHeight, emitEvent) {
        let height = contentHeight
        let heightChanged = height != this.block.height
        this.changePageBlockHeight({
          blockId: this.block.id,
          height,
          contentHeight,
        })
        if (emitEvent || (emitEvent == null && heightChanged)) {
          this.$emit('height-change', height)
        }
      },
      handleContentHeightChange(contentHeight) {
        this.changeHeight(contentHeight)
      },
      // 调整高度过程中不重排
      handleResize(contentHeight) {
        this.changeHeight(contentHeight, false)
      },
      handleResizeEnd() {
        this.changeHeight(this.contentHeight, true)
      },

      /**
       * 操作
       */
      handleDelete() {
        let questionCodes = Array.from(new Set(this.block.questionGroup.questions.map(q => q.questionCode)))
        this.$Modal.confirm({
          title: '确认删除',
          content: `您将删除第${questionCodes.join('、')}题`,
          onOk: () => {
            this.$store.commit('answerSheet/deleteSubjectiveQuestionGroup', this.block.questionGroup.id)
            this.$store.commit('answerSheet/generatePages')
          },
        })
      },
      handleMerge() {
        this.$Modal.confirm({
          title: '合并题块',
          content: '合并后的区域内容将重新生成',
          onOk: () => {
            this.$store.commit('answerSheet/mergeQuestionGroup', this.block.questionGroup.id)
            this.$store.commit('answerSheet/generatePages')
          },
        })
      },
      handleSplit() {
        this.$Modal.confirm({
          title: '拆分题块',
          content: '拆分后的各区域内容将重新生成',
          onOk: () => {
            this.$store.commit('answerSheet/splitQuestionGroup', this.block.questionGroup.id)
            this.$store.commit('answerSheet/generatePages')
          },
        })
      },
      handleRefreshScoreBox() {
        this.$store.commit('answerSheet/setFillBlankQuestionGroupScoreBox', this.block.questionGroup.id)
      },

      /**
       * 拆分
       */
      split(getNextPageAvailableHeightFunc) {
        let blocks
        try {
          blocks = this.$refs['contentBox'].split(getNextPageAvailableHeightFunc, this.block.questionGroup)
        } catch {
          blocks = []
        }
        return blocks
      },

      /**
       * 智能评分
       */
      changeAiScoreTypeKouSuan() {
        if (!this.isAiScoreModuleNormal) {
          this.$Modal.warning({
            title: this.isAiScoreModuleExpired ? '智能阅卷服务已过期' : '尚未开通智能阅卷服务',
            content: `请联系我们申请${this.isAiScoreModuleExpired ? '继续使用' : '开通'}<br>服务电话：020-32061103`,
          })
          return
        }
        let aiScoreTypeId = 0
        if (this.aiScoreTypeId == 0) {
          aiScoreTypeId = AIScoreTypeEnum.KouSuan.id
        }
        this.$store.commit('answerSheet/changeAiScoreType', {
          questionGroupId: this.questionGroupId,
          aiScoreTypeId,
        })
        this.$store.commit('answerSheet/generatePages')
      },

      /**
       * 扫描模板
       */
      getScoreAreas(areaLeft, areaTop) {
        if (!this.isPostScan) {
          return []
        }
        let elRect = this.$el.getBoundingClientRect()
        let squares = this.$el.querySelectorAll(`.${TrueFalseBoxCssClass}`)
        let list = []
        Array.from(squares).forEach(s => {
          let { questionCode, branchCode, blankIndex } = s.dataset
          let scoreArea = new TemplateTrueFalseScoreArea()
          scoreArea.id = `TrueFalseScoreArea-${questionCode}.${branchCode}.${blankIndex}`
          scoreArea.emptyDefault = this.scoreTypeTrueFalseEmptyDefault
          scoreArea.enableHalfScore = false

          let range = document.createRange()
          range.selectNodeContents(s)
          let squareRect = range.getBoundingClientRect()
          let left = squareRect.left - elRect.left + areaLeft
          let top = squareRect.top - elRect.top + areaTop
          let width = squareRect.width
          let height = squareRect.height

          scoreArea.searchArea = TemplateRect.createFromAnswerSheetPageRect(left, top, width, height)
          scoreArea._extra = {
            scoreType: ScoreTypeEnum.TrueFalse.id,
            questionCode: Number(questionCode),
            branchCode: Number(branchCode),
            blankIndex: Number(blankIndex),
          }
          list.push(scoreArea)
        })
        return list
      },
      getKouSuanQuestionRects() {
        if (!this.isAiKouSuan) {
          return []
        }
        if (this.block.questionGroup.questions.length <= 1) {
          return []
        }
        let blockRect = this.$el.getBoundingClientRect()
        let branches = this.$el.querySelectorAll('.kousuan-branch')
        return Array.from(branches).map(node => {
          let rect = node.getBoundingClientRect()
          return TemplateRect.createFromAnswerSheetPageRect(
            rect.x - blockRect.x,
            rect.y - blockRect.y,
            rect.width,
            rect.height
          )
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-block-toolbar {
    .tbtn {
      display: block;
    }
  }

  .ai-score-mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;

    span {
      position: absolute;
      top: 10px;
      right: 10px;
      color: $color-disabled;
      font-size: 20px;
      line-height: 1;
    }
  }

  .icon-ai {
    font-size: 14px;
    font-family: 'PingFang SC', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  }

  :deep(.true-false-score-box) {
    position: relative;
    display: inline-block;

    &::after {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #aaa;
      font-size: 12px;
      opacity: 0.7;
      content: attr(data-blank-info);
      pointer-events: none;
    }

    &.print::after {
      display: none;
    }
  }
</style>
