{
  "editor.tabSize": 2,
  "editor.formatOnPaste": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "eslint.run": "onSave",
  "stylelint.validate": ["css", "postcss", "less", "scss", "vue"],
  "problems.showCurrentInStatus": true,
  "files.associations": {
    "*.wxss": "css",
    "*.wxs": "javascript",
    "*.wxml": "html"
  },
  "minapp-vscode.disableAutoConfig": true,
  "vue.codeActions.enabled": false,
}