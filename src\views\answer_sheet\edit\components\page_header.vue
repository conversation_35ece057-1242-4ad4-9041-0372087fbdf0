<template>
  <div class="page-header" :style="pageHeaderStyle">
    <div v-if="showAnchorTopLeft" :style="anchorTopLeftStyle"></div>
    <div v-if="showAnchorTopRight" :style="anchorTopRightStyle"></div>
  </div>
</template>

<script>
  import TemplateRect from '@/helpers/scan_template_define/template_rect'
  import TemplateMarkArea from '@/helpers/scan_template_define/template_mark_area'

  export default {
    props: {
      page: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        // 定位点
        anchorWidth: 20,
        anchorHeight: 16,
        // 定位点与页面左上角、右上角的距离
        anchorMargin: 28,
      }
    },
    computed: {
      pageSize() {
        return this.$store.getters['answerSheet/pageSize'](this.page.pageIndex)
      },
      // 页头样式
      pageHeaderStyle() {
        return {
          position: 'absolute',
          left: this.pageSize.left + 'px',
          top: 0,
          width: this.pageSize.contentWidth + 'px',
          height: this.pageSize.top + 'px',
        }
      },
      // 每面第一栏显示左上定位点
      showAnchorTopLeft() {
        return this.pageSize.sideColumnIndex == 0
      },
      anchorTopLeftStyle() {
        return {
          position: 'absolute',
          left: `-${this.pageSize.left - this.anchorMargin}px`,
          top: this.anchorMargin + 'px',
          width: this.anchorWidth + 'px',
          height: this.anchorHeight + 'px',
          backgroundColor: 'black',
        }
      },
      // 每面最后一栏显示右上定位点
      showAnchorTopRight() {
        return this.pageSize.sideColumnIndex == this.pageSize.sideColumnCount - 1
      },
      anchorTopRightStyle() {
        return {
          position: 'absolute',
          right: `-${this.pageSize.right - this.anchorMargin}px`,
          top: this.anchorMargin + 'px',
          width: this.anchorWidth + 'px',
          height: this.anchorHeight + 'px',
          backgroundColor: 'black',
        }
      },
    },
    created() {
      this.changeInstance()
    },
    updated() {
      this.changeInstance()
    },
    methods: {
      changeInstance() {
        this.$store.commit('answerSheet/changePageHeaderInstance', {
          pageIndex: this.page.pageIndex,
          instance: this,
        })
      },
      // 获取扫描模板元素
      getTemplateElements(pageLeft) {
        let data = {}
        if (this.showAnchorTopLeft) {
          let rect = TemplateRect.createFromAnswerSheetPageRect(
            pageLeft + this.anchorMargin,
            this.anchorMargin,
            this.anchorWidth,
            this.anchorHeight
          )
          data.anchorTopLeft = TemplateMarkArea.create('AnchorTopLeft', rect)
        }
        if (this.showAnchorTopRight) {
          let rect = TemplateRect.createFromAnswerSheetPageRect(
            pageLeft + this.pageSize.width - this.anchorMargin - this.anchorWidth,
            this.anchorMargin,
            this.anchorWidth,
            this.anchorHeight
          )
          data.anchorTopRight = TemplateMarkArea.create('AnchorTopRight', rect)
        }
        return data
      },
    },
  }
</script>
