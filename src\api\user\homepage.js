import ajax from '@/api/ajax'

export function apiGetRegionGeneralInfo(params) {
  return ajax
    .get({
      url: '/user/homepage/regionGeneralInfo',
      params: params,
      requestName: '获取区域概况',
    })
    .then(data => {
      return data
    })
}

export function apiGetRegionSchoolInfo(params) {
  return ajax
    .get({
      url: '/user/homepage/schoolSegmentDistribution',
      params: params,
      requestName: '获取区域学校概况',
    })
    .then(data => {
      return data
    })
}

export function apiGetRegionTeacherInfoBySubject(params) {
  return ajax
    .get({
      url: '/user/homepage/subjectDistribution',
      params: params,
      requestName: '获取区域教师概况',
    })
    .then(data => {
      return data
    })
}

export function apiGetRegionTeacherInfoByGrade(params) {
  return ajax
    .get({
      url: '/user/homepage/subjectGradeDistribution',
      params: params,
      requestName: '获取区域教师概况',
    })
    .then(data => {
      return data
    })
}

export function apiGetRegionTeacherInfoByLevel(params) {
  return ajax
    .get({
      url: '/user/homepage/regionTeacherDetail',
      params: params,
      requestName: '获取区域教师概况',
    })
    .then(data => {
      return data
    })
}

export function apiGetRegionSchoolTableData(params) {
  return ajax
    .get({
      url: '/user/homepage/educationUnitInfo',
      params: params,
      requestName: '获取区域学校数据',
    })
    .then(data => {
      return data
    })
}

export function apiGetRegionTeacherTableData(params) {
  return ajax
    .get({
      url: '/user/homepage/queryTeacherDistribution',
      params: params,
      requestName: '获取区域教师数据',
    })
    .then(data => {
      return data
    })
}

export function apiGetRegionStudentTableData(params) {
  return ajax
    .get({
      url: '/user/homepage/queryStudentDistribution',
      params: params,
      requestName: '获取区域学生数据',
    })
    .then(data => {
      return data
    })
}

export function apiGetRegionStudentInfoByGrade(params) {
  return ajax
    .get({
      url: '/user/homepage/studentSubjectGradeDistribution',
      params: params,
      requestName: '获取区域学生概况',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheEduExamTemplates(params) {
  return ajax
    .get({
      url: '/report/situations/eduExamTemplates',
      params: params,
      requestName: '获取教育局考试模板',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheEduExamSchoolCompare(params) {
  return ajax
    .get({
      url: '/report/situations/eduExamSchoolCompare',
      // data: params.templateIds,
      params: {
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        templateIds: params.templateIds.join(','),
      },
      requestName: '获取教育局考试对比',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheTeacherHomeNum() {
  return ajax
    .get({
      url: '/report/homePage/moduleStatistic',
      params: {},
      requestName: '获取教师首页右边入口',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheTeacherExams() {
  return ajax
    .get({
      url: '/report/homePage/examScore',
      params: {},
      requestName: '获取教师所教年级班级最近一次可见考试',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheTeacherExamDetails(params) {
  return ajax
    .get({
      url: '/report/homePage/examScoreDetail',
      params: params,
      requestName: '获取教师所教年级班级最近一次可见考试详情',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheLearningSituation(params) {
  return ajax
    .get({
      url: '/report/homePage/learningSituation',
      params: params,
      requestName: '获取教师所教年级班级最近一次可见考试学情',
    })
    .then(data => {
      return data
    })
}
