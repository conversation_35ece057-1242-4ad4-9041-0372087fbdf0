<template>
  <div class="container-create-new-test">
    <div v-if="noPermission" class="no-permissions">抱歉，您尚无任教班级，不能新建测练</div>
    <div v-else class="form-panel">
      <div class="title">新建测练</div>

      <Alert v-if="!isServiceActive" class="alert-service" type="warning" show-icon>{{ alertContent }}</Alert>

      <Form class="form" :label-width="120" label-position="left">
        <FormItem label="测练年级">
          <component-radio-group
            class="component-radio-group-grade"
            :radioes="gradeList"
            :model-value="gradeId"
            @update:model-value="selectGrade"
          ></component-radio-group>
        </FormItem>
        <FormItem label="测练科目">
          <component-radio-group
            class="component-radio-group-subject"
            :radioes="subjectList"
            :model-value="subjectId"
            @update:model-value="selectSubject"
          ></component-radio-group>
        </FormItem>

        <FormItem label="测练班级">
          <div class="exam-classes-panel">
            <div v-if="selectedGradeClasses.normalClasses.length" class="normal-classes">
              <div class="oper-panel">
                <span v-if="gradeId > 9" class="class-title">行政班</span>
                <Checkbox
                  v-if="selectedGradeClasses.normalClasses.length > 1"
                  :indeterminate="checkExamNormalClassesIndeterminate"
                  :model-value="checkExamNormalClassesAll"
                  @click.prevent="handleCheckAllExamNormalClasses"
                  >全选</Checkbox
                >
              </div>
              <div class="list-panel">
                <CheckboxGroup v-model="examNormalClassIds">
                  <Checkbox
                    v-for="cls of selectedGradeClasses.normalClasses"
                    :key="cls.classId"
                    class="class-item"
                    :label="cls.classId"
                    >{{ cls.className }}</Checkbox
                  >
                </CheckboxGroup>
              </div>
            </div>
            <div v-if="selectedGradeClasses.teachClasses.length" class="teach-classes">
              <div class="oper-panel">
                <span v-if="gradeId > 9" class="class-title">教学班</span>
                <Checkbox
                  v-if="selectedGradeClasses.teachClasses.length > 1"
                  :indeterminate="checkExamTeachClassesIndeterminate"
                  :model-value="checkExamTeachClassesAll"
                  @click.prevent="handleCheckAllExamTeachClasses"
                  >全选</Checkbox
                >
              </div>
              <div class="list-panel">
                <CheckboxGroup v-model="examTeachClassIds">
                  <Checkbox
                    v-for="cls of selectedGradeClasses.teachClasses"
                    :key="cls.classId"
                    class="class-item"
                    :label="cls.classId"
                    >{{ cls.className }}</Checkbox
                  >
                </CheckboxGroup>
              </div>
            </div>
            <div
              v-if="!selectedGradeClasses.normalClasses.length && !selectedGradeClasses.teachClasses.length"
              style="color: gray"
            >
              当前所选择的年级科目无可选班级
            </div>
          </div>
        </FormItem>

        <FormItem label="答题卡模板">
          <Select
            class="form-item-answer-sheet"
            :model-value="answerSheetId"
            transfer
            clearable
            filterable
            @on-change="handleSelectAnswerSheetChange"
          >
            <Option v-for="op of answerSheetList" :key="op.id" :value="op.id">{{ op.name }}</Option>
          </Select>
          <TextButton v-if="!answerSheetList.length" type="primary" @click="handleBtnCreateNewAnswerSheetClick"
            >新建答题卡</TextButton
          >
          <span v-show="answerSheetId">（总分：{{ answerSheetFullScore }} 分）</span>
          <TextButton v-if="answerSheetId" type="primary" @click="openAnswerSheet">查看</TextButton>
        </FormItem>
        <FormItem v-if="answerSheetId && objectives.length" label="客观题设置">
          <span>客观题相关设置{{ objectivesSetted ? '已完成' : '未完成' }}</span>
          <span>（客观题总分：{{ objectivesScore }} 分）</span>
          <TextButton :type="objectivesSetted ? 'primary' : 'warning'" @click="handleTbtnShowModalObjectivesClick"
            >查看</TextButton
          >
        </FormItem>
        <FormItem v-if="answerSheetId && subjectives.length" label="主观题设置">
          <span>主观题相关设置{{ subjectivesSetted ? '已完成' : '未完成' }}</span>
          <span>（主观题总分：{{ subjectivesScore }} 分）</span>
          <TextButton :type="subjectivesSetted ? 'primary' : 'warning'" @click="handleTbtnShowModalSubjectivesClick"
            >查看</TextButton
          >
        </FormItem>
        <FormItem label="班级测练名称">
          <Input
            v-model="testName"
            class="input-exam-name"
            clearable
            maxlength="35"
            @on-change="autoBuildTestName = false"
          />
        </FormItem>
      </Form>

      <div class="footer-btns">
        <Button type="text" :disabled="disableCreate" @click="backToPageTestList">取消</Button>
        <Button
          v-if="isServiceActive"
          type="primary"
          :loading="disableCreate"
          @click="handleBtnConfirmCreateNewTestClick"
          >确定</Button
        >
      </div>
    </div>

    <component-modal-objectives
      v-model="modalObjectivesShowed"
      :objectives="objectives"
      :test-name="testName"
      @change-objectives="refreshObjectives"
    ></component-modal-objectives>
    <component-modal-subjectives
      v-model="modalSubjectivesShowed"
      :subjectives="subjectives"
      :enable-change-score="!isPostScan"
      @change-subjectives="refreshSubjectives"
    ></component-modal-subjectives>

    <Modal v-model="modalAnswerSheetPicture" :title="picAnswerSheetTitle" width="80" footer-hide mask-closable>
      <div ref="modalImagePanel" class="modal-answer-sheet-picture-container">
        <img
          v-for="(url, urlIndex) of picAnswerSheetUrls"
          :key="'url' + urlIndex"
          :src="url"
          class="answer-sheet-image"
        />
      </div>
    </Modal>
  </div>
</template>

<script>
  import ComponentRadioGroup from '@/components/radio_group'
  import ComponentModalObjectives from '../components/modal_objectives'
  import ComponentModalSubjectives from '../components/modal_subjectives'

  import { apiCreateClassPractise } from '@/api/emarking'
  import { apiGetAnswerSheetDetail, apiGetMyAnswerSheetList } from '@/api/emarking/answer_sheet'

  import { nextTick } from 'vue'
  import ObjectiveQuestion from '@/helpers/emarking/objective_question'
  import SubjectiveQuestion from '@/helpers/emarking/subjective_question'
  import { formatDate } from '@/utils/date'
  import ModuleEnum from '@/enum/user/module'
  import ModeEnum from '@/enum/answer_sheet/mode'

  export default {
    components: {
      'component-radio-group': ComponentRadioGroup,
      'component-modal-objectives': ComponentModalObjectives,
      'component-modal-subjectives': ComponentModalSubjectives,
    },

    data() {
      return {
        modalObjectivesShowed: false,
        modalSubjectivesShowed: false,
        modalBlocksShowed: false,
        disableCreate: false,

        // request params
        testDate: ['', ''],
        gradeId: null,
        subjectId: null,
        answerSheetId: '',
        testName: '',

        // data list
        answerSheetList: [],
        blocks: [],
        objectives: [],
        subjectives: [],
        isPostScan: false,

        autoBuildTestName: true,

        // 已选择的行政班ID
        examNormalClassIds: [],
        // 已选择的教学班ID
        examTeachClassIds: [],

        // 预览答题卡
        modalAnswerSheetPicture: false,
        picAnswerSheetTitle: '答题卡预览',
        picAnswerSheetUrls: [],
      }
    },

    computed: {
      isServiceActive() {
        return this.$store.getters['user/isModuleNormal'](ModuleEnum.ClassPractise.id)
      },

      alertContent() {
        if (this.$store.getters['user/isModuleExpired'](ModuleEnum.ClassPractise.id)) {
          let serviceEndDate = this.$store.getters['user/moduleEndDate'](ModuleEnum.ClassPractise.id)
          return `您的${ModuleEnum.ClassPractise.name}已于 ${serviceEndDate} 到期`
        } else {
          return `您尚未开通${ModuleEnum.ClassPractise.name}`
        }
      },

      isSchoolAdministrator() {
        return this.$store.getters['user/isSchoolAdministrator']
      },

      teachInfos() {
        return this.$store.state.user.teachings || []
      },

      noPermission() {
        return !this.isSchoolAdministrator && !this.teachInfos.length
      },

      gradeClassSubjects() {
        let schoolStageGradeClassSubjects = this.$store.state.school.schoolStageGradeClassSubjects
        let result = []

        schoolStageGradeClassSubjects.forEach(stage => {
          if (stage.grades) {
            result = result.concat(stage.grades)
          }
        })
        return result.filter(r => r.classes.length)
      },

      gradeList() {
        let allGrades = this.gradeClassSubjects.map(x => ({
          ...x,
          id: x.gradeId,
          name: x.gradeName,
        }))

        if (this.isSchoolAdministrator) {
          return allGrades
        }
        return allGrades.filter(x => this.teachInfos.some(y => y.gradeId === x.gradeId))
      },

      subjectList() {
        let gradeSubjects = (this.gradeList.find(x => x.id === this.gradeId) || { subjects: [] }).subjects.map(s => ({
          ...s,
          id: s.subjectId,
          name: s.subjectName,
        }))
        if (this.isSchoolAdministrator) {
          return gradeSubjects
        } else {
          return gradeSubjects.filter(x =>
            this.teachInfos.some(y => y.subjectId === x.id && y.gradeId === this.gradeId)
          )
        }
      },

      // 本校所有班级列表
      schoolClassesByGrade() {
        let schoolStages = this.$store.state.school.schoolStageGradeClassSubjects || []

        let gradeClasses = []

        schoolStages.forEach(stage => {
          if (stage.grades) {
            stage.grades.forEach(grade => {
              let translatedGradeClasses = (grade.classes || []).map(cls => ({
                classId: cls.classId,
                className: cls.className,
                teachingSubjectIds: (cls.teachingSubjects || []).map(ts => ts.id),
                isTeachClasses: !!cls.classType,
              }))
              gradeClasses.push({
                gradeId: grade.gradeId,
                gradeName: grade.gradeName,
                normalClasses: translatedGradeClasses.filter(x => !x.isTeachClasses),
                teachClasses: translatedGradeClasses.filter(x => x.isTeachClasses),
              })
            })
          }
        })

        return gradeClasses
      },

      selectedGradeClasses() {
        let normalClasses = []
        let teachClasses = []
        let grade = this.schoolClassesByGrade.find(x => x.gradeId === this.gradeId)
        if (grade) {
          normalClasses = grade.normalClasses.slice()
          teachClasses = grade.teachClasses.slice()
        }

        teachClasses = teachClasses.filter(cls => cls.teachingSubjectIds.includes(this.subjectId))

        if (this.isSchoolAdministrator) return { normalClasses, teachClasses }
        return {
          normalClasses: normalClasses.filter(cls =>
            this.teachInfos.some(t => t.classId === cls.classId && t.subjectId === this.subjectId)
          ),
          teachClasses: teachClasses.filter(cls =>
            this.teachInfos.some(t => t.classId === cls.classId && t.subjectId === this.subjectId)
          ),
        }
      },
      // 考试班级全选联动
      checkExamNormalClassesIndeterminate() {
        return (
          this.examNormalClassIds.length > 0 &&
          this.examNormalClassIds.length < this.selectedGradeClasses.normalClasses.length
        )
      },
      checkExamNormalClassesAll() {
        return this.examNormalClassIds.length === this.selectedGradeClasses.normalClasses.length
      },
      checkExamTeachClassesIndeterminate() {
        return (
          this.examTeachClassIds.length > 0 &&
          this.examTeachClassIds.length < this.selectedGradeClasses.teachClasses.length
        )
      },
      checkExamTeachClassesAll() {
        return this.examTeachClassIds.length === this.selectedGradeClasses.teachClasses.length
      },

      answerSheetFullScore() {
        return this.objectivesScore + this.subjectivesScore
      },

      objectivesScore() {
        return this.objectives.reduce((acc, cur) => acc + cur.fullScore, 0)
      },

      objectivesSetted() {
        return this.objectives.every(x => x.fullScore > 0 && x.standardAnswer)
      },

      subjectivesScore() {
        return this.subjectives.reduce((acc, cur) => acc + cur.fullScore, 0)
      },

      subjectivesSetted() {
        return this.subjectives.every(x => x.fullScore > 0)
      },
    },

    watch: {
      gradeList: {
        handler() {
          if (this.gradeList.length && !this.gradeId) {
            this.gradeId = this.gradeList[0].id
          }
        },
        immediate: true,
      },

      gradeId: {
        handler() {
          this.examNormalClassIds = []
          this.examTeachClassIds = []

          if (!this.subjectId || (this.subjectList.length && !this.subjectList.some(x => x.id === this.subjectId))) {
            this.subjectId = this.subjectList[0].id
          }

          this.buildAutoTestName()
          this.fetchAnswerSheetList()
        },
        immediate: true,
      },

      subjectId() {
        this.examNormalClassIds = this.examNormalClassIds.filter(id =>
          this.selectedGradeClasses.normalClasses.some(c => c.classId === id)
        )
        this.examTeachClassIds = this.examTeachClassIds.filter(id =>
          this.selectedGradeClasses.teachClasses.some(c => c.classId === id)
        )
        this.fetchAnswerSheetList()
        this.buildAutoTestName()
      },
    },

    created() {
      // init time
      let beginDate = new Date()
      let endDate = new Date(beginDate.getTime() + 1000 * 60 * 60 * 24 * 7)
      this.testDate = [beginDate, endDate]

      this.testName = formatDate(beginDate) + ' 班级测练' // default test name
    },

    methods: {
      fetchAnswerSheetList() {
        apiGetMyAnswerSheetList({
          // mode: 2, // 模式限定在手阅
          status: 0,
          stageId: this.gradeId ? (this.gradeId < 7 ? 1 : this.gradeId < 10 ? 2 : 4) : undefined,
          subjectId: this.subjectId || undefined,
        }).then(response => (this.answerSheetList = response || []))
      },

      fetchDetailAnswerSheet(id) {
        apiGetAnswerSheetDetail(id).then(response => {
          this.picAnswerSheetTitle = '答题卡预览'
          this.blocks = (response.blocksJson && JSON.parse(response.blocksJson)) || []
          this.objectives = (response.objectivesJson && JSON.parse(response.objectivesJson)) || []
          this.subjectives = []
          this.blocks.forEach(block => (this.subjectives = this.subjectives.concat(block.questions || [])))
          this.objectives.forEach(q => Object.setPrototypeOf(q, ObjectiveQuestion.prototype))
          this.subjectives.forEach(q => Object.setPrototypeOf(q, SubjectiveQuestion.prototype))
          this.picAnswerSheetUrls = response.imageUrls.split(',')
          this.picAnswerSheetTitle += '（' + response.name + '）'
          this.isPostScan = ModeEnum.PostScan.id == response.mode
        })
      },

      selectGrade(id) {
        this.gradeId = id
        this.answerSheetId = ''
        this.answerSheetList = []
      },

      selectSubject(id) {
        this.subjectId = id
      },

      refreshObjectives(modalObjectives) {
        this.objectives = modalObjectives || []
        this.iViewMessageSuccess()
      },

      refreshSubjectives(modalSubjectives) {
        this.subjectives = modalSubjectives || []
        this.iViewMessageSuccess()
      },

      backToPageTestList() {
        this.$router.back()
      },

      checkRequestParams() {
        if (!(this.gradeId && this.subjectId)) {
          return '请选择要进行测练的年级和科目'
        }
        if (!this.examNormalClassIds.length && !this.examTeachClassIds.length) {
          return '请选择要进行测练的班级'
        }

        if (!this.answerSheetId) {
          return '请选择测练要使用的答题卡'
        }
        if (!this.objectivesSetted) {
          return '请先完成答题卡客观题设置'
        }
        if (!this.subjectivesSetted) {
          return '请先完成答题卡主观题设置'
        }
        if (!this.testName) {
          return '班级测练名称不能为空'
        }

        return ''
      },

      // handle
      handleBtnConfirmCreateNewTestClick() {
        let checkWarning = this.checkRequestParams()
        if (checkWarning) {
          this.$Message.warning({
            duration: 4,
            content: checkWarning,
          })
          return
        }

        if (this.disableCreate) {
          return
        }
        this.disableCreate = true
        let objectives = this.objectives.map(obj => obj.export())
        let subjectives = this.subjectives.map(subj => subj.export())
        apiCreateClassPractise({
          answerSheetId: this.answerSheetId,
          beginTime: formatDate(this.testDate[0]),
          endTime: formatDate(this.testDate[1]),
          examName: this.testName,
          subjectId: this.subjectId,
          gradeId: this.gradeId,
          classIds: this.examNormalClassIds.concat(this.examTeachClassIds),
          objectives,
          subjectives,
          blocks: this.blocks.map(x => {
            return {
              blockId: x.blockId,
              blockName: x.blockName,
              subjectives: subjectives.filter(y =>
                x.questions.some(z => z.questionCode == y.questionCode && z.branchCode == y.branchCode)
              ),
              aiScoreType: x.aiScoreTypeId,
              aiScoreExtraJson: x.aiScoreExtraJson,
            }
          }),
        })
          .then(() => {
            this.$Message.success({
              duration: 1,
              content: '新建测练成功',
              onClose: () => {
                this.backToPageTestList()
              },
            })
          })
          .catch(err => {
            this.$Modal.warning({
              title: '注意',
              content: err.msg || err.message,
            })
          })
          .finally(() => {
            this.disableCreate = false
          })
      },

      handleBtnCreateNewAnswerSheetClick() {
        this.$router.push({
          name: 'answerSheet-list',
        })
      },

      openAnswerSheet() {
        if (!this.answerSheetId) {
          this.$Message.warning({
            content: '未选择答题卡',
            duration: 4,
          })
          return
        }
        this.modalAnswerSheetPicture = true
        nextTick().then(() => this.$refs.modalImagePanel.scrollTo(0, 0))
      },

      handleSelectAnswerSheetChange(id) {
        if (this.answerSheetId !== id) {
          this.answerSheetId = id
          if (this.answerSheetId) {
            this.fetchDetailAnswerSheet(id)
          }
        }
      },

      handleTbtnShowModalObjectivesClick() {
        this.modalObjectivesShowed = true
      },

      handleTbtnShowModalSubjectivesClick() {
        this.modalSubjectivesShowed = true
      },

      handleTbtnShowModalBlocksClick() {
        this.modalBlocksShowed = true
      },

      iViewMessageSuccess() {
        this.$Message.success({
          duration: 3,
          content: '设置成功',
        })
      },

      buildAutoTestName() {
        if (!this.autoBuildTestName) {
          return
        }

        let gradeName = this.gradeId ? this.gradeList.find(x => x.id === this.gradeId).name : ''
        let subjectName = this.subjectId ? this.subjectList.find(x => x.id === this.subjectId).name : ''

        this.testName = formatDate(new Date()) + ' ' + gradeName + subjectName + '班级测练'
      },

      handleCheckAllExamNormalClasses() {
        if (this.checkExamNormalClassesAll) {
          this.examNormalClassIds = []
        } else {
          this.examNormalClassIds = this.selectedGradeClasses.normalClasses.map(x => x.classId)
        }
      },
      handleCheckAllExamTeachClasses() {
        if (this.checkExamTeachClassesAll) {
          this.examTeachClassIds = []
        } else {
          this.examTeachClassIds = this.selectedGradeClasses.teachClasses.map(x => x.classId)
        }
      },
      changeCheckSubject(subjectId, checked) {
        let index = this.examSubjectIds.indexOf(subjectId)
        if (checked && index < 0) {
          this.examSubjectIds.push(subjectId)
        } else if (!checked && index >= 0) {
          this.examSubjectIds.splice(index, 1)
        }

        if (this.examGradeId > 9) {
          this.autoSelectTeachClasses()
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-create-new-test {
    .form-panel {
      padding: 20px;
      background-color: white;

      .title {
        padding-bottom: 10px;
        border-bottom: 1px solid $color-border;
        font-weight: bold;
        font-size: $font-size-medium-x;
        line-height: 30px;
      }

      .alert-service {
        margin-top: 20px;
      }

      .form {
        margin: 20px 0;

        .date-picker-exam {
          width: 210px;
        }

        .component-radio-group-grade,
        .component-radio-group-subject {
          padding: 4px 0;
        }

        .exam-classes-panel {
          .oper-panel {
            margin-bottom: 2px;

            .class-title {
              margin-right: 20px;
            }
          }

          .list-panel {
            width: 86%;

            .class-item {
              min-width: 80px;
              margin-right: 20px;
            }
          }
        }

        .exam-classes-panel > div:not(:last-child) {
          margin-bottom: 16px;
        }

        .form-item-classes {
          .checkbox-row-required {
            display: inline-block;
            min-width: 80px;
            margin-right: 10px;

            .head {
              margin-bottom: 4px;
            }
          }

          .checkbox-classes-item {
            margin-right: 20px;
          }
        }

        .form-item-answer-sheet {
          width: 210px;
        }

        .input-exam-name {
          width: 500px;
        }
      }
    }

    .footer-btns {
      text-align: right;
    }
  }

  .modal-answer-sheet-picture-container {
    height: 65vh;
    border: 1px solid $color-disabled;
    overflow-y: auto;

    .answer-sheet-image {
      width: 100%;
    }
  }
</style>
