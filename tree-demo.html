<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>树结构算法演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        
        .demo-section h3 {
            color: #2c3e50;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .tree-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .result {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .button-group {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background: #2980b9;
        }
        
        .tree-node {
            margin-left: 20px;
            position: relative;
        }
        
        .tree-node::before {
            content: "├─ ";
            position: absolute;
            left: -20px;
            color: #666;
        }
        
        .tree-node:last-child::before {
            content: "└─ ";
        }
        
        .node-info {
            color: #2c3e50;
            font-weight: bold;
        }
        
        .node-details {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌳 树结构算法演示</h1>
        
        <div class="demo-section">
            <h3>示例树结构</h3>
            <div class="tree-display" id="tree-structure"></div>
        </div>
        
        <div class="button-group">
            <button onclick="runAllDemos()">运行所有演示</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="results-container"></div>
    </div>

    <script type="module">
        // 树结构算法实现（简化版，用于演示）
        const sampleTree = [
            {
                id: 1,
                name: 'Root 1',
                children: [
                    {
                        id: 2,
                        name: 'Child 1.1',
                        children: [
                            { id: 4, name: 'Grandchild 1.1.1', children: [] },
                            { id: 5, name: 'Grandchild 1.1.2', children: [] }
                        ]
                    },
                    {
                        id: 3,
                        name: 'Child 1.2',
                        children: []
                    }
                ]
            },
            {
                id: 6,
                name: 'Root 2',
                children: [
                    { id: 7, name: 'Child 2.1', children: [] }
                ]
            }
        ];

        // 基础遍历函数
        function traverse(tree, callback, getChildren = node => (node && node.children) || []) {
            if (Array.isArray(tree)) {
                tree.forEach(node => traverse(node, callback, getChildren));
                return;
            }
            
            callback(tree);
            const children = getChildren(tree);
            children.forEach(child => traverse(child, callback, getChildren));
        }

        // 查找节点
        function findNode(tree, predicate, getChildren = node => (node && node.children) || []) {
            if (Array.isArray(tree)) {
                for (const node of tree) {
                    const result = findNode(node, predicate, getChildren);
                    if (result) return result;
                }
                return null;
            }
            
            if (predicate(tree)) {
                return tree;
            }
            
            const children = getChildren(tree);
            for (const child of children) {
                const result = findNode(child, predicate, getChildren);
                if (result) return result;
            }
            
            return null;
        }

        // 获取最大深度
        function getMaxDepth(tree, getChildren = node => (node && node.children) || []) {
            if (Array.isArray(tree)) {
                return Math.max(...tree.map(node => getMaxDepth(node, getChildren)));
            }
            
            const children = getChildren(tree);
            if (children.length === 0) {
                return 1;
            }
            
            return 1 + Math.max(...children.map(child => getMaxDepth(child, getChildren)));
        }

        // 扁平化树
        function flattenTree(tree, getChildren = node => (node && node.children) || []) {
            const result = [];
            traverse(tree, node => result.push(node), getChildren);
            return result;
        }

        // 显示树结构
        function displayTree(tree, level = 0) {
            let result = '';
            if (Array.isArray(tree)) {
                tree.forEach(node => {
                    result += displayTree(node, level);
                });
            } else {
                const indent = '  '.repeat(level);
                const prefix = level === 0 ? '🌳 ' : (level === 1 ? '📁 ' : '📄 ');
                result += `${indent}${prefix}${tree.name} (ID: ${tree.id})\n`;
                
                if (tree.children && tree.children.length > 0) {
                    tree.children.forEach(child => {
                        result += displayTree(child, level + 1);
                    });
                }
            }
            return result;
        }

        // 创建结果显示区域
        function createResultSection(title, content) {
            return `
                <div class="demo-section">
                    <h3>${title}</h3>
                    <div class="result">${content}</div>
                </div>
            `;
        }

        // 运行所有演示
        function runAllDemos() {
            const container = document.getElementById('results-container');
            let html = '';

            // 1. 深度优先遍历
            const dfsResult = [];
            traverse(sampleTree, node => dfsResult.push(node.name));
            html += createResultSection(
                '1. 深度优先遍历 (DFS)',
                `遍历顺序: ${dfsResult.join(' → ')}`
            );

            // 2. 查找节点
            const foundNode = findNode(sampleTree, node => node.id === 4);
            html += createResultSection(
                '2. 查找节点',
                `查找ID为4的节点: ${foundNode ? foundNode.name : '未找到'}`
            );

            // 3. 获取最大深度
            const maxDepth = getMaxDepth(sampleTree);
            html += createResultSection(
                '3. 树的最大深度',
                `最大深度: ${maxDepth} 层`
            );

            // 4. 扁平化树
            const flattened = flattenTree(sampleTree);
            html += createResultSection(
                '4. 扁平化树结构',
                `扁平化结果: ${flattened.map(n => n.name).join(', ')}<br>
                 总节点数: ${flattened.length}`
            );

            // 5. 查找叶子节点
            const leafNodes = [];
            traverse(sampleTree, node => {
                if (!node.children || node.children.length === 0) {
                    leafNodes.push(node);
                }
            });
            html += createResultSection(
                '5. 叶子节点',
                `叶子节点: ${leafNodes.map(n => n.name).join(', ')}`
            );

            container.innerHTML = html;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('results-container').innerHTML = '';
        }

        // 初始化显示树结构
        function initTreeDisplay() {
            const treeDisplay = document.getElementById('tree-structure');
            treeDisplay.textContent = displayTree(sampleTree);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTreeDisplay();
        });

        // 将函数暴露到全局作用域
        window.runAllDemos = runAllDemos;
        window.clearResults = clearResults;
    </script>
</body>
</html>
