export default {
  namespaced: true,
  state: {
    currentGradeSubject: [],
    currentTemplateIds: [],
    examTemplates: [],
    currentGradeNameAndSubjectName: [],
  },
  getters: {},
  mutations: {},
  actions: {
    setGradeSubject(context, params) {
      context.state.currentGradeSubject = params.currentGradeSubject
    },
    setGradeNameAndSubjectName(context, params) {
      context.state.currentGradeNameAndSubjectName = params.currentGradeNameAndSubjectName
    },
    setTemplateIds(context, params) {
      context.state.currentTemplateIds = params.currentTemplateIds
    },
    setExamTemplates(context, params) {
      context.state.examTemplates = params.examTemplates
    },
  },
}
