﻿/**
 * 可复制粘贴mathjax渲染后的公式，以及插入新公式。
 * 本插件修改自ckeditor官方mathematical formula插件，取消iframe，并使用外部mathjax
 */

CKEDITOR.plugins.add( 'mathjax', {
		requires: 'widget,dialog',
		icons: 'mathjax',
		hidpi: true, // %REMOVE_LINE_CORE%

		init: function( editor ) {
			var cls = 'math-formula';

			editor.widgets.add( 'mathjax', {
				inline: true,
				dialog: 'mathjax',
				button: '输入代码插入公式',
				mask: true,
				allowedContent: 'span(!' + cls + ')',

				template: '<span class="' + cls + '" style="display:inline-block"></span>',

				defaults: {
					math: ''
				},

				init: function() {
					this.showMathFormula = function (el, math) {
						if (!el) {
							el = this.element;
						}

						if (!math) {
							math = this.data.math
						}

						if (math == '$$') {
							math = ''
						}

						el.setHtml(math)
						if (MathJax) {
							MathJax.Hub.Queue(['Typeset', MathJax.Hub, el.$])
						}
					};

					this.once( 'ready', function() {
						this.showMathFormula();
					});
				},

				upcast: function( el, data ) {
					if ( !( el.name == 'span' && el.hasClass( cls ) ) )
						return false;

					if ( el.children.length > 1)
						return false;

					var child = el.children[0]
					if (child.type === CKEDITOR.NODE_TEXT) {
						data.math = CKEDITOR.tools.htmlDecode( child.value );
						return true;
					}
					else if (child.name.toLowerCase() === 'math') {
						data.math = el.getHtml()
						return true;
					}
					
					return false;
				},

				downcast: function( el ) {
					el.setHtml(this.data.math);
					return el;
				}
			} );

			// 添加dialog
			CKEDITOR.dialog.add( 'mathjax', this.path + 'dialogs/mathjax.js' );

			// 粘贴过滤
			// editor.on( 'paste', function( evt ) {
			// 	// 提取mathjax渲染后的span元素中的data-mathml属性
			// 	var dataValue = evt.data.dataValue;
			// 	dataValue = dataValue.replace(/<span[^>]+data-mathml=[^>]*>[^<]*<\/span>/g, function(m) {
			// 		let matched = m.match(/data-mathml="([^"]+)/)
			// 		return matched && matched[1] ? '<span class="' + cls + '">' + matched[0] + '</span>' : ''
			// 		});
			// 	evt.data.dataValue = dataValue;
			// } );
		}
	}
)