import { apiGetSubjectCompare } from '@/api/report'
import { generateExcelBlob } from '@/utils/excel_export'

import Store from '@/store/index'

// 考情概览
export function generateExcelReportGeneralBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let fileName = `${Store.getters['report/examName']}_考情概览_`

  let organizationId
  let schoolId
  let reportName = 'sch_overview'

  if (category && category.categoryName) {
    fileName = category.categoryName + '/' + fileName
  }
  if (isMultipleSchoolLevel) {
    if (isNextLevel) {
      if (school) {
        fileName = `下级机构或学校报表/(学校)${school.schoolName}/${fileName}${school.schoolName}`
        schoolId = school.schoolId
      } else {
        fileName = `下级机构或学校报表/(机构)${institution.name}/${fileName}${institution.name}`
        organizationId = institution.id
        reportName = 'union_overview'
      }
    } else {
      if (school) {
        fileName = `学校报表/${school.schoolName}/${fileName}${school.schoolName}`
        schoolId = school.schoolId
      } else {
        fileName += '联考'
        organizationId = Store.getters['report/examCreatedInstitutionId']
        reportName = 'union_overview'
      }
    }
  } else {
    fileName += `${Store.getters['report/currentSchoolName']}`
    schoolId = Store.getters['report/currentSchoolId']
  }
  if (category && category.categoryName) {
    fileName += '_' + category.categoryName
  }
  fileName += '.xlsx'

  let regionRankPositions = null
  let temp = Store.getters['report/reportsByLevel'].find(x => x.levelName === 'school')
  if (temp) {
    temp = (temp.reports || []).find(x => x.text === '考情概览')
    if (temp) {
      regionRankPositions = temp.rolePositions
    }
  }

  let showRegionRank =
    excelShowRank &&
    !!schoolId &&
    (Store.getters['report/isAdministrator'] ||
      (regionRankPositions && regionRankPositions.some(r => r.detail && r.detail.showSchoolRegionRank)))

  return fetchSheets(organizationId, schoolId, reportName, showRegionRank, category)
    .then(sheets => generateExcelBlob(sheets))
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}

async function fetchSheets(organizationId, schoolId, reportName, showRegionRank, category) {
  let tableColumns = [
    {
      title: '学科',
      key: 'subjectName',
      width: 'auto',
    },
    {
      title: '实考人数',
      key: 'examedStudent',
    },
    {
      title: '缺考人数',
      key: 'absentStudent',
    },
    {
      title: '满分',
      key: 'totalFullScore',
    },
    {
      title: '最高分',
      key: 'maxScore',
      cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
    },
    {
      title: '最低分',
      key: 'minScore',
      cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
    },
    {
      title: '平均分',
      key: 'avgScore',
      cellNumberFormat: value => (Number.isInteger(value) ? undefined : '0.##'),
    },
  ]

  let tableData = await apiGetSubjectCompare({
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    organizationId: organizationId,
    schoolId: schoolId,
    reportName: reportName,
    subjectCategory: category ? category.categoryId : undefined,
  })

  if (tableData && tableData.length) {
    tableData.forEach(x => {
      if (x.subjName) {
        x.subjectName = x.subjName
      } else if (x.subjId == 0) {
        x.subjectName = '总分'
      } else {
        x.subjectName = (
          Store.getters['report/templateSubjects'].find(ts => ts.subjectId === x.subjId) || { subjectName: '' }
        ).subjectName
      }

      x.examedStudent = x.present
      x.absentStudent = x.absent
    })

    tableData.sort((a, b) => a.subjId - b.subjId)

    let rates = tableData[0].rates || []
    rates.forEach(x => {
      tableColumns.push({
        title: x.id,
        cellNumberFormat: value => (isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%'),
        key: row => {
          let rate = row.rates.find(r => r.id === x.id)
          return (rate && rate.name && Number(rate.name)) || 0
        },
      })
    })

    if (showRegionRank) {
      tableColumns.push({
        title: '联考排名',
        key: 'regionRank',
      })
    }
  }

  return [
    {
      sheetName: '考情概览',
      rows: tableData,
      columns: tableColumns,
    },
  ]
}
