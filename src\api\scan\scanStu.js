import ajax from '@/api/ajax'
import { transformScanUnit } from './scanUnit'

export function transformStudentScanUnits(stu) {
  stu.scanUnits = (stu.scanUnits || [])
    .map(transformScanUnit)
    // 按页面顺序排
    .sort((a, b) => {
      let minPageNoA = Math.min(...a.pageNos)
      let minPageNoB = Math.min(...b.pageNos)
      return minPageNoA - minPageNoB
    })
}

export function apiGetSubjectRooms({ examSubjectId, scanStationId }) {
  return ajax.get({
    url: 'scan/scanStu/rooms',
    params: {
      examSubjectId,
      scanStationId,
    },
    requestName: '获取考场列表',
  })
}

export function apiGetRoomStudents({ examSubjectId, roomNo }) {
  return ajax
    .get({
      url: 'scan/scanStu/students',
      params: {
        examSubjectId,
        roomNo,
      },
      requestName: '获取考场考生',
    })
    .then(data => {
      data.forEach(transformStudentScanUnits)
      return data
    })
}

// 考场考生，无扫描单元详情
export function apiGetRoomStudentsInfo({ examSubjectId, roomNo }) {
  return ajax
    .get({
      url: 'scan/scanStu/roomStudentsInfo',
      params: {
        examSubjectId,
        roomNo,
      },
      requestName: '获取考场考生',
    })
    .then(data => {
      data.forEach(transformStudentScanUnits)
      return data
    })
}

// 批次相关考场号
export function apiGetBatchRoomNos({ examSubjectId, batchId }) {
  return ajax.get({
    url: 'scan/scanStu/batchRoomNos',
    params: {
      examSubjectId,
      batchId,
    },
    requestName: '获取批次相关考场号',
  })
}

export function apiGetStudentSubPapers({ examSubjectId, studentId }) {
  return ajax.get({
    url: 'scan/scanStu/subPapers',
    params: {
      examSubjectId,
      studentId,
    },
    requestName: '获取考生子图',
  })
}
