import ajax from '@/api/ajax'

function transformTeacher(responseTeacher) {
  return {
    userId: responseTeacher.teacher.teacherId,
    userName: responseTeacher.teacher.userName,
    realName: responseTeacher.teacher.realName,
    sex: responseTeacher.teacher.sex,
    mobile: responseTeacher.teacher.mobile || '',
    teachings: (responseTeacher.teachings || []).map(x => ({
      gradeId: x.gradeId,
      gradeName: x.gradeName,
      classId: x.classId,
      className: x.className,
      subjectId: x.subjectId,
      subjectName: x.subjectName,
    })),
    roles: (responseTeacher.roles || []).map(r => ({
      roleId: r.roleId,
      roleName: r.roleName,
      stageId: r.gradeLevelId,
      stageName: r.gradeLevelName,
      gradeId: r.gradeId,
      gradeName: r.gradeName,
      classId: r.classId,
      className: r.className,
      subjectId: r.subjectId,
      subjectName: r.subjectName,
    })),
  }
}

function sortTeachings(teachings) {
  teachings.sort((a, b) => {
    if (a.subjectId != b.subjectId) {
      return a.subjectId - b.subjectId
    } else if (a.gradeId != b.gradeId) {
      return a.gradeId - b.gradeId
    } else {
      let classCodeA = estimateClassSortCode(a.className || '')
      let classCodeB = estimateClassSortCode(b.className || '')
      return classCodeA - classCodeB
    }
  })
  return teachings
}

function estimateClassSortCode(className) {
  let match = className.match(/(.+)(\d+)班$/)
  if (match) {
    let code = Number(match[2])
    // 一般教学点名称或教学班名称较长
    if (match[1].length > 3) {
      code += 100
    }
    return code
  } else {
    return 1000
  }
}

export function apiGetTeacherPositionsAndTeachings() {
  return ajax
    .get({
      url: 'user/teacher/personalTeaching',
      requestName: '获取教师职务与任教',
    })
    .then(data => {
      return {
        positions: (data.roles || []).map(x => ({
          roleId: x.roleId,
          stageId: x.gradeLevelId,
          gradeId: x.gradeId,
          subjectId: x.subjectId,
          classId: x.classId,
          className: x.className,
        })),
        teachings: sortTeachings(
          (data.teachings || []).map(x => ({
            stageId: x.gradeLevelId,
            gradeId: x.gradeId,
            subjectId: x.subjectId,
            classId: x.classId,
            className: x.className,
            classCode: x.classCode,
            classType: x.classType,
          }))
        ),
      }
    })
}

export function apiGetTeachers(params) {
  return ajax
    .get({
      url: 'user/teacher/list',
      params: {
        key: params.keyword || null,
        size: params.pageSize,
        current: params.currentPage,
      },
      requestName: '获取教师列表',
    })
    .then(data => ({
      total: data.total,
      teachers: data.records.map(transformTeacher),
    }))
}

export function apiAddTeacher(teacher) {
  return ajax.post({
    url: 'user/teacher',
    data: {
      realName: teacher.realName,
      sex: teacher.sex,
      mobile: teacher.mobile,
      password: teacher.password,
    },
    requestName: '添加教师',
  })
}

export function apiEditTeacher(teacher) {
  return ajax.put({
    url: 'user/teacher',
    data: {
      teacherId: teacher.userId,
      realName: teacher.realName,
      sex: teacher.sex,
      mobile: teacher.mobile,
    },
    requestName: '编辑教师',
  })
}

export function apiRecoverTeacherByMobile(mobile) {
  return ajax.put({
    url: 'user/teacher/recover',
    params: {
      mobile: mobile,
    },
    requestName: '根据手机号码恢复被删除老师',
  })
}

export function apiDeleteTeacher(teacherId) {
  return ajax.delete({
    url: 'user/teacher',
    params: {
      teacherId,
    },
    requestName: '删除教师',
  })
}

export function apiDeleteTeachers(teachers) {
  return ajax.delete({
    url: 'user/teacher/delTeacherBatch',
    data: teachers,
    requestName: '批量删除教师',
  })
}

export function apiSetEditorStageSubjects(data) {
  return ajax.put({
    url: 'user/teacher/editorRoles',
    params: {
      teacherId: data.userId,
    },
    data: data.roles.map(r => ({
      id: data.userId,
      roleId: r.roleId,
      gradeLevelId: r.stageId || null,
      gradeId: r.gradeId || null,
      classId: r.classId || null,
      subjectId: r.subjectId || null,
    })),
    requestName: '修改编辑学段学科',
  })
}

export function apiSetTeacherPositions(data) {
  return ajax.put({
    url: 'user/teacher/teacherRoles',
    params: {
      teacherId: data.userId,
    },
    data: data.roles.map(r => ({
      id: data.userId,
      roleId: r.roleId,
      gradeLevelId: r.stageId || null,
      gradeId: r.gradeId || null,
      classId: r.classId || null,
      subjectId: r.subjectId || null,
    })),
    requestName: '修改教师任职',
  })
}

export function apiSetTeacherTeachings(data) {
  return ajax.put({
    url: 'user/teacher/teacherSubs',
    params: {
      teacherId: data.userId,
    },
    data: data.teachings.map(x => ({
      teacherId: data.userId,
      classId: x.classId,
      subjectId: x.subjectId,
    })),
    requestName: '修改教师任教',
  })
}

export function apiImportTeacher(requestParams) {
  return ajax.upload({
    url: 'user/teacher/import',
    params: {
      updateName: requestParams.updateName, // [Boolean] 是否更新教师姓名，后端默认 false
    },
    data: {
      file: requestParams.file,
    },
  })
}

// 获取班（年级）任课教师及班主任
export function apiGetClassSubjectTeachers(params) {
  return ajax
    .get({
      url: 'user/teacher/teaching',
      params: {
        classId: params.classId,
        gradeId: params.gradeId,
      },
      requestName: '获取任教老师',
    })
    .then(data =>
      (data || []).map(c => {
        return {
          classId: c.classId,
          className: c.className,
          classLeaders: (c.leaders || []).map(x => ({
            userId: x.id,
            realName: x.name,
          })),
          teachings: (c.teachings || []).map(x => ({
            subjectId: parseInt(x.subjectId),
            subjectName: x.subjectName,
            userId: x.teacherId,
            realName: x.teacherName,
          })),
        }
      })
    )
}

export function apiSetClassSubjectTeachers(data) {
  return ajax.put({
    url: 'user/teacher/teaching',
    params: {
      gradeId: data.gradeId,
      classId: data.classId,
      subjectId: data.subjectId,
    },
    data: data.teachers.map(t => t.userId),
    requestName: '设置任教老师',
  })
}

export function apiSetClassLeaders(data) {
  return ajax.put({
    url: 'user/teacher/classLeader',
    params: {
      gradeId: data.gradeId,
      classId: data.classId,
    },
    data: data.teachers.map(t => t.userId),
    requestName: '设置班主任',
  })
}

export function apiGetGradeSubjectLeaders() {
  return ajax
    .get({
      url: 'user/teacher/gradeSubjectLeader',
      requestName: '获取备课组长',
    })
    .then(data =>
      (data || []).map(x => ({
        gradeId: x.gradeId,
        gradeName: x.gradeName,
        subjectId: x.subjectId,
        subjectName: x.subjectName,
        teachers: x.teachers.map(t => ({
          userId: t.id,
          realName: t.name,
        })),
      }))
    )
}

export function apiSetGradeSubjectLeaders(data) {
  return ajax.put({
    url: 'user/teacher/gradeSubjectLeader',
    params: {
      gradeId: data.gradeId,
      subjectId: data.subjectId,
    },
    data: data.teachers.map(t => t.userId),
    requestName: '设置备课组长',
  })
}

export function apiGetSubjectLeaders() {
  return ajax
    .get({
      url: 'user/teacher/subjectLeader',
      requestName: '获取学科组长',
    })
    .then(data =>
      data.map(item => ({
        stageId: item.gradeLevelId,
        stageName: item.gradeLevelName,
        subjectId: item.subjectId,
        subjectName: item.subjectName,
        teachers: item.teachers.map(t => ({
          userId: t.id,
          realName: t.name,
        })),
      }))
    )
}

export function apiSetSubjectLeaders(data) {
  return ajax.put({
    url: 'user/teacher/subjectLeader',
    params: {
      gradeLevel: data.stageId,
      subjectId: data.subjectId,
    },
    data: data.teachers.map(t => t.userId),
    requestName: '设置学科组长',
  })
}

export function apiGetGradeLeaders() {
  return ajax
    .get({
      url: 'user/teacher/gradeLeader',
      requestName: '获取年级主任',
    })
    .then(data =>
      (data || []).map(x => ({
        gradeId: x.gradeId,
        gradeName: x.gradeName,
        teachers: x.leaders.map(t => ({
          userId: t.id,
          realName: t.name,
        })),
      }))
    )
}

export function apiSetGradeLeaders(data) {
  return ajax.put({
    url: 'user/teacher/gradeLeader',
    params: {
      gradeId: data.gradeId,
    },
    data: data.teachers.map(t => t.userId),
    requestName: '设置年级主任',
  })
}

export function apiGetSchoolLeaders() {
  return ajax
    .get({
      url: 'user/teacher/schoolLeader',
      requestName: '获取学校领导',
    })
    .then(teachers =>
      teachers.map(t => ({
        userId: t.id,
        realName: t.name,
      }))
    )
}

export function apiSetSchoolLeaders(teachers) {
  return ajax.put({
    url: 'user/teacher/schoolLeader',
    data: teachers.map(t => t.userId),
    requestName: '设置学校领导',
  })
}

export function apiGetSchoolAdministrators() {
  return ajax
    .get({
      url: 'user/teacher/schoolAdmin',
      requestName: '获取学校管理员',
    })
    .then(teachers =>
      teachers.map(t => ({
        userId: t.id,
        realName: t.name,
      }))
    )
}

export function apiSetSchoolAdministrators(teachers) {
  return ajax.put({
    url: 'user/teacher/schoolAdmin',
    data: teachers.map(t => t.userId),
    requestName: '设置学校管理员',
  })
}

export function apiGetResearchers() {
  return ajax
    .get({
      url: 'user/teacher/researcher',
      requestName: '获取教研员',
    })
    .then(data =>
      data.map(item => ({
        stageId: item.gradeLevelId,
        stageName: item.gradeLevelName,
        subjectId: item.subjectId,
        subjectName: item.subjectName,
        teachers: item.teachers.map(t => ({
          userId: t.id,
          realName: t.name,
        })),
      }))
    )
}

export function apiSetResearchers(data) {
  return ajax.put({
    url: 'user/teacher/researcher',
    params: {
      gradeLevel: data.stageId,
      subjectId: data.subjectId,
    },
    data: data.teachers.map(t => t.userId),
    requestName: '设置教研员',
  })
}

export function apiGetResearcherLeaders() {
  return ajax
    .get({
      url: 'user/teacher/researcherLeader',
      requestName: '获取教研领导',
    })
    .then(teachers =>
      teachers.map(t => ({
        userId: t.id,
        realName: t.name,
      }))
    )
}

export function apiSetResearcherLeaders(teachers) {
  return ajax.put({
    url: 'user/teacher/researcherLeader',
    data: teachers.map(t => t.userId),
    requestName: '设置教研领导',
  })
}

export function apiResetTeacherPassword(data) {
  return ajax.put({
    url: 'user/teacher/resetPwd',
    params: {
      teacherId: data.teacherId,
      pwd: data.pwd,
    },
    // data: data.teachers.map(t => t.userId),
    requestName: '学校管理员重置教师账号密码',
  })
}

export function apiExportTeachers() {
  return ajax.download({
    url: 'user/teacher/export',
    requestName: '导出教师',
  })
}

export function apiExportAffiliatedManagersOfInstitution(requestParams) {
  return ajax.get({
    url: 'user/teacher/exportPlainAdmin',
    params: {
      schoolName: requestParams.schoolName,
      mobile: requestParams.mobile,
      pageNo: requestParams.currentPage,
      pageSize: requestParams.pageSize,
    },
    requestName: '导出机构下属单位所有管理员',
  })
}

export function apiDeleteAllTeacherInfoInGrade(requestParams) {
  return ajax.delete({
    url: 'user/teacher/deleteTeachAndClassTeacher',
    params: {
      gradeId: requestParams.gradeId,
    },
    requestParams: '删除年级所有班主任和任课老师信息',
  })
}

/**
 * 教师自己设置任教
 */
export function apiSetMyTeaching(data) {
  return ajax.put({
    url: 'user/teacher/setMyTeaching',
    data,
    requestName: '设置任教',
  })
}

export function apiGetFocusStudentIds(classId) {
  return ajax.get({
    url: 'user/teacher/getFocusStudentIds',
    params: {
      classId, // String*
    },
  })
}

export function apiAddFocusStudents(requestParams) {
  return ajax.put({
    url: 'user/teacher/addFocusStudents',
    params: {
      classId: requestParams.classId, // String*
    },
    data: requestParams.studentIds, // Array[String]*
  })
}

export function apiDelFocusStudents(requestParams) {
  return ajax.delete({
    url: 'user/teacher/delFocusStudents',
    params: {
      classId: requestParams.classId, // String*
    },
    data: requestParams.studentIds, // Array[String]*
  })
}

export function apiDownloadInstitutionSchoolsTeachers(requestParams) {
  return ajax.download({
    url: 'user/teacher/exportSubSchoolTeachers',
    params: {
      orgSchoolId: requestParams.institutionId, // *String
      gradeLevel: requestParams.gradeLevel, // *Number
      gradeId: requestParams.gradeId, // Number
      roleId: requestParams.roleId, // Number
    },
    requestName: '导出机构下属学校教师',
  })
}

export function apiImportTeachersPassword(excelFile) {
  return ajax.upload({
    url: 'user/teacher/resetPwdByExcel',
    data: {
      file: excelFile,
    },
  })
}
