import ajax from '@/api/ajax'
import { PaperInfo } from '@/helpers/qlib/paper_info'

/**
 * 个人组卷记录
 */
export function apiGetComposedPapers(params) {
  let apiParams = {
    stage: params.stage,
    subject: params.subject,
    size: params.pageSize,
    page: params.currentPage,
    keyword: params.keyword || '',
    bookType: params.bookType,
    flagCoachBookId: params.coachbookId || undefined,
    paperGroupId: params.paperGroupId || undefined,
  }
  return ajax
    .get({
      url: `ques/paperMake/records`,
      params: apiParams,
      requestName: '获取组卷记录',
    })
    .then(data => {
      return {
        total: data.total || 0,
        papers: (data.records || []).map(p => PaperInfo.import(p)),
      }
    })
}

// 再次编辑已组卷试卷，后台将试卷题目加入试题篮
export function apiEditComposedPaper(paperId) {
  return ajax.put({
    url: `ques/paperMake/edit`,
    params: {
      paperId,
    },
    requestName: '请求编辑试卷',
  })
}
