<template>
  <div class="add-objective">
    <Form :label-width="90" :show-message="false">
      <FormItem label="大题号" class="ivu-form-item-required">
        <Select :model-value="topicCode" class="input-short" @on-change="changeTopicCode">
          <Option v-for="t in topicChineseCodes" :key="t.code" :value="t.code">{{ t.codeInChinese }}</Option>
        </Select>
      </FormItem>
      <FormItem label="大题名" class="ivu-form-item-required">
        <Input v-model="topicName" :disabled="isExistTopic" maxlength="20"></Input>
      </FormItem>
      <FormItem label="题号" class="ivu-form-item-required">
        <div class="form-item-question-code">
          <InputNumber v-model="questionCodeFrom" class="input-short" :min="1" :precision="0"></InputNumber>
          <span>——</span>
          <InputNumber v-model="questionCodeTo" class="input-short" :min="1" :precision="0"></InputNumber>
        </div>
      </FormItem>
      <FormItem label="选项个数" class="ivu-form-item-required">
        <InputNumber
          v-model="optionCount"
          class="input-short"
          :disabled="isTrueOrFalse"
          :min="1"
          :precision="0"
        ></InputNumber>
      </FormItem>
      <FormItem label="分数" class="ivu-form-item-required">
        <InputNumber v-model="score" :min="0" class="input-short"></InputNumber>
      </FormItem>
    </Form>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import ObjectiveQuestion from '@/helpers/emarking/objective_question'
  import { intersection } from '@/utils/array'
  import { roundScore } from '@/utils/math'
  import { randomId } from '@/utils/string'
  import { isPositiveInteger, isPositiveFinite, numberToChinese } from '@/utils/number'
  import BranchTypeEnum from '@/enum/qlib/branch_type'
  import { Max_Question_Code } from '@/const/emarking'

  export default {
    props: {
      branchTypeId: Number,
      topicChineseCodes: Array,
    },
    data() {
      return {
        topicCode: 1,
        topicName: '',
        questionCodeFrom: 1,
        questionCodeTo: null,
        optionCount: 4,
        score: null,
      }
    },
    computed: {
      ...mapGetters('answerSheet', ['questionCodes', 'maxQuestionCode', 'topicCodeNames', 'maxTopicCode']),
      isExistTopic() {
        return this.topicCodeNames.some(t => t.topicCode == this.topicCode)
      },
      isTrueOrFalse() {
        return this.branchTypeId == BranchTypeEnum.TrueOrFalse.id
      },
    },
    created() {
      this.topicCode = this.maxTopicCode + 1
      this.topicName = numberToChinese(this.topicCode) + '、' + BranchTypeEnum.getNameById(this.branchTypeId)
      this.questionCodeFrom = this.maxQuestionCode + 1
      if (this.isTrueOrFalse) {
        this.optionCount = 2
      }
    },
    methods: {
      changeTopicCode(newTopicCode) {
        let oldTopicCode = this.topicCode
        this.topicCode = newTopicCode
        if (this.isExistTopic) {
          this.topicName = this.topicCodeNames.find(t => t.topicCode == this.topicCode).topicName
        } else {
          let oldTopicCodeChinese = numberToChinese(oldTopicCode)
          let newTopicCodeChinese = numberToChinese(newTopicCode)
          this.topicName = this.topicName.replace(new RegExp(`^${oldTopicCodeChinese}、`), newTopicCodeChinese + '、')
        }
      },
      check() {
        if (!this.topicCode) {
          return {
            message: '请选择大题',
          }
        }
        if (!this.topicName) {
          return {
            message: '请输入大题名',
          }
        }
        if (
          !(
            isPositiveInteger(this.questionCodeFrom) &&
            isPositiveInteger(this.questionCodeTo) &&
            this.questionCodeFrom <= this.questionCodeTo
          )
        ) {
          return {
            message: '请输入正确的题号',
          }
        }
        if (this.questionCodeFrom > Max_Question_Code || this.questionCodeTo > Max_Question_Code) {
          return {
            message: '题号过大',
          }
        }
        let newQuestionCodes = []
        for (let i = this.questionCodeFrom; i <= this.questionCodeTo; i++) {
          newQuestionCodes.push(i)
        }
        let intersections = intersection(newQuestionCodes, this.questionCodes)
        if (intersections.length > 0) {
          return {
            message: `以下题号已存在：${intersections.join('，')}`,
          }
        }
        if (!isPositiveInteger(this.optionCount)) {
          return {
            message: '请输入正确的选项个数',
          }
        }
        if (this.optionCount < 2) {
          return {
            message: '选项个数至少为2',
          }
        }
        if (this.optionCount > 10) {
          return {
            message: '选项个数过大',
          }
        }

        let score = roundScore(this.score)
        if (!score || !isPositiveFinite(score)) {
          return {
            message: '请输入分数',
          }
        }

        let objectiveQuestions = []
        newQuestionCodes.forEach(questionCode => {
          let obj = new ObjectiveQuestion()
          obj.questionId = randomId()
          obj.questionCode = questionCode
          obj.questionName = `${questionCode}`
          obj.branchCode = 0
          obj.branchName = obj.questionName
          obj.topicCode = this.topicCode
          obj.topicName = this.topicName
          obj.fullScore = score
          obj.branchTypeId = this.branchTypeId
          obj.optionCount = this.optionCount
          objectiveQuestions.push(obj)
        })

        return {
          message: '',
          data: {
            topicCode: this.topicCode,
            topicName: this.topicName,
            objectiveQuestions,
          },
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .input-short {
    width: 100px;
  }
</style>
