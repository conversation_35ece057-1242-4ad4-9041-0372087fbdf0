<template>
  <div class="tab-list">
    <div class="header">
      <div class="header-title">我的答题卡</div>
      <div class="section-btns">
        <Button type="success" class="btn-normal-height btn-success" ghost icon="md-add" @click="createNewAnswerSheet"
          >新建答题卡</Button
        >
        <Button
          type="error"
          class="btn-normal-height btn-error"
          ghost
          icon="md-trash"
          style="margin-left: 4px"
          @click="showModalRecycleBin = true"
          >回收站</Button
        >
      </div>
    </div>

    <div class="section-neck">
      <div class="filter">
        <div class="filter-item">
          <span class="filter-label">模式</span>
          <Select
            v-model="answerSheetMode"
            class="filter-select"
            placeholder="不限"
            clearable
            transfer
            @on-change="fetchPersonalAnswerSheetList"
          >
            <Option v-for="m in modes" :key="m.id" :value="m.id">{{ m.name }}</Option>
          </Select>
        </div>
        <div class="filter-item">
          <span class="filter-label">来源</span>
          <Select
            v-model="answerSheetSourceType"
            class="filter-select"
            placeholder="不限"
            clearable
            transfer
            @on-change="fetchPersonalAnswerSheetList"
            ><Option v-for="item of sourceTypes" :key="'souceType' + item.id" :value="item.id">{{
              item.name
            }}</Option></Select
          >
        </div>
        <div class="filter-item">
          <span class="filter-label">学段</span>
          <Select
            v-model="answerSheetStageId"
            class="filter-select"
            placeholder="不限"
            clearable
            transfer
            @on-change="changeAnswerSheetStageId"
            ><Option v-for="item of stages" :key="'stage' + item.id" :value="item.id">{{ item.name }}</Option></Select
          >
        </div>
        <div class="filter-item">
          <span class="filter-label">学科</span>
          <Select
            v-model="answerSheetSubjectId"
            class="filter-select"
            :disabled="!answerSheetStageId"
            placeholder="不限"
            clearable
            transfer
            @on-change="fetchPersonalAnswerSheetList"
            ><Option v-for="item of stageSubjects" :key="'subject' + item.id" :value="item.id">{{
              item.name
            }}</Option></Select
          >
        </div>
        <div class="filter-item">
          <span class="filter-label">名称</span>
          <SearchInput
            v-model="keyword"
            class="input-search"
            clearable
            placeholder="输入答题卡名称进行搜索"
            :maxlength="30"
            @on-change="fetchPersonalAnswerSheetList"
          ></SearchInput>
        </div>
      </div>
      <div class="switch">
        <i-switch v-model="referExam" @on-change="fetchPersonalAnswerSheetList"></i-switch>
        <span class="switch-label">已关联考试</span>
      </div>
    </div>

    <div v-if="answerSheetList.length" class="section-list">
      <div v-for="item of answerSheetList" :key="item.id" class="answer-sheet">
        <div class="answer-sheet-inner">
          <div class="box-icon">
            <img class="icon-subject" :src="item.subjectIcon" alt="" />
          </div>
          <div class="main">
            <div class="main-top">
              <div class="name">
                {{ item.name }}
                <span
                  v-if="item.modeName"
                  class="tag-answer-sheet"
                  :class="{ 'tag-online': item.mode === 1, 'tag-postscan': item.mode === 2 }"
                  >{{ item.modeName }}</span
                >
              </div>
              <div class="create-time">
                <span>{{ item.sourceTypeName }}</span>
                <span class="text-create-time">{{ item.createTime }}</span>
              </div>
            </div>
            <div class="main-middle">
              <div class="topics">
                <template v-if="item.topics.length">
                  <div v-for="t in item.topics" :key="t.topicCode" class="topic">
                    {{ t.topicName }}<span class="question-count">{{ t.questionCount }}</span
                    >道
                  </div>
                </template>
              </div>
              <div class="action">
                <Button
                  v-if="enableEdit(item)"
                  ghost
                  class="btn btn-success"
                  type="success"
                  shape="circle"
                  @click="handleEdit(item)"
                  >编辑</Button
                >
                <Button class="btn btn-info" type="info" ghost shape="circle" @click="handlePreview(item)">查看</Button>
                <Button
                  class="btn btn-warning"
                  type="warning"
                  shape="circle"
                  ghost
                  :loading="item.downloading"
                  @click="handleDownload(item)"
                  >{{ item.downloading ? '' : '下载' }}</Button
                >
                <Dropdown transfer @on-click="$event => handleDropdownClick(item, $event)">
                  <Button class="btn" type="default" shape="circle">更多<Icon type="ios-arrow-down"></Icon></Button>
                  <template #list>
                    <DropdownMenu>
                      <DropdownItem v-if="enableCopy(item)" name="copy">复制</DropdownItem>
                      <DropdownItem name="share">分享</DropdownItem>
                      <DropdownItem name="changeTemplate">调整题块</DropdownItem>
                      <DropdownItem name="delete">删除</DropdownItem>
                    </DropdownMenu>
                  </template>
                </Dropdown>
              </div>
            </div>
            <div v-if="item.sharerName || (item.referExams && item.referExams.length)" class="main-bottom">
              <div v-if="item.sharerName" class="share">分享者：{{ item.sharerName }}</div>
              <div v-if="item.referExams && item.referExams.length" class="exams">
                <div class="label">关联考试：</div>
                <div class="content">
                  <div v-for="e in item.referExams" :key="e.examId">{{ e.examName }}（{{ e.subjectNames }}）</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <NoData v-else :visible="!loading">
      <div style="text-align: center">
        <div>{{ emptyText }}</div>
        <Button type="primary" style="margin-top: 16px" @click="createNewAnswerSheet">新建</Button>
      </div>
    </NoData>

    <ModalPreview v-model="modalAnswerSheetPictureShowed" :sheet="previewAnswerSheet"></ModalPreview>

    <ModalCopy
      v-model="modalCopyAnswerSheetShowed"
      :source-answer-sheet="copySourceAnswerSheet"
      @fetch-personal-answer-sheet-list="fetchPersonalAnswerSheetList"
    ></ModalCopy>

    <ModalShareAnswerSheet
      v-model="modalShareAnswerSheetShowed"
      :answer-sheet="answerSheetForShare"
    ></ModalShareAnswerSheet>

    <ModalRecycleBin
      v-model="showModalRecycleBin"
      @fetch-personal-answer-sheet-list="fetchPersonalAnswerSheetList"
      @preview-answer-sheet="handlePreview"
    ></ModalRecycleBin>

    <ModalCreate v-model="showModalCreate" :stages="stages"></ModalCreate>
  </div>
</template>

<script>
  import ModalPreview from '../../edit/components/modal_preview.vue'
  import ModalCopy from './modal_copy.vue'
  import ModalShareAnswerSheet from './modal_answer_sheet_share.vue'
  import ModalRecycleBin from './modal_recycle_bin.vue'
  import ModalCreate from './modal_create.vue'
  import SearchInput from '@/components/searchInput.vue'
  import NoData from '@/components/no_data.vue'

  import IconChinesePrimary from '@/assets/images/answer_sheet/subjects_icon/小学语文.svg'
  import IconMathPrimary from '@/assets/images/answer_sheet/subjects_icon/小学数学.svg'
  import IconEnglishPrimary from '@/assets/images/answer_sheet/subjects_icon/小学英语.svg'
  import IconHistoryPrimary from '@/assets/images/answer_sheet/subjects_icon/小学历史.svg'
  import IconMoralityPrimary from '@/assets/images/answer_sheet/subjects_icon/小学道法.svg'
  import IconChineseJunior from '@/assets/images/answer_sheet/subjects_icon/初中语文.svg'
  import IconMathJunior from '@/assets/images/answer_sheet/subjects_icon/初中数学.svg'
  import IconEnglishJunior from '@/assets/images/answer_sheet/subjects_icon/初中英语.svg'
  import IconPhysicsJunior from '@/assets/images/answer_sheet/subjects_icon/初中物理.svg'
  import IconChemistryJunior from '@/assets/images/answer_sheet/subjects_icon/初中化学.svg'
  import IconBiologyJunior from '@/assets/images/answer_sheet/subjects_icon/初中生物.svg'
  import IconHistoryJunior from '@/assets/images/answer_sheet/subjects_icon/初中历史.svg'
  import IconGeographyJunior from '@/assets/images/answer_sheet/subjects_icon/初中地理.svg'
  import IconMoralityJunior from '@/assets/images/answer_sheet/subjects_icon/初中道法.svg'
  import IconChineseSenior from '@/assets/images/answer_sheet/subjects_icon/高中语文.svg'
  import IconMathSenior from '@/assets/images/answer_sheet/subjects_icon/高中数学.svg'
  import IconEnglishSenior from '@/assets/images/answer_sheet/subjects_icon/高中英语.svg'
  import IconPhysicsSenior from '@/assets/images/answer_sheet/subjects_icon/高中物理.svg'
  import IconChemistrySenior from '@/assets/images/answer_sheet/subjects_icon/高中化学.svg'
  import IconBiologySenior from '@/assets/images/answer_sheet/subjects_icon/高中生物.svg'
  import IconHistorySenior from '@/assets/images/answer_sheet/subjects_icon/高中历史.svg'
  import IconGeographySenior from '@/assets/images/answer_sheet/subjects_icon/高中地理.svg'
  import IconMoralitySenior from '@/assets/images/answer_sheet/subjects_icon/高中道法.svg'
  import IconPoliticsSenior from '@/assets/images/answer_sheet/subjects_icon/高中政治.svg'
  import IconRussianSenior from '@/assets/images/answer_sheet/subjects_icon/高中俄语.svg'
  import IconJapaneseSenior from '@/assets/images/answer_sheet/subjects_icon/高中日语.svg'
  import IconMusicSenior from '@/assets/images/answer_sheet/subjects_icon/高中音乐.svg'
  import IconArtSenior from '@/assets/images/answer_sheet/subjects_icon/高中美术.svg'
  import IconSportSenior from '@/assets/images/answer_sheet/subjects_icon/高中体育.svg'
  import IconTechnologySenior from '@/assets/images/answer_sheet/subjects_icon/高中通用技术.svg'
  import IconSubjectDefault from '@/assets/images/answer_sheet/subjects_icon/科目缺省图标.svg'

  import { apiGetStageGradeSubjects } from '@/api/user'
  import {
    apiGetMyAnswerSheetList,
    apiGetAnswerSheetInfo,
    apiGetAnswerSheetPDFUrl,
    apiUpdateAnswerSheetStatus,
    apiGetAnswerSheetDetail,
  } from '@/api/emarking/answer_sheet'

  import { downloadUrl } from '@/utils/download'
  import { formatDateTime } from '@/utils/date'

  import ModeEnum from '@/enum/answer_sheet/mode'
  import SourceTypeEnum from '@/enum/answer_sheet/source_type'

  const iconSubjects = [
    {
      gradeLevel: 1,
      subjectId: 1,
      subjectName: '语文',
      icon: IconChinesePrimary,
    },
    {
      gradeLevel: 1,
      subjectId: 2,
      subjectName: '数学',
      icon: IconMathPrimary,
    },
    {
      gradeLevel: 1,
      subjectId: 3,
      subjectName: '英语',
      icon: IconEnglishPrimary,
    },
    {
      gradeLevel: 1,
      subjectId: 4,
      subjectName: '道法',
      icon: IconMoralityPrimary,
    },
    {
      gradeLevel: 1,
      subjectId: 5,
      subjectName: '历史',
      icon: IconHistoryPrimary,
    },

    {
      gradeLevel: 2,
      subjectId: 1,
      subjectName: '语文',
      icon: IconChineseJunior,
    },
    {
      gradeLevel: 2,
      subjectId: 2,
      subjectName: '数学',
      icon: IconMathJunior,
    },
    {
      gradeLevel: 2,
      subjectId: 3,
      subjectName: '英语',
      icon: IconEnglishJunior,
    },
    {
      gradeLevel: 2,
      subjectId: 4,
      subjectName: '道法',
      icon: IconMoralityJunior,
    },
    {
      gradeLevel: 2,
      subjectId: 5,
      subjectName: '历史',
      icon: IconHistoryJunior,
    },
    {
      gradeLevel: 2,
      subjectId: 6,
      subjectName: '地理',
      icon: IconGeographyJunior,
    },
    {
      gradeLevel: 2,
      subjectId: 7,
      subjectName: '物理',
      icon: IconPhysicsJunior,
    },
    {
      gradeLevel: 2,
      subjectId: 8,
      subjectName: '化学',
      icon: IconChemistryJunior,
    },
    {
      gradeLevel: 2,
      subjectId: 9,
      subjectName: '生物',
      icon: IconBiologyJunior,
    },

    {
      gradeLevel: 4,
      subjectId: 1,
      subjectName: '语文',
      icon: IconChineseSenior,
    },
    {
      gradeLevel: 4,
      subjectId: 2,
      subjectName: '数学',
      icon: IconMathSenior,
    },
    {
      gradeLevel: 4,
      subjectId: 3,
      subjectName: '英语',
      icon: IconEnglishSenior,
    },
    {
      gradeLevel: 4,
      subjectId: 4,
      subjectName: '道法',
      icon: IconMoralitySenior,
    },
    {
      gradeLevel: 4,
      subjectId: 5,
      subjectName: '历史',
      icon: IconHistorySenior,
    },
    {
      gradeLevel: 4,
      subjectId: 6,
      subjectName: '地理',
      icon: IconGeographySenior,
    },
    {
      gradeLevel: 4,
      subjectId: 7,
      subjectName: '物理',
      icon: IconPhysicsSenior,
    },
    {
      gradeLevel: 4,
      subjectId: 8,
      subjectName: '化学',
      icon: IconChemistrySenior,
    },
    {
      gradeLevel: 4,
      subjectId: 9,
      subjectName: '生物',
      icon: IconBiologySenior,
    },
    {
      gradeLevel: 4,
      subjectId: 10,
      subjectName: '体育',
      icon: IconSportSenior,
    },
    {
      gradeLevel: 4,
      subjectId: 11,
      subjectName: '音乐',
      icon: IconMusicSenior,
    },
    {
      gradeLevel: 4,
      subjectId: 12,
      subjectName: '美术',
      icon: IconArtSenior,
    },
    {
      gradeLevel: 4,
      subjectId: 13,
      subjectName: '信息技术',
      icon: IconTechnologySenior,
    },
    {
      gradeLevel: 4,
      subjectId: 15,
      subjectName: '政治',
      icon: IconPoliticsSenior,
    },
    {
      gradeLevel: 4,
      subjectId: 16,
      subjectName: '日语',
      icon: IconJapaneseSenior,
    },
    {
      gradeLevel: 4,
      subjectId: 17,
      subjectName: '科学',
      icon: IconTechnologySenior,
    },
    {
      gradeLevel: 4,
      subjectId: 18,
      subjectName: '俄语',
      icon: IconRussianSenior,
    },
  ]

  export default {
    components: {
      ModalPreview,
      ModalCopy,
      ModalShareAnswerSheet,
      ModalRecycleBin,
      ModalCreate,
      SearchInput,
      NoData,
    },
    data() {
      return {
        // 学段学科字典
        stages: [],
        allSubjects: [],

        // 查询条件
        answerSheetMode: null,
        answerSheetSourceType: null,
        answerSheetStageId: null,
        answerSheetSubjectId: null,
        keyword: '',
        referExam: false,

        // 加载中
        loading: false,

        // 答题卡列表
        answerSheetList: [],

        // 查看
        modalAnswerSheetPictureShowed: false,
        pictureAnswerSheetId: '',
        modalAnswerSheetUrls: [],
        previewAnswerSheet: null,

        // 复制
        modalCopyAnswerSheetShowed: false,
        copySourceAnswerSheet: null,

        // 分享
        modalShareAnswerSheetShowed: false,
        answerSheetForShare: null,

        // 回收站
        showModalRecycleBin: false,

        // 创建新卡
        showModalCreate: false,
      }
    },

    computed: {
      // 模式
      modes() {
        return ModeEnum.getEntries()
      },
      // 来源
      sourceTypes() {
        return (SourceTypeEnum.getEntries() || []).filter(item => item.id != SourceTypeEnum.Exam.id)
      },
      // 选中学段的学科
      stageSubjects() {
        if (!this.answerSheetStageId) {
          return []
        }
        return this.stages.find(stage => stage.id === this.answerSheetStageId).subjects
      },
      emptyText() {
        if (
          this.answerSheetMode ||
          this.answerSheetSourceType ||
          this.answerSheetStageId ||
          this.answerSheetSubjectId ||
          this.keyword
        ) {
          return '该筛选条件下暂无答题卡'
        }
        return '暂无答题卡'
      },
    },

    created() {
      this.loading = true
      this.fetchStageGradeSubjects()
        .then(() => this.fetchPersonalAnswerSheetList())
        .finally(() => {
          this.loading = false
        })
    },

    methods: {
      /**
       * 加载学段学科
       */
      fetchStageGradeSubjects() {
        return apiGetStageGradeSubjects().then(response => {
          this.stages = (response || []).map(x => {
            let stageSubjects = []
            x.grades.forEach(grade =>
              grade.subjects.forEach(gradeSubject => {
                if (!stageSubjects.some(stageSubject => stageSubject.id === gradeSubject.id)) {
                  stageSubjects.push(gradeSubject)
                }
                if (!this.allSubjects.some(subject => subject.id === gradeSubject.id)) {
                  this.allSubjects.push(gradeSubject)
                }
              })
            )
            stageSubjects.sort((a, b) => a.id - b.id)

            return {
              id: x.id,
              name: x.name,
              subjects: stageSubjects,
            }
          })
        })
      },

      /**
       * 加载答题卡列表
       */
      fetchPersonalAnswerSheetList() {
        this.loading = true
        return apiGetMyAnswerSheetList({
          name: (this.keyword && this.keyword.trim()) || undefined,
          mode: this.answerSheetMode || undefined,
          status: 0,
          sourceType: this.answerSheetSourceType || undefined,
          stageId: this.answerSheetStageId || undefined,
          subjectId: this.answerSheetSubjectId || undefined,
          referExam: this.referExam,
        })
          .then(response => {
            response.forEach(r => {
              if (!r.referExams) {
                r.referExams = []
              }
              r.referExams.forEach(exam => {
                exam.subjectNames = exam.subjectNameList.join('、')
              })
              r.modeName = ModeEnum.getNameById(r.mode)
              r.sourceTypeName = SourceTypeEnum.getNameById(r.sourceType)
              r.stageSubjectName =
                (this.stages.find(stage => stage.id === r.gradeLevel) || { name: '' }).name +
                (this.allSubjects.find(subject => subject.id === r.subjectId) || { name: '' }).name
              let createTime = new Date(r.createTime)
              r.creatTime = formatDateTime(createTime)
              r.subjectIcon = this.getIconSubject(r.gradeLevel, r.subjectId)
            })
            this.answerSheetList = response
          })
          .finally(() => {
            this.loading = false
          })
      },
      getIconSubject(gradeLevel, subjectId) {
        const stageSubject = iconSubjects.find(item => item.gradeLevel == gradeLevel && item.subjectId == subjectId)
        if (stageSubject) {
          return stageSubject.icon
        }
        return IconSubjectDefault
      },

      changeAnswerSheetStageId() {
        if (!this.answerSheetStageId || !this.stageSubjects.some(subject => subject.id === this.answerSheetSubjectId)) {
          this.answerSheetSubjectId = null
        }
        this.fetchPersonalAnswerSheetList()
      },

      /**
       * 答题卡操作
       */
      // 分享的题库组卷的答题卡不可编辑
      enableEdit(item) {
        return item.sourceType != SourceTypeEnum.Paper.id || !item.shareBy
      },
      handleEdit(item) {
        let gotoEdit = () => {
          if (item.sourceType == SourceTypeEnum.Paper.id) {
            this.$router.push({
              name: 'answerSheet-editPaper',
              params: {
                paperId: item.sourceId,
              },
            })
          } else if (item.sourceType == SourceTypeEnum.Manual.id) {
            this.$router.push({
              name: 'answerSheet-editManual',
              params: {
                sheetId: item.id,
              },
            })
          }
        }

        if (item.referExams.length > 0) {
          this.$Modal.confirm({
            title: '本答题卡已关联考试',
            content:
              '修改本答题卡后，<strong>关联考试的答题卡不会改变</strong>。<br>由于考试答题卡与本答题卡可能不一致，若您需下载考试答题卡，请一律进入考试设置页面下载',
            okText: '继续编辑',
            onOk: () => {
              gotoEdit()
            },
          })
        } else {
          gotoEdit()
        }
      },

      async handlePreview(sheet) {
        this.$Spin.show({
          render: h =>
            h(
              'div',
              {
                style: {
                  fontSize: '24px',
                },
              },
              '加载中，请稍候'
            ),
        })
        try {
          this.previewAnswerSheet = await apiGetAnswerSheetDetail(sheet.id)
          this.modalAnswerSheetPictureShowed = true
        } finally {
          this.$Spin.hide()
        }
      },

      handleDownload(sheet) {
        let doDownload = () => {
          sheet.downloading = true
          Promise.all([apiGetAnswerSheetInfo(sheet.id), apiGetAnswerSheetPDFUrl(sheet.id)])
            .then(([sheetInfo, pdfUrl]) => {
              if (sheetInfo && pdfUrl) {
                let fileName = `${sheetInfo.name}.pdf`
                downloadUrl(pdfUrl, fileName, () => {
                  sheet.downloading = false
                })
              } else {
                sheet.downloading = false
              }
            })
            .catch(err => {
              sheet.downloading = false
              throw err
            })
        }

        if (sheet.referExams.length > 0) {
          this.$Modal.confirm({
            title: '本答题卡已关联考试',
            content: '由于考试答题卡与本答题卡可能不一致，若您需下载考试答题卡，请一律进入考试设置页面下载',
            okText: '继续下载',
            onOk: () => {
              doDownload()
            },
          })
        } else {
          doDownload()
        }
      },

      handleDropdownClick(item, name) {
        if (name == 'copy') {
          this.handleCopy(item)
        } else if (name == 'share') {
          this.handleShare(item)
        } else if (name == 'delete') {
          this.handleDelete(item.id)
        } else if (name == 'changeTemplate') {
          this.handleChangeTemplate(item)
        }
      },

      // 自主创建的答题卡可复制
      enableCopy(item) {
        return item.sourceType == SourceTypeEnum.Manual.id
      },
      handleCopy(item) {
        this.copySourceAnswerSheet = item
        this.modalCopyAnswerSheetShowed = true
      },
      handleShare(sheet) {
        this.answerSheetForShare = sheet
        this.modalShareAnswerSheetShowed = true
      },
      handleDelete(id) {
        apiUpdateAnswerSheetStatus({
          id: id,
          status: 1,
        })
          .then(() => {
            this.$Message.success({
              duration: 2,
              content: '已删除',
            })
          })
          .finally(() => this.fetchPersonalAnswerSheetList())
      },
      handleChangeTemplate(sheet) {
        this.$router.push({
          name: 'answerSheet-changeTemplate',
          params: {
            id: sheet.id,
          },
        })
      },

      /**
       * 页面操作
       */
      handleBtnSkipToEmarking() {
        // this.$router.push({
        //   name: 'emarking',
        // })
        this.$router.back()
      },
      createNewAnswerSheet() {
        this.$router.push({
          name: 'answerSheet-editManual',
          params: {
            sheetId: 'new',
          },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .tab-list {
    position: relative;

    .header {
      @include flex(row, space-between, center);
      height: 50px;
      margin-bottom: 15px;
      padding: 0 20px;
      border-bottom: 1px solid #f0f0f0;
    }
  }

  .section-neck {
    @include flex(row, space-between, center);
    padding: 0 20px 15px;
    // border-bottom: 1px solid #f0f0f0;

    .filter {
      @include flex(row, flex-start, center);

      .filter-item {
        &:not(:first-child) {
          margin-left: 24px;
        }

        .filter-label {
          margin-right: 4px;
        }

        .filter-select {
          width: 120px;
        }

        .input-search {
          display: inline-block;
          width: 220px;
        }
      }
    }

    .switch {
      @include flex(row, flex-start, center);

      .switch-label {
        margin-left: 4px;
      }
    }
  }

  .section-list {
    padding: 0 20px;

    .answer-sheet {
      padding-right: 16px;
      padding-left: 16px;
      border: 1px solid transparent;
      border-radius: 4px;
      box-shadow: 2px 2px 12px 0px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;

      &:hover {
        border-color: $color-primary;
        transform: scale(1.02);
      }

      &:not(:last-child) {
        margin-bottom: 24px;
      }

      .answer-sheet-inner {
        @include flex(row, flex-start, flex-start);
        padding-top: 20px;
        padding-bottom: 20px;
        // border-bottom: 1px solid #f0f0f0;

        .box-icon {
          flex-shrink: 0;
          margin-right: 16px;
          font-size: 0;

          .icon-subject {
            width: 80px;
            height: 80px;
          }
        }

        .main {
          flex: 1;

          .main-top,
          .main-middle {
            @include flex(row, space-between, center);
          }

          .main-bottom,
          .main-middle {
            margin-top: 24px;
          }

          .topics {
            .topic {
              display: inline-block;
              // height: 24px;
              margin-right: 20px;
              padding: 0 6px;
              // border: 1px solid #999;
              border-radius: 2px;
              font-size: 12px;
              // line-height: 24px;
            }

            .question-count {
              margin-right: 4px;
              margin-left: 4px;
              color: $color-primary;
            }
          }

          .share,
          .exams {
            color: $color-icon;
            font-size: 12px;
          }

          .share {
            margin-bottom: 8px;
          }

          .name {
            font-weight: bold;
            font-size: $font-size-medium-x;
          }

          .tag-answer-sheet {
            display: inline-block;
            height: 24px;
            margin-left: 20px;
            padding: 0 10px;
            border: 1px solid #dfdfdf;
            border-radius: 15px;
            color: $color-primary;
            font-weight: normal;
            font-size: 12px;
            line-height: 24px;
            background-color: #eaf0ff;
          }

          .tag-online {
            border-color: #409eff;
            color: #409eff;
            background-color: #eaf0ff;
          }

          .tag-postscan {
            border-color: #f56c6c;
            color: #f56c6c;
            background-color: #ffe5de;
          }

          .create-time {
            color: #666;
            font-size: 12px;
          }

          .text-create-time {
            display: inline-block;
            margin-left: 10px;
          }

          .action {
            @include flex(row, flex-end, center);
            flex-shrink: 0;
            align-self: center;
            margin-right: 0;
            margin-left: 16px;

            .btn {
              min-width: 60px;
              height: 28px;
              margin-left: 8px;
              padding: 0 12px;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .no-answer-sheet {
    color: $color-icon;
    line-height: 200px;
    text-align: center;
  }

  .copy-answer-sheet-panel {
    .panel-item {
      @include flex(row, flex-start, center);

      .panel-label {
        margin-right: 40px;
      }

      .panel-input {
        width: 440px;
      }
    }

    .checkbox-item {
      display: inline-block;
      margin-top: 2em;

      .panel-label {
        margin-right: 20px;
      }

      &:not(:first-child) {
        margin-left: 2em;
      }
    }
  }

  .empty {
    font-size: $font-size-large;
    line-height: 200px;
  }

  .modal-answer-sheet-picture-container {
    height: 65vh;
    border: 1px solid $color-disabled;
    overflow-y: auto;

    .answer-sheet-image {
      width: 100%;
    }
  }

  .btn-normal-height {
    height: 28px;
    font-size: 14px;
  }

  .btn-success {
    border-color: #13ce66;
    color: #13ce66;
    background-color: #e8faf0;
  }

  .btn-info {
    border-color: #409eff;
    color: #409eff;
    background-color: #ecf5ff;
  }

  .btn-warning {
    border-color: #fea860;
    color: #fea860;
    background-color: #fffaf5;
  }

  .btn-error {
    border-color: #ff4949;
    color: #ff4949;
    background-color: #ffeded;
  }

  @keyframes load-spin {
    from {
      transform: rotate(0deg);
    }

    50% {
      transform: rotate(180deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
</style>
