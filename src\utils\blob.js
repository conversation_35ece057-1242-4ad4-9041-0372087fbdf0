export function base64ToBlob(base64) {
  // 拆分头部和实际数据
  const [header, data] = base64.split(',')
  // 获取mime类型，注意去除左右方括号
  let mime = header.match(/:(.*?);/)[1]
  if (mime.startsWith('[')) {
    mime = mime.slice(1)
  }
  if (mime.endsWith(']')) {
    mime = mime.slice(0, -1)
  }
  // 将base64数据转换为uint8array
  const bstr = atob(data)
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  // 返回blob
  return new Blob([u8arr], { type: mime })
}
