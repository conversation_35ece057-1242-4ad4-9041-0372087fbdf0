export function getFile(isMultiple = false, fileTypes = [], callback = () => {}) {
  let inputElement = document.createElement('input')

  inputElement.type = 'file'
  inputElement.accept =
    fileTypes && fileTypes.length ? fileTypes.join(',') : ['audio/*', 'video/*', 'image/*', 'text/plain']
  inputElement.onchange = function () {
    callback(isMultiple ? this.files : this.files[0])
  }

  inputElement.click()
}

export function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    let reader = new FileReader()
    reader.onloadend = () => {
      let base64 = reader.result.split(',')[1]
      if (!base64) {
        reject('base64字符串为空')
      } else {
        resolve(base64)
      }
    }
    reader.onerror = err => {
      reject(err)
    }
    reader.readAsDataURL(file)
  })
}

export function arrayBufferToBase64(buffer) {
  let binary = ''
  const bytes = new Uint8Array(buffer)
  const len = bytes.byteLength
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return btoa(binary)
}
