import { uid, groupArray, unGroupArray } from './common'
import { numberToChinese } from '@/utils/number'
import store from '@/store/index'

const MaxQuestionCode = 200
const MaxQuestionScore = 1000

export class Paper {
  constructor(params) {
    this.examId = (params && params.examId) || ''
    this.examSubjectId = (params && params.examSubjectId) || ''
    this.bindPaper = null
    this.objectiveQuestions = []
    this.subjectiveQuestions = []
  }

  get distinctTopicQuestionCodes() {
    let questions = []
    this.objectiveQuestions.forEach(q => {
      questions.push({
        topicCode: q.topicCode,
        questionCode: q.questionCode,
      })
    })
    this.subjectiveQuestions.forEach(q => {
      // 小题会导致题号重复
      if (questions.every(x => x.questionCode !== q.questionCode)) {
        questions.push({
          topicCode: q.topicCode,
          questionCode: q.questionCode,
        })
      }
    })
    questions.sort((a, b) => a.questionCode - b.questionCode)
    return questions
  }

  get distinctQuestionCodes() {
    return this.distinctTopicQuestionCodes.map(q => q.questionCode)
  }

  get questions() {
    let list = []
    this.objectiveQuestions.forEach(q => {
      list.push({
        ...q,
        isObjective: true,
      })
    })
    this.subjectiveQuestions.forEach(q => {
      list.push({
        ...q,
        isObjective: false,
      })
    })

    list.sort((a, b) => a.questionCode - b.questionCode)
    return list
  }

  import({ bindPaper, objectiveQuestions, subjectiveQuestions }) {
    this.bindPaper = bindPaper || null
    let questionTypes = store.getters['emarking/questionTypes']()
    this.objectiveQuestions = (objectiveQuestions || []).map(x => ({
      questionId: x.id,
      topicCode: x.topicCode,
      topicCodeInChinese: numberToChinese(x.topicCode),
      topicName: x.topicName || numberToChinese(x.topicCode),
      questionCode: x.questionCode,
      fullScore: x.fullScore,
      standardAnswer: x.answer,
      optionCount: x.optionCount,
      questionType: questionTypes.find(t => t.id === Number(x.questionType)) || { id: '', name: '' },
      answerScoreList: x.answerScoreList,
      originalQuestionId: x.questionId,
      originalBranchId: x.branchId,
    }))

    this.subjectiveQuestions = (subjectiveQuestions || []).map(x => ({
      questionId: x.id,
      topicCode: x.topicCode,
      topicCodeInChinese: numberToChinese(x.topicCode),
      topicName: x.topicName || numberToChinese(x.topicName),
      questionCode: x.questionCode,
      branchCode: x.branchCode || 0,
      questionName: x.branchCode ? x.questionCode + '.' + x.branchCode : String(x.questionCode),
      fullScore: x.fullScore,
      isAdditional: x.isAdditional,
      originalQuestionId: x.questionId,
      originalBranchId: x.branchId,
    }))

    this.sortQuestionCodes()

    return this
  }

  exportObjectiveQuestions() {
    return this.objectiveQuestions.map(x => ({
      id: undefined,
      topicCode: x.topicCode,
      topicName: x.topicName,
      questionCode: x.questionCode,
      fullScore: x.fullScore,
      optionCount: x.questionType.name === '判断题' ? 2 : x.optionCount,
      questionType: x.questionType.id,
      answer: x.standardAnswer,
      answerScoreList: x.answerScoreList,
      questionId: x.originalQuestionId || 0,
      branchId: x.originalBranchId || 0,
    }))
  }

  exportSubjectiveQuestions() {
    return this.subjectiveQuestions.map(x => ({
      id: undefined,
      topicCode: x.topicCode,
      topicName: x.topicName,
      questionCode: x.questionCode,
      branchCode: x.branchCode || 0,
      fullScore: x.fullScore,
      isAdditional: x.isAdditional,
      questionId: x.originalQuestionId || 0,
      branchId: x.originalBranchId || 0,
    }))
  }

  sortQuestionCodes() {
    let objectiveQuestionsByTopic = groupArray(this.objectiveQuestions, q => q.topicCode).sort((a, b) => a.key - b.key)
    objectiveQuestionsByTopic.forEach(topic => {
      topic.group.sort((a, b) => a.questionCode - b.questionCode)
    })
    this.objectiveQuestions = unGroupArray(objectiveQuestionsByTopic, t => t.group)

    let subjectiveQuestionsByTopic = groupArray(this.subjectiveQuestions, q => q.topicCode).sort(
      (a, b) => a.key - b.key
    )
    subjectiveQuestionsByTopic.forEach(topic => {
      topic.group.sort((a, b) => a.questionCode * 10000 + a.branchCode - (b.questionCode * 10000 + b.branchCode))
    })
    this.subjectiveQuestions = unGroupArray(subjectiveQuestionsByTopic, t => t.group)

    // 某些浏览器排序不稳定，按大题号排序会导致大题内题目乱序
    // this.objectiveQuestions.sort((a, b) => a.questionCode - b.questionCode)
    // this.objectiveQuestions.sort((a, b) => a.topicCode - b.topicCode)
    // this.subjectiveQuestions.sort((a, b) => Number(a.questionName) - Number(b.questionName))
    // this.subjectiveQuestions.sort((a, b) => a.topicCode - b.topicCode)
  }

  getDuplicatedCodesString(codes) {
    let duplicatedCodes = groupArray(codes, x => x)
      .map(g => ({
        code: g.key,
        count: g.group.length,
      }))
      .filter(x => x.count > 1)
      .map(x => x.code)
      .sort((a, b) => a - b)
    let message = duplicatedCodes.slice(0, 10).join('，')
    if (duplicatedCodes.length >= 10) {
      message += '等'
    }
    return message
  }

  deleteObjectiveTopic(topicCode) {
    this.objectiveQuestions = this.objectiveQuestions.filter(q => q.topicCode !== topicCode)
  }

  deleteSubjectiveTopic(topicCode) {
    this.subjectiveQuestions = this.subjectiveQuestions.filter(q => q.topicCode !== topicCode)
  }

  addObjectiveQuestionsByRules(rules) {
    if (rules.length === 0) {
      return {
        message: '请添加题目',
      }
    }

    let questionTypes = store.getters['emarking/questionTypes']()
    let questionTypeIds = questionTypes.map(x => x.id)
    let trueOrFalseQuestionTypeId = (store.getters['emarking/questionTypes']().find(x => x.name === '判断题') || {}).id

    // 检查字段本身约束
    for (let i = 0; i < rules.length; i++) {
      let rule = rules[i]
      let prefix = `第${i + 1}行：`

      if (!rule.topicCode) {
        return {
          message: prefix + '未选择大题',
        }
      }

      if (!(rule.questionTypeId && questionTypeIds.includes(rule.questionTypeId))) {
        return {
          message: prefix + '未选择题型',
        }
      }

      if (rule.questionTypeId !== trueOrFalseQuestionTypeId) {
        if (!(Number.isInteger(rule.optionCount) && rule.optionCount >= 2 && rule.optionCount <= 10))
          return {
            message: prefix + '未选择选项个数',
          }
      }

      rule.codeFrom = Number(rule.codeFrom)
      rule.codeTo = Number(rule.codeTo)
      rule.score = Number(rule.score)
      rule.optionCount = Number(rule.optionCount)

      if (
        !(Number.isInteger(rule.codeFrom) && rule.codeFrom >= 1) ||
        !(Number.isInteger(rule.codeTo) && rule.codeTo >= 1)
      ) {
        return {
          message: prefix + '题号必须是正整数',
        }
      }
      if (rule.codeFrom > rule.codeTo) {
        return {
          message: prefix + '开始题号不能大于结束题号',
        }
      }
      if (rule.codeFrom > MaxQuestionCode || rule.codeTo > MaxQuestionCode) {
        return {
          message: prefix + '题号过大',
        }
      }

      if (!(rule.score > 0)) {
        return {
          message: prefix + '分数必须是正数',
        }
      } else if (rule.score > MaxQuestionScore) {
        return {
          message: prefix + '分数过大',
        }
      } else if (Math.floor(rule.score * 10) != rule.score * 10) {
        return {
          message: prefix + '分数最多精确到1位小数',
        }
      }
    }

    // 检查重复
    let newQuestions = []
    rules.forEach(rule => {
      for (let i = rule.codeFrom; i <= rule.codeTo; i++) {
        newQuestions.push({
          topicCode: rule.topicCode,
          questionCode: i,
          questionType: questionTypes.find(x => x.id === rule.questionTypeId),
          optionCount: rule.questionTypeId === trueOrFalseQuestionTypeId ? 2 : rule.optionCount,
          score: rule.score,
        })
      }
    })
    newQuestions.sort((a, b) => a.questionCode - b.questionCode)

    // 题目内部检查重复
    let newQuestionCodes = newQuestions.map(q => q.questionCode)
    let checkInnerDuplicateMessage = this.getDuplicatedCodesString(newQuestionCodes)
    if (checkInnerDuplicateMessage) {
      return {
        message: `题号${checkInnerDuplicateMessage}重复`,
      }
    }

    // 和现有题目对比检查重复
    let allQuestionCodes = [...newQuestionCodes, ...this.distinctTopicQuestionCodes.map(q => q.questionCode)]
    let checkAllDuplicatedMessage = this.getDuplicatedCodesString(allQuestionCodes)
    if (checkAllDuplicatedMessage) {
      return {
        message: `题号${checkAllDuplicatedMessage}和现有题号重复`,
      }
    }

    // 通过检查
    newQuestions = newQuestions.map(q => ({
      questionId: uid(),
      topicCode: q.topicCode,
      topicCodeInChinese: numberToChinese(q.topicCode),
      topicName: numberToChinese(q.topicCode),
      questionCode: q.questionCode,
      questionType: q.questionType,
      fullScore: q.score,
      optionCount: q.optionCount,
      standardAnswer: '',
      answerScoreList: '',
      originalQuestionId: 0,
      originalBranchId: 0,
    }))
    this.objectiveQuestions.push(...newQuestions)
    this.sortQuestionCodes()

    return {
      message: '',
      questions: newQuestions,
    }
  }

  addSubjectiveQuestions(newQuestions) {
    if (newQuestions.length === 0) {
      return {
        message: '请添加题目',
      }
    }

    for (let q of newQuestions) {
      for (let b of q.branches) {
        if (!(Number(b.score) > 0 && Number(b.score) <= MaxQuestionScore)) {
          if (q.branches.length === 1) {
            return {
              message: `${q.questionCode}题分数错误`,
            }
          } else {
            return {
              message: `${q.questionCode}.${b.branchCode}题分数错误`,
            }
          }
        }
      }
    }
    newQuestions.sort((a, b) => a.questionCode - b.questionCode)

    // 题目内部检查重复
    let newQuestionCodes = newQuestions.map(q => q.questionCode)
    let checkInnerDuplicateMessage = this.getDuplicatedCodesString(newQuestionCodes)
    if (checkInnerDuplicateMessage) {
      return {
        message: `题号${checkInnerDuplicateMessage}重复`,
      }
    }

    // 和现有题目对比检查重复
    let allQuestionCodes = [...newQuestionCodes, ...this.distinctTopicQuestionCodes.map(q => q.questionCode)]
    let checkAllDuplicatedMessage = this.getDuplicatedCodesString(allQuestionCodes)
    if (checkAllDuplicatedMessage) {
      return {
        message: `题号${checkAllDuplicatedMessage}和现有题号重复`,
      }
    }

    let list = []
    newQuestions.forEach(q => {
      q.branches.forEach(b => {
        list.push({
          questionId: uid(),
          topicCode: q.topicCode,
          topicCodeInChinese: numberToChinese(q.topicCode),
          topicName: numberToChinese(q.topicCode),
          questionCode: q.questionCode,
          branchCode: q.branches.length > 1 ? b.branchCode : 0,
          questionName: q.branches.length > 1 ? q.questionCode + '.' + b.branchCode : String(q.questionCode),
          fullScore: b.score,
          isAdditional: Boolean(b.isAdditional),
          originalQuestionId: 0,
          originalBranchId: 0,
        })
      })
    })

    this.subjectiveQuestions.push(...list)
    this.sortQuestionCodes()

    return {
      message: '',
      questions: list,
    }
  }

  checkObjectiveQuestion(q) {
    // 题号
    if (!q.questionCode) {
      return '未输入题号'
    } else if (!(Number.isInteger(q.questionCode) && q.questionCode >= 1)) {
      return '题号必须是正整数'
    } else if (q.questionCode > MaxQuestionCode) {
      return '题号过大'
    }

    // 题型
    if (!q.questionType.id) {
      return '未选择题型'
    }

    // 选项个数
    if (!(Number.isInteger(q.optionCount) && 2 <= q.optionCount && 10 >= q.optionCount)) {
      return '选项个数错误'
    }

    // 满分
    q.fullScore = Number(q.fullScore)
    if (!(q.fullScore > 0)) {
      return '满分错误'
    } else if (q.fullScore > MaxQuestionScore) {
      return '满分过大'
    }

    // 答案
    q.standardAnswer = String(q.standardAnswer || '')
      .replace(' ', '')
      .toUpperCase()
    if (q.standardAnswer) {
      if (q.questionType.name === '单选题') {
        if (
          !(
            q.standardAnswer.length === 1 &&
            q.standardAnswer >= 'A' &&
            q.standardAnswer < String.fromCharCode(65 + q.optionCount)
          )
        ) {
          return '答案格式错误'
        }
      } else if (q.questionType.name === '多选题') {
        if (!(q.standardAnswer.length <= q.optionCount)) {
          return '答案格式错误'
        }

        let chars = Array.from(q.standardAnswer)
        if (groupArray(chars, x => x).some(g => g.group.length > 1)) {
          return '答案格式错误'
        }
        if (!chars.every(char => char >= 'A' && char < String.fromCharCode(65 + q.optionCount))) {
          return '答案格式错误'
        }

        q.standardAnswer = chars.sort().join('')
      } else if (q.questionType.name === '判断题') {
        if (q.standardAnswer === 'A') {
          q.standardAnswer = 'T'
        } else if (q.standardAnswer === 'B') {
          q.standardAnswer = 'F'
        }

        if (q.standardAnswer !== 'T' && q.standardAnswer !== 'F') {
          return '答案格式错误'
        }
      }
    }

    // 其他答案分数
    if (!(q.questionType.name === '多选题')) {
      q.answerScoreList = ''
    } else {
      q.answerScoreList = String(q.answerScoreList || '')
        .replace(' ', '')
        .replace('；', ';')
        .toUpperCase()
      let answerScores = q.answerScoreList.split(';').filter(s => s.length > 0)
      let resultAnswerScores = []
      if (answerScores.length > 0) {
        for (let pair of answerScores) {
          let arr = pair.split('=')
          if (arr.length !== 2) {
            return '其他答案分数格式错误'
          }
          let answer = arr[0],
            score = Number(arr[1])
          if (!(answer.length <= q.optionCount)) {
            return '其他答案分数格式错误'
          }

          let chars = Array.from(answer)
          if (groupArray(chars, x => x).some(g => g.group.length > 1)) {
            return '其他答案分数格式错误'
          }
          if (!chars.every(char => char >= 'A' && char < String.fromCharCode(65 + q.optionCount))) {
            return '其他答案分数格式错误'
          }
          if (!(score > 0 && score <= q.fullScore)) {
            return '其他答案分数格式错误'
          }
          resultAnswerScores.push({
            answer: chars.sort().join(''),
            score,
          })
        }
      }
      if (groupArray(resultAnswerScores, x => x.answer).some(g => g.group.length > 1)) {
        return '其他答案分数格式错误'
      } else if (resultAnswerScores.some(x => x.answer === q.standardAnswer && x.score !== q.fullScore)) {
        return '其他答案分数与标准答案分数不一致'
      } else {
        q.answerScoreList = resultAnswerScores.map(x => `${x.answer}=${x.score}`).join(';')
      }
    }
  }

  checkObjectiveTopicQuestions(questions) {
    for (let q of questions) {
      let message = this.checkObjectiveQuestion(q)
      if (message) {
        return `第${q.questionCode}题：${message}`
      }
    }
  }

  changeObjectiveTopicQuestions(topicCode, questions) {
    for (let q of questions) {
      let message = this.checkObjectiveQuestion(q)
      if (message) {
        return {
          message: `第${q.questionCode}题：${message}`,
        }
      }
    }

    let newQuestionCodes = questions.map(q => q.questionCode)
    let oldObjectiveQuestions = this.objectiveQuestions
    this.deleteObjectiveTopic(topicCode)
    let allQuestionCodes = [...newQuestionCodes, ...this.distinctTopicQuestionCodes.map(q => q.questionCode)]
    let checkAllDuplicatedMessage = this.getDuplicatedCodesString(allQuestionCodes)
    if (checkAllDuplicatedMessage) {
      this.objectiveQuestions = oldObjectiveQuestions
      return {
        message: `题号${checkAllDuplicatedMessage}重复`,
      }
    } else {
      this.objectiveQuestions.push(...questions)
      this.sortQuestionCodes()

      return {
        questions,
      }
    }
  }

  changeObjectiveQuestionAnswers(questions) {
    for (let q of questions) {
      let message = this.checkObjectiveQuestion(q)
      if (message) {
        return {
          message: `第${q.questionCode}题：${message}`,
        }
      }
    }

    let unExistingCodes = questions
      .filter(q => this.objectiveQuestions.every(x => x.questionCode !== q.questionCode))
      .map(q => q.questionCode)
    if (unExistingCodes.length > 0) {
      return {
        message: `题号${unExistingCodes.slice(0, 10).join('，')}${unExistingCodes.length >= 10 ? '等' : ''}不存在`,
      }
    }

    questions.forEach(q => {
      let question = this.objectiveQuestions.find(x => x.questionCode === q.questionCode)
      question.fullScore = q.fullScore
      question.standardAnswer = q.standardAnswer
      question.answerScoreList = q.answerScoreList
    })

    return {
      questions,
    }
  }

  checkSubjectiveQuestion(q) {
    // 题号
    if (!q.questionCode) {
      return '未输入题号'
    } else if (!(Number.isInteger(q.questionCode) && q.questionCode >= 1)) {
      return '题号必须是正整数'
    } else if (q.questionCode > MaxQuestionCode) {
      return '题号过大'
    }

    // 小题号
    if (q.branchCode && !(Number.isInteger(q.branchCode) && q.branchCode >= 1)) {
      return '小题号错误'
    }

    // 满分
    q.fullScore = Number(q.fullScore)
    if (!(q.fullScore > 0)) {
      return '满分错误'
    } else if (q.fullScore > MaxQuestionScore) {
      return '满分过大'
    }

    // 附加题
    q.isAdditional = Boolean(q.isAdditional)
  }

  changeSubjectiveTopicQuestions(topicCode, questions) {
    for (let q of questions) {
      let message = this.checkSubjectiveQuestion(q)
      if (message) {
        return {
          message: `第${q.questionCode}题：${message}`,
        }
      }
    }

    // 编辑主观题时必须加originalQuestionCode以标记原来题号
    let newQuestionCodes = groupArray(questions, q => q.originalQuestionCode).map(g => g.group[0].questionCode)
    let oldSubjectiveQuestions = this.subjectiveQuestions
    this.deleteSubjectiveTopic(topicCode)
    let allQuestionCodes = [...newQuestionCodes, ...this.distinctTopicQuestionCodes.map(q => q.questionCode)]
    let checkAllDuplicatedMessage = this.getDuplicatedCodesString(allQuestionCodes)
    if (checkAllDuplicatedMessage) {
      this.subjectiveQuestions = oldSubjectiveQuestions
      return {
        message: `题号${checkAllDuplicatedMessage}重复`,
      }
    } else {
      this.subjectiveQuestions.push(...questions)
      this.sortQuestionCodes()

      return {
        questions,
      }
    }
  }

  changeSubjectiveQuestionAnswers(questions) {
    for (let q of questions) {
      let message = this.checkSubjectiveQuestion(q)
      if (message) {
        return {
          message: `第${q.questionCode}题：${message}`,
        }
      }
    }

    let unExistingCodes = questions
      .filter(q => this.subjectiveQuestions.every(x => x.questionName !== q.questionName))
      .map(q => q.questionName)
    if (unExistingCodes.length > 0) {
      return {
        message: `题号${unExistingCodes.slice(0, 10).join('，')}${unExistingCodes.length >= 10 ? '等' : ''}不存在`,
      }
    }

    questions.forEach(q => {
      let question = this.subjectiveQuestions.find(x => x.questionName === q.questionName)
      question.fullScore = q.fullScore
      question.isAdditional = q.isAdditional
    })

    return {
      questions,
    }
  }
}
