<template>
  <div class="tab-block">
    <div v-if="canEditExam && enableAiScore" class="section-actions">
      <TextButton type="primary" @click="changeToAiScore">转为AI评卷</TextButton>
      <TextButton type="primary" @click="changeToManualScore">转为人工评卷</TextButton>
      <Divider v-if="subjectStatusSuspended || subjectStatusStarted" type="vertical"></Divider>
      <TextButton v-if="subjectStatusSuspended" type="primary" @click="startMark">开启科目评卷</TextButton>
      <TextButton v-if="subjectStatusStarted" type="warning" @click="suspendMark">暂停科目评卷</TextButton>
    </div>
    <Table
      class="table-blocks"
      :columns="blockColumns"
      :data="blocks"
      border
      @on-selection-change="handleSelectTableRows"
    >
      <template #blockName="{ row }">
        <div class="box-block-name">
          <span>{{ row.blockName }}</span>
          <div v-if="isBlockAiScore(row)" class="btn btn-primary" :title="getBlockAiScoreTypeName(row)">
            <span class="icon-ai">AI</span>
          </div>
        </div>
      </template>
    </Table>

    <ModalPrompt
      v-model="showModalPrompt"
      :exam-subject-id="examSubjectId"
      :init-block-id="modalPromptInitBlockId"
      :blocks="blocks"
      @refresh-prompt="refreshBlocks"
    ></ModalPrompt>
  </div>
</template>

<script>
  import TextButton from '@/components/text_button'
  import ModalPrompt from './modal_prompt'

  import { apiChangeToAiScore, apiChangeToManualScore, apiChangeAiScoreStatus } from '@/api/emarking/ai'
  import { apiResetBlockMarking, apiStartMarking, apiSuspendMarking } from '@/api/emarking'

  import { sumByDefaultZero } from '@/utils/math'
  import AiScoreTypeEnum from '@/enum/emarking/ai_score_type'
  import AiScoreStatusEnum from '@/enum/emarking/ai_score_status'
  import SubjectStatusEnum from '@/enum/emarking/subject_status'

  export default {
    components: {
      ModalPrompt,
    },
    props: {
      examId: String,
      examSubjectId: String,
      subjectStatus: {
        type: Number,
        default: SubjectStatusEnum.Started.id,
      },
      blocks: Array,
      subjectives: Array,
      canEditExam: Boolean,
      enableAiScore: Boolean,
    },
    emits: ['refresh-blocks', 'refresh-subject'],
    data() {
      return {
        selectedRows: [],
        showModalPrompt: false,
        modalPromptInitBlockId: '',
      }
    },
    computed: {
      subjectStatusStarted() {
        return this.subjectStatus == SubjectStatusEnum.Started.id
      },
      subjectStatusSuspended() {
        return this.subjectStatus == SubjectStatusEnum.Suspended.id
      },
      existsBlockAiScore() {
        return this.blocks.some(block => this.isBlockAiScore(block))
      },
      existsBlockAiScoreTypeLLM() {
        return this.blocks.some(block => block.aiScoreType == AiScoreTypeEnum.LLM.id)
      },
      blockColumns() {
        let columns = [
          {
            title: '题块名称',
            // key: 'blockName',
            slot: 'blockName',
          },
          {
            title: '满分',
            align: 'center',
            render: (h, params) => {
              let questions = this.getBlockQuestions(params.row)
              let fullScore = sumByDefaultZero(questions, q => q.fullScore)
              return h('span', {}, fullScore)
            },
          },
          {
            title: '包含题目',
            align: 'center',
            render: (h, params) => {
              let questions = this.getBlockQuestions(params.row)
              let questionNames = questions.map(q => q.branchName || q.fullCode).join('、')
              return h('span', {}, questionNames)
            },
          },
        ]
        if (this.canEditExam && this.enableAiScore) {
          columns.unshift({
            type: 'selection',
            width: 60,
            align: 'center',
          })
          if (this.existsBlockAiScoreTypeLLM) {
            columns.push({
              title: 'AI评卷提示词',
              width: 120,
              align: 'center',
              render: (h, params) => {
                if (this.isAiScoreTypeLLM(params.row)) {
                  return h(
                    TextButton,
                    {
                      type: 'primary',
                      onClick: () => {
                        this.showModalPrompt = true
                        this.modalPromptInitBlockId = params.row.blockId
                      },
                    },
                    () => '点击查看'
                  )
                } else {
                  return h('span', {}, '-')
                }
              },
            })
          }
          if (this.existsBlockAiScore) {
            columns.push({
              title: 'AI评卷状态',
              width: 120,
              align: 'center',
              render: (h, params) => {
                if (!this.isBlockAiScore(params.row)) {
                  return h('span', {}, '-')
                }
                let status = params.row.aiScoreStatus
                let text = ''
                let cls = ''
                if (!status) {
                  text = 'AI评卷未开启'
                } else if (status == AiScoreStatusEnum.Doing.id) {
                  text = 'AI评卷进行中'
                  cls = 'status-primary'
                } else if (status == AiScoreStatusEnum.Pause.id) {
                  text = 'AI评卷已暂停'
                  cls = 'status-warning'
                } else if (status == AiScoreStatusEnum.Success.id) {
                  text = 'AI评卷已完成'
                }
                return h(
                  'span',
                  {
                    class: [cls],
                  },
                  text
                )
              },
            })
          }
          columns.push({
            title: '操作',
            width: 200,
            align: 'center',
            render: (h, params) => {
              let btns = []

              let isAiScore = this.isBlockAiScore(params.row)
              let aiScoreStatus = params.row.aiScoreStatus

              if (isAiScore && (!aiScoreStatus || aiScoreStatus == AiScoreStatusEnum.Pause.id)) {
                btns.push(
                  h(
                    TextButton,
                    {
                      type: 'primary',
                      disabled: this.subjectStatusSuspended,
                      onClick: () => {
                        this.changeAiScoreStatus(params.row, AiScoreStatusEnum.Doing.id)
                      },
                    },
                    () => '开启AI评卷'
                  )
                )
              }
              if (isAiScore && aiScoreStatus == AiScoreStatusEnum.Doing.id) {
                btns.push(
                  h(
                    TextButton,
                    {
                      type: 'warning',
                      onClick: () => {
                        this.changeAiScoreStatus(params.row, AiScoreStatusEnum.Pause.id)
                      },
                    },
                    () => '暂停AI评卷'
                  )
                )
              }
              btns.push(
                h(
                  TextButton,
                  {
                    type: 'warning',
                    onClick: () => {
                      this.resetMarking(params.row)
                    },
                  },
                  () => '重置评卷'
                )
              )
              return h('div', {}, btns)
            },
          })
        }
        return columns
      },
    },
    watch: {
      blocks() {
        this.selectedRows = []
      },
    },
    methods: {
      isBlockAiScore(block) {
        return AiScoreTypeEnum.hasId(block.aiScoreType)
      },
      getBlockAiScoreTypeName(block) {
        return AiScoreTypeEnum.getNameById(block.aiScoreType) || ''
      },
      isAiScoreTypeLLM(block) {
        return AiScoreTypeEnum.LLM.id == block.aiScoreType
      },
      getBlockQuestions(block) {
        return block.questions
          .map(q => this.subjectives.find(x => x.questionCode == q.questionCode && x.branchCode == q.branchCode))
          .filter(Boolean)
      },
      handleSelectTableRows(rows) {
        this.selectedRows = rows
      },
      changeToAiScore() {
        this.changeAiScoreType(true)
      },
      changeToManualScore() {
        this.changeAiScoreType(false)
      },
      changeAiScoreType(isAiScore) {
        if (this.selectedRows.length == 0) {
          this.$Message.warning({
            content: '请先选择题块',
          })
          return
        }

        let toTypeName
        let checkIsToType
        let changeApi
        if (isAiScore) {
          toTypeName = 'AI评卷'
          checkIsToType = block => this.isBlockAiScore(block)
          changeApi = apiChangeToAiScore
        } else {
          toTypeName = '人工评卷'
          checkIsToType = block => !this.isBlockAiScore(block)
          changeApi = apiChangeToManualScore
        }

        if (this.selectedRows.some(checkIsToType)) {
          this.$Message.warning({
            content: `勾选题块中存在题块已是${toTypeName}`,
          })
          return
        }

        let blockIds = this.selectedRows.map(block => block.blockId)
        let blockNames = this.selectedRows.map(block => block.blockName)
        this.$Modal.confirm({
          title: `转为${toTypeName}`,
          content: `是否将以下题块转为${toTypeName}？<br>${blockNames.join('<br>')}`,
          loading: true,
          onOk: () => {
            this.$TransparentSpin.show()
            changeApi({ examSubjectId: this.examSubjectId, blockIds }).finally(() => {
              this.$TransparentSpin.hide()
              this.$Modal.remove()
              this.refreshBlocks()
            })
          },
        })
      },
      changeAiScoreStatus(block, aiScoreStatus) {
        if (!this.isBlockAiScore(block)) {
          return
        }
        apiChangeAiScoreStatus({
          examSubjectId: this.examSubjectId,
          blockIds: [block.blockId],
          aiScoreStatus,
        }).then(() => {
          this.$Message.success('已更改状态 ')
          this.refreshBlocks()
        })
      },
      resetMarking(block) {
        if (block.aiScoreStatus == AiScoreStatusEnum.Doing.id) {
          this.$Message.warning('该题块是AI评卷，若需重置，请先暂停评卷')
          return
        }
        let doRest = () => {
          this.$Spin.show({
            render: h =>
              h(
                'div',
                {
                  style: {
                    fontSize: '24px',
                  },
                },
                `正在重置【${block.blockName}】评卷`
              ),
          })
          apiResetBlockMarking({
            examId: this.examId,
            examSubjectId: this.examSubjectId,
            blockId: block.blockId,
          })
            .then(() => {
              this.$Message.success({
                content: `已重置【${block.blockName}】评卷`,
              })
            })
            .finally(() => {
              this.$Spin.hide()
              this.refreshBlocks()
            })
        }
        let confirmMessage = `<span style="color:red">重置题块评卷将会清空本题块所有评卷数据，且不可恢复，请务必谨慎操作！</span><br><br>确定重置【${block.blockName}】？`
        this.$Modal.confirm({
          title: '重置题块评卷',
          content: confirmMessage,
          onOk: () => {
            setTimeout(() => {
              this.$Modal.confirm({
                title: '请再次确认',
                content: confirmMessage,
                onOk: () => {
                  doRest()
                },
              })
            }, 1000)
          },
        })
      },
      startMark(skipWarning) {
        this.$Spin.show({
          render: h =>
            h(
              'div',
              {
                style: {
                  fontSize: '24px',
                },
              },
              '正在开启科目评卷'
            ),
        })
        apiStartMarking({
          examId: this.examId,
          examSubjectId: this.examSubjectId,
          skipWarn: skipWarning,
        })
          .then(() => {
            this.$Message.success({
              content: `已开启科目评卷`,
            })
          })
          .catch(err => {
            if (err.code == 2000) {
              this.$Modal.confirm({
                title: '警告',
                content: err.msg || '',
                onOk: () => {
                  this.startMark(true)
                },
              })
            } else {
              this.$Modal.error({
                title: '开启评卷失败',
                content: err.msg || '',
              })
              throw err
            }
          })
          .finally(() => {
            this.$Spin.hide()
            this.$emit('refresh-subject')
          })
      },
      suspendMark() {
        this.$Modal.confirm({
          title: '暂停评卷',
          content: `是否确定暂停科目评卷？`,
          onOk: () => {
            this.$Spin.show({
              render: h =>
                h(
                  'div',
                  {
                    style: {
                      fontSize: '24px',
                    },
                  },
                  '正在暂停评卷'
                ),
            })
            apiSuspendMarking({
              examId: this.examId,
              examSubjectId: this.examSubjectId,
            })
              .then(() => {
                this.$Message.success({
                  content: `评卷已暂停`,
                })
              })
              .finally(() => {
                this.$Spin.hide()
                this.$emit('refresh-subject')
              })
          },
        })
      },
      refreshBlocks() {
        this.$emit('refresh-blocks')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .section-actions {
    margin-bottom: 10px;
  }

  .box-block-name {
    @include flex(row, flex-start, center);
  }

  .btn {
    box-sizing: content-box;
    width: 20px;
    height: 20px;
    margin-left: 6px;
    color: $color-primary;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    background-color: #f0f0f0;
    user-select: none;
  }

  .table-blocks {
    :deep(.status-primary) {
      color: $color-primary;
    }

    :deep(.status-warning) {
      color: $color-warning;
    }
  }
</style>
