import ajax from '@/api/ajax'
import { sortQuestionFunction } from '../../helpers/emarking/miscellaneous'

// export function apiGetPersonalAnswerSheetList(requestParams) {
//   return ajax.get({
//     url: 'mark/exam/selfAnswerSheetList',
//     params: {
//       name: requestParams.answerSheetName,
//       mode: requestParams.mode, // 1 - 线上阅卷 | 2 - 先阅后扫
//       status: requestParams.status, //  0 - 正常 | 1- 回收站 | 2 - 删除
//     },
//     requestName: '获取个人答题卡列表',
//   })
// }

// export function apiSetEnableDelayUpStudent(requestParams) {
//   return ajax.put({
//     url: 'mark/exam/setEnableDelayUpStudent',
//     params: {
//       examId: requestParams.examId, // String
//       schoolId: requestParams.schoolId, // String
//       enable: requestParams.enable, // Boolean
//     },
//     requestName: '设置学校是否可以延迟上报评考生',
//   })
// }

// export function apiGetAnswerSheetDetail(requestParams) {
//   return ajax.get({
//     url: 'mark/exam/answerSheet',
//     params: {
//       answerSheetId: requestParams.answerSheetId,
//     },
//     requestName: '获取答题卡详情',
//   })
// }

// export function apiGetTestList({
//   semesterId,
//   term,
//   gradeId,
//   subjectId,
//   beginTime,
//   endTime,
//   keyword,
//   pageSize,
//   currentPage,
// }) {
//   return ajax.get({
//     url: 'mark/exam/exams2',
//     params: {
//       semesterId,
//       term,
//       gradeId,
//       subjectId,
//       beginTime,
//       endTime,
//       key: keyword,
//       size: pageSize,
//       current: currentPage,
//     },
//     requestName: '获取手阅测试列表',
//   })
// }

// export function apiCreateTest(requestParams) {
//   return ajax.post({
//     url: 'mark/exam/create3',
//     data: {
//       answerSheetId: requestParams.answerSheetId,
//       beginTime: requestParams.beginTime,
//       endTime: requestParams.endTime,
//       examName: requestParams.examName,
//       examMode: requestParams.examMode || 'normal',
//       subjectId: requestParams.subjectId,
//       gradeId: requestParams.gradeId,
//       classIds: requestParams.classIds,
//       objectives: requestParams.objectives,
//       /*
//         objectives: [{
//           answer: [string],
//           answerScoreList: [string],
//           branchId: [string],
//           branchName: [string],
//           fullScore: [Number],
//           id: [Number],
//           isGiven: [Number],
//           optionCount: [Number],
//           questionCode: [Number],
//           questionId: [string],
//           questionName: [string],
//           questionType: [Number],
//           topicCode: [Number],
//           topicName: [String]
//         }]
//       */
//       subjectives: requestParams.subjectives,
//       /*
//         subjectives: [{
//           blankScores: [string],
//           branchCode: [Number],
//           branchId: [string],
//           branchName: [string],
//           fullScore: [Number],
//           id: [Number],
//           isAdditional: [Boolean],
//           questionCode: [Number],
//           questionId: [string],
//           questionName: [string],
//           topicCode: [Number],
//           topicName: [string]
//         }]
//       */
//       blocks: requestParams.blocks,
//       /*
//         blocks: [{
//           blockName: [string],
//           subjectives: [Array(object)]
//         }]
//       */
//     },
//     requestName: '创建手阅测试',
//   })
// }

// export function apiGetProjectDetail(requestParams) {
//   return ajax.get({
//     url: 'mark/exam/detail2',
//     params: {
//       examSubjectId: requestParams.examSubjectId,
//     },
//     // requestName: '获取手阅测试详情',
//   })
// }

// export function apiUpdateAnswerSheetStatus(requestParams) {
//   return ajax.post({
//     url: 'mark/exam/updateAnswerSheetStatus',
//     params: {
//       id: requestParams.id,
//       status: requestParams.status, //  0 - 正常 | 1- 回收站 | 2 - 删除
//     },
//     requestName: '更改答题卡状态',
//   })
// }

// 班级测练
export function apiGetPractiseList({
  semesterId,
  term,
  gradeId,
  subjectId,
  // keyword,
  pageSize,
  currentPage,
}) {
  return ajax.get({
    url: 'mark/classPractice/list',
    params: {
      semesterId,
      term,
      gradeId: gradeId || '',
      subjectId: subjectId || '',
      // key: keyword,
      size: pageSize,
      page: currentPage,
    },
    requestName: '获取班级测练列表',
  })
}

export function apiCreateClassPractise(requestParams) {
  return ajax.post({
    url: 'mark/exam/create4',
    data: {
      answerSheetId: requestParams.answerSheetId,
      beginTime: requestParams.beginTime,
      endTime: requestParams.endTime,
      examName: requestParams.examName,
      examMode: requestParams.examMode || 'normal',
      subjectId: requestParams.subjectId,
      gradeId: requestParams.gradeId,
      classIds: requestParams.classIds,
      objectives: requestParams.objectives,
      /*
        objectives: [{
          answer: [string],
          answerScoreList: [string],
          branchId: [string],
          branchName: [string],
          fullScore: [Number],
          id: [Number],
          isGiven: [Number],
          optionCount: [Number],
          questionCode: [Number],
          questionId: [string],
          questionName: [string],
          questionType: [Number],
          topicCode: [Number],
          topicName: [String]
        }]
      */
      subjectives: requestParams.subjectives,
      /*
        subjectives: [{
          blankScores: [string],
          branchCode: [Number],
          branchId: [string],
          branchName: [string],
          fullScore: [Number],
          id: [Number],
          isAdditional: [Boolean],
          questionCode: [Number],
          questionId: [string],
          questionName: [string],
          topicCode: [Number],
          topicName: [string]
        }]
      */
      blocks: requestParams.blocks,
      /*
        blocks: [{
          blockName: [string],
          subjectives: [Array(object)]
        }]
      */
    },
  })
}

export function apiGetThePractise({ examId }) {
  return ajax.get({
    url: 'mark/classPractice/detail',
    params: {
      examId,
    },
    requestName: '获取班级测练',
  })
}

export function apiGetStudentPractiseScore({ classId, examSubjectId }) {
  return ajax.get({
    url: 'report/classPractice/classStudentScores',
    params: {
      classId,
      examSubjectId,
    },
    requestName: '获取班级测练学生成绩',
  })
}

export function apiGetSubjectFullScore(examSubjectId) {
  return ajax.get({
    url: 'mark/subject/fullScore',
    params: {
      examSubjectId,
    },
    requestName: '获取考试科目满分',
  })
}

export function apiGetPractisePaperComment({ examSubjectId, classId }) {
  return ajax
    .get({
      url: 'report/classPractice/paperComment',
      params: {
        examSubjectId,
        classId,
      },
      requestName: '获取班级测练试卷讲评',
    })
    .then(data => {
      // 没有小题的，转换为只有一个小题，小题号为0
      // 客观题均无小题
      let objectiveQuestions = data.objects.map(q => {
        let questionName = q.quesName || String(q.quesCode)
        let branchName = questionName
        return {
          questionName,
          questionCode: q.quesCode,
          branchName,
          branchCode: 0,
          blockId: q.blockId,
          answer: q.answer,
          answers: q.answers.map(a => ({
            ...a,
            answer: a.stuAnswer,
            score: a.stuScore,
            studentId: a.stuId,
            studentName: a.stuName,
          })),
          branchTypeId: q.quesType,
          fullScore: q.fullScore,
          optionCount: q.optionCount,
          standardAnswer: q.answer,
          gradeAvgScore: q.graAvg,
          classAvgScore: q.claAvg,
          originalQuestionId: q.quesId,
          originalBranchId: q.branchId,
          isObjective: true,
        }
      })
      let subjectiveQuestions = data.subjects.map(q => {
        let questionName = q.quesName || String(q.quesCode)
        let branchCode = data.subjects.length > 1 ? q.branchCode : 0
        let branchName = q.branchName || (branchCode == 0 ? questionName : `${questionName}.${branchCode}`)
        return {
          questionName,
          questionCode: q.quesCode,
          branchName,
          branchCode,
          blockId: q.blockId,
          answer: q.answer,
          answers: q.answers.map(a => ({
            ...a,
            answer: a.stuAnswer,
            score: a.stuScore,
            studentId: a.stuId,
            studentName: a.stuName,
          })),
          fullScore: q.fullScore,
          originalQuestionId: q.quesId,
          originalBranchId: q.branchId,
          gradeAvgScore: q.graAvg,
          classAvgScore: q.claAvg,
          isObjective: false,
        }
      })

      let questions = [...objectiveQuestions, ...subjectiveQuestions]
      questions.sort(sortQuestionFunction)

      return {
        paperId: !data.paperId || data.paperId == 0 ? null : data.paperId,
        questions,
      }
    })
}

export function apiGetStuPaperAndMark({ examSubjectId, studentId }) {
  return ajax.get({
    url: 'report/classPractice/stuPaperAndMark',
    params: {
      examSubjectId,
      studentId,
    },
    requestName: '获取学生答卷和评卷信息',
  })
}

export function apiGetClassPractiseScanTemplate(examSubjectId) {
  return ajax.get({
    url: 'mark/classPractice/getScanTemplate',
    params: {
      examSubjectId,
    },
    requestName: '获取班级测练模板',
  })
}

export function apiAddAsScanner(examSubjectId) {
  return ajax.post({
    url: 'mark/classPractice/addAsScanner',
    params: {
      examSubjectId,
    },
    requestName: '添加为扫描员',
  })
}

export function apiAddAsMarker(examSubjectId) {
  return ajax.post({
    url: 'mark/classPractice/addAsMarker',
    params: {
      examSubjectId,
    },
    requestName: '添加为评卷员',
  })
}

export function apiSyncStudents(examSubjectId) {
  return ajax.put({
    url: 'mark/classPractice/syncStudents',
    params: {
      examSubjectId,
    },
  })
}

export function apiSyncTeachers(examSubjectId) {
  return ajax.put({
    url: 'mark/classPractice/syncTeachers',
    params: {
      examSubjectId,
    },
  })
}

export function apiGetReportTemplate(examId) {
  return ajax.get({
    url: 'report/score/reportTemplates',
    params: {
      examId,
    },
    requestName: '查询报表模板列表',
  })
}
