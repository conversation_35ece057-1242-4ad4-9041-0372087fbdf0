/**
 * 格式化日期与时间
 * @param {Date} date
 * @param {String} formatString
 * @returns String
 */
export function formatDateTime(date, formatString = 'YYYY-MM-DD HH:mm:ss') {
  let matchList = [
    {
      pattern: 'YYYY',
      text: date => String(date.getFullYear()).padStart(4, '0'),
    },
    {
      pattern: 'YY',
      text: date => String(date.getFullYear() % 100).padStart(2, '0'),
    },
    {
      pattern: 'MM',
      text: date => String(date.getMonth() + 1).padStart(2, '0'),
    },
    {
      pattern: 'M',
      text: date => String(date.getMonth() + 1),
    },
    {
      pattern: 'DD',
      text: date => String(date.getDate()).padStart(2, '0'),
    },
    {
      pattern: 'D',
      text: date => String(date.getDate()),
    },
    {
      pattern: 'HH',
      text: date => String(date.getHours()).padStart(2, '0'),
    },
    {
      pattern: 'H',
      text: date => String(date.getHours()),
    },
    {
      pattern: 'mm',
      text: date => String(date.getMinutes()).padStart(2, '0'),
    },
    {
      pattern: 'm',
      text: date => String(date.getMinutes()),
    },
    {
      pattern: 'ss',
      text: date => String(date.getSeconds()).padStart(2, '0'),
    },
    {
      pattern: 's',
      text: date => String(date.getSeconds()),
    },
    {
      pattern: 'SSS',
      text: date => String(date.getMilliseconds()).padStart(3, '0'),
    },
    {
      pattern: 'SS',
      text: date => String(Math.round(date.getMilliseconds() / 10)).padStart(2, '0'),
    },
    {
      pattern: 'S',
      text: date => String(Math.round(date.getMilliseconds() / 100)),
    },
  ]

  matchList.forEach(m => {
    formatString = formatString.replace(m.pattern, m.text(date))
  })

  return formatString
}

export function formatDateStartTime(date) {
  if (date && date instanceof Date) {
    let dateTransStartTime = new Date(date)
    dateTransStartTime.setHours(0, 0, 0, 0)
    return formatDateTime(dateTransStartTime)
  } else {
    return new Error('参数并不是合法Date类型对象')
  }
}

export function formatDateEndTime(date) {
  if (date && date instanceof Date) {
    let dateTransStartTime = new Date(date)
    dateTransStartTime.setHours(23, 59, 59, 999)
    return formatDateTime(dateTransStartTime)
  } else {
    return new Error('参数并不是合法Date类型对象')
  }
}

/**
 * 用默认格式格式化日期
 * @param {Date} date
 * @returns String
 */
export function formatDate(date) {
  return formatDateTime(date, 'YYYY-MM-DD')
}

/**
 * 用默认格式格式化时间
 * @param {Date} date
 * @returns String
 */
export function formatTime(date) {
  return formatDateTime(date, 'HH:mm:ss')
}

/**
 * 用默认格式格式化年份月份
 * @param {Date} date
 * @returns String
 */
export function formatYearMonth(date) {
  return formatDateTime(date, 'YYYY-MM')
}

/**
 * 用默认格式格式化月份日期
 * @param {Date} date
 * @returns String
 */
export function formatMonthDay(date) {
  return formatDateTime(date, 'MM-DD')
}

/**
 * 用默认格式格式化月份日期与时间
 * @param {Date} date
 * @returns String
 */
export function formatMonthDayTime(date) {
  return formatDateTime(date, 'MM-DD HH:mm:ss')
}

/**
 * 用默认格式格式化年月日时分（中文间隔）
 * @param {Date} date
 * @returns String
 */
export function formatDateHourMinutesByChinese(date) {
  return formatDateTime(date, `YYYY年MM月DD日HH时mm分`)
}
