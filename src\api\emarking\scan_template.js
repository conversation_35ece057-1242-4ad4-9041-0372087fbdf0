import ajax from '@/api/ajax'

export function apiGetSubjectScanTemplates(examSubjectId) {
  return ajax.get({
    url: 'mark/scanTemplate/list',
    params: {
      examSubjectId,
    },
    requestName: '获取科目扫描模板',
  })
}

// 指定Id的模板信息
export function apiGetScanTemplate(params) {
  return ajax.get({
    url: 'mark/scanTemplate',
    params: {
      examSubjectId: params.examSubjectId,
      scanTemplateId: params.scanTemplateId,
    },
    requestName: '获取扫描模板详情',
  })
}

// 指定Id的模板不存在副本，返回本身信息，否则返回副本信息
export function apiGetScanTemplateForEdit(params) {
  return ajax.get({
    url: 'mark/scanTemplate/getCopy',
    params: {
      examSubjectId: params.examSubjectId,
      scanTemplateId: params.scanTemplateId,
    },
    requestName: '获取扫描模板编辑内容',
  })
}

export function apiSaveScanTemplate(data) {
  return ajax.upload({
    url: 'mark/scanTemplate/save',
    data,
    requestName: '保存扫描模板',
  })
}

// 检查是否可完成制作
export function apiCheckScanTemplateFinish({ examSubjectId, scanTemplateId }) {
  return ajax.get({
    url: 'mark/scanTemplate/canFinish',
    params: {
      examSubjectId,
      scanTemplateId,
    },
  })
}

// 完成制作
export function apiFinishScanTemplate({
  examSubjectId,
  scanTemplateId,
  redoRecognizeUnit,
  redoRecognizeObjective,
  redoClipImage,
}) {
  return ajax.put({
    url: 'mark/scanTemplate/finish',
    params: {
      examSubjectId,
      scanTemplateId,
      redoRecognizeUnit,
      redoRecognizeObjective,
      redoClipImage,
    },
  })
}

export function apiDeleteScanTemplate({ examSubjectId, scanTemplateId }) {
  return ajax.delete({
    url: 'mark/scanTemplate/delete',
    params: {
      examSubjectId,
      scanTemplateId,
    },
    requestName: '删除扫描模板',
  })
}

// 删除扫描模板副本
export function apiDeleteScanTemplateCopy({ examSubjectId, scanTemplateId }) {
  return ajax.delete({
    url: 'mark/scanTemplate/deleteCopy',
    params: {
      examSubjectId,
      scanTemplateId,
    },
    requestName: '清除扫描模板修改',
  })
}

export function apiGetCanApplyAnswerSheetList(examSubjectId) {
  return ajax.get({
    url: 'mark/scanTemplate/canApplyAnswerSheetList',
    params: {
      examSubjectId,
    },
    requestName: '获取我的答题卡',
  })
}

// 检查应用答题卡
export function apiCheckApplyAnswerSheet({ examSubjectId, answerSheetId, bindPaper }) {
  return ajax.get({
    url: 'mark/scanTemplate/canApplyAnswerSheet',
    params: {
      examSubjectId,
      answerSheetId,
      bindPaper,
    },
  })
}

// 应用答题卡
export function apiApplyAnswerSheet({ examSubjectId, answerSheetId, bindPaper }) {
  return ajax.put({
    url: 'mark/scanTemplate/applyAnswerSheet',
    params: {
      examSubjectId,
      answerSheetId,
      bindPaper,
    },
  })
}
