<template>
  <div class="block-header">
    <div v-if="showPageHeader" :style="pageHeaderStyle" v-html="pageHeaderContent"></div>

    <ComExamInfoTitle ref="title" :style="titleStyle" :editable="block.pageIndex === 0"></ComExamInfoTitle>

    <div class="group" :style="groupStyle">
      <ComTicketNumber
        ref="ticketNumber"
        :size="headerGroupLayout.ticketNumber"
        :style="getGroupItemStyle(headerGroupLayout.ticketNumber)"
      ></ComTicketNumber>

      <ComStudentInfo
        :size="headerGroupLayout.studentInfo"
        :style="getGroupItemStyle(headerGroupLayout.studentInfo)"
      ></ComStudentInfo>

      <ComQrcode
        v-if="showQrcode"
        :size="headerGroupLayout.qrcode"
        :style="getGroupItemStyle(headerGroupLayout.qrcode)"
      ></ComQrcode>

      <ComInstruction v-if="showInstruction" :style="getGroupItemStyle(headerGroupLayout.instruction)"></ComInstruction>

      <ComMissing
        v-if="showMissingMark && block.pageIndex === 0"
        ref="missing"
        :size="headerGroupLayout.missing"
        :style="getGroupItemStyle(headerGroupLayout.missing)"
      ></ComMissing>

      <ComCautions
        v-if="showCautions"
        :editable="block.pageIndex === 0"
        :size="headerGroupLayout.cautions"
        :style="getGroupItemStyle(headerGroupLayout.cautions)"
      ></ComCautions>
    </div>
  </div>
</template>

<script>
  import ComExamInfoTitle from './block_header_title.vue'
  import ComInstruction from './block_header_instruction.vue'
  import ComTicketNumber from './block_header_ticket_number.vue'
  import ComStudentInfo from './block_header_student_info.vue'
  import ComQrcode from './block_header_qrcode.vue'
  import ComMissing from './block_header_missing.vue'
  import ComCautions from './block_header_cautions.vue'

  import { mapGetters, mapMutations } from 'vuex'

  import TemplateRect from '@/helpers/scan_template_define/template_rect'
  import TemplateMarkArea from '@/helpers/scan_template_define/template_mark_area'
  import TemplateBarcodeArea from '@/helpers/scan_template_define/template_barcode_area'
  import TemplateOmrArea from '@/helpers/scan_template_define/template_omr_area'
  import TemplateHandwritingArea from '@/helpers/scan_template_define/template_handwriting_area'

  export default {
    components: {
      ComExamInfoTitle,
      ComInstruction,
      ComTicketNumber,
      ComStudentInfo,
      ComQrcode,
      ComMissing,
      ComCautions,
    },
    props: {
      block: {
        type: Object,
        required: true,
      },
    },
    emits: ['height-change'],
    computed: {
      ...mapGetters('answerSheet', [
        'pageContentWidth',
        'pageHeaderLayout',
        'headerTitleLayout',
        'headerGroupLayout',
        'headerHeight',
        'showQrcode',
        'qrcodeText',
        'showCautions',
        'showMissingMark',
        'pageSize',
        'showPageHeader',
        'pageHeaderContent',
        'showInstruction',
      ]),
      pageHeaderStyle() {
        return {
          position: 'absolute',
          top: this.pageHeaderLayout.top + 'px',
          left: '0',
          width: '100%',
          height: this.pageHeaderLayout.height + 'px',
        }
      },
      titleStyle() {
        return {
          position: 'absolute',
          top: this.headerTitleLayout.top + 'px',
          left: '0',
          width: this.headerTitleLayout.width + 'px',
          height: this.headerTitleLayout.height + 'px',
        }
      },
      groupStyle() {
        return {
          position: 'absolute',
          top: this.headerGroupLayout.top + 'px',
          left: this.headerGroupLayout.left + 'px',
          width: this.headerGroupLayout.width + 'px',
          height: this.headerGroupLayout.height + 'px',
          border: `${this.headerGroupLayout.borderWidth}px solid var(--base-border-color)`,
        }
      },
    },
    created() {
      this.changeInstance()
      this.updateHeight()
    },
    updated() {
      this.changeInstance()
      this.updateHeight()
    },
    methods: {
      ...mapMutations('answerSheet', ['changePageBlockInstance', 'changePageBlockHeight']),
      changeInstance() {
        this.changePageBlockInstance({
          blockId: this.block.id,
          instance: this,
        })
      },
      changeHeight(height) {
        this.changePageBlockHeight({
          blockId: this.block.id,
          height: height,
        })
      },
      updateHeight() {
        if (this.block.height === this.headerHeight) {
          return
        }
        this.changeHeight(this.headerHeight)
        this.$emit('height-change', this.headerHeight)
      },

      getGroupItemStyle({ left, top, width, height }) {
        let style = {
          position: 'absolute',
          left: left + 'px',
          top: top + 'px',
          width: width + 'px',
          height: height + 'px',
        }
        if (Math.abs(left) <= 2) {
          style.borderLeftColor = 'transparent'
        }
        if (Math.abs(top) <= 2) {
          style.borderTopColor = 'transparent'
        }
        if (Math.abs(left + width - this.pageContentWidth) <= 2) {
          style.borderRightColor = 'transparent'
        }
        if (Math.abs(top + height - this.headerGroupLayout.height) <= 2) {
          style.borderBottomColor = 'transparent'
        }
        return style
      },

      getTemplateElements(pageLeft) {
        let pageSize = this.pageSize(this.block.pageIndex)

        let title = this.$refs['title'].getTemplateElements()
        if (title) {
          title.left += pageLeft + pageSize.left
          title.top += pageSize.top + this.headerTitleLayout.top
        }

        let groupLeft = pageLeft + pageSize.left + this.headerGroupLayout.left
        let groupTop = pageSize.top + this.headerGroupLayout.top

        let ticketNumber = this.$refs['ticketNumber'].getTemplateElements()
        if (ticketNumber.barcode) {
          ticketNumber.barcode.left += groupLeft
          ticketNumber.barcode.top += groupTop
        } else if (ticketNumber.omr) {
          ticketNumber.omr.rect.left += groupLeft
          ticketNumber.omr.rect.top += groupTop
          ticketNumber.omr.options.forEach(cell => {
            cell.left += groupLeft
            cell.top += groupTop
          })
        } else if (ticketNumber.handwriting) {
          ticketNumber.handwriting.cells.forEach(cell => {
            cell.left += groupLeft
            cell.top += groupTop
          })
        }

        let missing = null
        if (this.block.pageIndex === 0 && this.showMissingMark) {
          missing = this.$refs['missing'].getTemplateElements()
          missing.rect.left += groupLeft
          missing.rect.top += groupTop
        }

        let qrcode = null
        if (this.showQrcode) {
          let gap = this.headerGroupLayout.gap
          let { left, top, width, height } = this.headerGroupLayout.qrcode
          qrcode = {
            left: groupLeft + left - gap,
            top: groupTop + top - gap,
            width: width + gap * 2,
            height: height + gap * 2,
          }
        }

        // 组装
        let titleArea = null
        if (title) {
          titleArea = TemplateRect.createFromAnswerSheetPageRect(title.left, title.top, title.width, title.height)
        }

        let missingMarkArea = null
        if (missing) {
          let missingMarkRect = TemplateRect.createFromAnswerSheetPageRect(
            missing.rect.left,
            missing.rect.top,
            missing.rect.width,
            missing.rect.height
          )
          missingMarkArea = TemplateMarkArea.create('MissingMark', missingMarkRect)
        }

        let admissionBarcodeArea = null
        if (ticketNumber.barcode) {
          admissionBarcodeArea = new TemplateBarcodeArea()
          admissionBarcodeArea.length = ticketNumber.length
          admissionBarcodeArea.searchArea = TemplateRect.createFromAnswerSheetPageRect(
            ticketNumber.barcode.left,
            ticketNumber.barcode.top,
            ticketNumber.barcode.width,
            ticketNumber.barcode.height
          )
        }
        let admissionOmrArea = null
        if (ticketNumber.omr) {
          admissionOmrArea = new TemplateOmrArea()
          admissionOmrArea.id = 'AdmissionOmrArea'
          admissionOmrArea.direction = ticketNumber.omr.direction
          admissionOmrArea.optionCount = ticketNumber.omr.optionCount
          admissionOmrArea.options = ticketNumber.omr.options.map(opt =>
            TemplateRect.createFromAnswerSheetPageRect(opt.left, opt.top, opt.width, opt.height)
          )
        }
        let admissionHandwritingArea = null
        if (ticketNumber.handwriting) {
          admissionHandwritingArea = new TemplateHandwritingArea()
          admissionHandwritingArea.length = ticketNumber.length
          let cells = ticketNumber.handwriting.cells.map(cell =>
            TemplateRect.createFromAnswerSheetPageRect(cell.left, cell.top, cell.width, cell.height)
          )
          admissionHandwritingArea.cells = cells
          for (let i = 0; i < cells.length - 1; i++) {
            cells[i].width = cells[i + 1].x - cells[i].x
          }
        }

        let qrcodeArea = null
        let qrcodeText = ''
        if (qrcode) {
          qrcodeArea = TemplateRect.createFromAnswerSheetPageRect(qrcode.left, qrcode.top, qrcode.width, qrcode.height)
          qrcodeText = this.qrcodeText
        }

        return {
          titleArea,
          missingMarkArea,
          admissionBarcodeArea,
          admissionOmrArea,
          admissionHandwritingArea,
          qrcodeArea,
          qrcodeText,
        }
      },
    },
  }
</script>
