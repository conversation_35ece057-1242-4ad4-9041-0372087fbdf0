/**
 * 考试科目成绩复制类型
 */

import Enum from '@/enum/enum'

export default new Enum({
  WrittenScoreOnly: {
    id: 'w-w',
    name: '仅复制笔试分',
  },
  OtherScoreOnly: {
    id: 'o-o',
    name: '仅复制其它分',
  },
  WrittenScoreAndOtherScore: {
    id: 'w-w,o-o',
    name: '复制笔试分和其它分',
  },
  OverallScore: {
    id: 'w+o-w',
    name: '复制笔试分与其它分之和',
  },
  QuestionScoreOnly: {
    id: 'q-q',
    name: '仅复制小题分',
  },
  QuestionScoreAndOtherScore: {
    id: 'q-q,o-o',
    name: '复制小题分和其它分',
  },
})
