import { groupArray, unGroupArray } from '@/utils/array'
import { sumByDefaultZero, roundNumber } from '@/utils/math'
import BranchTypeEnum from '@/enum/qlib/branch_type'

export function initializePaperQuestions(reportQuestions, paper, studentBranchAnswers, studentComments) {
  let questions = addQuestionContent(reportQuestions, paper)
  questions.forEach(q => {
    addQuestionName(q)
    calcScoreRate(q)
    q.branches.forEach(b => {
      setBranchStudentScoreDetail(b, studentBranchAnswers, studentComments)
    })
    setQuestionStats(q)
  })
  return questions
}

// 关联题目内容
function addQuestionContent(reportQuestions, paper) {
  // 找出匹配组卷中的题
  let questionList = (paper && paper.questionList) || []
  reportQuestions.forEach(q => {
    q.originalQuestion = questionList.find(x => x.id === q.originalQuestionId)
    if (!q.originalQuestion) {
      q.originalQuestionId = ''
      q.originalQuestion = null
      q.originalBranchId = ''
      q.originalBranch = null
      return
    }

    q.originalBranch = q.originalQuestion.branches.find(x => x.id === q.originalBranchId)
    if (!q.originalBranch) {
      q.originalBranchId = ''
      q.originalBranch = null
    }
  })

  // 按题号分组
  let questionsByCode = groupArray(reportQuestions, q => q.questionCode)
    .map(q => ({
      questionCode: q.group[0].questionCode,
      originalQuestionId: q.group[0].originalQuestionId, // 采用第一个小题的originalQuestion，不考虑各个小题关联组卷中的不同题目的小题
      originalQuestion: q.group[0].originalQuestion,
      branches: q.group,
    }))
    .sort((a, b) => a.questionCode - b.questionCode)

  // 适应小题展开情形
  let questions = []
  let lastQuestion = null
  for (let q of questionsByCode) {
    // 连续的几个题originalQuestionId相同则对应题库中同一题，原理上展开后的每个题只有一个branch，故只取branches[0]
    if (lastQuestion && lastQuestion.originalQuestionId && lastQuestion.originalQuestionId == q.originalQuestionId) {
      lastQuestion.branches.push(q.branches[0])
    } else {
      lastQuestion = q
      questions.push(lastQuestion)
    }
  }
  return questions
}

// 补充题名
function addQuestionName(q) {
  let firstBranch = q.branches[0]
  let lastBranch = q.branches[q.branches.length - 1]
  if (firstBranch.questionName == lastBranch.questionName) {
    q.questionName = firstBranch.questionName
  } else {
    q.questionName = `${firstBranch.questionName} - ${lastBranch.questionName}`
  }
}

// 计算得分率
function calcScoreRate(q) {
  q.fullScore = sumByDefaultZero(q.branches, b => b.fullScore)
  q.classAvgScore = roundNumber(
    sumByDefaultZero(q.branches, b => b.classAvgScore),
    2
  )
  q.gradeAvgScore = roundNumber(
    sumByDefaultZero(q.branches, b => b.gradeAvgScore),
    2
  )
  q.classScoreRate = q.fullScore > 0 ? roundNumber((q.classAvgScore / q.fullScore) * 100, 0) : 0
  q.gradeScoreRate = q.fullScore > 0 ? roundNumber((q.gradeAvgScore / q.fullScore) * 100, 0) : 0

  q.statsByScore = []
  q.branches.forEach(b => {
    b.classAvgScore = roundNumber(b.classAvgScore, 2)
    b.gradeAvgScore = roundNumber(b.gradeAvgScore, 2)
    b.classScoreRate = b.fullScore > 0 ? roundNumber((b.classAvgScore / b.fullScore) * 100, 0) : 0
    b.gradeScoreRate = b.fullScore > 0 ? roundNumber((b.gradeAvgScore / b.fullScore) * 100, 0) : 0
    b.statsByScore = [] // 主观题按分数统计
    b.statsByAnswer = [] // 客观题按答案统计
  })

  q.difficulty = q.classScoreRate >= 70 ? 'easy' : q.classScoreRate >= 40 ? 'normal' : 'hard'
}

// 设置各小题各学生的得分数据和评卷批注
function setBranchStudentScoreDetail(b, studentBranchAnswers, studentComments) {
  let blockId = null
  let students = []
  // 单独接口获取的学生得分数据
  if (studentBranchAnswers && studentBranchAnswers.length > 0) {
    let finding = studentBranchAnswers.find(x => x.questionCode === b.questionCode && x.branchCode === b.branchCode)
    blockId = (finding && finding.blockId) || null
    students = (finding && finding.answers) || []
  }
  // 题目中已包含的学生得分数据
  else if (b.answers && b.answers.length > 0) {
    blockId = b.blockId || null
    students = b.answers
  }

  students.forEach(s => {
    if (blockId) {
      let comments = (studentComments && studentComments[s.studentId] && studentComments[s.studentId][blockId]) || []
      s.blocks = [
        {
          blockId,
          src: s.answer,
          comments,
        },
      ]
    } else {
      s.blocks = []
    }
  })
  b.students = students
}

// 设置题目统计
function setQuestionStats(question) {
  // 小题分段统计
  question.branches.forEach(branch => {
    if (branch.isObjective) {
      setObjectiveStats(branch)
    } else {
      setSubjectiveStats(branch)
    }
  })

  // 题目分段统计
  let allBranchStudents = unGroupArray(question.branches, b =>
    b.students.map(s => ({
      ...s,
      isObjective: b.isObjective,
    }))
  )
  question.students = groupArray(allBranchStudents, s => s.studentId).map(s => {
    let blocks = []
    s.group.forEach(b => {
      b.blocks.forEach(block => {
        if (blocks.every(x => x.blockId != block.blockId)) {
          blocks.push(block)
        }
      })
    })
    return {
      studentId: s.key,
      studentName: s.group[0].studentName,
      score: s.group.reduce((a, c) => a + c.score, 0),
      answer: '',
      blocks,
    }
  })
  setSubjectiveStats(question)
}

// 客观题按选项统计
function setObjectiveStats(branch) {
  let statsByAnswer = []
  // 单选题列出所有选项
  if (branch.branchTypeId === BranchTypeEnum.SingleChoice.id) {
    for (let i = 0; i < branch.optionCount; i++) {
      let answer = String.fromCharCode(65 + i)
      let students = branch.students.filter(s => s.answer === answer)
      statsByAnswer.push({
        answer,
        studentCount: students.length,
        percentage: branch.students.length == 0 ? 0 : roundNumber((students.length / branch.students.length) * 100, 0),
        students,
      })
    }
  }
  // 非单选题列出学生选择的所有选项
  else {
    statsByAnswer = groupArray(branch.students, s => s.answer)
      .map(g => ({
        answer: g.key,
        studentCount: g.group.length,
        percentage: branch.students.length == 0 ? 0 : roundNumber((g.group.length / branch.students.length) * 100, 0),
        students: g.group,
      }))
      .sort((a, b) => (a.answer < b.answer ? -1 : 1))
  }

  branch.statsByAnswer = statsByAnswer
}

// 主观题按得分分段统计
function setSubjectiveStats(branch) {
  // 主观题最多分4段
  const MaxIntervals = 4
  let minScorePerInterval = Math.floor(branch.fullScore / MaxIntervals)
  let intervalScores = []
  for (let i = 0; i < MaxIntervals; i++) {
    intervalScores.push(minScorePerInterval)
  }

  let remainingScore = branch.fullScore - MaxIntervals * minScorePerInterval
  let idx = 0
  while (remainingScore > 0) {
    let s = remainingScore >= 1 ? 1 : remainingScore
    intervalScores[idx] += s
    idx++
    remainingScore -= s
  }
  intervalScores = intervalScores.filter(x => x > 0)

  let scoreFrom = 0
  let statsByScore = intervalScores.map((x, index) => {
    let isLast = index === intervalScores.length - 1
    let scoreTo = isLast ? branch.fullScore : scoreFrom + x

    let students = isLast
      ? branch.students.filter(s => s.score >= scoreFrom)
      : branch.students.filter(s => s.score >= scoreFrom && s.score < scoreTo)
    students.sort((a, b) => b.score - a.score)

    let interval = {
      scoreFrom,
      scoreTo,
      intervalName: `${scoreFrom} ~ ${scoreTo} 分`,
      studentCount: students.length,
      percentage: branch.students.length == 0 ? 0 : roundNumber((students.length / branch.students.length) * 100, 0),
      students,
    }

    scoreFrom = scoreTo
    return interval
  })

  statsByScore.reverse()

  branch.statsByScore = statsByScore
}
