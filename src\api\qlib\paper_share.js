import ajax from '@/api/ajax'
import STORE from '@/store/index'

function transformSharePaper(p) {
  let stages = STORE.state.qlib.stages || []
  let stage = stages.find(x => x.id === Number(p.gradeLevel || p.stage)) || {}
  let subject = (stage.subjects || []).find(x => x.id === Number(p.subjectId || p.subject)) || {}
  let grade = (stage.grades || []).find(x => x.id === Number(p.gradeId)) || {}
  let term = (stage.terms || []).find(x => x.id === Number(p.term)) || {}
  let paperType = (stage.paperTypes || []).find(x => x.id === Number(p.paperTypeId || p.type)) || {}
  let region = STORE.getters['common/regionsById'](Number(p.regionId || p.region)) || []

  return {
    id: p.id,
    name: p.paperName || p.name,
    stage: {
      id: stage.id || 0,
      name: stage.name || '',
    },
    subject: {
      id: subject.id || 0,
      name: subject.name || '',
    },
    grade: {
      id: grade.id || 0,
      name: grade.name || '',
    },
    term: {
      id: term.id || 0,
      name: term.name || '',
    },
    paperType,
    region,
    year: Number(p.year) || new Date().getFullYear(),
    paperBank: p.paperBank,
    sharePaperId: p.sharePaperId,
    createTime: new Date(p.createTime),
    srcUserId: p.srcUserId,
    srcUserName: p.srcUserName,
    destUserId: p.destUserId,
    destUserName: p.destUserName,
    isDelete: p.isDelete,
    coachbookId: p.flagCoachBookId || '',
    coachbookName: p.flagCoachBookName || '',
    paperGroupId: p.paperGroupId,
  }
}

export function apiGetShareTargetUsers(params) {
  return ajax.get({
    url: 'ques/paperShare/targets',
    params: {
      key: params.keyword,
      schoolId: params.schoolId,
    },
    requestName: '获取可分享用户',
  })
}

export function apiSharePaper(params) {
  return ajax.put({
    url: 'ques/paperShare/share',
    params: {
      paperId: params.paperId,
      targetId: params.targetId,
    },
    requestName: '分享试卷',
  })
}

export function apiRecallSharedPaper(sharePaperId) {
  return ajax.delete({
    url: 'ques/paperShare/recall',
    params: {
      id: sharePaperId,
    },
    requestName: '撤回已分享试卷',
  })
}

export function apiGetSharePapers(params) {
  return ajax
    .get({
      url: 'ques/paperShare/myShares',
      params: {
        stage: params.stageId,
        subject: params.subjectId,
        grade: params.grade || '',
        term: params.term || '',
        year: params.year || '',
        region: (params.region && params.region[params.region.length - 1]) || '',
        type: params.paperType || '',
        page: params.currentPage,
        size: params.pageSize,
        keyword: params.keyword || undefined,
        bookType: params.coachbookType || undefined,
        flagCoachBookId: params.coachbookId || undefined,
      },
      requestName: '获取已分享试卷',
    })
    .then(data => {
      return {
        total: data.total || 0,
        papers: (data.records || []).map(transformSharePaper),
      }
    })
}

// 某试卷的分享记录
export function apiGetShareSpecifiedPaperId(paperId) {
  return ajax
    .get({
      url: 'ques/paperShare/myShareSpecifiedPaperId',
      params: {
        paperId,
      },
      requestName: '获取分享记录',
    })
    .then(data => (data || []).map(transformSharePaper))
}

export function apiGetPapersSharedToMe(params) {
  return ajax
    .get({
      url: 'ques/paperShare/shareToMe',
      params: {
        stage: params.stageId,
        subject: params.subjectId,
        grade: params.grade || '',
        term: params.term || '',
        year: params.year || '',
        region: (params.region && params.region[params.region.length - 1]) || '',
        type: params.paperType || '',
        bank: params.status || '',
        page: params.currentPage,
        size: params.pageSize,
        keyword: params.keyword || '',
        bookType: params.coachbookType || undefined,
        flagCoachBookId: params.coachbookId || undefined,
        paperGroupId: params.paperGroupId || undefined,
      },
      requestName: '获取分享给我的试卷',
    })
    .then(data => {
      return {
        total: data.total || 0,
        papers: (data.records || []).map(transformSharePaper),
      }
    })
}

export function apiDeletePaperSharedToMe(sharePaperId) {
  return ajax.delete({
    url: 'ques/paperShare/hide',
    params: {
      id: sharePaperId,
    },
    requestName: '删除分享给我的试卷',
  })
}
