<template>
  <div class="student-info">
    <div v-for="item in items" :key="item.label" class="item" :style="item.style">
      <div class="item-label" :style="itemLabelStyle">{{ item.text }}：</div>
    </div>
  </div>
</template>

<script>
  import { StudentInfo } from '@/helpers/answer_sheet/header'

  export default {
    props: {
      size: {
        type: Object,
        required: true,
      },
    },
    computed: {
      // 勾选的标签
      labels() {
        return this.$store.getters['answerSheet/stuInfoLabels']
      },
      // 标签别名
      alias() {
        return this.$store.getters['answerSheet/stuInfoLabelAlias']
      },
      // 考生填写各项样式
      items() {
        return StudentInfo.getItems(this.size.width, this.labels, this.alias)
      },
      // 标签文字样式
      itemLabelStyle() {
        return StudentInfo.getItemLabelStyle()
      },
    },
  }
</script>
