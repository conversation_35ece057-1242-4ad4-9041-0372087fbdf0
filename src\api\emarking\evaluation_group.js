import ajax from '@/api/ajax'
import MarkingWayEnum from '@/enum/emarking/marking_way'

/**
 * 评卷组定义
 */
export function apiGetEvaluationGroupList(params) {
  return ajax
    .get({
      url: 'mark/evaluationGroup/list',
      params: {
        examSubjectId: params.examSubjectId,
        markingWay: params.markingWayId,
      },
      requestName: '获取评卷分组',
    })
    .then(data => {
      return (data || []).map(g => ({
        groupId: g.groupId,
        groupName: g.groupName,
        markingWay: MarkingWayEnum.getEntryById(g.markingWay) || { id: g.markingWay, key: '', name: '' },
        teacherNum: g.teacher<PERSON>um,
      }))
    })
}

export function apiAddEvaluationGroup(params) {
  return ajax.post({
    url: 'mark/evaluationGroup/add',
    params: {
      examSubjectId: params.examSubjectId,
      groupName: params.groupName,
      markingWay: params.markingWayId,
    },
    requestName: '添加分组',
  })
}

export function apiUpdateEvaluationGroupName(params) {
  return ajax.put({
    url: 'mark/evaluationGroup/update',
    params: {
      examSubjectId: params.examSubjectId,
      groupId: params.groupId,
      groupName: params.groupName,
    },
    requestName: '修改分组名称',
  })
}

export function apiDeleteEvaluationGroup(params) {
  return ajax.delete({
    url: 'mark/evaluationGroup/delete',
    params: {
      examSubjectId: params.examSubjectId,
      groupId: params.groupId,
    },
    requestName: '删除分组',
  })
}

// 导入评卷组
export function apiImportEvaluationGroup({ examSubjectIds, file }) {
  return ajax.upload({
    url: 'mark//evaluationGroup/student/importGroupSchool',
    params: {
      examSubjectIds: examSubjectIds.join(','),
    },
    data: {
      file,
    },
  })
}

/**
 * 组考生
 */
export function apiGetEvaluationGroupStudentSchools(params) {
  return ajax.get({
    url: 'mark/evaluationGroup/student/list',
    params: {
      examSubjectId: params.examSubjectId,
      groupId: params.groupId,
    },
    requestName: '获取分组考生',
  })
}

export function apiGetEvaluationGroupStudentUngroupedSchools(params) {
  return ajax.get({
    url: 'mark/evaluationGroup/student/ungrouped',
    params: {
      examSubjectId: params.examSubjectId,
      groupId: params.groupId,
    },
    requestName: '获取未分组学校',
  })
}

export function apiAddEvaluationGroupStudentSchools(data) {
  return ajax.post({
    url: 'mark/evaluationGroup/student/add',
    params: {
      examSubjectId: data.examSubjectId,
      groupId: data.groupId,
    },
    data: data.schoolIds,
    requestName: '添加学校',
  })
}

export function apiDeleteEvaluationGroupStudentSchools(data) {
  return ajax.delete({
    url: 'mark/evaluationGroup/student/delete',
    params: {
      examSubjectId: data.examSubjectId,
      groupId: data.groupId,
    },
    data: data.schoolIds,
    requestName: '移除学校',
  })
}

/**
 * 组评卷员
 */
export function apiGetEvaluationGroupTeachers(params) {
  return ajax.get({
    url: 'mark/evaluationGroup/teacher/list',
    params: {
      examSubjectId: params.examSubjectId,
      groupId: params.groupId,
      realName: params.realName,
      mobile: params.mobile,
    },
    requestName: '获取分组评卷员',
  })
}

export function apiAddEvaluationGroupTeachers(data) {
  return ajax.post({
    url: 'mark/evaluationGroup/teacher/add',
    params: {
      examSubjectId: data.examSubjectId,
      groupId: data.groupId,
    },
    data: data.teacherIds,
    requestName: '添加分组评卷员',
  })
}

export function apiDeleteEvaluationGroupTeachers(data) {
  return ajax.delete({
    url: 'mark/evaluationGroup/teacher/delete',
    params: {
      examSubjectId: data.examSubjectId,
      groupId: data.groupId,
    },
    data: data.teacherIds,
    requestName: '删除分组评卷员',
  })
}
