import { numberToChinese } from '@/utils/number'

import cell_0 from '@/assets/images/omr/cell_0.svg'
import cell_1 from '@/assets/images/omr/cell_1.svg'
import cell_2 from '@/assets/images/omr/cell_2.svg'
import cell_3 from '@/assets/images/omr/cell_3.svg'
import cell_4 from '@/assets/images/omr/cell_4.svg'
import cell_5 from '@/assets/images/omr/cell_5.svg'
import cell_6 from '@/assets/images/omr/cell_6.svg'
import cell_7 from '@/assets/images/omr/cell_7.svg'
import cell_8 from '@/assets/images/omr/cell_8.svg'
import cell_9 from '@/assets/images/omr/cell_9.svg'
import cell_A from '@/assets/images/omr/cell_A.svg'
import cell_B from '@/assets/images/omr/cell_B.svg'
import cell_C from '@/assets/images/omr/cell_C.svg'
import cell_D from '@/assets/images/omr/cell_D.svg'
import cell_E from '@/assets/images/omr/cell_E.svg'
import cell_F from '@/assets/images/omr/cell_F.svg'
import cell_G from '@/assets/images/omr/cell_G.svg'
import cell_H from '@/assets/images/omr/cell_H.svg'
import cell_I from '@/assets/images/omr/cell_I.svg'
import cell_J from '@/assets/images/omr/cell_J.svg'
import cell_K from '@/assets/images/omr/cell_K.svg'
import cell_L from '@/assets/images/omr/cell_L.svg'
import cell_M from '@/assets/images/omr/cell_M.svg'
import cell_N from '@/assets/images/omr/cell_N.svg'
import cell_O from '@/assets/images/omr/cell_O.svg'
import cell_P from '@/assets/images/omr/cell_P.svg'
import cell_Q from '@/assets/images/omr/cell_Q.svg'
import cell_R from '@/assets/images/omr/cell_R.svg'
import cell_S from '@/assets/images/omr/cell_S.svg'
import cell_T from '@/assets/images/omr/cell_T.svg'
import cell_U from '@/assets/images/omr/cell_U.svg'
import cell_V from '@/assets/images/omr/cell_V.svg'
import cell_W from '@/assets/images/omr/cell_W.svg'
import cell_X from '@/assets/images/omr/cell_X.svg'
import cell_Y from '@/assets/images/omr/cell_Y.svg'
import cell_Z from '@/assets/images/omr/cell_Z.svg'
import cell_tick from '@/assets/images/omr/cell_tick.svg'
import cell_cross from '@/assets/images/omr/cell_cross.svg'
import cell_full from '@/assets/images/omr/cell_full.svg'
import cell_empty from '@/assets/images/omr/cell_empty.svg'
import cell_empty_seal from '@/assets/images/omr/cell_empty_seal.svg'
import cell_wrong_cross from '@/assets/images/omr/cell_wrong_cross.svg'
import cell_wrong_dot from '@/assets/images/omr/cell_wrong_dot.svg'
import cell_wrong_slash from '@/assets/images/omr/cell_wrong_slash.svg'
import cell_wrong_tick from '@/assets/images/omr/cell_wrong_tick.svg'
import opt_label_A from '@/assets/images/omr/opt_label_A.svg'
import opt_label_B from '@/assets/images/omr/opt_label_B.svg'
import opt_label_C from '@/assets/images/omr/opt_label_C.svg'
import opt_label_D from '@/assets/images/omr/opt_label_D.svg'
import opt_label_E from '@/assets/images/omr/opt_label_E.svg'
import opt_label_F from '@/assets/images/omr/opt_label_F.svg'
import opt_label_G from '@/assets/images/omr/opt_label_G.svg'
import opt_label_H from '@/assets/images/omr/opt_label_H.svg'
import opt_label_I from '@/assets/images/omr/opt_label_I.svg'
import opt_label_J from '@/assets/images/omr/opt_label_J.svg'
import opt_label_T from '@/assets/images/omr/opt_label_T.svg'
import opt_label_Tick from '@/assets/images/omr/opt_label_Tick.svg'
import opt_label_Cross from '@/assets/images/omr/opt_label_Cross.svg'

// 当前答题卡代码版本，以整形表示
export const SheetVersion = 4
// 实际1mm在页面上显示的像素
export const PixelsPerMillimeter = 4
// 客观题组区域扩展宽度
export const ObjectiveBlockExpandMillimeters = 1
// 题块区域扩展宽度/mm
export const SubjectiveBlockExpandMillimeters = 5
// 定位点搜索区域扩展宽度/mm
export const AnchorSearchAreaExpandMillimeters = 4
// 其他标记搜索区域扩展宽度/mm
export const OtherSearchAreaExpandMillimeters = 1
// 扫描端模板图片分辨率
export const TemplateImageResolution = 150
export const InchMillimeters = 25.4
// 页面1像素对应扫描端模板图片的像素
export const TemplateResolutionScale = TemplateImageResolution / (InchMillimeters * PixelsPerMillimeter)
// 最大准考号长度
export const MaxTicketNumberLength = 15
// 最小准考号长度
export const MinTicketNumberLength = 4
// 大题汉字名
export const TopicChineseCodes = Array.from({ length: 20 }).map((v, idx) => ({
  code: idx + 1,
  codeInChinese: numberToChinese(idx + 1),
}))
// 打分条高度
export const BarScoreHeight = 16
// 选做框高度
export const SelectPanelHeight = 68
// 对错框尺寸
export const TrueFalseBoxWidth = 40
export const TrueFalseBoxHeight = 24
// 对错框边距
export const TrueFalseBoxPadding = 4
// 对错框CSS类
export const TrueFalseBoxCssClass = 'true-false-score-box'
// 对错框打分说明（留空表示错误）
export const ScoreTypeTrueFalseTipEmptyFalse = '（打分说明：正确的打✔，错误的打✖或留空）'
// 对错框打分说明（留空表示正确）
export const ScoreTypeTrueFalseTipEmptyTrue = '（打分说明：错误的打✖，正确的打✔或留空）'
// omr选项svg
export const OmrSVG = {
  cell_0,
  cell_1,
  cell_2,
  cell_3,
  cell_4,
  cell_5,
  cell_6,
  cell_7,
  cell_8,
  cell_9,
  cell_A,
  cell_B,
  cell_C,
  cell_D,
  cell_E,
  cell_F,
  cell_G,
  cell_H,
  cell_I,
  cell_J,
  cell_K,
  cell_L,
  cell_M,
  cell_N,
  cell_O,
  cell_P,
  cell_Q,
  cell_R,
  cell_S,
  cell_T,
  cell_U,
  cell_V,
  cell_W,
  cell_X,
  cell_Y,
  cell_Z,
  cell_tick,
  cell_cross,
  cell_full,
  cell_empty,
  cell_empty_seal,
  cell_wrong_cross,
  cell_wrong_dot,
  cell_wrong_slash,
  cell_wrong_tick,
  opt_label_A,
  opt_label_B,
  opt_label_C,
  opt_label_D,
  opt_label_E,
  opt_label_F,
  opt_label_G,
  opt_label_H,
  opt_label_I,
  opt_label_J,
  opt_label_T,
  opt_label_Tick,
  opt_label_Cross,
}
