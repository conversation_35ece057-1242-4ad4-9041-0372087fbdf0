function download(url, fileName, target) {
  return new Promise(resolve => {
    let anchor = document.createElement('a')
    if (target) {
      anchor.target = target
    }
    anchor.href = url
    anchor.download = fileName || ''
    anchor.style.display = 'none'

    document.body.appendChild(anchor)
    anchor.click()
    document.body.removeChild(anchor)

    resolve()
  })
}

export function downloadBlob(blob, fileName) {
  let url = window.URL.createObjectURL(blob)
  return download(url, fileName)
}

export function downloadUrl(url, fileName, callback = () => {}) {
  let xhr = new XMLHttpRequest()

  xhr.open('GET', `${url}${url.includes('?') ? '&' : '?'}_t_=${new Date().getTime()}`) // url 添加时间戳规避浏览器缓存
  xhr.responseType = 'blob'
  xhr.send()

  xhr.onload = () => {
    let blob = xhr.response
    if (blob) {
      return downloadBlob(blob, fileName)
    }
  }

  xhr.onreadystatechange = () => {
    if (xhr.readyState === 4) {
      // DONE
      callback()
    }
  }

  // 报错的情况下（主要是跨域），尝试打开新窗口预览（手动下载）
  xhr.onerror = () => {
    download(url, fileName, '_blank')
    callback()
  }
}
