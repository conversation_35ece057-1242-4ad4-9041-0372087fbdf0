<script>
  import { apiDownloadInstitutionSchoolsTeachers } from '@/api/user/teacher'

  import { deepCopy } from '@/utils/object'
  import { downloadBlob } from '@/utils/download'
  import { formatDateHourMinutesByChinese } from '@/utils/date'

  import Role from '@/enum/user/role'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      school: {
        type: Object,
        default: () => {},
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        exporting: false,
        exportStageId: 0,
        exportGradeId: 0,
        exportRoleId: -1,
      }
    },

    computed: {
      schoolStageGradeClassSubjects() {
        return this.$store.state.school.schoolStageGradeClassSubjects
      },
      exportStages() {
        const Stages = (this.schoolStageGradeClassSubjects || []).map(stage => ({
          stageId: stage.stageId,
          stageName: stage.stageName,
        }))
        Stages.unshift({
          stageId: 0,
          stageName: '全部学段',
        })
        return Stages
      },
      exportGradeClasses() {
        if (!this.exportStageId) {
          let gradeClasses = [
            {
              gradeId: 0,
              gradeName: '全部年级',
              classes: [
                {
                  classId: 0,
                  className: '全部班级',
                },
              ],
            },
          ]

          this.schoolStageGradeClassSubjects.forEach(stage => {
            stage.grades.forEach(grade => {
              gradeClasses.push({
                gradeId: grade.gradeId,
                gradeName: grade.gradeName,
                classes: [
                  {
                    classId: 0,
                    className: '全部班级',
                  },
                ].concat(Array.isArray(grade.classes) ? grade.classes : []),
              })
            })
          })

          return gradeClasses
        } else {
          let gradeClasses = deepCopy(
            (this.schoolStageGradeClassSubjects.find(stage => stage.stageId === this.exportStageId) || { grades: [] })
              .grades
          )
          gradeClasses.unshift({
            gradeId: 0,
            gradeName: '全部年级',
          })
          gradeClasses.forEach(grade => {
            if (grade.classes && Array.isArray(grade.classes)) {
              grade.classes.unshift({
                classId: 0,
                className: '全部班级',
              })
            } else {
              grade.classes = [
                {
                  classId: 0,
                  className: '全部班级',
                },
              ]
            }
          })

          return gradeClasses
        }
      },
      exportRoles() {
        const SchoolRoles = (Role.getEntries() || []).filter(role => role.id < 8)
        if (SchoolRoles && SchoolRoles.length) {
          SchoolRoles.push({
            id: 0,
            key: 'noCharacter',
            name: '普通老师（无角色）',
          })

          SchoolRoles.unshift({
            id: -1,
            key: 'AllSchoolRoles',
            name: '全部角色',
          })
        }
        return SchoolRoles
      },
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },
      handleVisibleChange(visibility) {
        if (visibility) {
          if (this.exportStages && this.exportStages.length) {
            this.exportStageId = this.exportStages.find(stage => stage.stageId).stageId
          }
        } else {
          this.closeModal()
        }
      },

      changeExportStage(stageId) {
        this.exportStageId = stageId
        if (
          !(this.schoolStageGradeClassSubjects.find(stage => stage.stageId === stageId) || { grades: [] }).grades.some(
            grade => grade.gradeId === this.exportGradeId
          )
        ) {
          this.exportGradeId = 0
        }
      },

      changeExportGrade(gradeId) {
        const Grade = this.exportGradeClasses.find(grade => grade.gradeId === gradeId)
        if (!Grade) {
          return
        }

        this.exportGradeId = gradeId
      },

      handleBtnExportExcelClick() {
        if (this.exporting || !this.school || !this.school.value) {
          return
        }

        this.exporting = true
        return apiDownloadInstitutionSchoolsTeachers({
          institutionId: this.school.value,
          gradeLevel: this.exportStageId || undefined,
          gradeId: this.exportGradeId || undefined,
          roleId: this.exportRoleId === -1 ? undefined : this.exportRoleId,
        })
          .then(responseBlob =>
            downloadBlob(
              responseBlob,
              `${this.school.title}_下属学校教师名单（${formatDateHourMinutesByChinese(new Date())}）.xlsx`
            )
          )
          .then(() => this.closeModal())
          .finally(() => (this.exporting = false))
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    class="modal-export-institutions-schools-teachers"
    title="导出机构下属学校教师"
    @on-visible-change="handleVisibleChange"
  >
    <Form :label-width="60">
      <Form-item label="学段">
        <Select :model-value="exportStageId" transfer @on-change="changeExportStage">
          <Option v-for="s of exportStages" :key="s.stageId" :value="s.stageId">{{ s.stageName }}</Option>
        </Select>
      </Form-item>
      <Form-item label="年级">
        <Select :model-value="exportGradeId" transfer @on-change="changeExportGrade">
          <Option v-for="g in exportGradeClasses" :key="g.gradeId" :value="g.gradeId">{{ g.gradeName }}</Option>
        </Select>
      </Form-item>
      <Form-item label="角色">
        <Select v-model="exportRoleId" transfer>
          <Option v-for="r of exportRoles" :key="r.key" :value="r.id">{{ r.name }}</Option>
        </Select>
      </Form-item>
    </Form>
    <template #footer>
      <Button type="text" @click="closeModal">取消</Button>
      <Button type="primary" :loading="exporting" @click="handleBtnExportExcelClick">{{
        exporting ? '正在导出' : '导出'
      }}</Button>
    </template>
  </Modal>
</template>
