<template>
  <Modal
    :model-value="modelValue"
    class="modal-import-student"
    :title="isMultipleSchool ? '导入下属学校学生' : '导入本校学生'"
    width="730"
    footer-hide
    @on-visible-change="handleVisibilityChange"
  >
    <div class="section-instructions">
      <Alert type="info">
        <ol class="tips">
          <li v-if="isMultipleSchool">该操作只能导入本机构下属学校的学生</li>
          <li>新学生导入时，若所在行政班尚不存在，则自动创建该班级；对于小学教学点、高中教学班，则需在网站上先创建</li>
          <li>同一学生再次导入时，其姓名应与系统内的姓名一致，否则导入失败</li>
          <li>同一学生再次导入时，若勾选更新班级，其所在班级将被更新；否则如果所在班级与系统不一致，则导入失败</li>
        </ol>
      </Alert>
      <div class="template">
        <Button class="btn-download-template" type="default" @click="downloadImportExcelTemplate">
          <Icon type="md-download" size="30"></Icon>
          <br />
          <span>Excel 模板</span>
        </Button>
      </div>
    </div>
    <div class="section-options">
      <!-- <Checkbox v-model="updateClass"
        >导入时根据学籍号更新学生班级
        <Tooltip placement="top">
          <Icon style="color: gray" type="md-help-circle" />
          <template #content>
            <div>导入的学生中，与系统现有学生学籍号相同的，当作同一学生</div>
          </template>
        </Tooltip>
      </Checkbox> -->
      <Checkbox v-model="cleanParentRelate"
        >导入时清除现有家长
        <Tooltip placement="top">
          <Icon style="color: gray" type="md-help-circle" />
          <template #content>
            <div>
              同一学生再次导入时，若勾选清除家长，则以导入的家长替换现有家长；否则：<br />
              如果家长手机号码与系统中手机号码相同，则更新家长姓名，不同，则新增家长
            </div>
          </template>
        </Tooltip>
      </Checkbox>
    </div>
    <div class="section-file">
      <com-upload
        class="com-upload"
        :accepts="['.xlsx', '.xls']"
        :file-name="importFile && importFile.name"
        @on-selected="handleImportFileSelected"
      ></com-upload>
      <Button v-if="importFile" class="btn-import" type="primary" :loading="uploadingFile" @click="handleImportOK">{{
        uploadingFile ? '正在导入' : '导入'
      }}</Button>
    </div>
    <div v-if="showModalImportErrorAlert" class="section-result">
      <div class="status">
        <div v-if="importResult.success" class="status-success">
          <Icon type="md-checkmark-circle"></Icon>
        </div>
        <div v-else class="status-fail">
          <Icon type="md-close-circle"></Icon>
        </div>
      </div>
      <div class="detail" v-html="importResult && importResult.detail"></div>
    </div>
  </Modal>
</template>

<script>
  import ComUpload from '@/components/upload.vue'

  import { apiImportOwnSchoolStudents, apiImportMultipleSchoolStudents } from '@/api/user/school'

  import { downloadUrl } from '@/utils/download'

  export default {
    components: {
      ComUpload,
    },
    props: {
      modelValue: Boolean,
      isMultipleSchool: Boolean,
      schoolId: String,
    },
    emits: ['update:modelValue', 'on-refresh'],
    data() {
      return {
        // updateClass: false,
        cleanParentRelate: false,
        importFile: null,
        uploadingFile: false,
        importResult: null,
        useUnionExamExcelTemplate: true,
      }
    },
    computed: {
      schoolStageGradeClassSubjects() {
        return this.$store.state.school.schoolStageGradeClassSubjects
      },
      showModalImportErrorAlert() {
        return !this.uploadingFile && this.importResult
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          // this.updateClass = false
          this.cleanParentRelate = false
          this.importFile = null
          this.uploadingFile = false
          this.importResult = null
        }
      },
    },
    methods: {
      handleCancel() {
        this.$emit('update:modelValue', false)
      },
      handleVisibilityChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
      downloadImportExcelTemplate() {
        let url = ''

        const emarkingBasicDataFiles = this.$store.getters['emarking/files']()
        const tempType = this.isMultipleSchool ? 'studentTemplateChildSchool' : 'studentTemplate'

        if (emarkingBasicDataFiles && emarkingBasicDataFiles.length) {
          const theTemp = emarkingBasicDataFiles.find(item => item.id === tempType)
          url = theTemp.url
        }

        if (!url) {
          this.$Message.warning({
            duration: 4,
            content: '无法获取到文件路径',
          })
          return
        }

        downloadUrl(url, '导入学生模板.xlsx')
      },
      handleImportFileSelected(file) {
        this.importFile = file
        this.importResult = null
      },
      async handleImportOK() {
        if (!this.importFile) {
          this.$Message.info({
            content: '未选择文件',
          })
          return
        }

        this.uploadingFile = true

        if (this.isMultipleSchool) {
          apiImportMultipleSchoolStudents(this.importFile, {
            // updateClass: this.updateClass,
            updateClass: true,
            cleanParentRelate: this.cleanParentRelate,
            schoolId: this.schoolId,
          })
            .then(successMessage => {
              this.importResult = {
                success: true,
                detail: successMessage,
              }
            })
            .catch(err => {
              if (err.code === -1 && err.msg === '网络连接超时') {
                this.importResult = {
                  success: false,
                  detail: err.msg + ' 或 文件在被选择后非法修改，请重新选择文件后进行导入操作',
                }
              } else {
                this.importResult = {
                  success: false,
                  detail: err.msg,
                }
              }
            })
            .finally(() => {
              this.uploadingFile = false
              this.$store.dispatch('school/getGradeClassesAndSubjects')
              this.$emit('on-refresh')
            })
        } else {
          apiImportOwnSchoolStudents(this.importFile, {
            // updateClass: this.updateClass,
            updateClass: true,
            cleanParentRelate: this.cleanParentRelate,
            schoolId: this.schoolId,
          })
            .then(successMessage => {
              this.importResult = {
                success: true,
                detail: successMessage,
              }
            })
            .catch(err => {
              if (err.code === -1 && err.msg === '网络连接超时') {
                this.importResult = {
                  success: false,
                  detail: err.msg + ' 或 文件在被选择后非法修改，请重新选择文件后进行导入操作',
                }
              } else {
                this.importResult = {
                  success: false,
                  detail: err.msg,
                }
              }
            })
            .finally(() => {
              this.uploadingFile = false
              this.$store.dispatch('school/getGradeClassesAndSubjects')
              this.$emit('on-refresh')
            })
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .section-instructions {
    @include flex(row, space-between, flex-start);

    .tips {
      margin-right: 10px;
      margin-left: 10px;
      line-height: 1.5;
    }

    .btn-download-template {
      flex-shrink: 0;
      height: auto;
      margin-left: 20px;
      padding-top: 10px;
      padding-bottom: 10px;
    }

    .checkbox-template-wrapper {
      margin-top: 10px;
      margin-left: 20px;
      text-align: center;

      .checkbox-template {
        margin-right: 0;
      }
    }
  }

  .section-options {
    @include flex(row, space-between, center);
    width: 550px;
    margin-bottom: 10px;
    padding: 10px;
    color: $color-warning;
  }

  .excel-columns {
    width: 580px;
    padding: 10px;
    color: $color-second-title;
    font-size: $font-size-small;
  }

  .section-file {
    @include flex(row, space-between, center);
    margin-bottom: 20px;

    .com-upload {
      flex-grow: 0;
      width: 550px;
    }

    .btn-import {
      flex-grow: 0;
    }
  }

  .section-result {
    margin-bottom: 10px;
    border-top: 1px solid $color-border;

    .status {
      font-size: 40px;
      text-align: center;

      .status-success {
        color: $color-success;
      }

      .status-fail {
        color: $color-error;
      }
    }

    .detail {
      max-height: 160px;
      overflow-y: auto;
      white-space: pre;
    }
  }

  :deep(.ivu-tooltip-inner) {
    max-width: 1000px;
  }
</style>
