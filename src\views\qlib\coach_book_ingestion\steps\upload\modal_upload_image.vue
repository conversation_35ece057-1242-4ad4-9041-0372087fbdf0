<template>
  <Modal
    :model-value="props.modelValue"
    title="上传图片文件"
    width="600"
    :closable="!uploading"
    :mask-closable="false"
    @on-visible-change="handleVisibleChange"
  >
    <div class="select-file">
      <ComUpload
        :accepts="['.jpg', '.png']"
        :max-size="2"
        :multiple="true"
        :file-name="selectedFilesName"
        :file-size="selectedFilesSize"
        :show-file-size="true"
        @on-selected="onFileSelected"
      ></ComUpload>
    </div>
    <div class="section-progress">
      <div class="label">上传进度</div>
      <Progress :percent="uploadProgress" :stroke-width="10"></Progress>
    </div>
    <template #footer>
      <Button type="text" :disabled="uploading" @click="handleCancel">取消</Button>
      <Button type="primary" :disabled="selectedFiles.length == 0" :loading="uploading" @click="onBtnUploadClick">{{
        uploading ? '上传中' : '上传'
      }}</Button>
    </template>
  </Modal>
</template>

<script setup>
  import { ref, watch, computed } from 'vue'
  import iView from '@/iview'

  import ComUpload from '@/components/upload'

  import { useCoachBookIngestionStore } from '@/store/qlib/coach_book_ingestion'

  import {
    apiCoachBookIngestionChangeFileType,
    apiCoachBookIngestionGetUploadImagesBatchNumber,
    apiCoachBookIngestionUploadImage,
    apiCoachBookIngestionCompleteUploadImagesBatch,
  } from '@/api/qlib/coach_book_ingestion'

  import { sumByDefaultZero } from '@/utils/math'

  import FileTypeEnum from '@/enum/qlib/ingestion/file_type'

  const props = defineProps({
    modelValue: Boolean,
  })

  const emits = defineEmits(['update:modelValue', 'uploaded'])

  const ingestionStore = useCoachBookIngestionStore()

  const selectedFiles = ref([])
  const selectedFilesName = computed(() => {
    if (selectedFiles.value.length == 0) {
      return ''
    }
    return `${selectedFiles.value.length} 个文件（${selectedFiles.value[0].name}...）`
  })
  const selectedFilesSize = computed(() => {
    return sumByDefaultZero(selectedFiles.value, f => f.size)
  })
  const uploading = ref(false)
  const uploadedFileSize = ref(0)
  const uploadProgress = computed(() => {
    if (selectedFilesSize.value == 0) {
      return 0
    }
    return Math.round((uploadedFileSize.value / selectedFilesSize.value) * 100)
  })

  function onFileSelected(files) {
    if (!uploading.value) {
      selectedFiles.value = files
      uploadedFileSize.value = 0
    }
  }

  function handleVisibleChange(visible) {
    if (!visible) {
      handleCancel()
    }
  }

  function handleCancel() {
    emits('update:modelValue', false)
  }

  watch(
    () => props.modelValue,
    () => {
      if (props.modelValue) {
        selectedFiles.value = []
        uploadedFileSize.value = 0
        uploading.value = false
      }
    }
  )

  async function onBtnUploadClick() {
    if (!selectedFiles.value.length) {
      return
    }
    uploading.value = true
    try {
      let ingestionId = ingestionStore.ingestionInfo.id

      let fileType = FileTypeEnum.Image.id
      if (ingestionStore.ingestionInfo.fileType != fileType) {
        ingestionStore.ingestionInfo.fileType = fileType
        await apiCoachBookIngestionChangeFileType({
          ingestionId,
          fileType,
        })
      }

      let batchNumber = await apiCoachBookIngestionGetUploadImagesBatchNumber(ingestionId)
      let { succeededFiles, failedFiles } = await uploadImages({
        ingestionId: ingestionId,
        batchNumber,
        files: selectedFiles.value,
      })
      await apiCoachBookIngestionCompleteUploadImagesBatch({
        ingestionId,
        batchNumber,
      })
      if (failedFiles.length == 0) {
        iView.Message.success('上传成功')
        emits('uploaded')
        handleCancel()
      } else {
        let failedFilesName = failedFiles.map(f => f.name).join('<br>')
        iView.Modal.warning({
          title: '部分文件上传失败',
          content: `${succeededFiles.length} 个文件上传成功，<strong>${failedFiles.length} 个文件上传失败，请复制文件名</strong>：<br>${failedFilesName}`,
          onOk: () => {
            emits('uploaded')
            handleCancel()
          },
        })
      }
    } catch (err) {
      iView.Modal.error({
        title: '上传失败',
        content: err?.msg || '上传失败',
        onOk: () => {
          emits('uploaded')
          handleCancel()
        },
      })
    } finally {
      uploading.value = false
    }
  }

  async function uploadImages({ ingestionId, batchNumber, files }) {
    // 编号
    files.forEach((file, idx) => {
      file.index = idx
    })
    let succeededFiles = []
    let failedFiles = [...files]
    let retryCount = 0
    // 最多传3次
    while (failedFiles.length > 0 && retryCount < 3) {
      let succeededFilesInBatch = await uploadImagesBatch({
        ingestionId,
        batchNumber,
        files: failedFiles,
      })
      succeededFiles.push(...succeededFilesInBatch)
      failedFiles = failedFiles.filter(f => !succeededFilesInBatch.includes(f))
      retryCount++
    }
    return {
      succeededFiles,
      failedFiles,
    }
  }

  async function uploadImagesBatch({ ingestionId, batchNumber, files, concurrency = 5 }) {
    let succeededFiles = []
    let queue = [...files]

    const worker = async () => {
      while (queue.length > 0) {
        let file = queue.shift()
        try {
          await uploadOneImage({
            ingestionId,
            batchNumber,
            index: file.index,
            file,
          })
          succeededFiles.push(file)
          uploadedFileSize.value += file.size
        } catch {
          //
        }
      }
    }

    const workers = []
    for (let i = 0; i < concurrency; i++) {
      workers.push(worker())
    }
    await Promise.all(workers)

    return succeededFiles
  }

  async function uploadOneImage({ ingestionId, batchNumber, index, file }) {
    await apiCoachBookIngestionUploadImage({
      ingestionId,
      batchNumber,
      index,
      file,
    })
  }
</script>

<style lang="scss" scoped>
  .section-progress {
    @include flex(row, flex-start, center);
    margin-top: 16px;
    margin-bottom: 16px;

    .label {
      flex-grow: 0;
      flex-shrink: 0;
      width: 88px;
      margin-right: 10px;
      text-align: right;
    }
  }
</style>
