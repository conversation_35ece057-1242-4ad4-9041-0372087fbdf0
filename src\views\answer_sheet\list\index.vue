<template>
  <div class="container-answer-sheet-list">
    <div class="wrapper-tabs">
      <!-- <Tabs v-model="activeTab" class="tabs-panel" :animated="false">
        <TabPane label="我的答题卡" name="list"></TabPane>
      </Tabs> -->
      <TabList />
    </div>
  </div>
</template>

<script>
  import TabList from './components/tab_list'

  export default {
    components: {
      TabList,
    },
    data() {
      return {
        // activeTab: 'list',
      }
    },
  }
</script>

<style lang="scss" scoped>
  .container-answer-sheet-list {
    background-color: white;

    .wrapper-tabs {
      padding-bottom: 25px;

      :deep(.ivu-tabs-nav .ivu-tabs-tab) {
        padding-bottom: 14px;
      }
    }
  }
</style>
