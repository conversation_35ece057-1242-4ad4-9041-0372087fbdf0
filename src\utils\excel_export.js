import ExcelJS from 'exceljs'
import { downloadBlob } from '@/utils/download'

export { exportExcel, generateExcelBlob, estimateCellWidth, getStringWidth }

const MinColumnWidth = 10
const MaxColumnWidth = 100
const Font = {
  name: '微软雅黑',
  family: 3,
  size: 10,
}
const CaptionFont = {
  name: '微软雅黑',
  family: 3,
  size: 12,
}
const CaptionRowHeight = 30
const HeaderFill = {
  type: 'pattern',
  pattern: 'solid',
  fgColor: {
    // argb: 'FFF3F3F3',
    argb: 'FFCCCCFF',
  },
}
const BorderSideStyle = {
  style: 'thin',
  color: {
    argb: 'FF808080',
  },
}
const BorderStyle = {
  top: BorderSideStyle,
  right: BorderSideStyle,
  bottom: BorderSideStyle,
  left: BorderSideStyle,
}
const Alignment = {
  vertical: 'middle',
  horizontal: 'center',
}

/**
 * 导出excel
 * @param {Array} sheets
 * sheet: {
 *   sheetName: String,                     // sheet名称
 *   caption: String,                       // 表标题
 *   rows: Array,                           // 数据数组
 *   freezeHeader: Boolean,                 // 是否冻结表头
 *   columns: [
 *    {
 *     title: String,                       // 列名称
 *     key: String | Function,              // 单元格值 = row[key] | function(row)
 *     numberFormat: String,                // 数字格式
 *     width: 'auto' | Number | Function,   // 列宽 = 'auto' | number | function(cellValueString)
 *     group: Boolean,                      // 是否按值分组合并单元格
 *     freeze: Boolean,                     // 是否冻结该列
 *     children: [columns]                   // 子列，子列存在时该列本身属性除列名称外均失效
 *    }
 *   ]
 * }
 * @param {String} fileName
 * @returns void
 */
function exportExcel(sheets, fileName = 'workbook.xlsx') {
  return generateExcelBlob(sheets).then(blob => {
    exportExcelByBlob(blob, fileName)
  })
}

function exportExcelByBlob(blob, fileName = 'workbook.xlsx') {
  return downloadBlob(blob, fileName)
}

function generateExcelBlob(sheets) {
  return new Promise((resolve, reject) => {
    // 1. 创建workbook
    let workbook = new ExcelJS.Workbook()

    // 2. 对每一个sheet，检查sheet名称，填充sheet数据
    let sheetNames = []
    sheets.forEach((config, idx) => {
      let sheetName = config.sheetName || `sheet${idx + 1}`
      if (sheetNames.includes(sheetName)) {
        reject(`sheet名称重复：${sheetName}`)
      } else {
        sheetNames.push(sheetName)
      }

      let sheet = workbook.addWorksheet(sheetName)
      try {
        fillSheet(sheet, config)
      } catch (err) {
        reject(err)
      }
    })

    // 3. 生成文件Blob
    workbook.xlsx.writeBuffer().then(data => {
      let blob = new Blob([data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })
      resolve(blob)
    })
  })
}

function fillSheet(sheet, config) {
  // 展开列
  let expandColumns = getExpandColumns(config.columns)

  // 填充内容
  let { headerRows, bodyRows } = addContent(sheet, expandColumns, config)

  // 调整格式
  addFormat(sheet, expandColumns, config, headerRows, bodyRows)

  // 冻结视图
  addViews(sheet, expandColumns, config, headerRows.length)
}

/**
 * 提取展开的列
 */
function getExpandColumns(columns) {
  let expandColumns = []
  let traverse = col => {
    if (col.children && col.children.length > 0) {
      col.children.forEach(traverse)
    } else {
      expandColumns.push(col)
    }
  }
  columns.forEach(traverse)
  return expandColumns
}

/**
 * 填充sheet内容
 */
function addContent(sheet, expandColumns, config) {
  let rows = []
  let mergedCells = []

  // 标题
  let captionRows = []
  if (config.caption) {
    let captionRow = [config.caption]
    for (let i = 1; i < expandColumns.length; i++) {
      captionRow.push(null)
    }
    captionRows.push(captionRow)
    rows.push(captionRow)
    if (expandColumns.length > 1) {
      mergedCells.push([1, 1, 1, expandColumns.length])
    }
  }

  // 表头
  let headerRows = getHeaderRows(config.columns)
  mergedCells.push(...getHeaderMergedCells(headerRows, rows.length + 1))
  rows.push(...headerRows)

  // 表数据
  let bodyRows = getBodyRows(expandColumns, config.rows)
  mergedCells.push(...getBodyMergedCells(bodyRows, rows.length + 1, expandColumns))
  rows.push(...bodyRows)

  // 写入sheet
  sheet.addRows(rows)
  // sheet.mergeCells(startRow, startCol, endRow, endCol)
  mergedCells.forEach(cellGroup => sheet.mergeCells(cellGroup[0], cellGroup[1], cellGroup[2], cellGroup[3]))

  return {
    captionRows,
    headerRows,
    bodyRows,
  }
}

/**
 * 获取表头内容
 */
function getHeaderRows(columns) {
  let rows = []

  // 当前列序号，每次遇到非首列时加1
  let colIndex = 0
  let traverse = (col, rowIndex) => {
    if (rowIndex >= 0) {
      let row = rows[rowIndex]
      if (!row) {
        row = []
        rows[rowIndex] = row
      }
      row[colIndex] = col.title
    }
    if (col.children && col.children.length > 0) {
      col.children.forEach((child, idx) => {
        if (idx > 0) {
          colIndex++
        }
        traverse(child, rowIndex + 1)
      })
    }
  }
  traverse({ children: columns }, -1)

  // 统一各行长度
  let columnCount = Math.max(...rows.map(row => row.length))
  rows.forEach(row => {
    for (let i = 0; i < columnCount; i++) {
      if (!(i in row)) {
        row[i] = null
      }
    }
  })

  return rows
}

/**
 * 获取表头合并单元格
 * @returns
 */
function getHeaderMergedCells(headerRows, startRowNo) {
  let mergedCells = []
  let columnCount = headerRows[0].length
  let rowCount = headerRows.length

  headerRows.forEach((row, rowIndex) => {
    row.forEach((cell, colIndex) => {
      if (cell == null) {
        return
      }

      let rowSpan = 0
      for (let i = rowIndex + 1; i < rowCount; i++) {
        if (headerRows[i][colIndex] == null) {
          rowSpan++
        } else {
          break
        }
      }

      let colSpan = 0
      for (let j = colIndex + 1; j < columnCount; j++) {
        if (headerRows[rowIndex][j] == null) {
          if (!rowIndex || !headerRows[rowIndex - 1][j]) {
            colSpan++
          }
        } else {
          break
        }
      }

      if (rowSpan > 0 || colSpan > 0) {
        mergedCells.push([rowIndex + startRowNo, colIndex + 1, rowIndex + startRowNo + rowSpan, colIndex + 1 + colSpan])
      }
    })
  })

  return mergedCells
}

/**
 * 获取表数据
 */
function getBodyRows(expandColumns, dataRows) {
  return dataRows.map(data => expandColumns.map(col => (typeof col.key === 'function' ? col.key(data) : data[col.key])))
}

/**
 * 获取表数据合并单元格
 */
function getBodyMergedCells(bodyRows, startRowNo, expandColumns) {
  // 需要分组的列
  let columnIndexes = []
  expandColumns.forEach((col, idx) => {
    if (col.group) {
      columnIndexes.push(idx)
    }
  })
  if (columnIndexes.length == 0) {
    return []
  }

  let mergedCells = []
  columnIndexes.forEach(colIndex => {
    let groupFirstRowIndex = 0
    let groupFirstRow = bodyRows[groupFirstRowIndex]
    let groupRowSpan = 0
    bodyRows.forEach((row, i) => {
      if (i == 0) {
        return
      }
      // 如果本列及之前列数据与该组第一行的相同，则合并到该组
      if (Array.from({ length: colIndex + 1 }).every((x, idx) => row[idx] === groupFirstRow[idx])) {
        groupRowSpan++
      } else {
        if (groupRowSpan > 0) {
          mergedCells.push([
            groupFirstRowIndex + startRowNo,
            colIndex + 1,
            groupFirstRowIndex + startRowNo + groupRowSpan,
            colIndex + 1,
          ])
        }
        groupFirstRowIndex = i
        groupFirstRow = row
        groupRowSpan = 0
      }
    })
    if (groupRowSpan > 0) {
      mergedCells.push([
        groupFirstRowIndex + startRowNo,
        colIndex + 1,
        groupFirstRowIndex + startRowNo + groupRowSpan,
        colIndex + 1,
      ])
    }
  })

  return mergedCells
}

/**
 * 添加格式
 */
function addFormat(sheet, expandColumns, config, headerRows, bodyRows) {
  let hasCaption = Boolean(config.caption)
  let headerStartRowNo = hasCaption ? 2 : 1
  let tableRowCount = headerRows.length + bodyRows.length

  // 列格式
  addColumnFormats(sheet, expandColumns, headerRows, bodyRows)

  // 标题格式
  if (hasCaption) {
    addCaptionFormats(sheet)
  }

  // 表头格式
  addHeaderFormats(sheet, headerStartRowNo, headerRows.length)

  // 单元格格式
  addCellFormats(sheet, expandColumns, headerStartRowNo, tableRowCount)
}

function addCaptionFormats(sheet) {
  let firstRow = sheet.getRow(1)
  firstRow.font = CaptionFont
  firstRow.height = CaptionRowHeight
}

function addHeaderFormats(sheet, headerStartRowNo, headerRowCount) {
  sheet.getRows(headerStartRowNo, headerRowCount).forEach(row => {
    row.eachCell({ includeEmpty: true }, cell => {
      cell.fill = HeaderFill
    })
  })
}

function addCellFormats(sheet, columns, headerStartRowNo, tableRowCount) {
  sheet.getRows(headerStartRowNo, tableRowCount).forEach(row => {
    for (let i = 1; i <= columns.length; i++) {
      let cell = row.getCell(i)
      cell.border = BorderStyle
      if (!columns[i - 1].numberFormat && columns[i - 1].cellNumberFormat) {
        cell.numFmt = columns[i - 1].cellNumberFormat(cell.value)
      }
    }
  })
}

function addColumnFormats(sheet, expandColumns, headerRows, bodyRows) {
  // 计算宽度时,只考虑表头最后一行和表数据行
  let tableValues = [headerRows[headerRows.length - 1], ...bodyRows]
  expandColumns.forEach((col, idx) => {
    let column = sheet.getColumn(idx + 1)
    column.font = Font
    column.alignment = Alignment
    column.numFmt = col.numberFormat || undefined
    column.width = getColumnWidth(
      col,
      tableValues.map(x => x[idx])
    )
  })
}

function getColumnWidth(columnConfig, columnValues) {
  let width = MinColumnWidth
  let getWidthFunction = null
  if (typeof columnConfig.width == 'number') {
    width = columnConfig.width
  } else if (typeof columnConfig.width == 'function') {
    getWidthFunction = columnConfig.width
  } else if (columnConfig.width == 'auto') {
    getWidthFunction = getCellWidth
  }

  if (getWidthFunction) {
    let formatCellValueFunction = getFormatCellValueFunction(columnConfig.numberFormat)
    let cellWidths = columnValues.map(formatCellValueFunction).map(getWidthFunction)
    width = Math.max(...cellWidths)
  }

  return Math.min(MaxColumnWidth, Math.max(MinColumnWidth, width))
}

function getFormatCellValueFunction(numberFormat) {
  let match = (numberFormat || '').match(/\.(0*)(#*)(%?)$/)
  let isPercent = match && match[3] == '%'
  let fixedLength = (match && match[1].length) || 0
  let autoLength = (match && match[2].length) || 0
  return value => {
    if (typeof value != 'number') {
      return value
    }
    if (isPercent) {
      value *= 100
    }
    let str = ''
    if (fixedLength > 0 || autoLength > 0) {
      str = value.toFixed(fixedLength + autoLength)
      for (let i = 0; i < autoLength; i++) {
        if (str.endsWith('0')) {
          str = str.substring(0, str.length - 1)
        } else {
          break
        }
      }
    } else {
      str = value.toString()
    }
    return str + (isPercent ? '%' : '')
  }
}

function getCellWidth(str) {
  if (!str) {
    return 0
  }
  return estimateCellWidth(getStringWidth(str))
}

/**
 * 返回某长度字符串在excel中完全显示需要的列宽
 */
function estimateCellWidth(stringWidth) {
  return Math.round(stringWidth * 0.08 * Font.size + 6)
}

/**
 * 返回字符串宽度，假设非ascii字符是ascii字符宽度的2倍
 */
function getStringWidth(str) {
  let asciiCharCount = 0
  for (let i = 0; i < str.length; i++) {
    if (str.charCodeAt(i) < 128) {
      asciiCharCount++
    }
  }
  return asciiCharCount + (str.length - asciiCharCount) * 2
}

/**
 * 冻结视图
 */
function addViews(sheet, expandColumns, config, headerRowCount) {
  let frozenColumnCount = 0
  for (let i = 0; i < expandColumns.length; i++) {
    if (expandColumns[i].freeze) {
      frozenColumnCount++
    } else {
      break
    }
  }
  let frozenRowCount = 0
  if (config.freezeHeader) {
    frozenRowCount = headerRowCount
    if (config.caption) {
      frozenRowCount++
    }
  }
  if (frozenColumnCount > 0 || frozenRowCount > 0) {
    sheet.views = [
      {
        state: 'frozen',
        xSplit: frozenColumnCount,
        ySplit: frozenRowCount,
      },
    ]
  }
}
