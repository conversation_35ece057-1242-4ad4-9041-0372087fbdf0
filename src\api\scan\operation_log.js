import ajax from '@/api/ajax'
import { transformScanUnit } from './scanUnit'

export function apiGetOperationLogPage(params) {
  return ajax
    .post({
      url: 'scan/operationLog/page',
      data: {
        examSubjectId: params.examSubjectId,
        scanStationId: params.scanStationId,
        scanUserId: params.scanUserId,
        operationTypes: params.operationTypes,
        operator: params.operator,
        beginTime: params.beginTime,
        endTime: params.endTime,
        admissionNum: params.admissionNum,
        page: params.currentPage,
        size: params.pageSize,
        id: params.id,
      },
      requestName: '获取处理记录',
    })
    .then(data => {
      data.records.forEach(item => {
        if (item.scanUnit) {
          item.scanUnit = transformScanUnit(item.scanUnit)
        }
      })
      return data
    })
}
