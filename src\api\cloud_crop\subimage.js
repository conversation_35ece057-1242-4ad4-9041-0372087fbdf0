import ajax from '@/api/ajax'

export function apiGetCutStatus(data) {
  return ajax
    .get({
      url: '/imgproc/subimage/stats',
      params: {
        examId: data.examId,
        examSubjectId: data.examSubjectId,
      },
      requestName: '获取切割状态统计',
    })
    .then(data => {
      return data
    })
}

export function apiGetCutByStatus(data) {
  return ajax
    .get({
      url: '/imgproc/subimage/students',
      params: {
        examId: data.examId,
        examSubjectId: data.examSubjectId,
        page: data.page,
        size: data.size,
        status: data.status,
        roomId: data.roomId,
        zkzh: data.zkzh,
      },
      requestName: '获取切割各状态统计',
    })
    .then(data => {
      return data
    })
}

export function apiReCutThePaper(data) {
  const urls = {
    success: '/imgproc/subimage/resetsucceed',
    fail: '/imgproc/subimage/resetfailed',
  }
  return ajax
    .put({
      url: urls[data.type],
      data: data.msg,
      requestName: '重切选定卷',
    })
    .then(data => {
      return data
    })
}

export function apiFixCutting(data) {
  return ajax.put({
    url: 'imgproc/subimage/resetcut',
    data: {
      examSubjectId: data.examSubjectId,
      roomIds: [],
      scanInfoIds: data.serialNos,
    },
    requestName: '刷新子图切割',
  })
}

export function apiFixCutWaiting(data) {
  return ajax.put({
    url: 'imgproc/subimage/resetpre',
    data: {
      examSubjectId: data.examSubjectId,
      roomIds: [],
      scanInfoIds: data.serialNos,
    },
    requestName: '刷新子图切割',
  })
}

export function apiGetRoomScanStats(params) {
  return ajax.get({
    url: 'imgproc/subimage/roomScanStat',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
    },
    requestName: '获取扫描统计',
  })
}

export function apiGetAllExamRooms(params) {
  return ajax.get({
    url: 'imgproc/subimage/rooms',
    params: {
      examSubjectId: params.examSubjectId,
    },
    requestName: '获取本科目所有考场列表',
  })
}

export function apiGetExamRoomDetail(params) {
  return ajax.get({
    url: 'imgproc/subimage/roomDetail',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      roomId: params.roomId,
    },
    requestName: '获取具体考场扫描情况',
  })
}

export function apiGetStudentScanObjective(params) {
  return ajax.get({
    url: 'imgproc/subimage/objs',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      scaninfoserialno: params.serialNo,
    },
    requestName: '获取试卷具体客观题识别情况',
  })
}
