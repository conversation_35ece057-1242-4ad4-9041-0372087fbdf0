export function replaceBlockImageUrlPixelTypeKeyword(originUrl, pixelType) {
  let url = originUrl
  if (pixelType) {
    if (pixelType === 'gray') {
      if (!originUrl.endsWith('-grayscale.jpg')) {
        url = originUrl.replace(/.jpg/, '-grayscale.jpg')
      }
    } else if (pixelType === 'binary') {
      if (!originUrl.endsWith('-binary.jpg')) {
        url = originUrl.replace(/.jpg/, '-binary.jpg')
      }
    }
  }
  return url
}
