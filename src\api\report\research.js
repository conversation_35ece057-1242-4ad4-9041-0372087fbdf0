import ajax from '@/api/ajax'

export function apiGetUnionTemplate(params) {
  return ajax
    .get({
      url: 'report/situations/eduExamTemplates',
      params: {
        gradeId: params.gradeId,
        subjectId: params.subjectId,
      },
      requestName: '获取历次联考模板',
    })
    .then(data => {
      return data
    })
}

export function apiGetEduExamSchoolAvgCompare(params) {
  return ajax
    .get({
      url: 'report/situations/eduExamSchoolCompare',
      params: {
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        templateIds: params.templateIds,
      },
      requestName: '获取历次联考学校均分对比',
    })
    .then(data => {
      return data
    })
}

export function apiGetEduExamSchoolLevelCompare(params) {
  return ajax
    .post({
      url: 'report/situations/eduExamSchoolScoreLevelCompare',
      data: params.config,
      params: params.params,
      requestName: '获取历次联考学校等级对比',
    })
    .then(data => {
      return data
    })
}

export function apiGetEduExamClassAvgCompare(params) {
  return ajax
    .get({
      url: 'report/situations/eduExamClassCompare',
      params: {
        gradeId: params.gradeId,
        subjectId: params.subjectId,
        templateIds: params.templateIds,
      },
      requestName: '获取历次联考班级均分对比',
    })
    .then(data => {
      return data
    })
}

export function apiGetEduExamClassLevelCompare(params) {
  return ajax
    .post({
      url: 'report/situations/eduExamClassScoreLevelCompare',
      data: params.config,
      params: params.params,
      requestName: '获取历次联考班级等级对比',
    })
    .then(data => {
      return data
    })
}

export function apiGetEduExamStuCompare(params) {
  return ajax
    .get({
      url: 'report/situations/eduExamStuCompare',
      params: params,
      requestName: '获取历次联考学生对比',
    })
    .then(data => {
      return data
    })
}

export function apiExportEduExamStuCompare(data) {
  return ajax
    .download({
      url: 'report/situations/exportEduExamStuCompare',
      params: data,
      requestName: '导出学生跟踪',
    })
    .then(data => {
      return data
    })
}

export function apiGetCommentTemplate(params) {
  return ajax
    .get({
      url: 'report/assess/listTemplate',
      params: params,
      requestName: '获取增量评价模板列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheCommentTemplate(params) {
  return ajax
    .get({
      url: 'report/assess/template',
      params: params,
      requestName: '获取增量评价模板列表',
    })
    .then(data => {
      return data
    })
}

export function apiCreateCommentTemplate(params) {
  return ajax
    .upload({
      url: 'report/assess/createTemplate',
      data: params,
      requestName: '创建考核评价模板',
    })
    .then(data => {
      return data
    })
}

export function apiUpdateCommentTemplate(params) {
  return ajax
    .upload({
      url: 'report/assess/updateTemplate',
      data: params,
      requestName: '更新考核评价模板',
    })
    .then(data => {
      return data
    })
}

export function apiGetEduSchoolExamList(params) {
  return ajax
    .get({
      url: 'report/assess/getExamList',
      params: params,
      requestName: '获取可选项目列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetTemplateList(params) {
  return ajax
    .get({
      url: 'report/assess/listTemplate',
      params: params,
      requestName: '获取可选模板列表',
    })
    .then(data => {
      return data
    })
}

export function apiCreateComment(params) {
  return ajax
    .upload({
      url: 'report/assess/createProject',
      data: params,
      requestName: '创建考核评价',
    })
    .then(data => {
      return data
    })
}

export function apiUpdateComment(params) {
  return ajax
    .upload({
      url: 'report/assess/updateProject',
      data: params,
      requestName: '更新考核评价',
    })
    .then(data => {
      return data
    })
}

export function apiGetCommentList(params) {
  return ajax
    .get({
      url: 'report/assess/listProject',
      params: params,
      requestName: '获取评价项目列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheCommentSettings(params) {
  return ajax
    .get({
      url: 'report/assess/project',
      params: params,
      requestName: '获取某个评价项目详情',
    })
    .then(data => {
      return data
    })
}

export function apiGenAssessReport(params) {
  return ajax
    .post({
      url: 'report/assess/generateAssessReport',
      params: params,
      requestName: '生成考核报告',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheSchoolCommentDetails(params) {
  return ajax
    .get({
      url: 'report/assess/getSchoolAssessData',
      params: params,
      requestName: '获取某个评价项目学校增量分析详情',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheClassCommentDetails(params) {
  return ajax
    .get({
      url: 'report/assess/getClassAssessData',
      params: params,
      requestName: '获取某个评价项目班级增量分析详情',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheTeacherCommentDetails(params) {
  return ajax
    .get({
      url: 'report/assess/getTeacherAssessData',
      params: params,
      requestName: '获取某个评价项目教师增量分析详情',
    })
    .then(data => {
      return data
    })
}

export function apiDeleteComment(params) {
  return ajax
    .delete({
      url: 'report/assess/deleteProject',
      params: params,
      requestName: '删除增量评价',
    })
    .then(data => {
      return data
    })
}

export function apiGetTheCommentSchoolList(params) {
  return ajax
    .get({
      url: 'report/assess/listExamSchool',
      params: params,
      requestName: '获取某个评价项目的参考学校列表',
    })
    .then(data => {
      return data
    })
}
