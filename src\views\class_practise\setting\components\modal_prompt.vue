<template>
  <Modal
    :model-value="modelValue"
    :width="800"
    :title="title"
    :mask-closable="!loading"
    @on-visible-change="handleVisibilityChange"
  >
    <div class="section-actions">
      <span class="switch-show-all">显示完整提示词 <i-switch v-model="showAllPromptParts"></i-switch></span>
      <TextButton class="btn-regenerate" type="primary" @click="regeneratePrompt">重新生成提示词</TextButton>
      <TextButton :disabled="!hasPrevious" icon="md-arrow-back" @click="toPreviousBlock">上个题块</TextButton>
      <TextButton :disabled="!hasNext" icon="md-arrow-forward" @click="toNextBlock">下个题块</TextButton>
    </div>
    <div class="section-prompt">
      <div v-if="showAllPromptParts" class="prompt-part prompt-goal">
        <div class="prompt-part-header">
          <div class="prompt-part-title">目标任务</div>
        </div>
        <div class="prompt-part-content pre-wrap" v-html="currentBlockPrompt?.goal || ''"></div>
      </div>
      <div class="prompt-part prompt-questions">
        <div class="prompt-part-header">
          <div class="prompt-part-title">题目答案</div>
          <Poptip
            placement="bottom-end"
            width="300"
            trigger="click"
            title="选择小题"
            @on-popper-show="addQuestionCodes = []"
          >
            <TextButton type="primary" icon="md-add" @click="showModalAddQuestion = true">添加</TextButton>
            <template #content>
              <TextButton class="poptip-btn-select-all" type="primary" @click="selectAllQuestions">全选</TextButton>
              <CheckboxGroup v-model="addQuestionCodes" class="poptip-check">
                <Checkbox
                  v-for="item in currentBlockQuestionCodes"
                  :key="item.code"
                  :label="item.code"
                  :disabled="item.disabled"
                ></Checkbox>
              </CheckboxGroup>
              <div class="poptip-footer">
                <Button type="primary" size="small" @click="handleAddQuestionOK">确定</Button>
              </div>
            </template>
          </Poptip>
        </div>
        <div class="prompt-part-content">
          <div v-for="q in currentBlockPromptQuestions" :key="q.key" class="prompt-question">
            <div class="question-title">
              <TextButton type="warning" icon="md-close" title="删除" @click="removeQuestion(q)"></TextButton>
              <div class="question-codes">主观题：{{ (q.codes || []).join('、') }}</div>
              <Input v-model="q.intro" type="text" maxlength="100" class="question-intro"></Input>
            </div>
            <PromptContent v-model="q.contents"></PromptContent>
          </div>
        </div>
      </div>
      <div class="prompt-part prompt-standard">
        <div class="prompt-part-header">
          <div class="prompt-part-title">评分标准</div>
        </div>
        <div v-if="currentBlockPromptStandard" class="prompt-part-content">
          <Input v-model="currentBlockPromptStandard.intro" class="standard-intro" type="text" maxlength="100"></Input>
          <div v-for="item in currentBlockPromptStandard.items" :key="item.key" class="standard-item">
            <div class="question-code">主观题：{{ item.code }}</div>
            <div class="question-full-score">{{ item.fullScoreText }}</div>
            <PromptContent v-model="item.contents" class="standard-item-contents"></PromptContent>
          </div>
          <div class="standard-extra">
            <div class="standard-extra-title">补充评分标准</div>
            <PromptContent v-model="currentBlockPromptStandard.extra"></PromptContent>
          </div>
        </div>
      </div>
      <div v-if="showAllPromptParts" class="prompt-part prompt-response">
        <div class="prompt-part-header">
          <div class="prompt-part-title">返回结果规范</div>
        </div>
        <div class="prompt-part-content pre-wrap" v-html="currentBlockPrompt?.responseRule || ''"></div>
      </div>
    </div>
    <template #footer>
      <div>
        <Button type="text" :disabled="loading" @click="handleCancel">取消</Button>
        <Button type="primary" :loading="loading" @click="handleOK">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import PromptContent from './prompt_content.vue'

  import { apiModifyAiBlockPrompt, apiGenerateAiBlockPrompt } from '@/api/emarking/ai'

  import { getFullCode } from '@/helpers/emarking/miscellaneous'
  import { deepCopy } from '@/utils/object'
  import { randomId } from '@/utils/string'
  import AiScoreTypeEnum from '@/enum/emarking/ai_score_type'

  export default {
    components: {
      PromptContent,
    },
    props: {
      modelValue: Boolean,
      examSubjectId: String,
      blocks: Array,
      initBlockId: String,
    },
    emits: ['update:modelValue', 'refresh-prompt'],
    data() {
      return {
        currentBlockId: '',
        showAllPromptParts: false,
        currentBlockPrompt: null,
        loading: false,
        showModalAddQuestion: false,
        addQuestionCodes: [],
      }
    },
    computed: {
      aiScoreTypeLLMBlocks() {
        return this.blocks.filter(block => block.aiScoreType == AiScoreTypeEnum.LLM.id)
      },
      currentBlock() {
        return this.aiScoreTypeLLMBlocks.find(block => block.blockId == this.currentBlockId)
      },
      currentBlockIndex() {
        return this.aiScoreTypeLLMBlocks.findIndex(block => block.blockId == this.currentBlockId)
      },
      title() {
        if (!this.currentBlock) {
          return '提示词'
        }
        return `提示词【${this.currentBlock.blockName}】`
      },
      hasPrevious() {
        return this.currentBlockIndex > 0
      },
      hasNext() {
        return this.currentBlockIndex >= 0 && this.currentBlockIndex < this.aiScoreTypeLLMBlocks.length - 1
      },
      currentBlockPromptQuestions() {
        return this.currentBlockPrompt?.questions || []
      },
      currentBlockPromptStandard() {
        return this.currentBlockPrompt?.standard || null
      },
      currentBlockQuestionCodes() {
        let questions = this.currentBlock?.questions
        if (!questions) {
          return []
        }
        let existCodes = []
        this.currentBlockPrompt?.questions?.forEach(q => {
          q.codes?.forEach(code => existCodes.push(code))
        })
        return questions.map(q => {
          let code = getFullCode(q.questionCode, q.branchCode)
          return {
            code,
            questionCode: q.questionCode,
            branchCode: q.branchCode,
            disabled: existCodes.includes(code),
          }
        })
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.init(this.initBlockId)
        } else {
          this.currentBlockId = ''
          this.currentBlockPrompt = null
        }
      },
      blocks() {
        if (this.modelValue) {
          this.init(this.currentBlockId)
        }
      },
    },
    methods: {
      init(blockId) {
        let index = this.aiScoreTypeLLMBlocks.findIndex(block => block.blockId == blockId)
        if (index >= 0) {
          this.toBlockSpecifyIndex(index)
        } else if (this.aiScoreTypeLLMBlocks.length > 0) {
          this.toBlockSpecifyIndex(0)
        } else {
          this.currentBlockId = ''
        }
      },
      toPreviousBlock() {
        this.toBlockSpecifyIndex(this.currentBlockIndex - 1)
      },
      toNextBlock() {
        this.toBlockSpecifyIndex(this.currentBlockIndex + 1)
      },
      toBlockSpecifyIndex(index) {
        if (this.aiScoreTypeLLMBlocks[index]) {
          this.currentBlockId = this.aiScoreTypeLLMBlocks[index].blockId
          if (!this.currentBlock.aiScoreExtraJson) {
            this.currentBlockPrompt = null
            return
          }
          let prompt = JSON.parse(this.currentBlock.aiScoreExtraJson)
          prompt.questions?.forEach((q, idx) => {
            q.key = this.getKey(idx)
          })
          prompt.standard?.items?.forEach((item, idx) => {
            item.key = this.getKey(idx)
          })
          this.currentBlockPrompt = prompt
        }
      },
      getKey(idx) {
        return `${idx}-${randomId()}`
      },
      removeQuestion(q) {
        let questions = this.currentBlockPrompt?.questions || []
        let index = questions.indexOf(q)
        if (index >= 0) {
          questions.splice(index, 1)
        }
      },
      getResultPrompt() {
        if (!this.currentBlockPrompt) {
          return ''
        }
        let prompt = deepCopy(this.currentBlockPrompt)
        prompt.questions?.forEach(q => {
          delete q.key
        })
        prompt.standard?.items?.forEach(item => {
          delete item.key
        })
        return prompt
      },
      handleOK() {
        if (!this.currentBlock) {
          return
        }
        let resultPrompt = this.getResultPrompt()
        let promptJson = JSON.stringify(resultPrompt)
        if (promptJson == this.currentBlock.aiScoreExtraJson) {
          this.$Message.info('未作修改')
          this.handleCancel()
          return
        }
        this.$Modal.confirm({
          title: '修改提示词',
          content: `确定修改题块【${this.currentBlock.blockName}】的AI评卷提示词？`,
          onOk: () => {
            this.loading = true
            apiModifyAiBlockPrompt({
              examSubjectId: this.examSubjectId,
              prompt: resultPrompt,
            })
              .then(() => {
                this.$Message.success('提示词已修改')
                this.handleCancel()
              })
              .finally(() => {
                this.loading = false
                this.$emit('refresh-prompt')
              })
          },
        })
      },
      handleCancel() {
        this.$emit('update:modelValue', false)
      },
      handleVisibilityChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
      regeneratePrompt() {
        if (!this.currentBlock) {
          return
        }
        this.$Modal.confirm({
          title: '重新生成提示词',
          content: `确定重新生成题块【${this.currentBlock.blockName}】的AI评卷提示词？`,
          onOk: () => {
            this.loading = true
            apiGenerateAiBlockPrompt({ examSubjectId: this.examSubjectId, blockId: this.currentBlockId })
              .then(() => {
                this.$Message.success('已重新生成提示词')
              })
              .finally(() => {
                this.loading = false
                this.$emit('refresh-prompt')
              })
          },
        })
      },
      selectAllQuestions() {
        this.addQuestionCodes = this.currentBlockQuestionCodes.filter(x => !x.disabled).map(x => x.code)
      },
      handleAddQuestionOK() {
        if (!this.currentBlock) {
          return
        }
        if (this.addQuestionCodes.length == 0) {
          if (this.currentBlockQuestionCodes.every(x => x.disabled)) {
            this.$Message.warning('所有小题都已添加')
          } else {
            this.$Message.warning('请选择小题')
          }
          return
        }

        let questionCodes = []
        this.addQuestionCodes.forEach(code => {
          let q = this.currentBlockQuestionCodes.find(x => x.code == code)
          if (q && !questionCodes.includes(q.questionCode)) {
            questionCodes.push(q.questionCode)
          }
        })
        questionCodes.sort((a, b) => a - b)
        let codeText = ''
        if (questionCodes.length == 1) {
          codeText = `${questionCodes[0]}题`
        } else if (questionCodes.length == questionCodes[questionCodes.length - 1] - questionCodes[0] + 1) {
          codeText = `${questionCodes[0]}至${questionCodes[questionCodes.length - 1]}题`
        } else {
          codeText = questionCodes.map(questionCode => `${questionCode}题`).join('、')
        }
        let intro = `以下是${codeText}内容与参考答案：`

        let key = this.getKey(this.currentBlockPromptQuestions.length)
        this.currentBlockPromptQuestions.push({
          key,
          codes: this.addQuestionCodes.slice(),
          intro,
          contents: [],
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .section-actions {
    @include flex(row, flex-start, center);
    padding-bottom: 16px;
    border-bottom: 1px solid $color-border;

    .btn-regenerate {
      margin-right: auto;
      margin-left: 16px;
    }
  }

  .prompt-part {
    margin-top: 24px;

    .prompt-part-header {
      @include flex(row, space-between, center);

      .prompt-part-title {
        display: inline-block;
        padding: 8px;
        border: 2px solid $color-primary;
        border-radius: 4px;
        color: $color-primary;
        font-size: 16px;
        line-height: 1;
      }
    }

    .prompt-part-content {
      margin-top: 16px;
      margin-left: 24px;

      &.pre-wrap {
        white-space: pre-wrap;
      }
    }
  }

  .prompt-question {
    &:not(:first-child) {
      padding-top: 16px;
      border-top: 1px dashed $color-primary;
    }

    &:not(:last-child) {
      margin-bottom: 16px;
    }

    .question-title {
      @include flex(row, flex-start, center);
      margin-bottom: 8px;

      .question-codes {
        flex-grow: 0;
        flex-shrink: 0;
        margin-right: 16px;
        color: $color-primary;
      }

      .question-intro {
        flex-grow: 1;
      }
    }
  }

  .prompt-standard {
    .standard-intro {
      margin-bottom: 8px;
    }

    .standard-item {
      @include flex(row, flex-start, center);
      margin-bottom: 8px;

      .question-code {
        flex-grow: 0;
        flex-shrink: 0;
        margin-right: 16px;
        color: $color-primary;
      }

      .question-full-score {
        flex-grow: 0;
        flex-shrink: 0;
        margin-right: 8px;
      }

      .standard-item-contents {
        flex-grow: 1;
      }
    }

    .standard-extra-title {
      margin-bottom: 8px;
      color: $color-primary;
    }
  }

  .poptip-check {
    margin-top: 8px;
  }

  .poptip-footer {
    @include flex(row, flex-end, center);
    margin-top: 16px;
  }
</style>
