<template>
  <div class="layout-header" :class="headerClass" :style="headerStyle">
    <slot></slot>
    <div v-if="background" class="background" :class="backgroundClass"></div>
  </div>
</template>

<script>
  export default {
    props: {
      background: <PERSON><PERSON><PERSON>,
      sticky: <PERSON><PERSON><PERSON>,
      top: {
        type: Number,
        default: 0,
      },
      borderBottomShadow: Boolean,
    },
    emits: ['change-pinned'],
    data() {
      return {
        observer: null,
        pinned: false,
      }
    },
    computed: {
      headerClass() {
        return {
          sticky: this.sticky,
          pinned: this.sticky && this.pinned,
        }
      },
      headerStyle() {
        if (this.sticky) {
          return {
            top: this.top + 'px',
          }
        }
        return null
      },
      backgroundClass() {
        return {
          'bottom-shadow': this.borderBottomShadow,
        }
      },
    },
    watch: {
      sticky() {
        if (this.sticky) {
          this.addObserver()
        } else {
          this.removeObserver()
        }
      },
    },
    mounted() {
      if (this.sticky) {
        this.addObserver()
      }
    },
    beforeUnmount() {
      this.removeObserver()
    },
    methods: {
      addObserver() {
        if (this.observer) {
          return
        }
        if (!this.$el) {
          return
        }
        let observerOptions = {
          root: null,
          rootMargin: `${-(this.top + 1)}px 0px 0px 0px`,
          threshold: 1,
        }
        let observerCallback = entries => {
          let entry = entries[0]
          if (!entry) {
            return
          }
          this.pinned = entry.boundingClientRect.top <= this.top + 1
          this.$emit('change-pinned', this.pinned)
        }
        this.observer = new IntersectionObserver(observerCallback, observerOptions)
        this.observer.observe(this.$el)
      },
      removeObserver() {
        if (!this.observer) {
          return
        }
        this.observer.disconnect()
        this.observer = null
        this.pinned = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .layout-header {
    position: relative;

    &.sticky {
      position: sticky;
    }
  }

  .background {
    position: absolute;
    top: 0;
    right: calc((100% - 100vw) / 2);
    right: calc((100% - max(100vw, $page-min-width)) / 2);
    bottom: 0;
    left: calc((100% - 100vw) / 2);
    left: calc((100% - max(100vw, $page-min-width)) / 2);
    z-index: -1;
    background-color: white;

    &.bottom-shadow {
      box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
    }
  }
</style>
