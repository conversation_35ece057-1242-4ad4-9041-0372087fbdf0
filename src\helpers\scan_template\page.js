import TemplatePage from '@/helpers/scan_template_define/template_page'
import TemplatePageParam from '@/helpers/scan_template_define/template_page_param'
import TemplateMarkArea from '@/helpers/scan_template_define/template_mark_area'
import TemplateRect from '@/helpers/scan_template_define/template_rect'
import TemplateStudentInfo from '@/helpers/scan_template_define/template_student_info'
import TemplateBarcodeArea from '@/helpers/scan_template_define/template_barcode_area'
import TemplateHandwritingArea from '@/helpers/scan_template_define/template_handwriting_area'
import TemplateOmrArea from '@/helpers/scan_template_define/template_omr_area'
import TemplateSubjectiveArea from '@/helpers/scan_template_define/template_subjective_area'
import TemplateSelectMarkArea from '@/helpers/scan_template_define/template_select_mark_area'
import TemplateTrueFalseScoreArea from '../scan_template_define/template_true_false_score_area'
import TemplateBarScoreArea from '../scan_template_define/template_bar_score_area'
import TemplateBarScoreCell from '../scan_template_define/template_bar_score_cell'
import TemplateManualScoreArea from '../scan_template_define/template_manual_score_area'
import Rect from './rect'
import OmrBlock from './omr_block'
import SubjectiveArea from './subjective_area'
import SelectMarkArea from './select_mark_area'

import { sortQuestionFunction } from '@/helpers/emarking'
import AnchorPositionEnum from '@/enum/scan_template/anchor_position'
import AdmissionTypeEnum from '@/enum/scan_template/admission_type'
import LocateModeEnum from '@/enum/scan_template/locate_mode'

const AnchorPositions = AnchorPositionEnum.getIds()
export default class Page {
  constructor() {
    // 序号，从0开始
    this.index = 0
    // 名称
    this.name = ''
    // 图像
    this.image = {
      src: '',
      width: 0,
      height: 0,
      canvas: null,
    }
    // 页面校正方式
    this.locateMode = LocateModeEnum.Anchor.id
    // 定位点
    this.anchor = {
      topLeft: null,
      topRight: null,
      bottomRight: null,
      bottomLeft: null,
    }
    // 定位线
    this.line = null
    // 定位文字
    this.text = null
    // 定位文字2
    this.text2 = null
    // 标题
    this.title = null
    // 标题2
    this.title2 = null
    // 页码标记
    this.pageMark = null
    // 是否文字类型页码标记
    this.isPageMarkText = false
    // 缺考标记
    this.missingMark = null
    // 科目标记
    this.subjectMark = null
    // 遮挡检测跳过区域
    this.blockDetectSkipAreas = []
    // 学生信息区域
    this.studentInfoAreas = []
    // 准考号
    this.admissionNum = {
      type: AdmissionTypeEnum.Omr.id,
      length: null,
      omr: null,
      barcode: null,
      handwriting: null,
    }
    // 客观题区域
    this.objectiveAreas = []
    // 主观题区域
    this.subjectiveAreas = []
    // 选做标记
    this.selectMarkAreas = []
    // 对错打分框
    this.trueFalseScoreAreas = []
    // 打分条
    this.barScoreAreas = []
    // 手写打分框
    this.manualScoreAreas = []
  }

  /**
   * 导入导出
   */
  static import(p, imgElement, template, importSubjectiveQuestions) {
    let page = new Page()
    page.index = p.pageIndex - 1
    page.updateName(template.isDoubleSide)

    // 图片信息
    page.setImage(p.fileUrl, imgElement)

    // 定位信息
    page.locateMode = p.pageParam.correctType
    AnchorPositions.forEach((key, idx) => {
      let anchor = p.pageParam.anchorMarks[idx]
      if (anchor) {
        page.anchor[key] = new Rect(anchor.matchArea)
      }
    })
    if (p.pageParam.lineArea) {
      page.line = new Rect(p.pageParam.lineArea)
    }
    if (p.pageParam.textArea) {
      page.text = new Rect(p.pageParam.textArea)
    }
    if (p.pageParam.textArea2) {
      page.text2 = new Rect(p.pageParam.textArea2)
    }
    if (p.pageParam.titleArea) {
      page.title = new Rect(p.pageParam.titleArea)
    }
    if (p.pageParam.titleArea2) {
      page.title2 = new Rect(p.pageParam.titleArea2)
    }
    if (p.pageParam.pageMark) {
      page.pageMark = new Rect(p.pageParam.pageMark.matchArea)
    }
    if (p.pageParam.pageMark2) {
      page.pageMark = new Rect(p.pageParam.pageMark2.matchArea)
      page.isPageMarkText = true
    }
    if (p.pageParam.subjectArea) {
      page.subjectMark = new Rect(p.pageParam.subjectArea)
    }
    if (p.pageParam.blockDetectSkipAreas) {
      page.blockDetectSkipAreas = p.pageParam.blockDetectSkipAreas.map(a => new Rect(a))
    }
    if (p.pageParam.studentInfoAreas) {
      page.studentInfoAreas = p.pageParam.studentInfoAreas.map(a => new Rect(a))
    }
    // 考生信息
    page.admissionNum.type = p.studentInfo.admissionType
    if (p.studentInfo.admissionBarcodeArea) {
      page.admissionNum.length = p.studentInfo.admissionBarcodeArea.length
      page.admissionNum.barcode = new Rect(p.studentInfo.admissionBarcodeArea.searchArea)
    }
    if (p.studentInfo.admissionOmrArea) {
      page.admissionNum.omr = OmrBlock.import(p.studentInfo.admissionOmrArea)
      page.admissionNum.length = page.admissionNum.omr.questionCount
    }
    if (p.studentInfo.admissionHandwritingArea) {
      let { length, cells } = p.studentInfo.admissionHandwritingArea
      page.admissionNum.length = length
      if (!cells || cells.length == 0) {
        page.admissionNum.handwriting = null
      } else if (cells.length == 1) {
        page.admissionNum.handwriting = new Rect(cells[0])
      } else {
        let x = Math.min(...cells.map(cell => cell.x))
        let y = Math.min(...cells.map(cell => cell.y))
        let right = Math.max(...cells.map(cell => cell.x + cell.width))
        let bottom = Math.max(...cells.map(cell => cell.y + cell.height))
        page.admissionNum.handwriting = new Rect({ x, y, width: right - x, height: bottom - y })
      }
    }
    if (p.studentInfo.missingMarkArea) {
      page.missingMark = new Rect(p.studentInfo.missingMarkArea.matchArea)
    }

    // 客观题
    p.objectiveAreas.forEach(a => {
      let objective = template.questions.objectives.find(x => x.areaId == a.id)
      // 删除找不到客观题信息的
      if (!objective) {
        return
      }
      let omr = OmrBlock.import(a)
      omr.questionCodes = objective.questions.map(q => q.questionCode)
      page.objectiveAreas.push(omr)
    })

    // 主观题
    let blockQuestionCodesMap = new Map()
    template.subjectBlocks.forEach(block => {
      let questionCodes = block.questions
        .sort(sortQuestionFunction)
        .map(q => `${q.questionCode}.${q.branchCode}`)
        .join(',')
      blockQuestionCodesMap.set(questionCodes, block)
    })
    p.subjectiveAreas.forEach(a => {
      let subjective = template.questions.blocks.find(x => x.areaIds.includes(a.id))
      // 删除找不到主观题信息的
      if (!subjective) {
        return
      }
      let questionCodes = subjective.questions
        .sort(sortQuestionFunction)
        .map(q => `${q.questionCode}.${q.branchCode}`)
        .join(',')
      let block = blockQuestionCodesMap.get(questionCodes)
      // 若block为null，则与科目题块定义不一致，此处仍创建区域，待导入完成后检查并删除
      let area = new SubjectiveArea()
      area.id = a.id
      area.pageIndex = page.index
      area.blockId = (block && block.blockId) || subjective.subjectiveId || ''
      area.blockName = (block && block.blockName) || subjective.blockName || ''
      area.rect = new Rect(a.area)
      page.subjectiveAreas.push(area)
    })

    // 选做标记
    p.selectMarkAreas.forEach(a => {
      let subjective = template.questions.blocks.find(x => x.selectMarkId == a.id)
      // 删除找不到主观题信息的
      if (!subjective) {
        return
      }
      let questionCodes = subjective.questions
        .sort(sortQuestionFunction)
        .map(q => `${q.questionCode}.${q.branchCode}`)
        .join(',')
      let block = blockQuestionCodesMap.get(questionCodes)
      let area = new SelectMarkArea()
      area.id = a.id
      area.pageIndex = page.index
      area.blockId = (block && block.blockId) || subjective.subjectiveId || ''
      area.blockName = (block && block.blockName) || subjective.blockName || ''
      area.textRect = a.textArea ? new Rect(a.textArea) : null
      area.matchRect = new Rect(a.matchArea)
      page.selectMarkAreas.push(area)
    })

    // 打分区域
    if (p.trueFalseScoreAreas) {
      page.trueFalseScoreAreas = p.trueFalseScoreAreas.map(a => {
        let subj = importSubjectiveQuestions.find(x => x.scoreAreaIds.includes(a.id))
        let index = subj ? subj.scoreAreaIds.indexOf(a.id) : null
        return {
          id: a.id,
          emptyDefault: a.emptyDefault,
          enableHalfScore: a.enableHalfScore,
          rect: new Rect(a.searchArea),
          questionCode: subj ? subj.questionCode : null,
          branchCode: subj ? subj.branchCode : null,
          blankIndex: index,
        }
      })
    }
    if (p.barScoreAreas) {
      page.barScoreAreas = p.barScoreAreas.map(a => {
        let subj = importSubjectiveQuestions.find(x => x.scoreAreaIds.includes(a.id))
        return {
          id: a.id,
          cells: a.cells.map(c => ({ value: c.value, rect: new Rect(c.searchArea) })),
          questionCode: subj ? subj.questionCode : null,
          branchCode: subj ? subj.branchCode : null,
        }
      })
    }
    if (p.manualScoreAreas) {
      page.manualScoreAreas = p.manualScoreAreas.map(a => {
        let subj = importSubjectiveQuestions.find(x => x.scoreAreaIds.includes(a.id))
        return {
          id: a.id,
          digitCount: a.digitCount,
          rect: new Rect(a.searchArea),
          digits: a.digits.map(d => new Rect(d)),
          questionCode: subj ? subj.questionCode : null,
          branchCode: subj ? subj.branchCode : null,
        }
      })
    }

    return page
  }
  export() {
    let page = new TemplatePage()
    page.pageIndex = this.index + 1
    page.fileUrl = this.image.src

    // 定位信息
    let pageParam = new TemplatePageParam()
    page.pageParam = pageParam
    pageParam.correctType = this.locateMode
    if (this.locateMode == LocateModeEnum.Anchor.id) {
      pageParam.anchorMarks = AnchorPositions.map(key => {
        let rect = this.anchor[key]
        if (rect) {
          let markAreaId = `Anchor${key[0].toUpperCase() + key.substring(1)}-${page.pageIndex}`
          let templateRect = new TemplateRect(rect)
          return TemplateMarkArea.create(markAreaId, templateRect)
        } else {
          return null
        }
      })
    } else if (this.locateMode == LocateModeEnum.Line.id && this.line) {
      pageParam.lineArea = new TemplateRect(this.line)
    } else if (this.locateMode == LocateModeEnum.Text.id && (this.text || this.text2)) {
      if (this.text) {
        pageParam.textArea = new TemplateRect(this.text)
      }
      if (this.text2) {
        pageParam.textArea2 = new TemplateRect(this.text2)
      }
      this.title = null
      this.title2 = null
    }
    if (this.title) {
      pageParam.titleArea = new TemplateRect(this.title)
    }
    if (this.title2) {
      pageParam.titleArea2 = new TemplateRect(this.title2)
    }
    if (this.pageMark) {
      if (this.isPageMarkText) {
        let markAreaId = `PageMark2-${page.pageIndex}`
        let templateRect = new TemplateRect(this.pageMark)
        pageParam.pageMark2 = TemplateMarkArea.create(markAreaId, templateRect)
      } else {
        let markAreaId = `PageMark-${page.pageIndex}`
        let templateRect = new TemplateRect(this.pageMark)
        pageParam.pageMark = TemplateMarkArea.create(markAreaId, templateRect)
      }
    }
    if (this.subjectMark) {
      pageParam.subjectArea = new TemplateRect(this.subjectMark)
    }
    pageParam.blockDetectSkipAreas = this.blockDetectSkipAreas.map(a => new TemplateRect(a))
    pageParam.studentInfoAreas = this.studentInfoAreas.map(a => new TemplateRect(a))

    // 考生信息
    let studentInfo = new TemplateStudentInfo()
    page.studentInfo = studentInfo
    if (this.admissionNum.barcode) {
      studentInfo.admissionType = AdmissionTypeEnum.Barcode.id
      studentInfo.admissionBarcodeArea = new TemplateBarcodeArea()
      studentInfo.admissionBarcodeArea.length = this.admissionNum.length
      studentInfo.admissionBarcodeArea.searchArea = new TemplateRect(this.admissionNum.barcode)
    } else if (this.admissionNum.omr) {
      studentInfo.admissionType = AdmissionTypeEnum.Omr.id
      studentInfo.admissionOmrArea = TemplateOmrArea.import(this.admissionNum.omr)
    } else if (this.admissionNum.handwriting) {
      studentInfo.admissionType = AdmissionTypeEnum.Handwriting.id
      studentInfo.admissionHandwritingArea = new TemplateHandwritingArea()
      studentInfo.admissionHandwritingArea.length = this.admissionNum.length
      studentInfo.admissionHandwritingArea.cells = [new TemplateRect(this.admissionNum.handwriting)]
    } else {
      studentInfo.admissionType = AdmissionTypeEnum.None.id
    }
    if (this.missingMark) {
      let markAreaId = `MissingMark-${page.pageIndex}`
      let templateRect = new TemplateRect(this.missingMark)
      studentInfo.missingMarkArea = TemplateMarkArea.create(markAreaId, templateRect)
    }

    // 客观题
    page.objectiveAreas = this.objectiveAreas.map(omr => TemplateOmrArea.import(omr))

    // 主观题
    page.subjectiveAreas = this.subjectiveAreas.map(area => {
      let subjectiveArea = new TemplateSubjectiveArea()
      subjectiveArea.id = area.id
      subjectiveArea.area = new TemplateRect(area.rect)
      return subjectiveArea
    })

    // 选做标记
    page.selectMarkAreas = this.selectMarkAreas.map(area => {
      let selectMarkArea = new TemplateSelectMarkArea()
      selectMarkArea.id = area.id
      selectMarkArea.textArea = area.textRect ? new TemplateRect(area.textRect) : null
      selectMarkArea.matchArea = new TemplateRect(area.matchRect)
      return selectMarkArea
    })

    // 打分区域
    page.trueFalseScoreAreas = this.trueFalseScoreAreas.map(area => {
      let trueFalseScoreArea = new TemplateTrueFalseScoreArea()
      trueFalseScoreArea.id = area.id
      trueFalseScoreArea.emptyDefault = area.emptyDefault
      trueFalseScoreArea.enableHalfScore = area.enableHalfScore
      trueFalseScoreArea.searchArea = new TemplateRect(area.rect)
      return trueFalseScoreArea
    })
    page.barScoreAreas = this.barScoreAreas.map(area => {
      let barScoreArea = new TemplateBarScoreArea()
      barScoreArea.id = area.id
      barScoreArea.cells = area.cells.map(cell => {
        let barScoreCell = new TemplateBarScoreCell()
        barScoreCell.value = cell.value
        barScoreCell.searchArea = new TemplateRect(cell.rect)
        return barScoreCell
      })
      return barScoreArea
    })
    page.manualScoreAreas = this.manualScoreAreas.map(area => {
      let manualScoreArea = new TemplateManualScoreArea()
      manualScoreArea.id = area.id
      manualScoreArea.digitCount = area.digitCount
      manualScoreArea.searchArea = new TemplateRect(area.rect)
      manualScoreArea.digits = area.digits.map(d => new TemplateRect(d))
      return manualScoreArea
    })

    return page
  }
  updateName(isDoubleSide) {
    this.name = getPageName(this.index + 1, isDoubleSide)
  }
  async setImage(url, img) {
    let canvas = document.createElement('canvas')
    canvas.width = img.naturalWidth
    canvas.height = img.naturalHeight
    let ctx = canvas.getContext('2d')
    ctx.drawImage(img, 0, 0)
    this.image = {
      src: url,
      width: img.naturalWidth,
      height: img.naturalHeight,
      canvas,
    }
  }

  /**
   * 清空
   */
  clear() {
    this.locateMode = LocateModeEnum.Anchor.id
    this.anchor = {
      topLeft: null,
      topRight: null,
      bottomRight: null,
      bottomLeft: null,
    }
    this.line = null
    this.text = null
    this.text2 = null
    this.title = null
    this.title2 = null
    this.pageMark = null
    this.isPageMarkText = false
    this.missingMark = null
    this.subjectMark = null
    this.admissionNum = {
      type: AdmissionTypeEnum.Omr.id,
      length: null,
      omr: null,
      barcode: null,
      handwriting: null,
    }
    this.objectiveAreas = []
    this.subjectiveAreas = []
    this.selectMarkAreas = []
    this.trueFalseScoreAreas = []
    this.barScoreAreas = []
    this.manualScoreAreas = []
  }

  /**
   * 定位
   */
  changeLocateMode(mode) {
    this.locateMode = mode
    if (this.locateMode != LocateModeEnum.Anchor.id) {
      AnchorPositions.forEach(key => (this.anchor[key] = null))
    }
    if (this.locateMode != LocateModeEnum.Line.id) {
      this.line = null
    }
    if (this.locateMode != LocateModeEnum.Text.id) {
      this.text = null
      this.text2 = null
    }
  }
  addAnchor(position, rect) {
    if (!AnchorPositions.includes(position)) {
      return
    }
    if (this.anchor[position]) {
      return
    }
    this.anchor[position] = new Rect(rect)
  }
  deleteAnchor(position) {
    if (!AnchorPositions.includes(position)) {
      return
    }
    if (this.anchor[position]) {
      this.anchor[position] = null
    }
  }
  addLine(rect) {
    if (this.line) {
      return
    }
    this.line = new Rect(rect)
  }
  deleteLine() {
    this.line = null
  }
  addText(rect) {
    if (this.text && this.text2) {
      return
    }
    if (!this.text) {
      this.text = new Rect(rect)
    } else {
      this.text2 = new Rect(rect)
    }
  }
  deleteText(rect) {
    let id = (rect && rect.id) || ''
    if (!id) {
      this.text = null
      this.text2 = null
    } else if (this.text && this.text.id == id) {
      if (this.text2) {
        this.text = this.text2
        this.text2 = null
      } else {
        this.text = null
      }
    } else if (this.text2 && this.text2.id == id) {
      this.text2 = null
    }
  }

  /**
   * 标题
   */
  addTitle(rect) {
    if (this.title && this.title2) {
      return
    }
    if (!this.title) {
      this.title = new Rect(rect)
    } else {
      this.title2 = new Rect(rect)
    }
  }
  deleteTitle(rect) {
    let id = (rect && rect.id) || ''
    if (!id) {
      this.title = null
      this.title2 = null
    } else if (this.title && this.title.id == id) {
      if (this.title2) {
        this.title = this.title2
        this.title2 = null
      } else {
        this.title = null
      }
    } else if (this.title2 && this.title2.id == id) {
      this.title2 = null
    }
  }

  /**
   * 准考号
   */
  addAdmissionNumOmr(block) {
    if (!(this.admissionNum.type == AdmissionTypeEnum.Omr.id && block instanceof OmrBlock && !this.admissionNum.omr)) {
      return
    }
    this.admissionNum.omr = block
    this.admissionNum.length = block.questionCount
  }
  replaceAdmissionNumOmr(block) {
    if (!(this.admissionNum.type == AdmissionTypeEnum.Omr.id && block instanceof OmrBlock && this.admissionNum.omr)) {
      return
    }
    this.admissionNum.omr = block
    this.admissionNum.length = block.questionCount
  }
  deleteAdmissionNumOmr() {
    if (this.admissionNum.type == AdmissionTypeEnum.Omr.id) {
      this.admissionNum.omr = null
    }
  }
  addAdmissionNumBarcode(rect) {
    if (this.admissionNum.type == AdmissionTypeEnum.Barcode.id) {
      this.admissionNum.barcode = new Rect(rect)
    }
  }
  deleteAdmissionNumBarcode() {
    if (this.admissionNum.type == AdmissionTypeEnum.Barcode.id) {
      this.admissionNum.barcode = null
    }
  }
  addAdmissionNumHandwriting(rect) {
    if (this.admissionNum.type == AdmissionTypeEnum.Handwriting.id) {
      this.admissionNum.handwriting = new Rect(rect)
    }
  }
  deleteAdmissionNumHandwriting() {
    if (this.admissionNum.type == AdmissionTypeEnum.Handwriting.id) {
      this.admissionNum.handwriting = null
    }
  }
  changeAdmissionNumLength(len) {
    if ([AdmissionTypeEnum.Barcode.id, AdmissionTypeEnum.Handwriting.id].includes(this.admissionNum.type)) {
      this.admissionNum.length = len
    }
  }
  changeAdmissionNumType(type) {
    if (type == AdmissionTypeEnum.Omr.id) {
      this.admissionNum.barcode = null
      this.admissionNum.handwriting = null
    } else if (type == AdmissionTypeEnum.Barcode.id) {
      this.admissionNum.omr = null
      this.admissionNum.handwriting = null
    } else if (type == AdmissionTypeEnum.Handwriting.id) {
      this.admissionNum.omr = null
      this.admissionNum.barcode = null
    }
    this.admissionNum.type = type
  }

  /**
   * 客观题
   */
  addObjectiveArea(block) {
    if (!(block instanceof OmrBlock)) {
      return
    }
    this.objectiveAreas.push(block)
  }
  replaceObjectiveArea(block) {
    if (!(block instanceof OmrBlock)) {
      return
    }
    let index = this.objectiveAreas.findIndex(x => x.id === block.id)
    if (index >= 0) {
      this.objectiveAreas.splice(index, 1, block)
    }
  }
  deleteObjectiveArea(block) {
    let index = this.objectiveAreas.findIndex(x => x.id === block.id)
    if (index >= 0) {
      this.objectiveAreas.splice(index, 1)
    }
  }

  /**
   * 主观题
   */
  addSubjectiveArea(area) {
    if (area instanceof SubjectiveArea) {
      this.subjectiveAreas.push(area)
    }
  }
  changeSubjectiveAreaRect(area) {
    let old = this.subjectiveAreas.find(x => x.id == area.id)
    if (old) {
      old.rect.x = area.rect.x
      old.rect.y = area.rect.y
      old.rect.width = area.rect.width
      old.rect.height = area.rect.height
    }
  }
  deleteSubjectiveArea(area) {
    let index = this.subjectiveAreas.findIndex(x => x.id === area.id)
    if (index >= 0) {
      this.subjectiveAreas.splice(index, 1)
    }
  }

  /**
   * 页码标记
   */
  addPageMark(rect) {
    this.pageMark = new Rect(rect)
  }
  deletePageMark() {
    this.pageMark = null
  }
  changeIsPageMarkText(value) {
    this.isPageMarkText = Boolean(value)
  }

  /**
   * 缺考标记
   */
  addMissingMark(rect) {
    if (this.index == 0) {
      this.missingMark = new Rect(rect)
    }
  }
  deleteMissingMark() {
    this.missingMark = null
  }

  /**
   * 选做标记
   */
  addSelectMarkArea(area) {
    if (area instanceof SelectMarkArea) {
      this.selectMarkAreas.push(area)
    }
  }
  deleteSelectMarkArea(area) {
    let index = this.selectMarkAreas.findIndex(x => x.id == area.id)
    if (index >= 0) {
      this.selectMarkAreas.splice(index, 1)
    }
  }

  /**
   * 科目标记
   */
  addSubjectMark(rect) {
    this.subjectMark = new Rect(rect)
  }
  deleteSubjectMark() {
    this.subjectMark = null
  }

  /**
   * 遮挡检测
   */
  addBlockDetectSkipArea(rect) {
    this.blockDetectSkipAreas.push(new Rect(rect))
  }
  deleteBlockDetectSkipArea(rect) {
    let index = this.blockDetectSkipAreas.findIndex(x => x.id == rect.id)
    if (index >= 0) {
      this.blockDetectSkipAreas.splice(index, 1)
    }
  }

  /**
   * 学生信息区域
   */
  addStudentInfoArea(rect) {
    this.studentInfoAreas.push(new Rect(rect))
  }
  deleteStudentInfoArea(rect) {
    let index = this.studentInfoAreas.findIndex(x => x.id == rect.id)
    if (index >= 0) {
      this.studentInfoAreas.splice(index, 1)
    }
  }

  /**
   * 对错打分框
   */
  changeTrueFalseScoreAreaRect(area) {
    let old = this.trueFalseScoreAreas.find(x => x.id == area.id)
    if (old) {
      if (area.rect) {
        old.rect = new Rect(area.rect)
      } else {
        old.rect = null
      }
    }
  }
}

/**
 * 页面名称
 * @param {Number} pageNo 页面序号，从1开始
 * @param {Boolean} doubleSide 是否双面
 * @returns String
 */
export function getPageName(pageNo, doubleSide) {
  if (!doubleSide) {
    return `第${pageNo}张`
  }
  return `第${Math.ceil(pageNo / 2)}张${pageNo % 2 == 1 ? '正面' : '背面'}`
}
