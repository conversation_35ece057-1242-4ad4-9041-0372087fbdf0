<template>
  <div class="page-container-school">
    <div class="content">
      <div class="left-panel">
        <div class="folder-tree-directory">
          <div class="block-title">学校目录</div>
          <div class="block-content">
            <div class="folder-tree">
              <Tree :data="schoolTree" empty-text="暂无设置所属机构" @on-select-change="handleTreeSelect" />
            </div>
          </div>
        </div>
      </div>
      <div class="right-panel">
        <div class="title-bar">
          <div class="school-name">
            <span class="title">{{ currentSelectedSchool.title }}</span>
            <span v-if="currentSelectedSchool.schoolType === 1" class="sub-title">{{
              `（直接下属机构 ${childOrgsCount} 所，下属学校 ${descendantSchoolsCount} 所）`
            }}</span>
          </div>
        </div>
        <div class="tab-box">
          <Tabs v-model="tabType">
            <TabPane label="学生管理" name="student" />
            <TabPane label="教师管理" name="teacher" />
          </Tabs>
          <component :is="tabComponent" :current-selected-school="currentSelectedSchool" :is-sys-admin="isSysAdmin" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import BlockStudent from './components/block_student.vue'
  import BlockTeacher from './components/block_teacher.vue'
  import { apiGetChildOrgAndSchool } from '@/api/user/school'
  import { scrollToTop } from '@/utils/scroll'
  import { deepCopy } from '@/utils/object'

  export default {
    data() {
      return {
        schoolTree: [],
        currentSelectedSchool: {},
        tabType: 'student',
      }
    },
    computed: {
      tabComponent() {
        if (this.tabType === 'student') {
          return BlockStudent
        } else if (this.tabType === 'teacher') {
          return BlockTeacher
        }
      },
      schoolId() {
        return this.$store.state.user.schoolId || ''
      },
      isSysAdmin() {
        return this.$store.state.user.isSystem
      },
      childOrgsCount() {
        const { currentSelectedSchool } = this
        if (!currentSelectedSchool.children || !currentSelectedSchool.children.length) {
          return 0
        }
        const sum = currentSelectedSchool.children.reduce((prev, cur) => {
          if (cur.schoolType === 1) {
            return prev + 1
          }
          return prev
        }, 0)
        return sum
      },
      descendantSchoolsCount() {
        const { currentSelectedSchool } = this
        if (!currentSelectedSchool.children || !currentSelectedSchool.children.length) {
          return 0
        }
        const sum = currentSelectedSchool.children.reduce((prev, cur) => {
          if (cur.schoolType === 0) {
            return prev + 1
          }
          if (!cur.children || !cur.children.length) {
            return prev
          }
          return prev + this.getChildSchoolCount(cur)
        }, 0)
        return sum
      },
    },
    created() {
      this.getSchools()
    },
    methods: {
      getChildSchoolCount(item) {
        const sum = item.children.reduce((prev, cur) => {
          if (cur.children.length) {
            return this.getChildSchoolCount(cur) + prev
          }
          if (cur.schoolType === 0) {
            return prev + 1
          }
          return prev
        }, 0)
        return sum
      },
      buildTree(data = []) {
        const filterData = deepCopy(data).filter(item => !item.centerSchoolId)

        return filterData.map(item => {
          const children = item.schools ? this.buildTree(item.schools) : []
          const teachingPoints = data
            .filter(s => s.centerSchoolId === item.schoolId)
            .map(s => ({
              title: s.schoolName,
              value: s.schoolId,
              schoolType: s.schoolType,
              centerSchoolId: s.centerSchoolId,
              children: s.schools ? this.buildTree(s.schools) : [],
              render: (h, { data }) => {
                if (data.centerSchoolId) {
                  return h('span', {}, [
                    h(
                      'span',
                      {
                        class: 'tree-node-teaching-point',
                      },
                      '教学点'
                    ),
                    h('span', data.title),
                  ])
                }
                return h('span', {}, h('span', data.title))
              },
            }))

          return {
            title: item.schoolName,
            value: item.schoolId,
            schoolType: item.schoolType,
            centerSchoolId: item.centerSchoolId,
            children: [...teachingPoints, ...children],
          }
        })
      },
      getSchools() {
        this.$TransparentSpin.show()
        apiGetChildOrgAndSchool({
          eduSchoolId: this.schoolId,
        })
          .then(res => {
            const child = this.buildTree(res)
            this.schoolTree = [
              {
                title: this.$store.state.user.schoolName,
                value: this.schoolId,
                children: child,
                schoolType: this.$store.state.user.schoolType,
                expand: true,
                selected: true,
              },
            ]
            this.currentSelectedSchool = this.schoolTree[0]
          })
          .catch(() => {
            this.schoolTree = []
            this.currentSelectedSchool = {}
          })
          .finally(() => {
            this.$TransparentSpin.hide()
          })
      },
      async handleTreeSelect(_, item) {
        this.currentSelectedSchool = item

        scrollToTop()
      },
    },
  }
</script>

<style scoped lang="scss">
  .page-container-school {
    position: relative;
    padding-top: 10px;

    :deep(.tree-node-teaching-point) {
      display: inline-block;
      height: 20px;
      margin-right: 7px;
      padding: 0 5px;
      border-radius: 8px;
      color: #fff;
      font-size: 10px;
      line-height: 22px;
      background-color: $color-primary;
    }

    .content {
      @include flex(row, flex-start, flex-start);

      :deep(.left-panel) {
        position: sticky;
        top: 55px;
        flex-grow: 0;
        flex-shrink: 0;
        width: 288px;
        min-height: 400px;
        max-height: calc(100vh - 175px);
        margin-right: 15px;
        padding: 15px 20px;
        overflow-y: auto;
        background-color: #fff;

        .ivu-tree-title {
          padding: 0 8px;
        }

        .block-title {
          margin-bottom: 15px;
          font-size: 18px;
        }
      }

      .right-panel {
        flex: 1;
        padding: 15px 20px;
        background-color: #fff;

        .title-bar {
          @include flex(row, space-between, center);
          margin-bottom: 16px;

          .school-name {
            @include flex(row, flex-start, baseline);

            .title {
              font-weight: bold;
              font-size: $font-size-large;
            }
          }
        }
      }
    }
  }
</style>
