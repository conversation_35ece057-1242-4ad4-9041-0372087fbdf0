/**
 * 手阅测试报告状态
 */

import Enum from '@/enum/enum'

export default new Enum({
  UnGenerated: {
    id: 'notGenerated',
    name: '未生成',
    operation: 'GenerateTestReport',
    operationLabel: '生成报告',
  },
  Generated: {
    id: 'success',
    name: '已生成',
    operation: 'CheckTestReport',
    operationLabel: '查看报告',
  },
  Generating: {
    id: 'generating',
    name: '生成中',
    operation: 'WaitingForGenerating',
    operationLabel: '报告生成中',
  },
  GenerateFail: {
    id: 'fail',
    name: '生成失败',
    operation: 'RegenerateTestReport',
    operationLabel: '重新生成报告',
  },
})
