<template>
  <div ref="block" class="block" :style="style">
    <slot></slot>
  </div>
</template>

<script>
  export default {
    props: {
      top: {
        type: Number,
        default: 0,
      },
      scroll: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        sticky: false,
      }
    },
    computed: {
      style() {
        return this.sticky
          ? {
              position: 'fixed',
              top: `${this.top}px`,
            }
          : {}
      },
    },
    mounted() {
      window.addEventListener('scroll', this.rePosition)
      this.rePosition()
    },
    beforeUnmount() {
      window.removeEventListener('scroll', this.rePosition)
    },
    methods: {
      rePosition() {
        this.sticky = window.scrollY > this.scroll
      },
    },
  }
</script>
