import basicData from './basic_data'
import currentStageSubject from './current_stage_subject'
import basket from './basket'

export default {
  namespaced: true,
  state: {
    ...basicData.state,
    ...currentStageSubject.state,
    ...basket.state,
  },
  getters: {
    ...basicData.getters,
    ...currentStageSubject.getters,
    ...basket.getters,
  },
  mutations: {
    ...basicData.mutations,
    ...currentStageSubject.mutations,
    ...basket.mutations,
  },
  actions: {
    ...basicData.actions,
    ...currentStageSubject.actions,
    ...basket.actions,
  },
}
