export default {
  name: 'remarks',
  path: 'remarks',
  meta: {
    title: '教师寄语',
    name: '教师寄语',
  },
  redirect: {
    name: 'remarks_home',
  },
  component: () => import('@/views/remarks/index.vue'),
  children: [
    {
      name: 'remarks_home',
      path: 'home',
      meta: {
        menu: '教师寄语',
      },
      component: () => import('@/views/remarks/home.vue'),
    },
    {
      name: 'remarks_details',
      path: 'details/:id',
      meta: {
        menu: '寄语详情',
      },
      component: () => import('@/views/remarks/remarks_details.vue'),
    },
    {
      name: 'remarks_template',
      path: 'template',
      meta: {
        menu: '寄语模板',
      },
      component: () => import('@/views/remarks/remarks_template.vue'),
    },
  ],
}
