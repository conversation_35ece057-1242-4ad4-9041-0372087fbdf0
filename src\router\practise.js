import SchoolTypeEnum from '@/enum/school/school_type'

export default {
  name: 'class-practise',
  path: 'practise',
  meta: {
    title: 'AI测练',
    menu: 'AI测练',
    name: 'AI测练',
    roles: user => {
      return user.schoolType == SchoolTypeEnum.School.id
    },
  },
  component: () => import('@/views/class_practise/index.vue'),
  children: [
    {
      name: 'class-practise-home',
      path: 'home',
      component: () => import('@/views/class_practise/list/index.vue'),
    },
    {
      name: 'class-practise-create',
      path: 'create',
      component: () => import('@/views/class_practise/create/index.vue'),
    },
    {
      name: 'class-practise-setting',
      path: 'setting/:examId?/:examSubjectId?',
      component: () => import('@/views/class_practise/setting/index.vue'),
    },
    {
      name: 'class-practise-report',
      path: 'report/:examId?',
      meta: {
        showHeader: false,
      },
      // 与数智教辅共用一套报表
      component: () => import('@/views/supplementary_exercise/report/index.vue'),
    },
  ],
}
