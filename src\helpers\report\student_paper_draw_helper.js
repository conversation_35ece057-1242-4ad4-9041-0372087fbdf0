import tickSrc from '@/assets/images/common/tick.svg'
import crossSrc from '@/assets/images/common/cross.svg'
import { roundScore } from '@/utils/math'
import { groupArray } from '@/utils/array'
import { loadImage } from '@/utils/promise'
export default class StudentPaperDrawHelper {
  constructor(templateStr) {
    this.template = null
    this.objectives = []
    this.subjectives = []
    this.objectiveLeftTopPoint = {
      pageIndex: -1,
      x: Number.MAX_SAFE_INTEGER,
      y: Number.MAX_SAFE_INTEGER,
    }

    try {
      this.template = JSON.parse(templateStr)

      if (this.template.A) {
        this.extractTemplateOld()
      } else {
        this.extractTemplateNew()
      }
    } catch (err) {
      throw '扫描模板错误'
    }
  }

  // 从模板中提取题目位置和定义信息（旧扫描模板）
  extractTemplateOld() {
    let pages = this.template.A.pages || []
    // 1. 提取客观题位置
    pages.forEach((page, pageIndex) => {
      this.objectives[pageIndex] = []
      ;(page.choises || []).forEach(choice => {
        let rects = choice.opts.map((rect, index) => ({
          index,
          rect,
        }))
        groupArray(rects, rect => Math.floor(rect.index / choice.mode.span0)).forEach(g => {
          this.objectives[pageIndex].push({
            pageIndex,
            questionCode: choice.qno1 + g.key,
            rects: g.group.map(x => x.rect),
          })
        })
      })
    })

    // 2. 找客观题左上角
    this.objectiveLeftTopPoint.pageIndex = this.objectives.findIndex(x => x.length > 0)
    if (this.objectiveLeftTopPoint.pageIndex >= 0) {
      let pageObjectives = this.objectives[this.objectiveLeftTopPoint.pageIndex]
      pageObjectives.forEach(obj => {
        let rect = obj.rects[0]
        if (rect.x <= this.objectiveLeftTopPoint.x && rect.y <= this.objectiveLeftTopPoint.y) {
          this.objectiveLeftTopPoint.x = rect.x
          this.objectiveLeftTopPoint.y = rect.y
        }
      })
    }

    // 3. 提取主观题位置
    pages.forEach((page, pageIndex) => {
      this.subjectives[pageIndex] = []
      ;(page.fuzzys || []).forEach(subj => {
        this.subjectives[pageIndex].push({
          blockId: subj.outKey,
          part: 0,
          partOffsetY: 0,
          rect: subj.rect.r,
        })
      })
    })

    // 4. 找主观题在合并题块中的位置
    let blockPartMap = new Map()
    this.subjectives.forEach(page => {
      page.forEach(subj => {
        let thePart = blockPartMap.get(subj.blockId)
        if (thePart) {
          subj.part = thePart.part
          subj.partOffsetY = thePart.partOffsetY
        } else {
          subj.part = 0
          subj.partOffsetY = 0
        }
        blockPartMap.set(subj.blockId, {
          part: subj.part + 1,
          partOffsetY: subj.partOffsetY + subj.rect.height,
        })
      })
    })
  }

  // 从模板中提取题目位置和定义信息（新扫描模板）
  extractTemplateNew() {
    let objectiveAreaInfos = this.template.questions.objectives
    let subjectiveAreaInfos = this.template.questions.subjectives
    let pages = this.template.pages || this.template.tpl.tplFile.pages

    // 1. 提取客观题位置
    pages.forEach((page, pageIndex) => {
      this.objectives[pageIndex] = []
      ;(page.objectiveAreas || []).forEach(area => {
        let info = objectiveAreaInfos.find(x => x.areaId == area.id)
        if (!info) {
          return
        }
        for (let qIdx = 0; qIdx < Math.floor(area.options.length / area.optionCount); qIdx++) {
          let question = info.questions[qIdx]
          if (question) {
            this.objectives[pageIndex].push({
              pageIndex,
              questionCode: question.questionCode,
              rects: area.options.slice(qIdx * area.optionCount, (qIdx + 1) * area.optionCount),
            })
          }
        }
      })
    })

    // 2. 找客观题左上角
    this.objectiveLeftTopPoint.pageIndex = this.objectives.findIndex(x => x.length > 0)
    if (this.objectiveLeftTopPoint.pageIndex >= 0) {
      let pageObjectives = this.objectives[this.objectiveLeftTopPoint.pageIndex]
      pageObjectives.forEach(obj => {
        let rect = obj.rects[0]
        if (rect.x <= this.objectiveLeftTopPoint.x) {
          this.objectiveLeftTopPoint.x = rect.x
        }
        if (rect.y <= this.objectiveLeftTopPoint.y) {
          this.objectiveLeftTopPoint.y = rect.y
        }
      })
    }

    // 3. 提取主观题位置
    pages.forEach((page, pageIndex) => {
      this.subjectives[pageIndex] = []
      ;(page.subjectiveAreas || []).forEach(area => {
        let info = subjectiveAreaInfos.find(x => x.areaIds.some(id => id == area.id))
        if (!info) {
          return
        }
        let part = info.areaIds.findIndex(id => id == area.id)
        this.subjectives[pageIndex].push({
          blockId: info.subjectiveId,
          part,
          partOffsetY: 0,
          rect: area.area,
        })
      })
    })

    // 4. 找主观题在合并题块中的位置
    let blockPartMap = new Map()
    this.subjectives.forEach(page => {
      page.forEach(subj => {
        let thePart = blockPartMap.get(subj.blockId)
        if (thePart) {
          subj.partOffsetY = thePart.partOffsetY
        } else {
          subj.partOffsetY = 0
        }
        blockPartMap.set(subj.blockId, {
          partOffsetY: subj.partOffsetY + subj.rect.height,
        })
      })
    })
  }

  async drawPaperCanvas(canvas, imgUrl, pageIndex, stu, options) {
    let imgElement = await loadImage(imgUrl)
    canvas.width = imgElement.naturalWidth
    canvas.height = imgElement.naturalHeight
    let ctx = canvas.getContext('2d')
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    await this.drawPage(ctx, imgElement, pageIndex, stu, options)
  }

  async drawPage(ctx, imgElement, pageIndex, stu, options) {
    ctx.fillStyle = 'red'
    ctx.strokeStyle = 'red'
    ctx.textBaseline = 'top'

    this.drawPaperImage(ctx, imgElement)
    if (pageIndex == 0) {
      this.drawTotalScore(ctx, stu.totalScoreMap.paperScore)
    }
    if (pageIndex == this.objectiveLeftTopPoint.pageIndex) {
      this.drawTotalObjectiveScore(
        ctx,
        stu.totalScoreMap.objectScore,
        stu.stuObjectiveVos.reduce((acc, cur) => acc + cur.fullScore, 0)
      )
    }
    if (this.objectives[pageIndex].length > 0) {
      this.drawObjectives(ctx, this.objectives[pageIndex], stu.stuObjectiveVos)
    }
    if (this.subjectives[pageIndex].length > 0) {
      await this.drawSubjectives(ctx, this.subjectives[pageIndex], stu.stuSubjectiveVos, options)
    }
  }

  // 画原卷图作为背景
  drawPaperImage(ctx, imgElement, offsetX = 0, offsetY = 0) {
    ctx.drawImage(imgElement, offsetX, offsetY)
  }

  // 全卷总分
  drawTotalScore(ctx, totalScore) {
    ctx.save()
    ctx.font = '120px san-serif'
    ctx.fillText(roundScore(totalScore), 50, 50)
    ctx.restore()
  }

  // 客观题总分
  drawTotalObjectiveScore(ctx, totalObjectiveScore, fullObjectiveScore) {
    let text = `客观题：${roundScore(totalObjectiveScore)}分（满分：${fullObjectiveScore}分）【绿框选项为正确答案】`
    this.drawTextWithBackground(ctx, text, 36, 50, Math.max(0, this.objectiveLeftTopPoint.y - 56))
  }

  // 客观题
  drawObjectives(ctx, pageObjectives, stuObjectives) {
    ctx.lineWidth = 4
    pageObjectives.forEach(obj => {
      let stuObjective = stuObjectives.find(x => x.quesCode == obj.questionCode)
      if (!stuObjective) {
        return
      }

      // 找客观题满分答案
      let answerScoreList = this.splitNameScoreList(stuObjective.standardAnswer)
      let fullScoreAnswer = answerScoreList.find(x => x.score == stuObjective.fullScore)
      // 判断题答案转为A，B
      if (stuObjective.questionType == 13) {
        if (fullScoreAnswer.name == 'T') {
          fullScoreAnswer.name = 'A'
        } else if (fullScoreAnswer.name == 'F') {
          fullScoreAnswer.name = 'B'
        }
      }

      // 绘制满分选项框
      this.getAnswerCharIndex(fullScoreAnswer.name).forEach(idx => {
        let rect = obj.rects[idx]
        if (!rect) {
          return
        }
        ctx.strokeStyle = '#00c800'
        ctx.strokeRect(rect.x, rect.y, rect.width, rect.height)
      })
    })
  }

  // 主观题
  async drawSubjectives(
    ctx,
    pageSubjectives,
    stuSubjectives,
    options = {
      showMarker: false,
      showComment: true,
    }
  ) {
    for (let subj of pageSubjectives) {
      let stuSubjective = stuSubjectives.find(x => x.blockId == subj.blockId)
      if (!stuSubjective) {
        continue
      }
      await this.drawSubjective(ctx, subj, stuSubjective, options)
    }
  }

  async drawSubjective(ctx, subj, stuSubjective, options) {
    // 题块得分
    if (subj.part == 0) {
      let text = `${stuSubjective.blockName}：${roundScore(stuSubjective.score)}分（满分：${roundScore(
        stuSubjective.fullScore
      )}分）`
      this.drawTextWithBackground(ctx, text, 36, subj.rect.x, Math.max(0, subj.rect.y - 36))
    }

    // 评卷人信息
    if (subj.part == 0 && options.showMarker) {
      let offsetY = 0
      stuSubjective.markerList.forEach(marker => {
        if (!marker.markerName || marker.score == null) {
          return
        }
        let text = ''
        if (stuSubjective.markerList.length == 1 && marker.markingType == '一评') {
          text = `评卷人：${marker.markerName}`
        } else {
          text = `${marker.markingType}：${marker.markerName},${roundScore(marker.score)}分`
        }

        this.drawTextWithBackground(
          ctx,
          text,
          36,
          subj.rect.x + subj.rect.width,
          Math.max(0, subj.rect.y - 36 + offsetY),
          true
        )
        offsetY += 50
      })
    }

    // 批注
    if (options.showComment) {
      let comments = []
      ;(stuSubjective.commentJsons || []).forEach(s => {
        try {
          let teacherComments = JSON.parse(s)
          teacherComments.forEach(c => comments.push(c))
        } catch (err) {
          // do nothing
        }
      })

      if (comments.length > 0) {
        // 限定区域
        ctx.save()
        ctx.beginPath()
        ctx.rect(subj.rect.x, subj.rect.y, subj.rect.width, subj.rect.height)
        ctx.clip()
        await this.drawBlockComments(ctx, comments, subj.rect.x, subj.rect.y - subj.partOffsetY)
        ctx.restore()
      }
    }
  }

  async drawBlockComments(ctx, comments, offsetX, offsetY) {
    ctx.save()
    for (let c of comments) {
      if (c.type == 'tick' || c.type == 'cross') {
        try {
          let img = await loadImage(c.type == 'tick' ? tickSrc : crossSrc)
          ctx.drawImage(img, c.x + offsetX, c.y + offsetY, c.width, c.height)
        } catch (err) {
          // do nothing
        }
      } else if (c.type == 'rect') {
        ctx.lineWidth = c.strokeWidth
        ctx.strokeRect(c.x + offsetX, c.y + offsetY, c.width, c.height)
      } else if (c.type == 'path') {
        ctx.lineWidth = c.strokeWidth
        ctx.beginPath()
        ctx.moveTo(c.points[0][0] + offsetX, c.points[0][1] + offsetY)
        for (let i = 1; i < c.points.length; i++) {
          let point = c.points[i]
          ctx.lineTo(point[0] + offsetX, point[1] + offsetY)
        }
        ctx.stroke()
      } else if (c.type == 'text') {
        ctx.font = `${c.fontSize}px sans-serif`
        ctx.textBaseline = 'top'
        ctx.fillText(c.content, c.x + offsetX, c.y + offsetY)
      }
    }
    ctx.restore()
  }

  drawTextWithBackground(ctx, text, fontSize, x, y, isAlignRight = false) {
    ctx.save()

    ctx.font = `bold ${fontSize}px sans-serif`
    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)'
    let textWidth = ctx.measureText(text).width
    let textX = isAlignRight ? x - textWidth : x
    ctx.fillRect(textX, y, textWidth, fontSize)

    ctx.fillStyle = 'red'
    ctx.textBaseline = 'top'
    ctx.fillText(text, textX, y)

    ctx.restore()
  }

  splitNameScoreList(nameScoreListStr) {
    let result = []
    let nameScorePairs = (nameScoreListStr || '').split(';').filter(x => x.length > 0)
    nameScorePairs.forEach(str => {
      let pair = str.split('=')
      if (pair.length != 2) {
        return
      }
      let name = pair[0]
      let score = Number(pair[1])
      if (!(score >= 0)) {
        return
      }
      result.push({
        name,
        score: roundScore(score),
      })
    })
    return result
  }

  getAnswerCharIndex(chars) {
    let list = []
    for (let i = 0; i < chars.length; i++) {
      list.push(chars.charCodeAt(i) - 65)
    }
    return list
  }
}
