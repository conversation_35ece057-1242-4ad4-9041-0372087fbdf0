<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#230;&#160;&#161;-&#229;&#183;&#178;&#229;&#174;&#140;&#230;&#136;&#144;">
<g id="Group 427318914">
<g id="Rectangle 346240803" filter="url(#filter0_d_2271_8079)">
<rect x="3" width="77" height="77" rx="19.7279" fill="#E5F9F7"/>
<rect x="3.90703" y="0.90703" width="75.1859" height="75.1859" rx="18.8209" stroke="white" stroke-width="1.81406"/>
</g>
<g id="Frame 1000006683">
<g clip-path="url(#clip0_2271_8079)">
<rect x="11.3701" y="8.9729" width="58.8" height="58.8" rx="23.5828" fill="#E5F9F7"/>
<rect id="Rectangle 346240804" x="11.3701" y="8.9729" width="64.1724" height="64.3991" rx="23.8095" fill="url(#paint0_linear_2271_8079)"/>
<path id="&#230;&#160;&#161;" d="M45.1958 29.8151H73.2583V35.4175H45.1958V29.8151ZM56.1483 22.7995H62.0535V32.3387H56.1483V22.7995ZM51.101 35.8213L56.5016 38.143C54.2808 42.4836 51.0506 46.8242 47.7194 49.6002C46.8109 48.4394 44.8929 46.6728 43.5807 45.7139C46.609 43.3417 49.4859 39.3544 51.101 35.8213ZM61.5488 38.3449L66.2427 35.6699C69.3215 38.8496 72.7031 43.1398 74.1668 46.2186L69.0187 49.1964C67.8073 46.2186 64.5266 41.6256 61.5488 38.3449ZM54.836 45.3101C58.1167 54.2437 64.8295 61.6631 74.2678 64.9438C72.905 66.2056 70.9871 68.6787 70.0786 70.3443C60.0346 66.1551 53.5237 57.6758 49.5869 46.7233L54.836 45.3101ZM62.1544 44.9568L68.2111 46.1681C64.8295 57.1711 58.1167 66.0037 45.9024 70.3443C45.2967 68.9816 43.833 66.7103 42.8236 65.5494C53.8265 61.9154 59.8832 54.1427 62.1544 44.9568ZM27.0762 33.1967H44.6406V38.8496H27.0762V33.1967ZM33.5367 22.7995H39.1391V70.1929H33.5367V22.7995ZM33.4357 37.0831L36.8679 38.2944C35.4042 46.37 32.3758 56.1111 28.6914 61.3098C28.1362 59.6442 26.8239 57.272 25.9154 55.8083C29.1961 51.4172 32.1739 43.5436 33.4357 37.0831ZM38.8867 41.1209C40.0981 42.5341 44.4892 49.1964 45.4481 50.7106L42.1674 55.2531C40.9056 52.1238 37.7259 46.2186 36.2622 43.6445L38.8867 41.1209Z" fill="white" fill-opacity="0.3"/>
<g id="Frame" clip-path="url(#clip1_2271_8079)">
<g id="Vector" filter="url(#filter1_d_2271_8079)">
<path d="M61.8694 25.6076C60.1355 23.8502 57.2988 23.8502 55.5649 25.6076L38.8668 42.5311L31.0459 34.6048C29.312 32.8474 26.4757 32.8474 24.7414 34.6048C22.9938 36.3763 22.9938 39.2228 24.7414 40.9943L35.7143 52.1152C37.4482 53.8725 40.2849 53.8725 42.0188 52.1152L61.8694 31.9966C63.617 30.2255 63.617 27.3787 61.8694 25.6076Z" fill="white"/>
</g>
</g>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_2271_8079" x="-1.6" y="-3.6" width="86.2" height="86.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_2271_8079"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2271_8079"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2271_8079" result="shape"/>
</filter>
<filter id="filter1_d_2271_8079" x="17.0307" y="21.8896" width="52.549" height="41.9436" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3.2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.390035 0 0 0 0 0.391434 0 0 0 0 0.393299 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2271_8079"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2271_8079" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2271_8079" x1="14.6581" y1="10.6736" x2="50.1456" y2="73.372" gradientUnits="userSpaceOnUse">
<stop stop-color="#A4E7DE"/>
<stop offset="1" stop-color="#05C1AE"/>
</linearGradient>
<clipPath id="clip0_2271_8079">
<rect x="11.3701" y="8.9729" width="58.8" height="58.8" rx="23.5828" fill="white"/>
</clipPath>
<clipPath id="clip1_2271_8079">
<rect width="45.3515" height="45.3515" fill="white" transform="translate(20.6738 11.6504)"/>
</clipPath>
</defs>
</svg>
