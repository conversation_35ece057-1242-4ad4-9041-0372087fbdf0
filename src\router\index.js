import { createRouter, createWebHistory } from 'vue-router'
import { staticRoutes, dynamicRoutes, referenceBookScannerRoutes } from './routes'
import { nextTick } from 'vue'
import store from '@/store'
import iView from '@/iview'

// 设置初始token
let urlParams = new URLSearchParams(window.location.search)
let token = urlParams.get('token')
if (token) {
  localStorage.setItem('token', token)
}

const router = createRouter({
  history: createWebHistory(),
  routes: staticRoutes,
})

const RouteLogin = {
  name: 'login',
}
const RouteIndex = {
  name: 'index',
}
const RouteNotFound = {
  name: 'notfound',
}
const StaticRouteNames = ['login', 'forget-password', 'oauth']

/**
 * 路由控制：
 *  1. 未登录用户只能访问登录页面、重置密码页面、第三方登录回调页面
 *  2. 已登录用户
 *  2.1 已有路由项，直接跳转（登录页面除外）
 *  2.2 无路由项，先获取用户角色，再动态添加路由项，然后跳转
 */

router.beforeEach((to, from, next) => {
  let loggedIn = localStorage.getItem('token')
  if (!loggedIn) {
    if (StaticRouteNames.includes(to.name)) {
      next()
    } else {
      next(RouteLogin)
    }
  } else {
    let hasUserRoutes = getUserRouteState()
    if (hasUserRoutes) {
      if (to.name === RouteLogin.name) {
        store.dispatch('user/logout').then(() => {
          next()
        })
      } else if (to.matched.length === 0) {
        next(RouteNotFound)
      } else {
        next()
      }
    } else {
      if (to.name === 'oauth') {
        next()
        return
      }
      store
        .dispatch('user/getInfo')
        .then(() => {
          addUserRoutes()
          // 重新解析
          next(to)
        })
        .catch(err => {
          store.dispatch('user/logout').then(() => {
            next(RouteLogin)
          })

          throw err
        })
    }
  }
})

router.afterEach(() => {
  nextTick().then(() => window.scrollTo(0, 0))
})

function getUserRouteState() {
  return router.hasRoute(RouteIndex.name)
}

export function addUserRoutes() {
  if (getUserRouteState()) {
    return
  }

  let user = store.state.user
  let userRoles = user.roles || []
  if (!userRoles.length) {
    iView.Message.error({
      content: '您的账号当前尚无角色',
      duration: 5,
    })

    throw {
      code: 1,
      msg: '添加路由时用户没有角色',
    }
  }

  let userRoutes = []
  ;(store.getters['user/isReferenceBookScanner'] ? referenceBookScannerRoutes : dynamicRoutes).forEach(routeTree => {
    let toAddRouteTree = filterRoutes(routeTree, user)
    if (toAddRouteTree) {
      userRoutes.push(toAddRouteTree)
    }
  })

  if (userRoutes.length) {
    userRoutes.forEach(route => router.addRoute(route))
    // options.routes中保存树形结构路由
    router.options.routes.push(...userRoutes)
  } else {
    iView.Message.error({
      content: '您的账号当前没有可浏览页面',
      duration: 5,
    })

    throw {
      code: 2,
      msg: '用户没有匹配到路由',
    }
  }

  function filterRoutes(routeTree, user) {
    let userRouteTree = null
    if (checkRouteAccessible(routeTree, user)) {
      userRouteTree = { ...routeTree }
      if (routeTree.children) {
        let children = []
        routeTree.children.forEach(child => {
          let subTree = filterRoutes(child, user)
          if (subTree) {
            children.push(subTree)
          }
        })
        if (children.length > 0) {
          userRouteTree.children = children
        } else {
          delete userRouteTree.children
          if (userRouteTree.children) {
            userRouteTree.children = []
          }
        }
      }
    }
    return userRouteTree

    function checkRouteAccessible(route, user) {
      if (route.meta && route.meta.needSystemAccount && !store.getters['user/info'].isSystem) {
        return false
      }
      let requireRoles = route.meta && route.meta.roles
      if (!requireRoles) {
        return true
      }
      if (typeof requireRoles == 'function') {
        return requireRoles(user)
      }
      if (requireRoles instanceof Array) {
        if (requireRoles.length == 0) {
          return true
        } else {
          return user.roles.some(x => requireRoles.includes(x))
        }
      }
      return false
    }
  }
}

export default router
