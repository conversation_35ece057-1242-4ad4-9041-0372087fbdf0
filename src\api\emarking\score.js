import ajax from '@/api/ajax'

export function apiImportWrittenScore(data) {
  return ajax.upload({
    url: 'mark/score/importWrittenScore',
    params: {
      examSubjectId: data.examSubjectId,
      skipAllSubjectsAbsentStudent: data.skipAllSubjectsAbsentStudent, // Boolean
    },
    data: {
      file: data.file,
    },
  })
}

export function apiImportWrittenScoreBySchool(data) {
  return ajax.upload({
    url: 'mark/score/importWrittenScoreBySchool',
    params: {
      examSubjectId: data.examSubjectId,
      schoolId: data.schoolId,
      skipAllSubjectsAbsentStudent: data.skipAllSubjectsAbsentStudent, // Boolean
    },
    data: {
      file: data.file,
    },
  })
}

export function apiImportOtherScore(data) {
  return ajax.upload({
    url: 'mark/score/importOtherScore',
    params: {
      examSubjectId: data.examSubjectId,
    },
    data: {
      file: data.file,
    },
  })
}

export function apiImportOtherScoreBySchool(data) {
  return ajax.upload({
    url: 'mark/score/importOtherScoreBySchool',
    params: {
      examSubjectId: data.examSubjectId,
      schoolId: data.schoolId,
    },
    data: {
      file: data.file,
    },
  })
}

export function apiImportQuestionScore(data) {
  return ajax.upload({
    url: 'mark/score/importQuestionScore',
    params: {
      examSubjectId: data.examSubjectId,
    },
    data: {
      file: data.file,
    },
  })
}

export function apiImportQuestionScoreBySchool(data) {
  return ajax.upload({
    url: 'mark/score/importQuestionScoreBySchool',
    params: {
      examSubjectId: data.examSubjectId,
      schoolId: data.schoolId,
    },
    data: {
      file: data.file,
    },
  })
}

export function apiGetStudentSubjectScore(params) {
  return ajax.get({
    url: 'mark/score/getStudentSubjectScore',
    params: {
      examSubjectId: params.examSubjectId,
      schoolId: params.schoolId,
      classId: params.classId,
      keyword: params.keyword,
      page: params.currentPage,
      size: params.pageSize,
    },
    requestName: '获取考生科目成绩',
  })
}

export function apiGetStudentQuestionScore(params) {
  return ajax.get({
    url: 'mark/score/getStudentQuestionScore',
    params: {
      examSubjectId: params.examSubjectId,
      schoolId: params.schoolId,
      classId: params.classId,
      keyword: params.keyword,
      page: params.currentPage,
      size: params.pageSize,
    },
    requestName: '获取考生小题分数',
  })
}

export function apiHandleScoreSwap(data) {
  return ajax.post({
    url: 'mark/exchange/exchange',
    params: {},
    data: {
      examSubjectId: data.examSubjectId,
      changeList: data.changeList,
    },
    requestName: '处理成绩调换',
  })
}

export function apiGetScoreSwapRecord(params) {
  return ajax.get({
    url: 'mark/exchange/list',
    params: {
      examSubjectId: params.examSubjectId,
    },
    requestName: '获取成绩调换记录',
  })
}

export function apiGetTheScoreSwapRecord(params) {
  return ajax.get({
    url: 'mark/exchange/history',
    params: {
      examSubjectId: params.examSubjectId,
      id: params.id,
    },
    requestName: '获取某个学生成绩调换记录',
  })
}

export function apiGetStudentListByKeyword(params) {
  return ajax.get({
    url: 'mark/exchange/getStudentList',
    params: {
      examSubjectId: params.examSubjectId,
      keyword: params.keyword,
    },
    requestName: '获取成绩调换记录',
  })
}

export function apiModifyStudentObjectiveAnswer(requestParams) {
  return ajax.put({
    url: 'mark/score/stuObj',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      studentId: requestParams.studentId,
    },
    data: {
      code: requestParams.code,
      answer: requestParams.answer,
      score: requestParams.score == null ? undefined : requestParams.score,
    },
    requestName: '修改考生客观题答案',
  })
}

export function apiModifyStudentSubjective(requestParams) {
  return ajax.put({
    url: 'mark/score/stuSubj',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      studentId: requestParams.studentId,
    },
    data: {
      blockId: requestParams.blockId,
      score: requestParams.score,
      subScore: requestParams.subScore,
    },
    requestName: '修改考生主观题答案',
  })
}

export function apiGetModifiedScoreLogs(requestParams) {
  return ajax.get({
    url: 'mark/score/updateScoreLogs',
    params: {
      examId: requestParams.examId,
      subjectId: requestParams.subjectId || undefined,
      keyword: requestParams.keyword || undefined,
      pageNum: requestParams.currentPage || 1,
      pageSize: requestParams.pageSize || 10,
    },
    requestName: '查询考试项目成绩修改记录',
  })
}

export function apiGetUnionExamSchoolImportScoreStatistics(requestParams) {
  return ajax.get({
    url: 'mark/score/schoolImportSubjects',
    params: {
      examId: requestParams.examId, // [String]*
      examSubjectId: requestParams.examSubjectId, // [String]
      schoolId: requestParams.schoolId, // [String]
    },
    requestParams: '获取联考学校自导入成绩统计',
  })
}

export function apiSubmitUnionExamSchoolImportScoreSettings(requestParams) {
  return ajax.post({
    url: 'mark/score/schoolImportScoreSetting',
    params: {
      examId: requestParams.examId, // [String]*
      schoolImportScoreEndTime: requestParams.importEndTime, // [String]
    },
    requestParams: '提交更改联考学校子导入成绩设置',
  })
}

export function apiSchoolExportUnionExamImportScoreTemplateAndRecord(requestParams) {
  return ajax.download({
    url: 'mark/score/exportImportScoresExcelTemplate',
    params: {
      examId: requestParams.examId, // [String]*
      schoolId: requestParams.schoolId, // [String]
    },
    requestParams: '下载学校已导入学生成绩',
  })
}

export function apiFreeSchoolImportScore(requestParams) {
  return ajax.upload({
    url: 'mark/score/importWrittenScores',
    params: {
      examId: requestParams.examId, // [String]
      skipAllSubjectsAbsentStudent: requestParams.skipAllSubjectsAbsentStudent, // Boolean
    },
    data: {
      file: requestParams.file, // *
    },
    // requestName: '学校导入学生成绩（不限导入学校）',
  })
}

export function apiSchoolImportScore(requestParams) {
  return ajax.upload({
    url: 'mark/score/importWrittenScoresBySchool',
    params: {
      examId: requestParams.examId, // [String]
      schoolId: requestParams.schoolId, //[String]
      skipAllSubjectsAbsentStudent: requestParams.skipAllSubjectsAbsentStudent, // Boolean
    },
    data: {
      file: requestParams.file, // *
    },
    // requestName: '学校导入学生成绩',
  })
}

export function apiSchoolCommitScores(requestParams) {
  return ajax.post({
    url: 'mark/score/upImportScore',
    params: {
      examId: requestParams.examId, // [String]*
      schoolId: requestParams.schoolId, // [String]*
    },
    requestName: '学校上报已导入成绩',
  })
}

export function apiSchoolCancelCommittedScores(requestParams) {
  return ajax.post({
    url: 'mark/score/cancelUpImportScore',
    params: {
      examId: requestParams.examId, // [Stiring]*
      schoolId: requestParams.schoolId, // [String]*
    },
    requestName: '学校取消上报已导入成绩',
  })
}

export function apiClearAbsentStudentOtherScore(requestParams) {
  return ajax.put({
    url: 'mark/score/clearAbsentStudentOtherScore',
    params: {
      examSubjectId: requestParams.examSubjectId,
    },
    requestName: '清空缺考考生其他分',
  })
}

export function apiClearAbsentStudentOtherScoreBySchool(requestParams) {
  return ajax.put({
    url: 'mark/score/clearAbsentStudentOtherScoreBySchool',
    params: {
      examSubjectId: requestParams.examSubjectId,
      schoolId: requestParams.schoolId,
    },
    requestName: '清空当前学校缺考考生其他分',
  })
}

export function apiClearAllStudentOtherScore(requestParams) {
  return ajax.put({
    url: 'mark/score/clearAllStudentOtherScore',
    params: {
      examSubjectId: requestParams.examSubjectId,
    },
    requestName: '清空全部考生其他分',
  })
}

export function apiClearAllStudentOtherScoreBySchool(requestParams) {
  return ajax.put({
    url: 'mark/score/clearAllStudentOtherScoreBySchool',
    params: {
      examSubjectId: requestParams.examSubjectId,
      schoolId: requestParams.schoolId,
    },
    requestName: '清空当前学校全部考生其他分',
  })
}

export function apiDeleteImportQuestionScore(examSubjectId) {
  return ajax.delete({
    url: 'mark/score/deleteQuestionScore',
    params: {
      examSubjectId: examSubjectId, // *String
    },
    requestName: '删除科目已记录的小题分',
  })
}

export function apiDeleteImportQuestionScoreBySchool(requestParams) {
  return ajax.delete({
    url: 'mark/score/deleteQuestionScoreBySchool',
    data: {
      examSubjectId: requestParams.examSubjectId, // *String
      schoolId: requestParams.schoolId, // *String
    },
    requestName: '删除学校科目已记录的小题分',
  })
}
