<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 427318973">
<g id="Group 427318834">
<g id="&#231;&#173;&#148;&#233;&#162;&#152;&#229;&#141;&#161;" clip-path="url(#clip0_2283_6856)">
<rect id="Rectangle 346240742" x="4.22223" y="2" width="15.5556" height="16.6667" rx="2" fill="url(#paint0_linear_2283_6856)"/>
<g id="Vector 100" filter="url(#filter0_d_2283_6856)">
<path d="M7.55566 5.33337H16.4446" stroke="white" stroke-linecap="round"/>
</g>
<g id="Vector 102" filter="url(#filter1_d_2283_6856)">
<path d="M7.55566 9H16.4446" stroke="white" stroke-linecap="round"/>
</g>
<g id="Vector 101" filter="url(#filter2_d_2283_6856)">
<path d="M7.55566 12H12.0001" stroke="white" stroke-linecap="round"/>
</g>
<g id="Rectangle 346240743" filter="url(#filter3_b_2283_6856)">
<path d="M2 15.5C2 14.3463 2 13.7694 2.3753 13.4691C2.75061 13.1689 3.31341 13.2955 4.43902 13.5488L11.561 15.1512C11.7796 15.2004 11.8889 15.225 12 15.225C12.1111 15.225 12.2204 15.2004 12.439 15.1512L19.561 13.5488C20.6866 13.2955 21.2494 13.1689 21.6247 13.4691C22 13.7694 22 14.3463 22 15.5V20C22 20.9428 22 21.4142 21.7071 21.7071C21.4142 22 20.9428 22 20 22H4C3.05719 22 2.58579 22 2.29289 21.7071C2 21.4142 2 20.9428 2 20V15.5Z" fill="#5B9BFF" fill-opacity="0.2"/>
<path d="M2.25 15.5C2.25 14.9153 2.25062 14.5041 2.29436 14.2001C2.33716 13.9028 2.41507 13.7575 2.53148 13.6643C2.64789 13.5712 2.80675 13.5271 3.10623 13.5506C3.41239 13.5747 3.81372 13.6643 4.38415 13.7927L11.5061 15.3951C11.5146 15.397 11.5229 15.3989 11.5312 15.4008C11.7257 15.4446 11.8607 15.475 12 15.475C12.1393 15.475 12.2743 15.4446 12.4688 15.4008C12.4771 15.3989 12.4854 15.397 12.4939 15.3951L19.6159 13.7927C20.1863 13.6643 20.5876 13.5747 20.8938 13.5506C21.1933 13.5271 21.3521 13.5712 21.4685 13.6643C21.5849 13.7575 21.6628 13.9028 21.7056 14.2001C21.7494 14.5041 21.75 14.9153 21.75 15.5V20C21.75 20.4785 21.7495 20.8122 21.7156 21.064C21.6827 21.3086 21.6226 21.4381 21.5303 21.5303C21.4381 21.6226 21.3086 21.6827 21.064 21.7156C20.8122 21.7495 20.4785 21.75 20 21.75H4C3.52153 21.75 3.18782 21.7495 2.93604 21.7156C2.69145 21.6827 2.56192 21.6226 2.46967 21.5303C2.37742 21.4381 2.31727 21.3086 2.28438 21.064C2.25053 20.8122 2.25 20.4785 2.25 20V15.5Z" stroke="url(#paint1_angular_2283_6856)" stroke-width="0.5"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_2283_6856" x="6.05566" y="3.83337" width="13.8889" height="5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0477269 0 0 0 0 0.285795 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2283_6856"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2283_6856" result="shape"/>
</filter>
<filter id="filter1_d_2283_6856" x="6.05566" y="7.5" width="13.8889" height="5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0477269 0 0 0 0 0.285795 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2283_6856"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2283_6856" result="shape"/>
</filter>
<filter id="filter2_d_2283_6856" x="6.05566" y="10.5" width="9.44446" height="5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0477269 0 0 0 0 0.285795 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2283_6856"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2283_6856" result="shape"/>
</filter>
<filter id="filter3_b_2283_6856" x="0" y="11.2944" width="24" height="12.7056" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="1"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2283_6856"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_2283_6856" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2283_6856" x1="20.0001" y1="19" x2="19.94" y2="0.948662" gradientUnits="userSpaceOnUse">
<stop stop-color="#625AED"/>
<stop offset="1" stop-color="#8884EF"/>
</linearGradient>
<radialGradient id="paint1_angular_2283_6856" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.4444 18.625) scale(12.7778 4.06831)">
<stop stop-color="#2F63FF" stop-opacity="0.2"/>
<stop offset="0.756826" stop-color="#FCFDFF"/>
<stop offset="1" stop-color="#2F63FF" stop-opacity="0.2"/>
</radialGradient>
<clipPath id="clip0_2283_6856">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
