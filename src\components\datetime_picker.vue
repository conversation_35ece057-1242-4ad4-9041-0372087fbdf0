<template>
  <DatePicker
    v-model="dateTime"
    type="datetime"
    style="width: 180px"
    transfer
    confirm
    @on-open-change="handleOpenChange"
    @on-clear="handleClear"
  ></DatePicker>
</template>

<script>
  export default {
    props: {
      value: {
        type: Date,
        default: () => null,
      },
    },
    emits: ['on-change'],
    data() {
      return {
        dateTime: null,
      }
    },
    watch: {
      value() {
        this.dateTime = this.value || ''
      },
    },
    created() {
      this.dateTime = this.value || ''
    },
    methods: {
      handleOpenChange(open) {
        if (!open) {
          setTimeout(() => {
            this.$emit('on-change', this.dateTime || null)
          }, 0)
        }
      },
      handleClear() {
        this.previousEmitEventTime = Date.now()
        this.$emit('on-change', null)
      },
    },
  }
</script>
