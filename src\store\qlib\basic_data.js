import { apiGetQlibBasicData } from '@/api/qlib'
import { groupArray } from '@/utils/array'

export default {
  state: {
    stages: [],
    difficulties: [],
    currentStageSubject: {
      stageId: 0,
      stageName: '',
      subjectId: 0,
      subjectName: '',
    },
  },

  getters: {
    stages(state) {
      return () =>
        state.stages.map(x => ({
          id: x.id,
          name: x.name,
        }))
    },

    stageSubjects(state) {
      return () =>
        state.stages.map(stage => ({
          id: stage.id,
          name: stage.name,
          subjects: stage.subjects.map(subject => ({
            id: subject.id,
            name: subject.name,
          })),
        }))
    },

    gradesByStage(state) {
      return stageId => {
        let _stage = state.stages.find(x => x.id === stageId)
        return (
          (_stage &&
            _stage.grades.map(x => ({
              id: x.id,
              name: x.name,
            }))) ||
          []
        )
      }
    },

    subjectsByStage(state) {
      return stageId => {
        let _stage = state.stages.find(x => x.id === stageId)
        return (
          (_stage &&
            _stage.subjects.map(x => ({
              id: x.id,
              name: x.name,
            }))) ||
          []
        )
      }
    },

    termsByStage(stage) {
      return stageId => {
        let _stage = stage.stages.find(x => x.id === stageId)
        return (
          (_stage &&
            _stage.terms.map(x => ({
              id: x.id,
              name: x.name,
            }))) ||
          []
        )
      }
    },

    paperTypesByStage(state) {
      return stageId => {
        let _stage = state.stages.find(x => x.id === stageId)
        return (
          (_stage &&
            _stage.paperTypes &&
            _stage.paperTypes.map(t => ({
              id: t.id,
              name: t.name,
            }))) ||
          []
        )
      }
    },

    paperTypes(state) {
      return () => {
        let result = []
        state.stages.forEach(stage => {
          stage.paperTypes.forEach(t => {
            result.push({
              stageId: stage.id,
              stageName: stage.name,
              paperTypeId: t.id,
              paperTypeName: t.name,
            })
          })
        })
        return result
      }
    },

    questionTypesByStageSubject(state) {
      return (stageId, subjectId) => {
        let _stage = state.stages.find(x => x.id === stageId)
        if (!_stage) {
          return []
        }

        let _subject = _stage.subjects.find(x => x.id === subjectId)
        if (!_subject) {
          return []
        }

        return _subject.questionTypes.map(t => ({ ...t }))
      }
    },

    currentStageSubjectQuestionTypes(state, getters) {
      return () =>
        getters.questionTypesByStageSubject(state.currentStageSubject.stageId, state.currentStageSubject.subjectId)
    },

    pressBooksByStageSubject(state) {
      return (stageId, subjectId, gradeId, semesterId) => {
        let _stage = state.stages.find(x => x.id === stageId)
        if (!_stage) {
          return []
        }

        let _subject = _stage.subjects.find(x => x.id === subjectId)
        if (!_subject) {
          return []
        }

        let _books
        if (gradeId && semesterId) {
          _books = _subject.books.find(x => x.gradeId === gradeId && x.semesterId === semesterId)
        } else if (gradeId) {
          _books = _subject.books.find(x => x.gradeId === gradeId)
        } else {
          _books = _subject.books
        }

        return groupArray(_books, x => x.pressId).map(p => ({
          id: p.group[0].pressId,
          name: p.group[0].pressName,
          books: p.group.map(b => ({
            id: b.id,
            name: b.name,
            pressId: b.pressId,
            pressName: b.pressName,
          })),
        }))
      }
    },

    currentStageSubjectPressBooks(state, getters) {
      return () =>
        getters.pressBooksByStageSubject(state.currentStageSubject.stageId, state.currentStageSubject.subjectId)
    },

    difficulties(state) {
      return () =>
        state.difficulties.map(d => ({
          id: d.id,
          name: d.name,
        }))
    },
  },

  mutations: {
    updateBasicData(state, data) {
      Object.keys(data).forEach(key => {
        if (state[key]) {
          Object.freeze(data[key])
          state[key] = data[key]
        }
      })
    },
  },

  actions: {
    getBasicData(context) {
      return apiGetQlibBasicData().then(data => {
        context.commit('updateBasicData', data)
      })
    },
    refreshBasicData(context) {
      if (window.sessionStorage) {
        window.sessionStorage.removeItem('qlibBasicData')
      }
      return apiGetQlibBasicData().then(data => {
        context.commit('updateBasicData', data)
      })
    },
  },
}
