/**
 * 错题反馈卡
 */
import Template from '@/helpers/scan_template_define/template'
import TemplateObjective from '@/helpers/scan_template_define/template_objective'
import TemplateQuestionObjective from '@/helpers/scan_template_define/template_question_objective'

import {
  extractPaperStructure,
  buildBlocks,
  parseQuestionsAndBlocks,
  updateQuestionNameScoreAnswerBlockName,
} from './util'
import { sortQuestionFunction } from '../emarking/miscellaneous'

import QRCode from 'qrcode'

import PageSizeEnum from '@/enum/wrong_question_sheet/page_size'
import SourceTypeEnum from '@/enum/wrong_question_sheet/source_type'

import { SheetVersion } from '@/const/wrong_question_sheet'
import Page from './page'
import PageBlock from './page_block'
import { randomId } from '@/utils/string'

export default class Sheet {
  constructor() {
    // 反馈卡版本，为比较方便，以整数表示
    this.version = SheetVersion
    // 反馈卡ID
    this.id = ''
    // 反馈卡名称
    this.name = ''
    // 来源：由题库试卷生成|自主创建
    this.from = SourceTypeEnum.Paper.id
    // 学段Id
    this.stageId = 0
    // 年级Id
    this.gradeId = 0
    // 学科Id
    this.subjectId = 0
    // 题库组卷Id
    this.paperId = ''
    // 教辅Id
    this.coachBookId = ''
    // 书名
    this.bookName = ''
    // 章节名
    this.chapterName = ''
    // 准考号长度
    this.admissionNumLength = 7
    // 二维码内容
    this.qrcodeText = ''
    // 二维码图片
    this.qrcodeImageSrc = ''
    // 反馈卡尺寸
    this.size = PageSizeEnum.A4
    // 显示大题
    this.showTopic = false
    // 按题分组
    this.questionsSeparate = false
    // 每组题数
    this.questionGroupLength = 5
    // 客观题
    this.objectiveQuestions = []
    // 主观题
    this.subjectiveQuestions = []
    // 题块
    this.blocks = []
    // 反馈卡页（栏）
    this.pages = []
  }

  /**
   * setters ---------------------------------------------------------------------------
   */
  changeBookName(bookName) {
    this.bookName = bookName
    this.updateName()
  }
  changeChapterName(chapterName) {
    this.chapterName = chapterName
    this.updateName()
  }
  updateName() {
    this.name = `${this.bookName}-${this.chapterName}`
  }
  changeGradeId(gradeId) {
    this.gradeId = gradeId || 0
  }
  changeShowTopic(value) {
    this.showTopic = Boolean(value)
  }
  changeQuestionsSeparate(value) {
    this.questionsSeparate = Boolean(value)
  }
  changeQuestionGroupLength(len) {
    this.questionGroupLength = Math.min(25, Math.max(Number(len), 1))
  }
  changeSize(sizeId) {
    let size = PageSizeEnum.getEntryById(sizeId)
    if (size) {
      this.size = size
    }
  }
  changeAdmissionNumLength(len) {
    this.admissionNumLength = len
  }

  /**
   * 创建 ---------------------------------------------------------------------------
   */
  // 导入
  static async import({ sheet, paper, coachBookInfo }) {
    // 兼容旧版由试卷创建的反馈卡
    if (!sheet.from) {
      sheet.from = sheet.paperId ? SourceTypeEnum.Paper.id : SourceTypeEnum.Manual.id
    }
    if (coachBookInfo) {
      if (!sheet.stageId) {
        sheet.stageId = coachBookInfo.stageId
      }
      if (!sheet.gradeId) {
        sheet.gradeId = coachBookInfo.gradeId || 0
      }
      if (!sheet.subjectId) {
        sheet.subjectId = coachBookInfo.subjectId
      }
      if (!sheet.coachBookId) {
        sheet.coachBookId = coachBookInfo.id
      }
    }

    // 反馈卡版本要一致
    let s = new Sheet()
    if (sheet.version !== s.version) {
      throw new Error('错题反馈卡版本错误')
    }

    s.id = sheet.id
    s.name = sheet.name
    if (SourceTypeEnum.hasId(sheet.from)) {
      s.from = sheet.from
    } else {
      throw new Error('反馈卡来源错误')
    }
    s.stageId = sheet.stageId
    s.gradeId = sheet.gradeId || 0
    s.subjectId = sheet.subjectId
    s.paperId = sheet.paperId
    s.coachBookId = sheet.coachBookId || ''
    s.bookName = sheet.bookName
    s.chapterName = sheet.chapterName
    s.admissionNumLength = sheet.admissionNumLength
    s.qrcodeText = sheet.qrcodeText
    let size = PageSizeEnum.getEntryById((sheet.size && sheet.size.id) || '')
    if (size) {
      s.changeSize(size.id)
    } else {
      throw new Error('错题反馈卡尺寸错误')
    }
    s.showTopic = Boolean(sheet.showTopic)
    s.questionsSeparate = Boolean(sheet.questionsSeparate)
    s.questionGroupLength = sheet.questionGroupLength || 5

    // 导入题目题块
    let sheetStructure = parseQuestionsAndBlocks(sheet)
    s.objectiveQuestions = sheetStructure.objectiveQuestions
    s.subjectiveQuestions = sheetStructure.subjectiveQuestions
    s.blocks = sheetStructure.blocks

    // 由试卷生成的反馈卡需更新
    if (s.from == SourceTypeEnum.Paper.id) {
      let paperStructure = extractPaperStructure(paper)
      updateQuestionNameScoreAnswerBlockName({
        objectiveQuestions: s.objectiveQuestions,
        subjectiveQuestions: s.subjectiveQuestions,
        blocks: s.blocks,
        objectiveDefines: paperStructure.objectiveQuestions,
        subjectiveDefines: paperStructure.subjectiveQuestions,
        blockDefines: paperStructure.blocks,
      })
    }

    // 生成二维码
    await s.generateQrcodeImage()

    return s
  }

  // 由组卷试卷创建
  static async createFromPaper({ newId, paper, coachBookInfo }) {
    let s = new Sheet()
    let { id, name, stage, grade, subject } = paper.paperInfo
    s.id = newId
    s.from = SourceTypeEnum.Paper.id
    s.stageId = stage.id
    s.gradeId = grade.id || 0
    s.subjectId = subject.id
    s.paperId = id
    s.changeBookName(`${grade.name || stage.name}《${subject.name}》`)
    s.changeChapterName(name)
    s.qrcodeText = `WXFB|${newId}`

    // 生成题目题块
    let { objectiveQuestions, subjectiveQuestions, blocks } = extractPaperStructure(paper)
    s.objectiveQuestions = objectiveQuestions
    s.subjectiveQuestions = subjectiveQuestions
    s.blocks = blocks

    // 设置书名章节名
    if (coachBookInfo) {
      if (!s.gradeId) {
        s.gradeId = coachBookInfo.gradeId || 0
      }
      s.coachBookId = coachBookInfo.id
      s.changeBookName(coachBookInfo.bookName)
      if (name.startsWith(coachBookInfo.bookName)) {
        let chapterName = name.substring(coachBookInfo.bookName.length)
        chapterName = chapterName.replace(/^[-—_|]+/g, '').trim()
        s.changeChapterName(chapterName)
      }
    }

    await s.generateQrcodeImage()
    return s
  }

  // 自主创建
  static async createFromManual({ newId, coachBookInfo }) {
    let s = new Sheet()
    s.id = newId
    s.from = SourceTypeEnum.Manual.id
    s.stageId = coachBookInfo.stageId
    s.gradeId = coachBookInfo.gradeId || 0
    s.subjectId = coachBookInfo.subjectId
    s.coachBookId = coachBookInfo.id
    s.changeBookName(coachBookInfo.bookName)
    s.qrcodeText = `WXFB|${newId}`
    await s.generateQrcodeImage()
    return s
  }

  generateQrcodeImage() {
    return QRCode.toDataURL(this.qrcodeText, {
      margin: 0,
      errorCorrectionLevel: 'H',
    }).then(url => {
      this.qrcodeImageSrc = url
    })
  }

  /**
   * 生成页面
   */
  generatePages() {
    // 现有页面块实例
    let oldExamInfoBlock = null
    let oldQuestionBlock = null
    if (this.pages.length > 0) {
      this.pages[0].blocks.forEach(x => {
        if (x.type == 'ExamInfo') {
          oldExamInfoBlock = x
        } else if (x.type == 'Question') {
          oldQuestionBlock = x
        }
      })
    }

    // 清空现有页面
    this.pages = []

    // 添加一页
    let newPage = new Page()
    newPage.pageId = randomId()
    newPage.pageIndex = 0
    this.pages.push(newPage)

    // 添加考试信息块
    let examInfoBlock = PageBlock.createExamInfoBlock(oldExamInfoBlock ? oldExamInfoBlock.height : 0)
    newPage.addBlock(examInfoBlock)

    // 添加题目块
    let questionBlocks = []
    if (oldQuestionBlock) {
      questionBlocks = oldQuestionBlock.instance.split(newPage.top)
    } else {
      questionBlocks = [
        {
          height: 0,
          questions: [...this.objectiveQuestions, ...this.subjectiveQuestions].sort(sortQuestionFunction),
        },
      ]
    }
    questionBlocks.forEach((questionBlock, idx) => {
      if (!questionBlock || questionBlock.questions.length == 0) {
        return
      }
      if (idx > 0) {
        // 添加一页
        newPage = new Page()
        newPage.pageId = randomId()
        newPage.pageIndex = this.pages.length
        this.pages.push(newPage)
      }
      let pageBlock = PageBlock.createQuestionBlock(questionBlock.questions, questionBlock.height)
      newPage.addBlock(pageBlock)
    })
  }
  // 设置页头实例
  changePageHeaderInstance(pageIndex, instance) {
    let page = this.pages[pageIndex]
    if (page) {
      page.header = instance
    }
  }
  // 设置页脚实例
  changePageFooterInstance(pageIndex, instance) {
    let page = this.pages[pageIndex]
    if (page) {
      page.footer = instance
    }
  }
  // 找页面块
  findBlockById(blockId) {
    for (let page of this.pages) {
      for (let block of page.blocks) {
        if (block.id == blockId) {
          return block
        }
      }
    }
    return null
  }
  // 设置页面块实例
  changePageBlockInstance(blockId, instance) {
    let block = this.findBlockById(blockId)
    if (block) {
      block.instance = instance
    }
  }
  // 设置页面块高度
  changePageBlockHeight(blockId, height) {
    let block = this.findBlockById(blockId)
    if (!block) {
      return
    }
    block.height = height
  }

  /**
   * 操作 ---------------------------------------------------------------------------
   */
  // 添加客观题
  addObjectiveQuestions(objs) {
    this.objectiveQuestions.push(...objs)
    this.objectiveQuestions.sort(sortQuestionFunction)
  }
  // 添加主观题
  addSubjectiveQuestions(subjs) {
    this.subjectiveQuestions.push(...subjs)
    this.subjectiveQuestions.sort(sortQuestionFunction)
    this.blocks = buildBlocks(this.subjectiveQuestions)
  }
  // 删除客观题
  deleteObjectiveQuestions(questionIds) {
    this.objectiveQuestions = this.objectiveQuestions.filter(q => !questionIds.includes(q.questionId))
    this.objectiveQuestions.sort(sortQuestionFunction)
  }
  // 删除主观题
  deleteSubjectiveQuestions(questionIds) {
    this.subjectiveQuestions = this.subjectiveQuestions.filter(q => !questionIds.includes(q.questionId))
    this.subjectiveQuestions.sort(sortQuestionFunction)
    this.blocks = buildBlocks(this.subjectiveQuestions)
  }
  // 修改客观题
  changeObjectiveQuestions(objs) {
    objs.forEach(newQ => {
      let oldQ = this.objectiveQuestions.find(q => q.questionId == newQ.questionId)
      if (oldQ) {
        ;['topicName', 'questionName', 'branchTypeId', 'optionCount', 'fullScore'].forEach(key => {
          oldQ[key] = newQ[key]
        })
      }
    })
  }
  // 修改主观题
  changeSubjectiveQuestions(subjs) {
    subjs.forEach(newQ => {
      let oldQ = this.subjectiveQuestions.find(q => q.questionId == newQ.questionId)
      if (oldQ) {
        ;['topicName', 'questionName', 'branchName', 'fullScore'].forEach(key => {
          oldQ[key] = newQ[key]
        })
      }
    })
    this.blocks = buildBlocks(this.subjectiveQuestions)
  }

  /**
   * 导出 ---------------------------------------------------------------------------
   */
  // 导出反馈卡
  exportSheet() {
    let sheetData = {
      version: this.version,
      id: this.id,
      name: this.name,
      from: this.from,
      stageId: this.stageId,
      gradeId: this.gradeId,
      subjectId: this.subjectId,
      paperId: this.paperId,
      coachBookId: this.coachBookId,
      bookName: this.bookName,
      chapterName: this.chapterName,
      admissionNumLength: this.admissionNumLength,
      qrcodeText: this.qrcodeText,
      size: {
        id: this.size.id,
        name: this.size.name,
        width: this.size.width,
        height: this.size.height,
      },
      showTopic: this.showTopic,
      questionsSeparate: this.questionsSeparate,
      questionGroupLength: this.questionGroupLength,
      paperStructure: {
        objectiveQuestions: this.objectiveQuestions,
        subjectiveQuestions: this.subjectiveQuestions,
      },
      blocks: this.blocks,
    }
    return JSON.stringify(sheetData)
  }
  // 导出扫描模板
  exportScanTemplate() {
    let template = new Template()

    // 各页面
    template.pages = this.pages.map(page => page.exportScanTemplatePage())
    template.isDoubleSide = template.pages.length > 1

    // 客观题
    template.pages.forEach(page => {
      page.objectiveAreas.forEach(area => {
        let objArea = new TemplateObjective()
        objArea.areaId = area.id
        objArea.questions = area._extra.questions.map(q => {
          let obj = new TemplateQuestionObjective()
          obj.questionCode = q.questionCode
          obj.branchCode = q.branchCode
          return obj
        })
        area._extra = undefined
        template.questions.objectives.push(objArea)
      })
    })

    return JSON.stringify(template)
  }
}
