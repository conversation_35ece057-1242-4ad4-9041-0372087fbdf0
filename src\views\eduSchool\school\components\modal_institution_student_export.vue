<template>
  <Modal
    :model-value="modelValue"
    class="modal-export-student"
    title="导出学生"
    @on-visible-change="handleVisibilityChange"
  >
    <Form :label-width="60">
      <Form-item label="学段">
        <Select :model-value="exportStageId" @on-change="changeExportStage">
          <Option v-for="s of exportStages" :key="s.stageId" :value="s.stageId">{{ s.stageName }}</Option>
        </Select>
      </Form-item>
      <Form-item label="年级">
        <Select :model-value="exportGradeId" @on-change="changeExportGrade">
          <Option v-for="g in exportGradeClasses" :key="g.gradeId" :value="g.gradeId">{{ g.gradeName }}</Option>
        </Select>
      </Form-item>
      <Form-item label="班级">
        <Select v-model="exportClassId">
          <Option v-for="c in exportClasses" :key="c.classId" :value="c.classId">{{ c.className }}</Option>
        </Select>
      </Form-item>
    </Form>
    <template #footer>
      <div class="footer">
        <Button type="text" @click="handleCancel">取消</Button>
        <Button type="primary" :loading="exporting" @click="handleBtnExportStudentsClick">{{
          exporting ? '正在导出' : '导出'
        }}</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { apiUserGetInstitutionStudents } from '@/api/user/student'

  import { deepCopy } from '@/utils/object'
  import { downloadBlob } from '@/utils/download'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      school: {
        type: Object,
        default: () => {},
      },
    },
    emits: ['update:modelValue'],
    data() {
      return {
        exportStageId: 0,
        exportGradeId: 0,
        exportClassId: 0,
        exporting: false,
        gradeList: [],
      }
    },
    computed: {
      schoolStageGradeClassSubjects() {
        return this.$store.state.school.schoolStageGradeClassSubjects
      },
      exportStages() {
        let stages = (this.schoolStageGradeClassSubjects || []).map(stage => ({
          stageId: stage.stageId,
          stageName: stage.stageName,
        }))
        stages.unshift({
          stageId: 0,
          stageName: '全部学段',
        })
        return stages
      },
      exportGradeClasses() {
        if (!this.exportStageId) {
          let gradeClasses = [
            {
              gradeId: 0,
              gradeName: '全部年级',
              classes: [
                {
                  classId: 0,
                  className: '全部班级',
                },
              ],
            },
          ]

          this.schoolStageGradeClassSubjects.forEach(stage => {
            stage.grades.forEach(grade => {
              gradeClasses.push({
                gradeId: grade.gradeId,
                gradeName: grade.gradeName,
                classes: [
                  {
                    classId: 0,
                    className: '全部班级',
                  },
                ].concat(Array.isArray(grade.classes) ? grade.classes : []),
              })
            })
          })

          return gradeClasses
        } else {
          let gradeClasses = deepCopy(
            this.schoolStageGradeClassSubjects.find(stage => stage.stageId === this.exportStageId).grades
          )
          gradeClasses.unshift({
            gradeId: 0,
            gradeName: '全部年级',
          })
          gradeClasses.forEach(grade => {
            if (grade.classes && Array.isArray(grade.classes)) {
              grade.classes.unshift({
                classId: 0,
                className: '全部班级',
              })
            } else {
              grade.classes = [
                {
                  classId: 0,
                  className: '全部班级',
                },
              ]
            }
          })

          return gradeClasses
        }
      },
      exportClasses() {
        let grade = this.exportGradeClasses.find(g => g.gradeId === this.exportGradeId)
        return (grade && grade.classes) || []
      },
      exportGradeClassName() {
        let grade = this.exportGradeClasses.find(g => g.gradeId === this.exportGradeId)
        if (grade.gradeId === 0) {
          return grade.gradeName
        } else {
          if (this.exportClassId === 0) {
            return grade.gradeName
          } else {
            let cls = this.exportClasses.find(c => c.classId === this.exportClassId)
            return cls.className
          }
        }
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.exportGradeId = 0
          this.exportClassId = 0
          this.exporting = false
        }
      },
    },
    methods: {
      handleCancel() {
        this.$emit('update:modelValue', false)
      },
      handleVisibilityChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
      changeExportStage(stageId) {
        this.exportStageId = stageId
        if (
          !(this.schoolStageGradeClassSubjects.find(stage => stage.stageId === stageId).grades || []).some(
            grade => grade.gradeId === this.exportGradeId
          )
        ) {
          this.exportGradeId = 0
        }
        this.exportClassId = 0
      },
      changeExportGrade(gradeId) {
        let grade = this.exportGradeClasses.find(g => g.gradeId === gradeId)
        if (!grade) {
          return
        }

        this.exportGradeId = gradeId
        this.exportClassId = 0
      },
      handleBtnExportStudentsClick() {
        this.exporting = true
        apiUserGetInstitutionStudents({
          schoolId: this.school.value,
          gradeLevel: this.exportStageId || undefined,
          gradeId: this.exportGradeId || undefined,
          classId: this.exportClassId || undefined,
        })
          .then(blob => {
            downloadBlob(blob, `${this.school.title}${this.exportGradeClassName}学生名单.xlsx`)
          })
          .then(() => {
            this.showModalExportStudent = false
          })
          .finally(() => {
            this.exporting = false
          })
      },
    },
  }
</script>
