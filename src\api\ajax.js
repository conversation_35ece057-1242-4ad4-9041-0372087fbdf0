import axios from 'axios'
import { ApiBaseUrl } from '@/config/config'
import store from '@/store/index'
import iView from '@/iview'
import { isFunction } from '@/utils/function'

let lastNetWorkErrorTime = 0

function showApiErrorMessage(requestName, err) {
  if (!err) {
    return
  }

  // 未登录不显示提示
  if (err.code == -401) {
    return
  }

  // 无请求名不显示提示
  if (!requestName) {
    return
  }

  // 网络连接超时5秒内只显示一次
  if (err && err.code == -1) {
    let currentTime = Date.now()
    if (currentTime - lastNetWorkErrorTime > 5 * 1000) {
      lastNetWorkErrorTime = currentTime
      iViewMessageError(err.msg)
    }
  }

  let errDetail = (err && err.msg) || ''
  let msg = (errDetail && `${requestName}失败：${errDetail}`) || ''
  iViewMessageError(msg)

  function iViewMessageError(msg) {
    iView.Message.error({
      content: msg,
      duration: 5,
      closable: true,
    })
  }
}

// axios 配置
axios.defaults.baseURL = ApiBaseUrl
axios.defaults.headers.common['Content-Type'] = 'application/json;charset=UTF-8'
axios.defaults.headers.common['Accept'] = 'application/json'
axios.defaults.validateStatus = status => status === 200

axios.interceptors.request.use(
  config => {
    config.headers.Authentication = (window.localStorage && window.localStorage.getItem('token')) || ''

    // 防止get请求缓存
    if (/get/i.test(config.method)) {
      config.params = config.params || {}

      config.params._t_ = new Date().getTime()
    }

    return config
  },
  err => {
    return Promise.reject(err)
  }
)

// 业务本身错误，code > 0；其他类型错误，code < 0
axios.interceptors.response.use(
  async res => {
    let url = (res.config && res.config.url) || ''
    let error

    if (!res.data) {
      error = {
        code: 9999,
        msg: '服务器无响应数据',
        detail: {
          url,
        },
      }
      return Promise.reject(error)
    }

    // 返回流数据
    if (res.config.responseType === 'blob' || res.config.responseType === 'arraybuffer') {
      // 如果返回json，则请求失败
      let contentType = res.headers['content-type'] || ''
      if (/application\/json/i.test(contentType)) {
        let resText = await res.data.text()
        let resObj = {}
        try {
          resObj = JSON.parse(resText)
        } catch (parseJsonError) {
          resObj = {}
        }
        res.data = resObj
      } else {
        return new Blob([res.data])
      }
    }

    // 返回json数据
    if (res.data.code === 0) {
      return res.data.data
    } else {
      error = {
        code: res.data.code || 9999,
        msg: (res.data.message || res.data.msg || '服务器未返回错误消息').replace(/\n/g, '<br>'),
        detail: {
          url,
          params: (res.config && res.config.params) || undefined,
        },
      }

      // 未登录
      if (error.code === 401) {
        error.code = -401
        store.dispatch('user/logout', {
          locally: true,
          reason: error.msg,
        })
      }
      return Promise.reject(error)
    }
  },
  err => {
    let url = (err.config && err.config.url) || ''
    let error

    // 服务器响应状态码非200
    if (err.response) {
      error = {
        code: 9999,
        msg: '服务器出错了',
        detail: {
          url,
          status: err.response.status,
          response: err.response.data,
        },
      }
    }

    // 已发送请求，服务器无响应
    else if (err.request) {
      error = {
        code: -1,
        msg: '网络连接超时',
        detail: {
          url,
        },
      }
    }

    // 请求未发送
    else {
      error = {
        code: 4,
        msg: '由于系统故障，网络请求发送失败',
        detail: {
          url,
        },
      }
    }

    return Promise.reject(error)
  }
)

const get = ({ url, params, data, requestName }) =>
  axios
    .get(url, {
      params,
      data,
    })
    .catch(err => {
      showApiErrorMessage(requestName, err)
      throw err
    })

const post = ({ url, params, data, requestName, useFormData = false }) =>
  axios
    .post(url, useFormData ? toFormData(data) : data, {
      params,
    })
    .catch(err => {
      showApiErrorMessage(requestName, err)
      throw err
    })

const put = ({ url, params, data, requestName }) =>
  axios
    .put(url, data, {
      params,
    })
    .catch(err => {
      showApiErrorMessage(requestName, err)
      throw err
    })

const del = ({ url, params, data, requestName }) =>
  axios
    .delete(url, {
      params,
      data,
    })
    .catch(err => {
      showApiErrorMessage(requestName, err)
      throw err
    })

const upload = ({ url, params, data, onProgress, requestName, isPostRequest = true, responseBlob = false }) => {
  let formData = toFormData(data)

  let config = {
    headers: {
      'Content-Type': 'multipart/form-data;charset=UTF-8',
    },
    params,
    onUploadProgress: isFunction(onProgress) ? onProgress : null,
  }
  if (responseBlob) {
    config.responseType = 'blob'
  }

  let method = isPostRequest ? axios.post : axios.put

  return method(url, formData, config).catch(err => {
    showApiErrorMessage(requestName, err)
    throw err
  })
}

const download = ({ url, params, data, isFormData, onProgress, requestName }) => {
  let onDownloadProgress = isFunction(onProgress) ? onProgress : null
  let req
  if (data) {
    let sendData = isFormData ? toFormData(data) : data
    req = axios.post(url, sendData, {
      params,
      onDownloadProgress,
      responseType: 'blob',
    })
  } else {
    req = axios.get(url, {
      params,
      onDownloadProgress,
      responseType: 'blob',
    })
  }

  return req.catch(err => {
    showApiErrorMessage(requestName, err)
    throw err
  })
}

function toFormData(object) {
  let formData = new FormData()
  if (object) {
    Object.keys(object).forEach(key => {
      formData.append(key, object[key])
    })
  }
  return formData
}

const request = ({
  requestName,
  url,
  method,
  params,
  data,
  useFormData = false,
  responseBlob = false,
  headers,
  timeout,
  signal,
  onUploadProgress,
  onDownloadProgress,
}) => {
  let config = {
    url,
    method,
  }
  if (params) {
    config.params = params
  }
  if (data) {
    config.data = useFormData ? toFormData(data) : data
  }
  if (responseBlob) {
    config.responseType = 'blob'
  }
  if (headers) {
    config.headers = headers
  }
  if (timeout) {
    config.timeout = timeout
  }
  if (signal) {
    config.signal = signal
  }
  if (onUploadProgress) {
    config.onUploadProgress = onUploadProgress
  }
  if (onDownloadProgress) {
    config.onDownloadProgress = onDownloadProgress
  }
  return axios.request(config).catch(err => {
    showApiErrorMessage(requestName, err)
    throw err
  })
}

export default {
  get,
  post,
  put,
  delete: del,
  upload,
  download,
  request,
}
