import ajax from '@/api/ajax'
import store from '@/store/index'
import { groupArray } from '@/utils/array'
import { addImageUrlParam } from '@/utils/url'
import { formatDate } from '@/utils/date'
import { transformUser } from './exam'
import ExamModeEnum from '@/enum/emarking/exam_mode'
import ExamScopeEnum from '@/enum/emarking/exam_scope'
import ExamTypeEnum from '@/enum/emarking/exam_type'
import ExamUserRoleEnum from '@/enum/emarking/exam_user_role'
import SubjectStatusEnum from '@/enum/emarking/subject_status'
import MarkModeEnum from '@/enum/emarking/mark_mode'
import MarkingWayEnum from '@/enum/emarking/marking_way'
import AssignModeEnum from '@/enum/emarking/assign_mode'

function transformExam(exam) {
  let grades = store.getters['emarking/grades']()
  let subjects = store.getters['emarking/subjects']()

  let creator = {
    ...transformUser(exam.creator || {}),
    examRole: ExamUserRoleEnum.ExamCreator,
  }
  let administrators = (exam.users || exam.managers || []).map(x => ({
    ...transformUser(x),
    examRole: ExamUserRoleEnum.ExamAdmin,
  }))

  // 统计科目进度
  let examSubjects = (exam.subjects || [])
    .map(s => ({
      examSubjectId: s.examSubjectId,
      subjectId: s.subjectId,
      subjectName: (subjects.find(x => x.id == s.subjectId) || { name: '' }).name,
      administrators: (s.users || []).map(x => ({
        ...transformUser(x),
        examRole: ExamUserRoleEnum.SubjectAdmin,
      })),
      status: SubjectStatusEnum.getEntryById(s.status) || { id: s.status, name: '' },
      studentCount: s.studentNum,
      attendStudentCount: s.studentSignCount,
      ...transformSubjectProgress(s),
    }))
    .sort((a, b) => a.subjectId - b.subjectId)

  // 统计考试进度
  let examProgress = {
    marked: exam.marked || 0,
    total: exam.total || 0,
  }
  examProgress.percent = calcPercent(examProgress)

  return {
    examId: exam.examId,
    examName: exam.examName,
    examMode: ExamModeEnum.getEntryById(exam.examMode) || { id: exam.examMode, name: '' },
    examScope: ExamScopeEnum.getEntryById(exam.examScope) || { id: exam.examScope, name: '' },
    examType: ExamTypeEnum.getEntryById(exam.examType) || { id: exam.examType, name: '' },
    grade: grades.find(x => x.id == exam.gradeId) || { id: exam.gradeId, name: '' },
    schools: (exam.schools || []).map(x => ({
      schoolId: x.id || x.schoolId,
      schoolName: x.name || x.schoolName || x.value,
    })),
    creator,
    administrators,
    startDateText: formatDate(new Date(exam.beginTime)),
    endDateText: formatDate(new Date(exam.endTime)),
    progress: examProgress,
    subjects: examSubjects,
  }
}

function transformSubjectProgress(item) {
  let firstProgress = {
    marked: item.firstMarked || 0,
    total: item.firstTotal || 0,
  }
  let secondProgress = {
    marked: item.secondMarked || 0,
    total: item.secondTotal || 0,
  }
  let thirdProgress = {
    marked: item.thirdMarked || 0,
    total: item.thirdTotal || 0,
  }
  let markProgress = {
    marked: item.markMarked || 0,
    total: item.markTotal || 0,
  }
  let abnormalProgress = {
    marked: item.abnormalMarked || 0,
    total: item.abnormalTotal || 0,
  }
  let arbitrateProgress = {
    marked: item.arbitrateMarked || 0,
    total: item.arbitrateTotal || 0,
  }
  let repeatMarkProgress = {
    marked: item.remarked || 0,
    total: item.remarkTotal || 0,
  }
  let generalProgress = {
    marked: markProgress.marked + abnormalProgress.marked + arbitrateProgress.marked + repeatMarkProgress.marked,
    total: markProgress.total + abnormalProgress.total + arbitrateProgress.total + repeatMarkProgress.total,
  }
  firstProgress.percent = calcPercent(firstProgress)
  secondProgress.percent = calcPercent(secondProgress)
  thirdProgress.percent = calcPercent(thirdProgress)
  markProgress.percent = calcPercent(markProgress)
  abnormalProgress.percent = calcPercent(abnormalProgress)
  arbitrateProgress.percent = calcPercent(arbitrateProgress)
  repeatMarkProgress.percent = calcPercent(repeatMarkProgress)
  generalProgress.percent = calcPercent(generalProgress)
  return {
    firstProgress,
    secondProgress,
    thirdProgress,
    markProgress,
    abnormalProgress,
    arbitrateProgress,
    repeatMarkProgress,
    generalProgress,
  }
}

function transformBlockProgress(item) {
  let markProgress = {
    total: item.firstTotal + item.secondTotal + item.thirdTotal,
    marked: item.firstMarked + item.secondMarked + item.thirdMarked,
  }
  markProgress.percent = calcPercent(markProgress)

  let generalProgress = {
    total: markProgress.total + item.abnormalTotal + item.arbitrateTotal + item.remarkTotal,
    marked: markProgress.marked + item.abnormalMarked + item.arbitrateMarked + item.remarked,
  }
  generalProgress.percent = calcPercent(generalProgress)

  let markingWay = MarkingWayEnum.getEntryById(Number(item.markingWay)) || {
    id: Number(item.markingWay),
    name: '',
  }
  let groupId = ''
  let groupName = ''
  let schoolId = ''
  let schoolName = ''
  if ([MarkingWayEnum.Group.id, MarkingWayEnum.GroupAmount.id].includes(markingWay.id)) {
    groupId = item.id || ''
    groupName = item.name || ''
  } else if ([MarkingWayEnum.School.id, MarkingWayEnum.SchoolAmount.id].includes(markingWay.id)) {
    schoolId = item.id || ''
    schoolName = item.name || ''
  }
  return {
    blockId: item.subjectiveId,
    blockName: item.subjectiveName,
    groupId,
    groupName,
    schoolId,
    schoolName,
    markMode: MarkModeEnum.getEntryById(Number(item.evaluationWay)) || {
      id: Number(item.evaluationWay),
      name: '',
    },
    errorScore: item.errorValue,
    doubleRate: item.doubleRate,
    markingWay,
    assignMode: AssignModeEnum.getEntryById(Number(item.assignWay)) || {
      id: Number(item.assignWay),
      name: '',
    },
    fullScore: item.fullScore,
    avgScore: Math.round(item.avgScore * 100) / 100,
    maxScore: item.maxScore,
    minScore: item.minScore,
    firstProgress: {
      total: item.firstTotal,
      marked: item.firstMarked,
      percent: calcPercent({
        marked: item.firstMarked,
        total: item.firstTotal,
      }),
    },
    secondProgress: {
      total: item.secondTotal,
      marked: item.secondMarked,
      percent: calcPercent({
        marked: item.secondMarked,
        total: item.secondTotal,
      }),
    },
    thirdProgress: {
      total: item.thirdTotal,
      marked: item.thirdMarked,
      percent: calcPercent({
        marked: item.thirdMarked,
        total: item.thirdTotal,
      }),
    },
    abnormalProgress: {
      total: item.abnormalTotal,
      marked: item.abnormalMarked,
      percent: calcPercent({
        marked: item.abnormalMarked,
        total: item.abnormalTotal,
      }),
    },
    arbitrateProgress: {
      total: item.arbitrateTotal,
      marked: item.arbitrateMarked,
      percent: calcPercent({
        marked: item.arbitrateMarked,
        total: item.arbitrateTotal,
      }),
    },
    repeatMarkProgress: {
      total: item.remarkTotal,
      marked: item.remarked,
      percent: calcPercent({
        total: item.remarkTotal,
        marked: item.remarked,
      }),
    },
    markProgress,
    generalProgress,
  }
}

function transformUserProgress(item) {
  return {
    blockId: item.subjectiveId,
    blockName: item.subjectiveName,
    userId: item.userId,
    userName: item.userName,
    realName: item.realName,
    schoolId: item.schoolId,
    schoolName: item.schoolName,
    groupId: item.groupId,
    groupName: item.groupName,
    maxNum: item.maxNum,
    markedNum: item.markedNum,
    occupiedNum: item.occupyNum,
    avgScore: Math.round(item.avgScore * 100) / 100,
    maxScore: item.maxScore,
    minScore: item.minScore,
    mobile: item.mobile,
    remarked: item.remarked,
    unRemark: item.remarkTotal - item.remarked,
    remarkTotal: item.remarkTotal,
  }
}

function transformUserScores(userScores) {
  let users = groupArray(userScores, x => x.userId).map(u => ({
    userId: u.group[0].userId,
    realName: u.group[0].realName,
    scores: u.group.map(s => ({
      score: s.score,
      studentCount: s.studentNum,
    })),
  }))
  users.forEach(u => u.scores.sort((a, b) => a.score - b.score))
  return users
}

function calcPercent({ marked, total }) {
  return total > 0 ? Math.min(100, Math.floor((marked / total) * 100)) : 0
}

// 考试列表
export function apiGetMonitorExams({
  semesterId,
  term,
  gradeId,
  examType,
  examScope,
  beginTime,
  endTime,
  keyword,
  currentPage,
  pageSize,
}) {
  return ajax
    .get({
      url: 'mark/monitor/exams2',
      params: {
        semesterId,
        term,
        gradeId,
        examType,
        examScope,
        beginTime,
        endTime,
        key: keyword,
        page: currentPage,
        size: pageSize,
      },
      requestName: '获取监控项目',
    })
    .then(data => {
      return {
        total: data.total,
        records: (data.records || []).map(transformExam),
      }
    })
}

// 科目进度
export function apiGetMonitorExamById(examId) {
  return ajax
    .get({
      url: 'mark/monitor/exam',
      params: {
        examId,
      },
      requestName: '获取科目进度',
    })
    .then(transformExam)
}

// 科目下评卷组进度
export function apiGetMonitorSubjectGroups(params) {
  return ajax
    .get({
      url: 'mark/monitor/groupProgress',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
      },
      requestName: '获取评卷组进度',
    })
    .then(groups => {
      return (groups || []).map(g => {
        return {
          groupName: g.name,
          status: g.status,
          studentCount: g.studentNum,
          attendStudentCount: g.studentSignCount,
          blocks: (g.groupBlockMaxMarkNumVoList || []).map(block => ({
            blockId: block.blockId,
            blockName: block.blockName,
            amount: block.maxMarkNum || 0,
          })),
          ...transformSubjectProgress(g),
        }
      })
    })
}

// 单个评卷组进度
export function apiGetMonitorSubjectSingleGroup(params) {
  return ajax
    .get({
      url: 'mark/monitor/oneGroupProgress',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
        groupId: params.groupId,
      },
      requestName: '获取评卷组进度',
    })
    .then(groups => {
      return (groups || []).map(g => {
        return {
          groupName: g.name,
          status: g.status,
          studentCount: g.studentNum,
          attendStudentCount: g.studentSignCount,
          blocks: (g.groupBlockMaxMarkNumVoList || []).map(block => ({
            blockId: block.blockId,
            blockName: block.blockName,
            amount: block.maxMarkNum || 0,
          })),
          ...transformSubjectProgress(g),
        }
      })
    })
}

// 科目下学校进度
export function apiGetMonitorSubjectSchools(params) {
  return ajax
    .get({
      url: 'mark/monitor/schoolProgress',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
      },
      requestName: '获取学校进度',
    })
    .then(schools => {
      return (schools || []).map(s => {
        return {
          schoolName: s.name,
          status: s.status,
          studentCount: s.studentNum,
          attendStudentCount: s.studentSignCount,
          amount: s.maxMarkNum || 0,
          ...transformSubjectProgress(s),
        }
      })
    })
}

// 单个学校进度
export function apiGetMonitorSubjectSingleSchool(params) {
  return ajax
    .get({
      url: 'mark/monitor/oneSchoolProgress',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
        schoolId: params.schoolId,
      },
      requestName: '获取学校进度',
    })
    .then(schools => {
      return (schools || []).map(s => {
        return {
          schoolName: s.name,
          status: s.status,
          studentCount: s.studentNum,
          attendStudentCount: s.studentSignCount,
          amount: s.maxMarkNum || 0,
          ...transformSubjectProgress(s),
        }
      })
    })
}

// 科目下题块进度
export function apiGetMonitorSubjectBlocks(params) {
  return ajax
    .get({
      url: 'mark/monitor/subject',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
      },
      requestName: '获取题块进度',
    })
    .then(blocks => {
      return (blocks || []).map(transformBlockProgress)
    })
}

// 评卷组下题块进度
export function apiGetMonitorSubjectGroupBlocks(params) {
  return ajax
    .get({
      url: 'mark/monitor/subjectGroupProgress',
      params: {
        examSubjectId: params.examSubjectId,
        groupId: params.groupId,
      },
      requestName: '获取评卷组题块进度',
    })
    .then(blocks => {
      return (blocks || []).map(item => {
        return {
          groupId: item.id,
          groupName: item.name,
          ...transformBlockProgress(item),
        }
      })
    })
}

// 学校下题块进度
export function apiGetMonitorSubjectSchoolBlocks(params) {
  return ajax
    .get({
      url: 'mark/monitor/subjectSchoolProgress',
      params: {
        examSubjectId: params.examSubjectId,
        schoolId: params.schoolId,
      },
      requestName: '获取学校题块进度',
    })
    .then(blocks => {
      return (blocks || []).map(item => {
        return {
          schoolId: item.id,
          schoolName: item.name,
          ...transformBlockProgress(item),
        }
      })
    })
}

// 科目下评卷员进度
export function apiGetMonitorSubjectBlockUsers(params) {
  return ajax
    .get({
      url: `mark/monitor/user`,
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
        subjectiveId: params.blockId,
      },
      requestName: '获取用户进度',
    })
    .then(users =>
      groupArray(users, x => x.subjectiveId).map(g => ({
        blockId: g.key,
        users: g.group.map(transformUserProgress),
      }))
    )
}

// 评卷组下评卷员进度
export function apiGetMonitorSubjectGroupBlockUsers(params) {
  return ajax
    .get({
      url: `mark/monitor/groupUser`,
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
        subjectiveId: params.blockId,
        groupId: params.groupId,
      },
      requestName: '获取评卷组用户进度',
    })
    .then(users =>
      groupArray(users, x => x.subjectiveId).map(g => ({
        blockId: g.key,
        users: g.group.map(transformUserProgress),
      }))
    )
}

// 学校下评卷员进度
export function apiGetMonitorSubjectSchoolBlockUsers(params) {
  return ajax
    .get({
      url: `mark/monitor/schoolUser`,
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
        subjectiveId: params.blockId,
        schoolId: params.schoolId,
      },
      requestName: '获取学校用户进度',
    })
    .then(users =>
      groupArray(users, x => x.subjectiveId).map(g => ({
        blockId: g.key,
        users: g.group.map(transformUserProgress),
      }))
    )
}

// 科目下评分分布
export function apiGetMonitorSubjectBlockScores(params) {
  return ajax
    .get({
      url: `mark/monitor/score`,
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
        subjectiveId: params.blockId,
      },
      requestName: '获取题块评分分布',
    })
    .then(transformUserScores)
}

// 评卷组下评分分布
export function apiGetMonitorSubjectGroupBlockScores(params) {
  return ajax
    .get({
      url: `mark/monitor/groupScore`,
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
        subjectiveId: params.blockId,
        groupId: params.groupId,
      },
      requestName: '获取评卷组题块评分分布',
    })
    .then(transformUserScores)
}

// 学校下评分分布
export function apiGetMonitorSubjectSchoolBlockScores(params) {
  return ajax
    .get({
      url: `mark/monitor/schoolScore`,
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
        subjectiveId: params.blockId,
        schoolId: params.schoolId,
      },
      requestName: '获取学校题块评分分布',
    })
    .then(transformUserScores)
}

export function apiGetProblemPaper(requestParams) {
  return ajax
    .get({
      url: 'mark/monitor/problemPaper',
      params: {
        blockId: requestParams.blockId, // [String]
        page: requestParams.currentPage, // [Integer]
        size: requestParams.pageSize, // [Integer]
        sign: requestParams.sign, // falsy - 全部；1 - 待处理； 2 - 处理中； 3 - 已处理
      },
      requestName: '获取问题卷记录',
    })
    .then(data => {
      if (data && data.records && data.records.length > 0) {
        data.records.forEach(item => {
          item.imgUrl = addImageUrlParam(item.imgUrl)
        })
      }

      return data
    })
}

export function apiGetArbitratePaper(requestParams) {
  return ajax
    .get({
      url: 'mark/monitor/arbitratePaper',
      params: {
        blockId: requestParams.blockId, // [Stirng]
        page: requestParams.currentPage, // [Integer]
        size: requestParams.pageSize, // [Integer]
        sign: requestParams.sign, // falsy - 全部; 1 - 待处理; 2 - 处理中; 3 - 已处理
      },
      requestName: '获取仲裁卷记录',
    })
    .then(data => {
      if (data && data.records && data.records.length > 0) {
        data.records.forEach(item => {
          item.imgUrl = addImageUrlParam(item.imgUrl)
        })
      }

      return data
    })
}

export function apiRecycleProblemOrArbitratePaper(requestParams) {
  return ajax.get({
    url: 'mark/monitor/recycle',
    params: {
      subjectiveId: requestParams.subjectiveId,
      subjectiveItemId: requestParams.subjectiveItemId,
    },
    requestName: '回收问题卷/仲裁卷',
  })
}

// 获取监控可见学校
export function apiGetMonitorVisibleSchools(examSubjectId) {
  return ajax
    .get({
      url: 'mark/monitor/examSchoolList',
      params: {
        examSubjectId,
      },
      requestName: '获取考试学校',
    })
    .then(data =>
      (data || []).map(s => ({
        schoolId: s.id,
        schoolName: s.name,
      }))
    )
}

export function apiGetPaperBySubjectiveId(params) {
  return ajax.get({
    url: 'mark/mark/problem/listOriginPaperBySubjectiveId',
    params: {
      subjectiveId: params.subjectiveId,
    },
    requestName: '获取该题块所有问题卷',
  })
}

export function apiGetMonitorAbnormalProgress(requestParams) {
  return ajax.get({
    url: 'mark/monitor/abnormalProgress',
    params: {
      examSubjectId: requestParams.examSubjectId, // *required [string]
      subjectiveId: requestParams.subjectiveId, // [string] 题块Id
    },
    requestParams: '获取评卷监控问题卷处理进度',
  })
}
