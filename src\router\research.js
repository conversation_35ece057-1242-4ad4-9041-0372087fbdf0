export default {
  name: 'research',
  path: 'research',
  meta: {
    title: '精准教研',
    menu: '精准教研',
    name: '精准教研',
    roles: user => {
      return user.schoolType == 1
    },
  },
  component: () => import('@/views/research/index.vue'),
  redirect: { name: 'trace_school' },
  children: [
    {
      name: 'trace_school',
      path: 'trace_school',
      meta: {
        menu: '学校跟踪',
      },
      component: () => import('@/views/research/trace_school/index.vue'),
      children: [
        {
          name: 'trace_school_home',
          path: 'trace_school_home',
          meta: {
            menu: 'trace_school_home',
          },
          component: () => import('@/views/research/trace_school/home.vue'),
        },
      ],
    },
    {
      name: 'trace_class',
      path: 'trace_class',
      meta: {
        menu: '班级跟踪',
      },
      component: () => import('@/views/research/trace_class/index.vue'),
      children: [
        {
          name: 'trace_class_home',
          path: 'trace_class_home',
          meta: {
            menu: 'trace_class_home',
          },
          component: () => import('@/views/research/trace_class/home.vue'),
        },
      ],
    },
    {
      name: 'trace_student',
      path: 'trace_student',
      meta: {
        menu: '学生跟踪',
      },
      component: () => import('@/views/research/trace_student/index.vue'),
      children: [
        {
          name: 'trace_student_home',
          path: 'trace_student_home',
          meta: {
            menu: 'trace_student_home',
          },
          component: () => import('@/views/research/trace_student/home.vue'),
        },
      ],
    },
    {
      name: 'increment_comment',
      path: 'increment_comment',
      meta: {
        menu: '增值评价',
      },
      component: () => import('@/views/research/increment_comment/index.vue'),
      children: [
        {
          name: 'increment_comment_home',
          path: 'increment_comment_home',
          meta: {
            menu: 'increment_comment_home',
          },
          component: () => import('@/views/research/increment_comment/home.vue'),
        },
        {
          name: 'comment_template',
          path: 'comment_template',
          meta: {
            menu: '评价模板',
          },
          component: () => import('@/views/research/comment_template/index.vue'),
          children: [
            {
              name: 'comment_template_home',
              path: 'comment_template_home',
              meta: {
                menu: 'comment_template_home',
              },
              component: () => import('@/views/research/comment_template/home.vue'),
            },
            {
              name: 'comment_template_edit',
              path: 'comment_template_edit/:templateId?',
              meta: {
                menu: 'comment_template_edit',
              },
              component: () => import('@/views/research/comment_template/comment_template_edit.vue'),
            },
          ],
        },
        {
          name: 'increment_comment_details',
          path: 'increment_comment_details/:projectId?',
          meta: {
            menu: 'increment_comment_details',
          },
          component: () => import('@/views/research/increment_comment/comment_details.vue'),
        },
      ],
    },
  ],
}
