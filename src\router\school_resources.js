import Role from '@/enum/user/role'

export default {
  name: 'school-resources',
  path: 'resources',
  meta: {
    name: '学校资源',
  },
  component: () => import('@/views/school_resources/index.vue'),
  children: [
    {
      name: 'school-resources-book',
      path: 'book',
      meta: {
        menu: '定制教辅',
      },
      redirect: {
        name: 'school-resources-book-list',
      },
      component: () => import('@/views/school_resources/book/index.vue'),
      children: [
        {
          name: 'school-resources-book-list',
          path: 'list',
          component: () => import('@/views/school_resources/book/list.vue'),
        },
        {
          name: 'school-resources-book-paper',
          path: 'paper/:coachBookId',
          component: () => import('@/views/school_resources/book/paper.vue'),
        },
        {
          name: 'school-resources-book-exercise',
          path: 'exercise/:coachBookId',
          component: () => import('@/views/school_resources/book/exercise.vue'),
        },
      ],
    },
    {
      name: 'school-resources-activation-code',
      path: 'activationCode',
      meta: {
        menu: '教辅激活码',
        roles: [Role.SchoolAdministrator.id],
      },
      component: () => import('@/views/school_resources/activation_code/index.vue'),
      children: [
        {
          name: 'school-resources-activation-code-book',
          path: 'book',
          meta: {
            menu: '教辅统计',
          },
          props: true,
          component: () => import('@/views/school_resources/activation_code/book.vue'),
        },
        {
          name: 'school-resources-activation-code-school',
          path: 'school',
          meta: {
            menu: '学校统计',
          },
          props: true,
          component: () => import('@/views/school_resources/activation_code/school.vue'),
        },
        {
          name: 'school-resources-activation-code-list',
          path: 'list',
          meta: {
            menu: '激活码列表',
          },
          params: {
            initParams: null,
          },
          props: true,
          component: () => import('@/views/school_resources/activation_code/code.vue'),
        },
      ],
    },
  ],
}
