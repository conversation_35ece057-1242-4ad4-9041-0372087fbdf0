<script>
  import { apiGetShareTargetUsers } from '@/api/qlib'
  import { apiGetSharedAnswerSheetList, apiShareAnswerSheet } from '@/api/emarking/answer_sheet'

  import { debounce } from '@/utils/function'
  import { formatDateTime } from '@/utils/date'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      answerSheet: {
        type: Object,
        default: () => {},
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        selectedTeacherId: '',
        shareHistorys: [],
        sharableUsers: [],
        fetchingSharableUsers: false,
      }
    },

    computed: {
      userId() {
        return this.$store.getters['user/info'].userId
      },
      currentSchoolId() {
        return this.$store.getters['user/info'].schoolId
      },
      shareHistoryText() {
        return this.shareHistorys.map(
          p => p.teacherName + (p.shareTime ? '（' + formatDateTime(new Date(p.shareTime)) + '）' : '')
        )
      },
    },

    methods: {
      handleModalCancel() {
        this.$emit('update:modelValue', false)
      },
      handleVisibleChange(visibility) {
        if (visibility) {
          this.selectedTeacherId = ''
          this.shareHistorys = []
          this.sharableUsers = []
          this.fetchingSharableUsers = false

          this.loadShareHistory()
        } else {
          this.handleModalCancel()
        }
      },

      loadShareHistory() {
        if (!this.answerSheet) {
          return
        }
        apiGetSharedAnswerSheetList(this.answerSheet.id).then(historys => (this.shareHistorys = historys))
      },

      handleModalOK() {
        apiShareAnswerSheet({
          answerSheetId: this.answerSheet.id,
          teacherId: this.selectedTeacherId,
        })
          .then(() => {
            this.$Message.success({
              duration: 4,
              content: '分享成功',
            })
            this.selectedTeacherId = ''
            this.handleModalCancel()
          })
          .finally(this.loadShareHistory)
      },
      fetchSharableUsers(keyword) {
        keyword = (keyword || '').trim()
        if (this.sharableUsers.some(t => t.label == keyword)) {
          return
        }
        this.fetchingSharableUsers = true
        this.debouncedSearch(keyword)
      },
      debouncedSearch: debounce(function (keyword) {
        if (!keyword) {
          this.sharableUsers = []
        }

        this.fetchingSharableUsers = true
        apiGetShareTargetUsers({
          keyword: keyword,
          schoolId: this.currentSchoolId,
        })
          .then(users => {
            let sharableUsers = users.filter(u => u.teacherId !== this.userId)
            sharableUsers.forEach(t => {
              t.label = t.realName + (t.mobile ? '（' + t.mobile + '）' : '')
            })
            this.sharableUsers = sharableUsers
          })
          .finally(() => {
            this.fetchingSharableUsers = false
          })
      }, 500),
    },
  }
</script>

<template>
  <Modal
    class="modal-answer-sheet-picture"
    :model-value="modelValue"
    title="分享答题卡"
    @on-visible-change="handleVisibleChange"
  >
    <div class="form">
      <div class="form-item">
        <div class="form-item-label">答题卡名称</div>
        <span>{{ answerSheet && answerSheet.name }}</span>
      </div>
      <div class="form-item">
        <div class="form-item-label">选择教师</div>
        <Select
          v-model="selectedTeacherId"
          class="form-selector"
          placeholder="输入姓名搜索"
          filterable
          clearable
          :remote-method="fetchSharableUsers"
          :loading="fetchingSharableUsers"
        >
          <Option v-for="t of sharableUsers" :key="t.userId" :value="t.userId" :label="t.label"></Option>
        </Select>
      </div>
      <div class="form-item" style="align-items: stretch">
        <div class="form-item-label">分享历史</div>
        <div class="history-content">
          <div v-for="item of shareHistoryText" :key="item" class="shared">{{ item }}</div>
        </div>
      </div>
    </div>

    <template #footer>
      <Button type="text" @click="handleModalCancel">取消</Button>
      <Button type="primary" :disabled="!selectedTeacherId" @click="handleModalOK">确定</Button>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-answer-sheet-picture {
    .form {
      .form-item {
        @include flex(row, flex-start, center);
        margin-bottom: 20px;

        .form-item-label {
          min-width: 6em;
          margin-right: 20px;
          text-align: right;
        }

        .form-selector {
          width: 300px;
        }
      }
    }
  }
</style>
