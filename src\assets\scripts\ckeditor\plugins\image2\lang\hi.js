/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'hi', {
	alt: 'वैकल्पिक टेक्स्ट',
	btnUpload: 'इसे सर्वर को भेजें',
	captioned: 'Captioned image', // MISSING
	captionPlaceholder: 'Caption', // MISSING
	infoTab: 'तस्वीर की जानकारी',
	lockRatio: 'लॉक अनुपात',
	menu: 'तस्वीर प्रॉपर्टीज़',
	pathName: 'image', // MISSING
	pathNameCaption: 'caption', // MISSING
	resetSize: 'रीसॅट साइज़',
	resizer: 'Click and drag to resize', // MISSING
	title: 'तस्वीर प्रॉपर्टीज़',
	uploadTab: 'अपलोड',
	urlMissing: 'Image source URL is missing.', // MISSING
	altMissing: 'Alternative text is missing.' // MISSING
} );
