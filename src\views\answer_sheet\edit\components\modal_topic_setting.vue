<template>
  <Modal
    class="modal-topic-setting no-print"
    :model-value="modelValue"
    title="大题设置"
    :mask-closable="false"
    @on-visible-change="handleVisibleChange"
  >
    <Form :label-width="90" label-position="left">
      <FormItem label="隐藏材料">
        <i-switch v-model="hideTrunkQuestionGroup"></i-switch>
      </FormItem>
      <FormItem v-if="showOptionLineHeight" label="选项行高">
        <InputNumber v-model="optionLineHeight" :min="0" :max="3" :step="0.1" :precision="1"></InputNumber>
      </FormItem>
      <FormItem v-if="showOptionPaddingLeft" label="选项左边距">
        <InputNumber v-model="optionPaddingLeft" :min="0" :max="100" :step="1" :precision="0"></InputNumber>
      </FormItem>
    </Form>
    <template #footer>
      <div>
        <Button type="text" @click="handleModalCancel">取消</Button>
        <Button type="primary" @click="handleModalOK">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { showScrollbarAfterHideModal } from '@/utils/iview'
  import QuestionGroupTypeEnum from '@/enum/answer_sheet/question_group_type'

  export default {
    props: {
      modelValue: Boolean,
      topic: Object,
    },
    emits: ['update:modelValue'],
    data() {
      return {
        hideTrunkQuestionGroup: false,
        optionLineHeight: 1.5,
      }
    },
    computed: {
      firstContentObjectiveQuestionGroup() {
        return this.topic.questionGroups.find(g => g.type == QuestionGroupTypeEnum.ContentObjective.id)
      },
      showOptionLineHeight() {
        return Boolean(this.firstContentObjectiveQuestionGroup)
      },
      showOptionPaddingLeft() {
        return Boolean(this.firstContentObjectiveQuestionGroup)
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.init()
        }
      },
    },
    methods: {
      init() {
        this.hideTrunkQuestionGroup = this.topic.hideTrunkQuestionGroup
        if (this.showOptionLineHeight) {
          this.optionLineHeight = this.firstContentObjectiveQuestionGroup.optionLineHeight || 1.5
        } else {
          this.optionLineHeight = 1.5
        }
        if (this.showOptionPaddingLeft) {
          this.optionPaddingLeft = this.firstContentObjectiveQuestionGroup.optionPaddingLeft || 31
        } else {
          this.optionPaddingLeft = 31
        }
      },
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.handleModalCancel()
        }
      },
      handleModalCancel() {
        this.$emit('update:modelValue', false)
        setTimeout(() => {
          showScrollbarAfterHideModal()
        }, 500)
      },
      handleModalOK() {
        let needGeneratePages = false
        if (Boolean(this.hideTrunkQuestionGroup) != Boolean(this.topic.hideTrunkQuestionGroup)) {
          this.$store.commit('answerSheet/toggleShowTrunkQuestionGroup', this.topic.code)
          needGeneratePages = true
        }
        if (
          this.showOptionLineHeight &&
          this.optionLineHeight != this.firstContentObjectiveQuestionGroup.optionLineHeight
        ) {
          this.topic.questionGroups.forEach(g => {
            if (g.type == QuestionGroupTypeEnum.ContentObjective.id) {
              g.optionLineHeight = this.optionLineHeight
            }
          })
          needGeneratePages = true
        }
        if (
          this.showOptionPaddingLeft &&
          this.optionPaddingLeft != this.firstContentObjectiveQuestionGroup.optionPaddingLeft
        ) {
          this.topic.questionGroups.forEach(g => {
            if (g.type == QuestionGroupTypeEnum.ContentObjective.id) {
              g.optionPaddingLeft = this.optionPaddingLeft
            }
          })
        }
        if (needGeneratePages) {
          this.$store.commit('answerSheet/generatePages')
        }
        this.handleModalCancel()
      },
    },
  }
</script>
