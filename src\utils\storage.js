/**
 * 存储类，提供对本地存储和会话存储的增强操作
 * 支持对整个对象的操作和对象字段的操作
 */
class EnhancedStorage {
  constructor(storageType = 'local') {
    this.storage = storageType === 'local' ? window.localStorage : window.sessionStorage
  }

  /**
   * 设置键值对象
   * @param {string} key 键名
   * @param {any} value 值对象
   */
  setItem(key, value) {
    if (value == null) {
      throw new Error('不能设置空值')
    }
    if (typeof value === 'object') {
      this.storage.setItem(key, JSON.stringify(value))
    } else {
      this.storage.setItem(key, value)
    }
  }

  /**
   * 获取键值对象
   * @param {string} key 键名
   * @returns {any} 存储的值
   */
  getItem(key) {
    const value = this.storage.getItem(key)
    if (!value) {
      return value
    }

    try {
      return JSON.parse(value)
    } catch {
      // 如果解析失败说明不是JSON字符串，直接返回值
      return value
    }
  }

  /**
   * 删除键值对象
   * @param {string} key 键名
   */
  removeItem(key) {
    this.storage.removeItem(key)
  }

  /**
   * 设置键值对象的某个字段
   * @param {string} key 键名
   * @param {string} field 字段名
   * @param {any} value 字段值
   */
  setField(key, field, value) {
    let obj = this.getItem(key)
    if (obj == null) {
      obj = {}
    }
    if (typeof obj !== 'object') {
      throw new Error('不能设置非对象的键值')
    }
    obj[field] = value
    this.setItem(key, obj)
  }

  /**
   * 获取键值对象的某个字段
   * @param {string} key 键名
   * @param {string} field 字段名
   * @returns {any} 字段值
   */
  getField(key, field) {
    const obj = this.getItem(key)
    if (obj == null) {
      return undefined
    }
    if (typeof obj !== 'object') {
      throw new Error('不能获取非对象的键值')
    }
    return obj[field]
  }

  /**
   * 删除键值对象的某个字段
   * @param {string} key 键名
   * @param {string} field 字段名
   */
  removeField(key, field) {
    const obj = this.getItem(key)
    if (obj == null) {
      return
    }
    if (typeof obj !== 'object') {
      throw new Error('不能删除非对象的键值')
    }
    if (obj[field] !== undefined) {
      delete obj[field]
      this.setItem(key, obj)
    }
  }

  /**
   * 清空存储
   */
  clear() {
    this.storage.clear()
  }
}

// 创建本地存储实例
export const localStore = new EnhancedStorage('local')

// 创建会话存储实例
export const sessionStore = new EnhancedStorage('session')
