import { apiGetSysSemster, apiGetClassList, apiGetGraduatedClassList } from '@/api/report'

export default {
  namespaced: true,
  state: {
    sysSemster: [],
    classList: [],
    graduatedClassList: [],
    classInfo: [],
    classSelectedData: [],
    sysSemsterItem: null,
    templateIds: [],
  },
  getters: {
    stages(state, getters, rootState, rootGetters) {
      return rootGetters['school/schoolGradeClassSubjects']
    },
  },
  mutations: {},
  actions: {
    getSysSemster(context) {
      return apiGetSysSemster().then(data => {
        context.state.sysSemster = data
        return data
      })
    },
    getClassList(context) {
      return apiGetClassList()
        .then(data => {
          context.state.classList = data
          return data
        })
        .catch(() => {
          context.state.classList = []
          return []
        })
    },
    getGraduatedClassList(context, params) {
      return apiGetGraduatedClassList(params)
        .then(data => {
          context.state.graduatedClassList = data
          return data
        })
        .catch(() => {
          context.state.graduatedClassList = []
          return []
        })
    },

    setClassInfo(context, params) {
      context.state.classInfo = params.classInfo
    },
    setClassSelectedData(context, params) {
      context.state.classSelectedData = params.classSelectedData
    },
    setSysSemsterItem(context, params) {
      context.state.sysSemsterItem = params.sysSemsterItem
    },
    setTemplateIds(context, params) {
      context.state.templateIds = params.templateIds
    },
  },
}
