<template>
  <div class="container-answer-sheet">
    <router-view></router-view>
  </div>
</template>

<script>
  import iView from '@/iview'
  import store from '@/store/index'

  export default {
    beforeRouteEnter(to, from, next) {
      iView.LoadingBar.start()
      Promise.all([
        store.dispatch('common/getRegions'),
        store.dispatch('qlib/getBasicData'),
        store.dispatch('emarking/getBasicData'),
        store.dispatch('school/getSchoolStageGradeSubjectsParameters'),
      ])
        .then(() => {
          next()
        })
        .finally(() => {
          iView.LoadingBar.finish()
        })
    },
    watch: {
      $route() {
        this.navToHome()
      },
    },
    created() {
      this.navToHome()
    },
    methods: {
      navToHome() {
        if (this.$route.name === 'answerSheet') {
          this.$router.replace({
            name: 'answerSheet-list',
          })
        }
      },
    },
  }
</script>
