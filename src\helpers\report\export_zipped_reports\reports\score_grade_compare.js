import { apiGetScoreGradesCompare } from '@/api/report'

import { generateExcelBlob } from '@/utils/excel_export'
import { groupArray } from '@/utils/array'

import { UUID_ZERO } from '@/const/string'
import statsWays from '@/enum/report/stats_way'
import { getScoreRanges } from '../../tools/tools'

import Store from '@/store/index'

// 成绩等级结构对比
export function generateExcelReportScoreGradeCompareBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let fileName = Store.getters['report/examName'] + '_'
  let requestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    subjectId: (subject && subject.subjectId) || undefined,
    examSubjectId: (subject && subject.examSubjectId) || UUID_ZERO,
  }

  if (category) {
    const CombinationName = (combination && combination.subjectName) || ''
    requestParams.categoryId = category.categoryId
    requestParams.examSubjectId = (combination && combination.examSubjectId) || UUID_ZERO

    if (isMultipleSchoolLevel) {
      if (school) {
        requestParams.schoolId = school.schooId
        fileName = `学校报表/${school.schoolName}/${category.categoryName}/${
          CombinationName ? CombinationName + '/' : ''
        }${fileName}班级成绩等级结构对比_${school.schoolName}_${category.categoryName}${
          CombinationName ? '_' + CombinationName : ''
        }`
      } else {
        requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']
        fileName = `${category.categoryName}/${
          CombinationName ? CombinationName + '/' : ''
        }${fileName}学校成绩等级结构对比_联考_${category.categoryName}${CombinationName ? '_' + CombinationName : ''}`
      }
    } else {
      requestParams.schoolId = Store.getters['report/currentSchoolId']
      fileName = `${category.categoryName}/${
        CombinationName ? CombinationName + '/' : ''
      }${fileName}班级成绩等级结构对比_${Store.getters['report/currentSchoolName']}_${category.categoryName}${
        CombinationName ? '_' + CombinationName : ''
      }`
    }
  } else {
    if (isMultipleSchoolLevel) {
      if (isNextLevel) {
        if (school) {
          fileName = subject
            ? `下级机构或学校报表/(学校)${school.schoolName}/${subject.subjectName}/${fileName}班级成绩等级结构对比_${school.schoolName}_${subject.subjectName}`
            : `下级机构或学校报表/(学校)${school.schoolName}/${fileName}班级成绩等级结构对比_${school.schoolName}`
          requestParams.schoolId = school.schoolId
        } else {
          fileName = subject
            ? `下级机构或学校报表/(机构)${institution.name}/${subject.subjectName}/${fileName}学校成绩等级结构对比_${institution.name}_${subject.subjectName}`
            : `下级机构或学校报表/(机构)${institution.name}/${fileName}学校成绩等级结构对比_${institution.name}`
          requestParams.organizationId = institution.id
        }
      } else {
        if (school) {
          fileName = subject
            ? `学校报表/${school.schoolName}/${subject.subjectName}/${fileName}班级成绩等级结构对比_${school.schoolName}_${subject.subjectName}`
            : `学校报表/${school.schoolName}/${fileName}班级成绩等级结构对比_${school.schoolName}`
          requestParams.schoolId = school.schoolId
        } else {
          fileName = subject
            ? `${subject.subjectName}/${fileName}学校成绩等级结构对比_联考_${subject.subjectName}`
            : `${fileName}学校成绩等级结构对比_联考`
          requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']
        }
      }
    } else {
      fileName = subject
        ? `${subject.subjectName}/${fileName}班级成绩等级结构对比_${Store.getters['report/currentSchoolName']}_${subject.subjectName}`
        : `${fileName}班级成绩等级结构对比_${Store.getters['report/currentSchoolName']}`
      requestParams.schoolId = Store.getters['report/currentSchoolId']
    }
  }

  fileName += '.xlsx'

  return fetchSheets(requestParams)
    .then(sheets => generateExcelBlob(sheets))
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}

async function fetchSheets(requestParams) {
  let scoreRanges = getScoreRanges()
  let adaptedRange = scoreRanges.find(range => range.subjectId === (requestParams.subjectId || 0)) || scoreRanges[0]
  let tableData = await apiGetScoreGradesCompare(requestParams)

  if (tableData && tableData.length && adaptedRange && adaptedRange.range && adaptedRange.range.length) {
    let mapStatsWays = Object.keys(statsWays).map(x => statsWays[x])
    const ExamSchools = Store.getters['report/examSchools']
    let classBelongCurrentSchool = (
      (requestParams.schoolId && ExamSchools.find(x => x.schoolId === requestParams.schoolId)) || {
        classes: [],
      }
    ).classes

    let tableColumns = [
      {
        title: requestParams.organizationId ? '学校' : '班级',
        key: 'itemName',
        width: 'auto',
      },
      {
        title: '统计人数',
        key: 'countNum',
        width: 12,
      },
    ]
    tableColumns.push(
      ...adaptedRange.range.map(r => ({
        title: r.name,
        children: [
          {
            title: mapStatsWays.find(x => x.id === adaptedRange.statsWay).name,
            width: 14,
            key: row => (row.scoreGrades.find(g => g.name === r.name) || { range: '(-, -]' }).range,
          },
          {
            title: '学生人数',
            width: 14,
            key: row => (row.scoreGrades.find(g => g.name === r.name) || { num: 0 }).num,
          },
          {
            title: '学生比例',
            width: 14,
            key: row => {
              const ScoreGrade = row.scoreGrades.find(sg => sg.name === r.name)
              let value = '-'
              if (ScoreGrade) {
                value = (ScoreGrade.rate && Number(ScoreGrade.rate)) || 0
              }
              return value
            },
            cellNumberFormat: value =>
              isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%',
          },
        ],
      }))
    )

    tableData = requestParams.organizationId
      ? groupArray(tableData, x => x.schoolId)
      : groupArray(tableData, x => x.classId)
    tableData = tableData.map(x => {
      let itemCode = 100
      let itemName = ''
      let scoreGrades = []

      if (requestParams.organizationId) {
        if (x.key === UUID_ZERO) {
          itemName = '联考'
          itemCode = -1
        } else {
          itemName = ExamSchools.find(school => school.schoolId === x.key)
          itemCode = ExamSchools.findIndex(school => school.schoolId === x.key)
          itemName = itemName ? itemName.schoolName : '-'
        }
      } else {
        if (x.key === UUID_ZERO) {
          itemName = '本校'
          itemCode = -1
        } else {
          itemName = (classBelongCurrentSchool.find(cls => cls.classId === x.key) || { className: '-' }).className
          itemCode = classBelongCurrentSchool.findIndex(cls => cls.classId === x.key)
        }
      }

      adaptedRange.range.forEach(r => {
        let scoreGrade = x.group.find(g => g.scoreGradeName === r.name)
        if (scoreGrade) {
          scoreGrade = {
            name: r.name,
            num: scoreGrade.scoreGradeNum,
            rate: scoreGrade.scoreGradeRate,
            range: r.value,
            countNum: scoreGrade.countNum || 0,
          }
        } else {
          scoreGrade = {
            name: r.name,
            num: 0,
            rate: 0,
            range: r.value,
            countNum: 0,
          }
        }
        scoreGrades.push(scoreGrade)
      })

      return {
        itemId: x.key,
        itemName: itemName,
        itemCode: itemCode,
        scoreGrades: scoreGrades,
        countNum: scoreGrades[0].countNum,
      }
    })
    tableData.sort((a, b) => a.itemCode - b.itemCode)

    return [
      {
        sheetName: tableColumns[0].title + '成绩等级结构对比',
        rows: tableData,
        columns: tableColumns,
      },
    ]
  } else {
    throw {
      code: 9998,
      msg: '暂无数据',
    }
  }
}
