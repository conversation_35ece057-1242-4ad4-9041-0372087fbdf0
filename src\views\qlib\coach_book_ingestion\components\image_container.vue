<template>
  <div class="image-container">
    <div class="container-main">
      <div class="image-wrapper" :style="imageWrapperStyle">
        <div
          v-for="item in imageList"
          :key="item.url"
          class="image-item"
          :class="getImageItemStyle(item)"
          :draggable="props.enableReorder"
          @dragstart="handleDragStart(item)"
          @dragenter="handleDragEnter(item, $event)"
          @dragover.prevent="handleDragOver(item, $event)"
          @dragleave="handleDragLeave"
          @drop="handleDrop(item, $event)"
          @dragend="handleDragEnd"
          @click="handleImageItemClick(item, $event)"
        >
          <img class="page-image" :src="item.url" loading="lazy" :style="item.imageStyle" />
          <div class="image-name">{{ item.name }}</div>
        </div>
      </div>
    </div>
    <div class="container-aside">
      <div class="toolbar">
        <div class="btn" title="放大图片" @click="zoomImage('in')">
          <img src="@/assets/images/common/zoom_in.svg" style="width: 16px" />
        </div>
        <div class="btn" title="缩小图片" @click="zoomImage('out')">
          <img src="@/assets/images/common/zoom_out.svg" style="width: 16px" />
        </div>
        <div class="btn" title="还原（每行4图）" @click="resetImage">
          <Icon class="btn-icon-reset" type="md-refresh" :size="18"></Icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onBeforeMount, onBeforeUnmount } from 'vue'

  import { findLastIndex } from '@/utils/array'

  const props = defineProps({
    images: {
      type: Array,
      default: () => [],
    },
    enableSelect: Boolean,
    enableReorder: Boolean,
  })

  const emits = defineEmits(['update:images'])

  const imagesPerRow = ref(4)
  const imageGap = ref(16)
  const imageWrapperPadding = ref(16)

  // 拖拽
  const draggedItems = ref([])
  const dragOverItem = ref(null)
  const dropSide = ref('')

  const imageWrapperStyle = computed(() => {
    return {
      gridTemplateColumns: `repeat(${imagesPerRow.value}, 1fr)`,
      gap: `${imageGap.value}px`,
      padding: `${imageWrapperPadding.value}px`,
    }
  })
  const imageList = computed(() => {
    return props.images.map((pageImage, idx) => ({
      index: idx,
      name: `第 ${idx + 1} 页`,
      url: pageImage.url,
      imageStyle: {
        aspectRatio: pageImage.w / pageImage.h,
      },
      selected: pageImage.selected,
    }))
  })

  function getImageItemStyle(item) {
    return {
      'enable-select': props.enableSelect,
      selected: item.selected,
      dragging: draggedItems.value.some(x => x.url == item.url),
      'drag-over': dragOverItem.value?.url == item.url,
      'drop-left': dragOverItem.value?.url == item.url && dropSide.value == 'left',
      'drop-right': dragOverItem.value?.url == item.url && dropSide.value == 'right',
    }
  }

  /**
   * 缩放
   */
  function zoomImage(option) {
    if (option === 'in') {
      imagesPerRow.value = Math.max(1, imagesPerRow.value - 1)
    } else {
      imagesPerRow.value = Math.min(10, imagesPerRow.value + 1)
    }
  }
  function resetImage() {
    imagesPerRow.value = 4
  }

  /**
   * 选择图片
   */
  function handleImageItemClick(item, e) {
    if (!props.enableSelect) {
      return
    }
    let images = props.images
    let itemIndex = images.findIndex(x => x.url == item.url)

    // 按住shift则连续选择
    if (e.shiftKey) {
      // 向前选
      let preSelectedIndex = findLastIndex(images.slice(0, itemIndex), x => x.selected)
      if (preSelectedIndex >= 0) {
        images.forEach((x, idx) => {
          if (idx >= preSelectedIndex && idx <= itemIndex) {
            x.selected = true
          }
        })
        return
      }
      // 向后选
      let nextSelectedIndex = images.findIndex((x, idx) => idx > itemIndex && x.selected)
      if (nextSelectedIndex >= 0) {
        images.forEach((x, idx) => {
          if (idx >= itemIndex && idx <= nextSelectedIndex) {
            x.selected = true
          }
        })
        return
      }
    } else {
      images[itemIndex].selected = !images[itemIndex].selected
    }
  }

  /**
   * 拖拽
   */
  function handleDragStart(item) {
    if (!props.enableReorder) {
      return
    }
    if (item.selected) {
      draggedItems.value = props.images.filter(x => x.selected)
    } else {
      let draggedItem = props.images.find(x => x.url == item.url)
      draggedItem.selected = true
      draggedItems.value = [draggedItem]
    }
  }

  function handleDragEnter(item, e) {
    dragOverItem.value = item
    updateDropSide(e)
  }

  function updateDropSide(e) {
    dropSide.value = e.offsetX > e.target.offsetWidth / 2 ? 'right' : 'left'
  }

  function handleDragOver(item, e) {
    dragOverItem.value = item
    updateDropSide(e)
    // 必须调用 event.preventDefault() 来允许放置
    e.preventDefault()
  }

  function handleDragLeave() {
    dragOverItem.value = null
    dropSide.value = ''
  }

  function handleDrop(item, e) {
    updateDropSide(e)
    let newImages = props.images.slice()
    // 插入null占位
    let targetIndex = newImages.findIndex(x => x.url == item.url)
    if (dropSide.value == 'right') {
      newImages.splice(targetIndex + 1, 0, null)
    } else {
      newImages.splice(targetIndex, 0, null)
    }
    // 删除被拖拽的项
    newImages = newImages.filter(x => x == null || !draggedItems.value.some(y => y.url == x.url))
    // 插入被拖拽的项
    let insertIndex = newImages.findIndex(x => x == null)
    newImages.splice(insertIndex, 1, ...draggedItems.value)
    // 清除所有选中项
    newImages.forEach(x => {
      x.selected = false
    })
    emits('update:images', newImages)
    cleanDragStatus()
  }

  function cleanDragStatus() {
    draggedItems.value = []
    dragOverItem.value = null
    dropSide.value = ''
    props.images.forEach(x => {
      x.selected = false
    })
  }

  function handleDragEnd() {
    cleanDragStatus()
  }

  // 按esc键清除所有选中
  function handleKeydown(e) {
    if (props.enableSelect && e.key == 'Escape') {
      props.images.forEach(x => {
        x.selected = false
      })
    }
  }
  onBeforeMount(() => {
    document.addEventListener('keydown', handleKeydown)
  })
  onBeforeUnmount(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
</script>

<style lang="scss" scoped>
  .image-container {
    @include flex(row, flex-start, stretch);
    width: 100%;
    height: 100%;
  }

  .container-main {
    flex-grow: 1;
    overflow-y: auto;
  }

  .image-wrapper {
    display: grid;
    align-items: center;
  }

  .image-item {
    border: 1px solid $color-border;
    border-radius: 2px;
    user-select: none;

    &:hover {
      outline: 2px solid $color-info;
    }

    &.enable-select {
      cursor: pointer;
    }

    &.selected {
      outline: 2px solid $color-primary-dark;
    }

    .page-image {
      pointer-events: none;
    }

    .image-name {
      padding-top: 8px;
      padding-bottom: 8px;
      color: $color-icon;
      font-size: $font-size-small;
      line-height: 1;
      text-align: center;
    }

    img {
      display: block;
      width: 100%;
    }

    &.dragging {
      opacity: 0.5;
    }

    &.drag-over {
      position: relative;
      border: 1px dashed $color-info;

      &.drop-left::before,
      &.drop-right::after {
        position: absolute;
        top: -5px;
        bottom: -5px;
        width: 4px;
        border-radius: 2px;
        background-color: $color-primary;
        content: '';
      }

      &.drop-left::before {
        left: -10px;
      }

      &.drop-right::after {
        right: -11px;
      }
    }
  }

  .container-aside {
    @include flex(column, flex-start, center);
    flex-grow: 0;
    flex-shrink: 0;
    width: 36px;
    padding-top: 16px;
    padding-bottom: 16px;

    .toolbar {
      .btn {
        margin-bottom: 10px;
        text-align: center;
        cursor: pointer;
        user-select: none;
      }
    }
  }
</style>
