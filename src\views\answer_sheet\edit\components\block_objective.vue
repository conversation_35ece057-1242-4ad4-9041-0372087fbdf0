<template>
  <div class="block-objective" :style="boxStyle.style">
    <div v-for="(group, groupIdx) in boxStyle.groups" :key="groupIdx" class="objective-group" :style="group.style">
      <div v-for="(qItem, qItemIdx) in group.questions" :key="qItemIdx" class="objective-item" :style="qItem.style">
        <div class="objective-code" :style="qItem.codeStyle">{{ qItem.questionCode }}</div>
        <img
          v-for="(cell, cellIdx) in qItem.cells"
          :key="cellIdx"
          class="cell-omr"
          :src="cell.src"
          :value="cell.value"
          :style="cell.style"
        />
      </div>
    </div>

    <div class="page-block-toolbar no-print">
      <div class="btn btn-primary" title="设置" @click="handleBtnSettingClick">
        <Icon class="btn-icon" type="ios-settings"></Icon>
      </div>
      <div v-if="enableEditQuestion" class="btn btn-error" title="删除" @click="handleBtnDeleteClick">
        <Icon class="btn-icon" type="md-close"></Icon>
      </div>
    </div>

    <Modal
      v-model="showModalSetting"
      title="客观题设置"
      class="no-print modal-objective-setting"
      @on-ok="handleModalSettingOK"
    >
      <div class="form-item">
        <div class="form-item-label">显示方式</div>
        <comRadioGroup v-model="modalObjectivesPosition" class="radios" :radioes="positionRadios"></comRadioGroup>
      </div>
      <div class="form-item">
        <div class="form-item-label">每组题数</div>
        <comRadioGroup v-model="modalGroupLength" class="radios" :radioes="questionNumRadios"></comRadioGroup>
      </div>
      <div v-if="hasTrueOrFalseQuestion" class="form-item">
        <div class="form-item-label">判断题显示</div>
        <comRadioGroup
          v-model="modalGroupTrueOrFalseType"
          class="radios"
          :radioes="trueOrFalseTypeRadios"
        ></comRadioGroup>
      </div>
    </Modal>

    <Modal v-model="showModalDelete" title="选择要删除的客观题" class="no-print modal-objective-delete">
      <Checkbox
        :indeterminate="indeterminate"
        :model-value="checkedAll"
        class="question-code-item"
        @click.prevent="handleCheckAll"
        >全选</Checkbox
      >
      <CheckboxGroup v-model="checkedQuestionCodes">
        <Checkbox v-for="q in questionGroupQuestionCodes" :key="q" class="question-code-item" :label="q">{{
          q
        }}</Checkbox>
      </CheckboxGroup>
      <template #footer>
        <Button type="text" @click="showModalDelete = false">取消</Button>
        <Button type="primary" :disabled="checkedQuestionCodes.length == 0" @click="handleModalDeleteOK">确定</Button>
      </template>
    </Modal>
  </div>
</template>
<script>
  import comRadioGroup from '@/components/radio_group.vue'

  import { mapMutations } from 'vuex'
  import TemplateRect from '@/helpers/scan_template_define/template_rect'
  import TemplateOmrArea from '@/helpers/scan_template_define/template_omr_area'
  import { groupArray, groupArrayAdjacent, unGroupArray } from '@/utils/array'
  import { showScrollbarAfterHideModal } from '@/utils/iview'
  import BranchTypeEnum from '@/enum/qlib/branch_type'
  import TrueFalseTypeEnum from '@/enum/answer_sheet/true_false_type'
  import { OmrSVG } from '@/const/answer_sheet'

  export default {
    components: {
      comRadioGroup,
    },
    props: {
      block: {
        type: Object,
        required: true,
      },
    },
    emits: ['height-change'],
    data() {
      return {
        borderWidth: 1,
        codeWidth: 20,
        codeFontSize: 10,
        cellGap: 8,
        cellWidth: 20,
        cellHeight: 8,
        // 客观题设置
        showModalSetting: false,
        modalObjectivesPosition: 'top',
        modalGroupLength: 5,
        modalGroupTrueOrFalseType: TrueFalseTypeEnum.TF.id,
        // 删除客观题
        showModalDelete: false,
        checkedQuestionCodes: [],
      }
    },
    computed: {
      enableEditQuestion() {
        return this.$store.getters['answerSheet/enableEditQuestion']
      },
      objectivesSeparate() {
        return this.$store.getters['answerSheet/objectivesSeparate']
      },
      pageSize() {
        return this.$store.getters['answerSheet/pageSize'](this.block.pageIndex)
      },
      box() {
        return this.getObjectiveBox(
          this.block.questions,
          this.block.questionGroup.groupLength,
          this.block.questionGroup.trueOrFalseType
        )
      },
      boxHeight() {
        return this.box.style.height
      },
      boxStyle() {
        let { top } = this.$store.getters['answerSheet/getShowBoxContentBorder'](this.block)
        return {
          style: {
            position: 'relative',
            height: this.box.style.height + 'px',
            border: `${this.borderWidth}px solid var(--base-border-color)`,
            borderTopColor: top ? 'var(--base-border-color)' : 'transparent',
          },
          groups: this.box.groups.map(g => ({
            style: {
              position: 'absolute',
              left: g.style.left + 'px',
              top: g.style.top + 'px',
              width: g.style.width + 'px',
              height: g.style.height + 'px',
            },
            questions: g.questions.map(q => ({
              style: {
                position: 'absolute',
                left: '0',
                top: q.style.top + 'px',
                width: q.style.width + 'px',
                height: q.style.height + 'px',
              },
              questionCode: q.questionCode,
              codeStyle: {
                position: 'absolute',
                top: '0',
                left: '0',
                width: `${this.codeWidth}px`,
                height: `${this.cellHeight}px`,
                lineHeight: `${this.cellHeight}px`,
                fontSize: `${this.codeFontSize}px`,
                textAlign: 'right',
              },
              cells: q.cells.map(c => ({
                value: c.value,
                src: OmrSVG[`cell_${c.value}`],
                style: {
                  position: 'absolute',
                  top: '0',
                  left: c.style.left + 'px',
                  width: c.style.width + 'px',
                  height: c.style.height + 'px',
                },
              })),
            })),
          })),
        }
      },
      // 设置弹窗
      questionNumRadios() {
        let list = []
        for (let i = 1; i <= 10; i++) {
          list.push({
            id: i,
            name: i,
          })
        }
        return list
      },
      positionRadios() {
        return [
          {
            id: 'top',
            name: '统一显示于顶部',
          },
          {
            id: 'topic',
            name: '分大题显示',
          },
        ]
      },
      trueOrFalseTypeRadios() {
        return TrueFalseTypeEnum.getEntries()
      },
      hasTrueOrFalseQuestion() {
        return this.block.questionGroup.questions.some(q => q.branchTypeId == BranchTypeEnum.TrueOrFalse.id)
      },

      // 删除题目
      questionGroupQuestionCodes() {
        return this.block.questionGroup.questions.map(q => q.questionCode)
      },
      checkedAll() {
        return this.checkedQuestionCodes.length == this.questionGroupQuestionCodes.length
      },
      indeterminate() {
        return (
          this.checkedQuestionCodes.length > 0 &&
          this.checkedQuestionCodes.length < this.questionGroupQuestionCodes.length
        )
      },
    },
    watch: {
      boxHeight() {
        this.checkHeightChangeAndEmitEvent()
      },
    },
    created() {
      this.changeInstance()
    },
    mounted() {
      this.checkHeightChangeAndEmitEvent()
    },
    updated() {
      this.changeInstance()
    },
    methods: {
      ...mapMutations('answerSheet', ['changePageBlockInstance', 'changePageBlockHeight']),
      changeInstance() {
        this.changePageBlockInstance({
          blockId: this.block.id,
          instance: this,
        })
      },
      changeHeight(height) {
        this.changePageBlockHeight({
          blockId: this.block.id,
          height: height,
        })
      },
      checkHeightChangeAndEmitEvent() {
        if (this.boxHeight === this.block.height) {
          return
        }
        this.changeHeight(this.boxHeight)
        this.$emit('height-change', this.block.height)
      },

      getObjectiveBox(questions, groupLength, trueOrFalseType) {
        /**
         * 分组
         */
        let groups = []

        // 先按选项个数相邻分组
        let groupByoptionCount = groupArrayAdjacent(questions, q => q.optionCount).map(g => ({
          optionCount: g.key,
          questions: g.group,
        }))

        // 每组中按题目数量再分组
        groupByoptionCount.forEach(g => {
          let questions = g.questions.map((q, idx) => ({
            question: q,
            qIndex: idx,
          }))
          g.group = groupArray(questions, q => Math.floor(q.qIndex / groupLength)).map(g1 => ({
            optionCount: g.optionCount,
            questions: g1.group.map(x => x.question),
          }))
        })

        // 拉平
        groups = unGroupArray(groupByoptionCount, g => g.group)

        /**
         * 计算位置
         */
        let boxWidth = this.pageSize.width - this.pageSize.left - this.pageSize.right - this.borderWidth * 2
        let rows = [
          {
            top: this.cellGap,
            height: 0,
            groups: [],
          },
        ]
        let left = 0

        groups.forEach(group => {
          let groupWidth = this.codeWidth + (this.cellWidth + this.cellGap) * Math.max(group.optionCount, 4)
          let groupHeight = (this.cellHeight + this.cellGap) * group.questions.length - this.cellGap

          let lastRow = rows[rows.length - 1]
          // 如果超宽则换行
          if (left + groupWidth > boxWidth) {
            left = 0
            lastRow = {
              top: lastRow.top + lastRow.height + this.cellGap * 2,
              height: 0,
              groups: [],
            }
            rows.push(lastRow)
          }
          group.style = {
            width: groupWidth,
            height: groupHeight,
            left: left,
            top: lastRow.top,
          }
          lastRow.groups.push(group)

          // 更新左边距和行高
          left += groupWidth + this.cellGap
          if (lastRow.height < groupHeight) {
            lastRow.height = groupHeight
          }

          group.questions.forEach((q, qIndex) => {
            let values = []
            if (q.branchTypeId !== BranchTypeEnum.TrueOrFalse.id) {
              for (let i = 0; i < q.optionCount; i++) {
                values.push(String.fromCharCode(65 + i))
              }
            } else if (trueOrFalseType == TrueFalseTypeEnum.TF.id) {
              values = ['T', 'F']
            } else if (trueOrFalseType == TrueFalseTypeEnum.TC.id) {
              values = ['tick', 'cross']
            }

            q.cells = values.map((v, idx) => {
              return {
                value: v,
                style: {
                  left: this.codeWidth + (idx + 1) * this.cellGap + idx * this.cellWidth,
                  width: this.cellWidth,
                  height: this.cellHeight,
                },
              }
            })
            q.style = {
              width: group.style.width,
              height: this.cellHeight,
              top: (this.cellHeight + this.cellGap) * qIndex,
            }
          })
        })

        let lastRow = rows[rows.length - 1]
        return {
          style: {
            height: lastRow.top + lastRow.height + this.cellGap + this.borderWidth * 2,
          },
          rows,
          groups,
        }
      },
      split(getNextPageAvailableHeightFunc) {
        let box = this.getObjectiveBox(this.block.questionGroup.questions, this.block.questionGroup.groupLength)

        // 可完全容纳
        let firstBlockHeight = getNextPageAvailableHeightFunc(0)
        if (box.style.height < firstBlockHeight) {
          return [
            {
              height: box.style.height,
              questions: this.block.questionGroup.questions,
            },
          ]
        }

        // 部分容纳
        let rowBlocks = []

        // 获取一定高度内可以容纳的客观题行
        let getRowsInHeight = (rows, height) => {
          let rowsInHeight = []
          let top = 0

          while (rows.length) {
            let row = rows[0]
            if (top + row.height + this.cellGap * 2 < height - this.borderWidth * 2) {
              rowsInHeight.push(rows.shift())
              top += row.height + this.cellGap * 2
            } else {
              break
            }
          }

          return {
            height: top + this.borderWidth * 2,
            rows: rowsInHeight,
          }
        }

        // 第一块
        let firstBlock = getRowsInHeight(box.rows, firstBlockHeight)
        rowBlocks.push(firstBlock)

        // 分页块
        while (box.rows.length) {
          let pageHeight = getNextPageAvailableHeightFunc(rowBlocks.length)
          rowBlocks.push(getRowsInHeight(box.rows, pageHeight))
        }

        let blocks = rowBlocks.map(x => {
          if (x.rows.length === 0) {
            return null
          }

          let blockQuestions = []
          x.rows.forEach(row => {
            row.groups.forEach(group => {
              group.questions.forEach(q => {
                blockQuestions.push(q)
              })
            })
          })
          return {
            height: x.height,
            questions: blockQuestions,
          }
        })

        return blocks
      },
      getTemplateElements(pageLeft) {
        let addLeft = pageLeft + this.pageSize.left + this.borderWidth
        let addTop = this.block.top + this.pageSize.top + this.borderWidth
        return this.box.groups.map(g => {
          let options = []
          let questions = []
          g.questions.forEach(q => {
            questions.push({
              questionCode: q.questionCode,
            })
            q.cells.forEach(c => {
              options.push({
                left: c.style.left + g.style.left + addLeft,
                top: q.style.top + g.style.top + addTop,
                width: this.cellWidth,
                height: this.cellHeight,
              })
            })
          })

          let omrArea = new TemplateOmrArea()
          omrArea.id = `ObjectiveArea-${g.questions.map(q => q.questionCode).join(',')}`
          omrArea.direction = 'H'
          omrArea.optionCount = g.optionCount
          omrArea.options = options.map(opt =>
            TemplateRect.createFromAnswerSheetPageRect(opt.left, opt.top, opt.width, opt.height)
          )
          omrArea._extra = {
            questions,
          }
          return omrArea
        })
      },

      /**设置 */
      handleBtnSettingClick() {
        this.modalObjectivesPosition = this.objectivesSeparate ? 'topic' : 'top'
        this.modalGroupLength = this.block.questionGroup.groupLength
        this.modalGroupTrueOrFalseType = this.block.questionGroup.trueOrFalseType
        this.showModalSetting = true
      },
      handleModalSettingOK() {
        let data = {
          questionGroupId: this.block.questionGroup.id,
          type: this.block.questionGroup.type,
          groupLength: this.modalGroupLength,
          trueOrFalseType: this.modalGroupTrueOrFalseType,
        }
        if (this.modalObjectivesPosition == 'top' && this.objectivesSeparate) {
          this.$store.commit('answerSheet/mergeObjectiveTopics', data)
          this.$store.commit('answerSheet/generatePages')
        } else if (this.modalObjectivesPosition == 'topic' && !this.objectivesSeparate) {
          this.$store.commit('answerSheet/splitObjectiveTopics', data)
          this.$store.commit('answerSheet/generatePages')
        } else if (
          this.block.questionGroup.groupLength != this.modalGroupLength ||
          this.block.questionGroup.trueOrFalseType != this.modalGroupTrueOrFalseType
        ) {
          this.$store.commit('answerSheet/changeQuestionGroupType', data)
        }
        this.hideModalSetting()
      },
      hideModalSetting() {
        this.showModalSetting = false
        setTimeout(() => {
          showScrollbarAfterHideModal()
        }, 500)
      },

      /**删除 */
      handleBtnDeleteClick() {
        this.checkedQuestionCodes = this.questionGroupQuestionCodes.slice()
        this.showModalDelete = true
      },
      handleCheckAll() {
        if (this.checkedAll) {
          this.checkedQuestionCodes = []
        } else {
          this.checkedQuestionCodes = this.questionGroupQuestionCodes.slice()
        }
      },
      handleModalDeleteOK() {
        if (this.checkedQuestionCodes.length == 0) {
          this.$Message.info({
            content: '请选择要删除的题目',
          })
          return
        }
        this.$Modal.confirm({
          title: '确认删除',
          content: `您将删除勾选的 ${this.checkedQuestionCodes.length} 道客观题`,
          onOk: () => {
            this.showModalDelete = false
            setTimeout(() => {
              showScrollbarAfterHideModal()
            }, 500)
            this.$store.commit('answerSheet/deleteObjectiveQuestionGroup', {
              questionGroupId: this.block.questionGroup.id,
              questionCodes: this.checkedQuestionCodes,
            })
            this.$store.commit('answerSheet/generatePages')
          },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .modal-objective-setting {
    .form-item {
      @include flex(row, flex-start, center);
      margin-bottom: 20px;

      .form-item-label {
        width: 80px;
        color: $color-icon;
      }
    }
  }

  .modal-objective-delete {
    .question-code-item {
      min-width: 40px;
      margin-bottom: 8px;
    }
  }
</style>
