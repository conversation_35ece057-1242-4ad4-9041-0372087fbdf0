import ajax from '@/api/ajax'

export function apiGetLevelDistribution(params) {
  return ajax.post({
    url: 'report/scoreLevelDistribution',
    params: {
      examId: params.examId,
      distributionType: params.distributionType,
      selectedSubject: params.selectedSubject,
    },
    data: {
      levels: params.levels,
      classes: params.classesList,
    },
    requestName: '获取学业等级分布信息',
  })
}
