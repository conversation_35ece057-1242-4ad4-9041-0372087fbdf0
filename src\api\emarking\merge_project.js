import ajax from '@/api/ajax'

export function apiGetMergeableExamList(requestParams) {
  return ajax.get({
    url: 'mark/exam/merge/canMergeExams',
    params: {
      schoolId: requestParams.schoolId, // *String
      semesterId: requestParams.semesterId, // *Number
      term: requestParams.term, // *Number,
      gradeId: requestParams.gradeId, // *Number
    },
    requestName: '查询学期机构可合并考试项目列表',
  })
}

export function apiGetMergeableExamDetail(examId) {
  return ajax.get({
    url: 'mark/exam/merge/canMergeExamDetail',
    params: {
      examId, // *String
    },
    requestName: '查询可合并项目详情',
  })
}

export function apiCreateMergeExam(requestParams) {
  return ajax.post({
    url: 'mark/exam/merge/create',
    params: {
      isConfigFinished: requestParams.isConfigFinished, // *Boolean 是否配置完成
    },
    data: requestParams,
    /*
      "beginTime": "string",
      "endTime": "string",
      "examName": "string",
      "examType": 0,
      "exams": [
        {
          "examId": "string",
          "order": 0,
          "subjects": [
            {
              "objectives": [
                {
                  "action": "string",
                  "dstQuestionCode": 0,
                  "srcQuestionCode": 0
                }
              ],
              "subjectId": 0,
              "subjectives": [
                {
                  "action": "string",
                  "dstQuestions": [
                    {
                      "branchCode": 0,
                      "questionCode": 0
                    }
                  ],
                  "splitScoreMethod": "string",
                  "srcQuestions": [
                    {
                      "branchCode": 0,
                      "questionCode": 0
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      "gradeId": 0,
      "semesterId": 0,
      "subjects": [
        {
          "copyType": "string",
          "foreignSubjectBranch": true,
          "objectives": [
            {
              "answer": "string",
              "answerScoreList": "string",
              "fullScore": 0,
              "isAdditional": true,
              "optionCount": 0,
              "questionCode": 0,
              "questionName": "string",
              "questionType": 0,
              "topicCode": 0,
              "topicName": "string"
            }
          ],
          "otherFullScore": 0,
          "subjectId": 0,
          "subjectType": 0,
          "subjectives": [
            {
              "branchCode": 0,
              "branchName": "string",
              "fullScore": 0,
              "isAdditional": true,
              "questionCode": 0,
              "questionName": "string",
              "topicCode": 0,
              "topicName": "string"
            }
          ],
          "writtenFullScore": 0
        }
      ],
      "term": 0
    */
    requestName: '创建合并项目',
  })
}

export function apiCopyMergeExam(requestParams) {
  return ajax.post({
    url: 'mark/exam/merge/copy',
    params: {
      id: requestParams.id, // *Number
      examName: requestParams.examName, // *String
    },
    requestName: '复制合并项目',
  })
}

export function apiModifyMergeExamConfig(requestParams) {
  return ajax.put({
    url: 'mark/exam/merge/config',
    params: {
      id: requestParams.id, // *Number
      isConfigFinished: requestParams.isConfigFinished, // *Boolean
    },
    data: requestParams.config, // *Object  创建项目同结构
    requestName: '修改合并考试项目配置',
  })
}

export function apiDeleteMergeExam(id) {
  return ajax.delete({
    url: 'mark/exam/merge/delete',
    params: {
      id: id, // *Number
    },
    requestName: '删除合并考试项目',
  })
}

export function apiGetMergeExamDetail(id) {
  return ajax.get({
    url: 'mark/exam/merge/detail',
    params: {
      id: id, // *Number
    },
    requestName: '查询合并项目详情',
  })
}

export function apiGetPageMergedExamList(requestParams) {
  return ajax.get({
    url: 'mark/exam/merge/page',
    params: {
      page: requestParams.currentPage || 1, // *Number
      size: requestParams.pageSize || 10, // *Number
    },
    requestName: '分页查询合并项目列表',
  })
}

export function apiGetExamMergeProgess(id) {
  return ajax.get({
    url: 'mark/exam/merge/progress',
    params: {
      id: id, // *Number
    },
    requestName: '查询项目合并执行进度',
  })
}

export function apiStartExamsMergeProgress(id) {
  return ajax.put({
    url: 'mark/exam/merge/run',
    params: {
      id: id, // *Number
    },
    requestName: '执行合并',
  })
}
