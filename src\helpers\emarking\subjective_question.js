import Question from './question'

export default class SubjectiveQuestion extends Question {
  constructor() {
    super()
    // 填空题每空分数
    this.blankScores = []
  }

  static import(q) {
    let subj = new SubjectiveQuestion()
    subj.import(q)
    if (q.blankScores) {
      if (q.blankScores.startsWith('[') || q.blankScores.endsWith(']')) {
        subj.blankScores = JSON.parse(q.blankScores).filter(Boolean).map(Number)
      } else {
        subj.blankScores = q.blankScores.split(';').filter(Boolean).map(Number)
      }
    }
    return subj
  }

  export(exportId = false) {
    return {
      id: exportId ? this.questionId : undefined,
      questionCode: this.questionCode,
      branchCode: this.branchCode,
      questionName: this.questionName,
      branchName: this.branchName,
      topicCode: this.topicCode,
      topicName: this.topicName,
      fullScore: this.fullScore,
      questionId: this.originalQuestionId || 0,
      branchId: this.originalBranchId || 0,
      isAdditional: this.isAdditional,
      blankScores: JSON.stringify(this.blankScores),
    }
  }

  check() {
    let message = super.check()
    if (message) {
      return message
    }

    // 填空题每空分数
    if (this.blankScores.length == 0) {
      return
    }
    this.blankScores = this.blankScores.map(Number)
    if (this.blankScores.some(s => !(s > 0))) {
      return '填空题每空分数错误'
    }
    if (this.blankScores.some(s => Math.floor(s * 10) != s * 10)) {
      return '填空题每空分数最多精确到1位小数'
    }
    if (this.blankScores.reduce((a, c) => a + c, 0) != this.fullScore) {
      return '填空题每空分数之和不等于题目满分'
    }
  }

  clone() {
    let subj = new SubjectiveQuestion()
    Object.assign(subj, this)
    return subj
  }
}
