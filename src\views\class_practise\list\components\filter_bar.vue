<template>
  <div class="filter-bar">
    <div class="filter-bar-left">
      <div v-if="fields.includes('term')" class="filter-item">
        <span class="filter-item-label">学期</span>
        <Select class="filter-item-body term" :model-value="termName" transfer @on-change="changeTerm">
          <Option v-for="t in terms" :key="t.termName" :value="t.termName">{{ t.termName }}</Option>
        </Select>
      </div>
      <div v-if="fields.includes('grade')" class="filter-item">
        <span class="filter-item-label">年级</span>
        <Select
          class="filter-item-body grade"
          :model-value="params.gradeId"
          clearable
          transfer
          @on-change="changeGrade"
        >
          <Option v-for="g in grades" :key="g.id" :value="g.id">{{ g.name }}</Option>
        </Select>
      </div>
      <div v-if="fields.includes('examType')" class="filter-item">
        <span class="filter-item-label">类型</span>
        <Select
          class="filter-item-body exam-type"
          clearable
          transfer
          :model-value="params.examType"
          @on-change="changeExamType"
        >
          <Option v-for="item in examTypes" :key="item.id" :value="item.id">{{ item.name }}</Option>
        </Select>
      </div>
      <div v-if="fields.includes('examScope')" class="filter-item">
        <span class="filter-item-label">范围</span>
        <Select
          class="filter-item-body exam-scope"
          clearable
          transfer
          :value="params.examScope"
          @on-change="changeExamScope"
        >
          <Option v-for="item in examScopes" :key="item.id" :value="item.id">{{ item.name }}</Option>
        </Select>
      </div>
      <div v-if="fields.includes('subject')" class="filter-item">
        <span class="filter-item-label">科目</span>
        <Select
          class="filter-item-body subject"
          :model-value="params.subjectId"
          clearable
          transfer
          @on-change="changeSubject"
        >
          <Option v-for="item of subjects" :key="item.id" :value="item.id">
            {{ item.name }}
          </Option>
        </Select>
      </div>
      <div v-if="fields.includes('subjectStatus')" class="filter-item">
        <span class="filter-item-label">状态</span>
        <Select
          class="filter-item-body subject-status"
          clearable
          transfer
          :model-value="params.status"
          @on-change="changeStatus"
        >
          <Option v-for="item in subjectStatusList" :key="item.id" :value="item.id">{{ item.name }}</Option>
        </Select>
      </div>
      <div v-if="fields.includes('time')" class="filter-item">
        <span class="filter-item-label">时间</span>
        <DatePicker
          class="filter-item-body time"
          type="daterange"
          clearable
          transfer
          :model-value="dateRange"
          :options="datePickerOptions"
          placeholder="开始日期 - 结束日期"
          placement="top-start"
          @on-change="changeTime"
        />
      </div>
    </div>
    <!-- <div v-if="searchPlaceholder" class="filter-bar-right">
      <Input
        class="input-esearch"
        clearable
        transfer
        :placeholder="searchPlaceholder"
        :model-value="params.keyword"
        suffix="md-search"
        maxlength="40"
        @on-change="changeKeyword"
      />
    </div> -->
  </div>
</template>

<script>
  import { debounce } from '@/utils/function'

  import ExamScopeEnum from '@/enum/emarking/exam_scope'
  import ExamTypeEnum from '@/enum/emarking/exam_type'
  import SubjectStatusEnum from '@/enum/emarking/subject_status'

  export default {
    props: {
      fields: {
        type: Array,
        default: () => ['term', 'grade', 'examType', 'examScope', 'subjectStatus', 'subject', 'time'],
      },
      params: {
        type: Object,
        default: () => ({
          semesterId: '',
          term: '',
          gradeId: '',
          examType: '',
          examScope: '',
          status: '',
          subjectId: '',
          beginTime: '',
          endTime: '',
          keyword: '',
        }),
      },
      searchPlaceholder: String,
    },
    emits: ['change'],
    computed: {
      terms() {
        return this.$store.getters['emarking/terms']()
      },
      selectedTerm() {
        return this.terms.find(x => x.semesterId == this.params.semesterId && x.term == this.params.term)
      },
      termName() {
        return this.selectedTerm ? this.selectedTerm.termName : ''
      },
      grades() {
        return this.$store.getters['emarking/gradeSubjects']()
      },
      subjects() {
        if (this.params.gradeId) {
          let grade = this.grades.find(g => g.id == this.params.gradeId)
          return grade ? grade.subjects : []
        } else {
          return this.$store.getters['emarking/subjects']()
        }
      },
      examScopes() {
        return ExamScopeEnum.getEntries()
      },
      examTypes() {
        return ExamTypeEnum.getEntries().filter(
          t => ![ExamTypeEnum.ClassPractise.id, ExamTypeEnum.Feedback.id].includes(t.id)
        )
      },
      subjectStatusList() {
        return [
          {
            id: SubjectStatusEnum.Started.id,
            name: '已开启',
          },
          {
            id: SubjectStatusEnum.Suspended.id,
            name: '已暂停',
          },
          {
            id: SubjectStatusEnum.Finished.id,
            name: '已完成',
          },
        ]
      },
      dateRange() {
        return [this.params.beginTime || '', this.params.endTime || '']
      },
      datePickerOptions() {
        if (!this.selectedTerm) {
          return {}
        }
        let beginTimeValue = new Date(this.selectedTerm.beginTime).getTime()
        let endTimeValue = new Date(this.selectedTerm.endTime).getTime()
        return {
          disabledDate: date => date.valueOf() < beginTimeValue || date.valueOf() > endTimeValue,
        }
      },
    },
    created() {
      if (!this.selectedTerm) {
        let semesterId = ''
        let term = ''
        if (this.terms.length > 0) {
          semesterId = this.terms[0].semesterId
          term = this.terms[0].term
        }
        this.$emit('change', {
          semesterId,
          term,
        })
      } else {
        this.$emit('change', this.params)
      }
    },
    methods: {
      changeTerm(termName) {
        let term = this.terms.find(t => t.termName == termName)
        if (term) {
          this.$emit('change', {
            semesterId: term.semesterId,
            term: term.term,
          })
        }
      },
      changeGrade(gradeId) {
        let data = {
          gradeId,
        }
        if (gradeId && this.fields.includes('subject')) {
          let grade = this.grades.find(g => g.id == gradeId)
          let subjects = grade ? grade.subjects : []
          if (subjects.every(s => s.id != this.params.subjectId)) {
            data.subjectId = ''
          }
        }
        this.$emit('change', data)
      },
      changeExamType(examType) {
        this.$emit('change', {
          examType,
        })
      },
      changeExamScope(examScope) {
        this.$emit('change', {
          examScope,
        })
      },
      changeSubject(subjectId) {
        this.$emit('change', {
          subjectId,
        })
      },
      changeStatus(status) {
        this.$emit('change', {
          status,
        })
      },
      changeTime([beginTime, endTime]) {
        this.$emit('change', {
          beginTime,
          endTime,
        })
      },
      changeKeyword: debounce(function (e) {
        this.$emit('change', {
          keyword: e.target.value,
        })
      }, 500),
    },
  }
</script>

<style lang="scss" scoped>
  .filter-bar {
    @include flex(row, space-between, center);

    .filter-bar-left {
      @include flex(row, flex-start, center);

      .filter-item {
        @include flex(row, flex-start, center);
        margin-right: 20px;

        .filter-item-label {
          flex-grow: 0;
          flex-shrink: 0;
          margin-right: 5px;
        }

        .filter-item-body {
          width: 100px;

          &.term {
            width: 200px;
          }

          &.time {
            width: 200px;
          }
        }
      }
    }

    .filter-bar-right {
      width: 200px;
    }
  }
</style>
