import { normalizeNumber, roundPercent } from '@/utils/math'
import { groupArray } from '@/utils/array'
import { Question } from './question'
import { PaperInfo } from './paper_info'
import { PaperStyle } from './paper_style'
import { PaperStructure } from './paper_structure'
import { splitBlankScores } from './miscellaneous'
import ObjectiveQuestion from '@/helpers/emarking/objective_question'
import SubjectiveQuestion from '@/helpers/emarking/subjective_question'
import BranchTypeEnum from '@/enum/qlib/branch_type'
import AutoScoreTypeEnum from '@/enum/emarking/auto_score_type'

export class Paper {
  constructor(info, style, structure, list) {
    this.paperInfo = info instanceof PaperInfo ? info : PaperInfo.import(info)
    this.paperStyle = style instanceof PaperStyle ? style : PaperStyle.import(style)
    this.paperStructure = structure instanceof PaperStructure ? structure : PaperStructure.import(structure, list || [])
    this.questionList = list || []
    this.innerUpdate()
  }

  // 更新结构及样式
  innerUpdate() {
    this.paperStructure.update()
    this.questionList.sort(
      (a, b) => (a.code === 0 ? a.branches[0].code : a.code) - (b.code === 0 ? b.branches[0].code : b.code)
    )
    this.paperStyle.updateExamInfo(this.paperStructure.fullScore)
    this.paperStyle.updateScoreBox(this.paperStructure.topics)
    this.paperStyle.volume.enabled = this.paperStructure.volumeEnabled
  }

  // 划题添加题目
  append(questions) {
    this.questionList.push(...questions)

    // 防止题号为0被误认为展开小题
    for (let i = 0; i < questions.length; i++) {
      if (questions[i].code === 0) {
        questions[i].code = (questions[i - 1] || { code: Number.MAX_SAFE_INTEGER }).code
      }
    }

    questions.forEach(q => this.paperStructure.insertQuestionByCode(q))

    this.innerUpdate()
  }

  /**
   * 组卷时添加题目
   * @param {Array<Question>} questions
   * @param {Boolean} keepOrder 整份试卷全部加入时为true，若试题为空，加入后题号与该试卷相同
   */
  add(questions, keepOrder) {
    // 题目复制一份
    let cloneQuestions = questions.map(Question.copy)
    this.questionList.push(...cloneQuestions)

    if (keepOrder) {
      cloneQuestions.forEach(q => this.paperStructure.insertQuestionByCode(q))
    } else {
      cloneQuestions.forEach(q => this.paperStructure.insertQuestionByQuestionType(q))
    }
    cloneQuestions.forEach(this.initializeExpandBranches)

    this.innerUpdate()
  }

  // 初始化小题展开
  initializeExpandBranches(q) {
    // 英语有多个小题，或地理有多个客观题小题
    if (
      (q.subject.name === '英语' && q.branches.length > 1) ||
      (q.subject.name === '地理' && q.branches.length > 1 && q.branches.every(b => b.branchType.isObjective))
    ) {
      q.code = 0
    } else if (q.code === 0) {
      // 避免题号为0被误认为小题展开
      q.code = Number.MAX_SAFE_INTEGER
    }
  }

  // 移除题目
  remove(qIds) {
    qIds.forEach(qId => {
      let qIndex = this.questionList.findIndex(q => q.id === qId)
      if (qIndex >= 0) {
        this.questionList.splice(qIndex, 1)
        this.paperStructure.removeQuestion(qId)
      }
    })

    this.innerUpdate()
  }

  // 更换或更改题目
  replace(oldQuestion, newQuestion) {
    newQuestion = Question.copy(newQuestion)
    let qIndex = this.questionList.indexOf(oldQuestion)
    if (qIndex < 0) {
      return
    }
    // 保持展开小题不变
    newQuestion.code = oldQuestion.code
    this.questionList.splice(qIndex, 1, newQuestion)
    this.paperStructure.replaceQuestion(oldQuestion, newQuestion)
    // 保持题名不变
    newQuestion.alias = oldQuestion.alias
    newQuestion.branches.forEach((newBranch, idx) => {
      let oldBranch = oldQuestion.branches[idx]
      if (oldBranch) {
        newBranch.alias = oldBranch.alias
      }
    })

    this.innerUpdate()
  }

  // 更换或更改题目，根据新题Id匹配
  replaceById(newQuestion) {
    newQuestion = Question.copy(newQuestion)
    let oldQuestion = this.questionList.find(q => q.id == newQuestion.id)
    if (!oldQuestion) {
      return
    }
    this.replace(oldQuestion, newQuestion)
  }

  // 大题内改变题目顺序
  changeQuestionOrderInTopic() {
    this.innerUpdate()
  }

  // 改变大题顺序
  changeTopicOrder(oldIndex, newIndex) {
    this.paperStructure.changeTopicOrder(oldIndex, newIndex)
    this.innerUpdate()
  }

  // 改分数
  changeScores({ questions, topics }) {
    questions.forEach(item => {
      let q = this.questionList.find(x => x.id === item.id)
      if (q) {
        q.alias = item.alias
        q.branches.forEach(b => {
          let branch = (item.branches || []).find(x => x.id === b.id)
          if (branch) {
            b.alias = branch.alias
            b.score = branch.score
          }
        })
      }
    })

    if (topics && topics.length > 0) {
      topics.forEach(item => {
        let t = this.paperStructure.topics.find(x => x.code == item.code)
        if (t) {
          if (item.name) {
            t.name = item.name
          }
          t.description = item.description
        }
      })
    }

    this.innerUpdate()
  }

  // 改样式
  changeStyles(styles) {
    let styleKeys = Object.keys(styles || {})

    styleKeys.forEach(key => {
      let style = this.paperStyle[key]
      if (!style) {
        return
      }

      let s = styles[key]
      Object.keys(s).forEach(key1 => {
        if (key === 'examInfo' && key1 === 'content') {
          style.examDuration = s[key1]
          style.content = `试卷满分 ${style.totalScore} 分，考试时间 ${style.examDuration} 分钟`
        } else {
          style[key1] = s[key1]
        }
      })
    })

    if (styleKeys.includes('volume')) {
      this.paperStructure.changeVolumeEnabled(styles.volume.enabled)
      this.innerUpdate()
    }
  }

  // 展开小题
  expandQuestionBranches(question) {
    if (question.code > 0) {
      question.branches[0].code = question.code
      question.code = 0
    }
    this.innerUpdate()
  }

  // 收起小题
  collapseQuestionBranches(question) {
    if (question.code === 0) {
      question.code = question.branches[0].code
    }
    this.innerUpdate()
  }

  // 改大题属性
  changeTopicInfo({ code, name, description, scores }) {
    this.paperStructure.changeTopicName(code, name)
    this.paperStructure.changeTopicDescription(code, description)
    this.changeScores({ questions: scores })
  }

  changeQuestionsTopic(questions) {
    this.paperStructure.changeQuestionsTopic(questions, this.questionList)
    this.innerUpdate()
  }

  // 导出
  export() {
    return {
      paperStyle: this.PaperStyle.export(),
      paperStructure: this.PaperStructure.export(),
    }
  }

  getBasicInfo() {
    if (!this.paperInfo.stage || !this.paperInfo.subject) {
      return []
    }

    let stage = this.paperInfo.stage
    let subject = this.paperInfo.subject

    let infoList = []
    infoList.push({
      name: '学科',
      value: stage && subject ? `${stage.name}${subject.name}` : '',
    })

    if (this.paperInfo.grade.id && this.paperInfo.grade.name) {
      infoList.push({
        name: '年级',
        value: this.paperInfo.grade.name,
      })
    }

    infoList.push({
      name: '类型',
      value: (this.paperInfo.paperType && this.paperInfo.paperType.name) || '',
    })

    if (this.paperInfo.year) {
      infoList.push({
        name: '年份',
        value: this.paperInfo.year,
      })
    }

    if (this.paperInfo.region && this.paperInfo.region.length) {
      infoList.push({
        name: '地区',
        value: this.paperInfo.region.map(r => r.label).join('') || '',
      })
    }

    infoList.push({
      name: '浏览量',
      value: this.paperInfo.viewTimes || 0,
    })

    infoList.push({
      name: '满分',
      value: this.paperStructure.fullScore + '分' || '',
    })

    return infoList
  }

  getStatsByTopic() {
    return this.paperStructure.topics.map(t => {
      let percentage = roundPercent(t.score / this.paperStructure.fullScore)
      let questionCodes = t.vQuestions.map(q => q.code).join(', ')

      return {
        code: t.code,
        name: t.name,
        questionCount: t.vQuestions.length,
        score: t.score,
        percentage,
        questionCodes,
      }
    })
  }

  changeTopicShowQuestionScoreState(topic) {
    let targetTopic = this.paperStructure.topics.find(t => t.code === topic.code && t.title === topic.title)
    if (targetTopic) {
      targetTopic.showQuestionScore = !targetTopic.showQuestionScore
    }
  }

  getStatsByDifficulty() {
    let questions = []
    this.paperStructure.topics.forEach(t => {
      t.vQuestions.forEach(q => {
        if (!q.originalQuestion) {
          return
        }

        questions.push({
          difficultyId: q.originalQuestion.difficulty.id,
          difficultyName: q.originalQuestion.difficulty.name,
          questionCode: q.code,
          score: q.score,
        })
      })
    })

    let list = groupArray(questions, q => q.difficultyId)
      .map(g => ({
        id: g.group[0].difficultyId,
        name: g.group[0].difficultyName,
        questionCount: g.group.length,
        score: g.group.reduce((a, c) => a + c.score, 0),
        questionCodes: g.group.map(q => q.questionCode).join(', '),
      }))
      .sort((a, b) => a.id - b.id)

    list.forEach(item => {
      item.percentage = roundPercent(item.score / this.paperStructure.fullScore)
    })

    return list
  }

  getStatsByKnowledge() {
    let knowledges = []
    this.paperStructure.topics.forEach(t => {
      t.vQuestions.forEach(q => {
        q.branches.forEach(b => {
          let branchKnowledgesCount = b.knowledges.length
          b.knowledges.forEach(k => {
            knowledges.push({
              id: k.id,
              name: k.name,
              questionCode: q.code,
              score: b.score / branchKnowledgesCount,
            })
          })
        })
      })
    })

    let knowledgeGroups = groupArray(knowledges, k => k.id)
      .map(x => ({
        id: x.group[0].id,
        name: x.group[0].name,
        questionCodes: Array.from(new Set(x.group.map(q => q.questionCode))).join(', '),
        score: normalizeNumber(
          x.group.reduce((a, c) => a + c.score, 0),
          0,
          1000000,
          1
        ),
      }))
      .sort((a, b) => a.id - b.id)

    knowledgeGroups.forEach(g => {
      g.percentage = roundPercent(g.score / this.paperStructure.fullScore)
    })

    return knowledgeGroups
  }

  // 提取阅卷题目定义
  extractEMarkingQuestions() {
    let objectiveQuestions = []
    let subjectiveQuestions = []

    this.paperStructure.topics.forEach(topic => {
      topic.vQuestions.forEach(q => {
        q.branches.forEach(b => {
          // 未展开的客观题当作填空题
          if (q.branches.length == 1 && b.branchType.isObjective) {
            objectiveQuestions.push(extractObjectiveQuestion(topic, q, b))
          } else {
            let subj = extractSubjectiveQuestion(topic, q, b)
            subjectiveQuestions.push(subj)
          }
        })
      })
    })

    return {
      objectiveQuestions,
      subjectiveQuestions,
    }

    function extractObjectiveQuestion(topic, question, branch) {
      let obj = new ObjectiveQuestion()
      extractQuestion(obj, topic, question, branch)
      obj.branchTypeId = branch.branchType.id
      obj.optionCount = branch.branchType.id == BranchTypeEnum.TrueOrFalse.id ? 2 : branch.options.length
      if (branch.branchType.id == BranchTypeEnum.SingleChoice.id) {
        obj.standardAnswer = branch.answer.singleChoice
      } else if (branch.branchType.id == BranchTypeEnum.MultipleChoice.id) {
        obj.standardAnswer = branch.answer.multipleChoice.join('')
      } else if (branch.branchType.id == BranchTypeEnum.TrueOrFalse.id) {
        obj.standardAnswer = branch.answer.trueOrFalse
      }
      obj.answerScoreList = ''
      obj.autoScoreTypeId = AutoScoreTypeEnum.No.id
      return obj
    }

    function extractSubjectiveQuestion(topic, question, branch) {
      let subj = new SubjectiveQuestion()
      extractQuestion(subj, topic, question, branch)
      // 设置各空分数
      if (branch.branchType.isObjective || branch.branchType.id == BranchTypeEnum.FillBlank.id) {
        subj.blankScores = splitBlankScores(branch.score, branch.answer.fillBlank.length)
      }
      return subj
    }

    function extractQuestion(q, topic, question, branch) {
      q.questionId = branch.id
      q.questionCode = question.code
      q.questionName = question.alias || String(q.questionCode)
      q.branchCode = question.branches.length > 1 ? branch.code : 0
      q.branchName = question.branches.length > 1 ? branch.alias || `${q.questionCode}.${q.branchCode}` : q.questionName
      q.topicCode = topic.code
      q.topicName = `${topic.codeInChinese}、${topic.name}`
      q.fullScore = branch.score
      q.originalQuestionId = question.originalQuestion.id
      q.originalBranchId = branch.id
      q.isAdditional = false
    }
  }
}
