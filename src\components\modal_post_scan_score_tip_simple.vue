<template>
  <Modal
    class="modal-score-tip-simple"
    :model-value="modelValue"
    :width="730"
    footer-hide
    @on-visible-change="handleVisibleChange"
  >
    <template #header>
      <div class="modal-header">
        <span class="modal-title">先阅后扫打分规范</span>
        <Button
          class="btn-download"
          icon="md-download"
          type="text"
          size="small"
          to="/file/同级生先阅后扫打分规范.pdf"
          target="_blank"
          >下载详细打分规范文档</Button
        >
      </div>
    </template>
    <section class="section-warning">
      <ul>
        <li>打分必须使用红色笔</li>
        <li>请告知学生不要将答案写到对错框、打分条或手写框内。</li>
      </ul>
    </section>
    <ol>
      <li>
        对错框打分规范：仅在错误的空上划线，正确的空无需打分。
        <ExampleImage :src="imgSrcTrueFalseCorrect1" caption="示例"></ExampleImage>
      </li>
      <li>
        打分条打分规范：在分值格上划斜线穿过分值格，注意不要穿过多个分值格。分值格有十位或0.5分的，分别在十位、个位、0.5分上划线，识别结果为十位、个位、0.5分划线分值之和。
        <ExampleImage :src="imgSrcBarCorrect1" caption="示例 1"></ExampleImage>
        <ExampleImage :src="imgSrcBarCorrect6" caption="示例 2"></ExampleImage>
      </li>
      <li>
        手写框打分规范：在手写框内工整填写数字，一个框只填一个数字，数字不要超出边框。
        <ExampleImage :src="imgSrcNumberCorrect1" caption="示例 1"></ExampleImage>
        <ExampleImage :src="imgSrcNumberCorrect2" caption="示例 2"></ExampleImage>
      </li>
    </ol>
  </Modal>
</template>

<script>
  import ExampleImage from './modal_post_scan_score_tip_image'

  import imgSrcTrueFalseCorrect1 from '@/assets/images/post_scan/truefalse_correct_1.png'
  import imgSrcBarCorrect1 from '@/assets/images/post_scan/bar_correct_1.png'
  import imgSrcBarCorrect6 from '@/assets/images/post_scan/bar_correct_6.png'
  import imgSrcNumberCorrect1 from '@/assets/images/post_scan/number_correct_1.png'
  import imgSrcNumberCorrect2 from '@/assets/images/post_scan/number_correct_2.png'

  export default {
    components: {
      ExampleImage,
    },
    props: {
      modelValue: Boolean,
    },
    emits: ['update:modelValue'],
    data() {
      return {
        imgSrcTrueFalseCorrect1,
        imgSrcBarCorrect1,
        imgSrcBarCorrect6,
        imgSrcNumberCorrect1,
        imgSrcNumberCorrect2,
      }
    },
    methods: {
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.$emit('update:modelValue', false)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .modal-header {
    .modal-title {
      margin-right: 10px;
      font-size: $font-size-medium-x;
    }

    .btn-download {
      color: $color-primary;
    }
  }

  ol {
    margin-left: 1em;
    list-style-position: outside;
    list-style-type: decimal;
  }

  li {
    margin-bottom: 8px;
    line-height: 1.7;
  }

  .cross {
    font-size: 18px;
  }

  .section-warning {
    margin-bottom: 16px;
    padding: 12px 16px;
    border-radius: 5px;
    background-color: #fff9e6;

    ul {
      color: red;
      font-weight: bold;
      list-style-position: inside;

      li {
        margin-bottom: 0;
      }
    }
  }
</style>
