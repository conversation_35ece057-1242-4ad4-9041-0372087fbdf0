import ajax from '@/api/ajax'

export function apiCreatePaperGroup(requestParams) {
  return ajax.post({
    url: 'ques/paperGroup/add',
    params: {
      sortCode: requestParams.sortCode || undefined, // 后端默认为0
      stage: requestParams.stage, // String*
      subjectId: requestParams.subjectId, // String*
      groupName: requestParams.groupName, // String*
    },
    requestName: '新建试卷分组',
  })
}

export function apiGetPaperGroupList(requestParams) {
  return ajax.get({
    url: 'ques/paperGroup/list',
    params: {
      stage: requestParams.stage,
      subjectId: requestParams.subjectId,
    },
    requestName: '获取试卷分组列表',
  })
}

export function apiDeletePaperGroup(id) {
  return ajax.delete({
    url: 'ques/paperGroup/delete',
    params: {
      id: id,
    },
    requestName: '删除试卷分组',
  })
}

export function apiChangePaperOrderingInList(sortCodeList) {
  return ajax.post({
    url: 'ques/paperGroup/updateSortCode',
    data: sortCodeList,
    requestName: '修改试卷分组排序',
  })
}

export function apiClearPaperGroupRelationByPaperGroupId(groupId) {
  return ajax.post({
    url: 'ques/paperGroup/clearByGroupId',
    params: {
      groupId: groupId,
    },
    requestName: '清空分组试卷',
  })
}

export function apiClearPaperGroupRelationByPaperIds(paperIds) {
  return ajax.post({
    url: 'ques/paperGroup/clearByPaperIds',
    data: paperIds,
    requestName: '重置试卷的所属分组',
  })
}
