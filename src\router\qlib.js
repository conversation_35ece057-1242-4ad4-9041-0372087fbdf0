import Role from '@/enum/user/role'

export default {
  name: 'qlib',
  path: 'qlib',
  meta: {
    title: '智能题库',
    menu: '组卷',
    name: '智能题库',
    roles: [
      Role.SchoolAdministrator.id,
      Role.SchoolLeader.id,
      Role.GradeLeader.id,
      Role.SubjectLeader.id,
      Role.GradeSubjectLeader.id,
      Role.ClassLeader.id,
      Role.Teacher.id,
      Role.ResearcherLeader.id,
      Role.Researcher.id,
      Role.QuestionLibraryAdministrator.id,
      Role.QuestionLibraryEditor.id,
      Role.QuestionLibraryKnowledgeEditor.id,
      Role.SystemAdministrator.id,
    ],
  },
  component: () => import('@/views/qlib/index.vue'),
  children: [
    {
      name: 'qlib-home',
      path: 'home',
      meta: {
        //menu: '组卷中心',
      },
      component: () => import('@/views/qlib/home/<USER>'),
    },
    {
      name: 'qlib-bybook',
      path: 'bybook',
      meta: {
        menu: '教材选题',
      },
      component: () => import('@/views/qlib/bybook/index.vue'),
    },
    {
      name: 'qlib-byknowledge',
      path: 'byknowledge',
      meta: {
        menu: '知识点选题',
      },
      component: () => import('@/views/qlib/byknowledge/index.vue'),
    },
    {
      name: 'qlib-bypaper',
      path: 'bypaper',
      meta: {
        menu: '试卷选题',
      },
      component: () => import('@/views/qlib/bypaper/index.vue'),
    },
    {
      name: 'qlib-learnPaper',
      path: 'learnpaper',
      meta: {
        menu: '学情组卷',
      },
      component: () => import('@/views/qlib/learn_paper/index.vue'),
    },
    {
      name: 'qlib-intelligentpaper',
      path: 'intelligentpaper',
      meta: {
        menu: '智能组卷',
      },
      component: () => import('@/views/qlib/intelligent_paper/index.vue'),
    },
    {
      name: 'qlib-featured',
      path: 'featuredpapers',
      meta: {
        //menu: '精品卷库',
      },
      component: () => import('@/views/qlib/featured_papers/index.vue'),
    },
    {
      name: 'qlib-school',
      path: 'school',
      meta: {
        menu: '学校资源',
      },
      component: () => import('@/views/qlib/school/index.vue'),
      children: [
        {
          name: 'qlib-school-paper',
          path: 'paper',
          meta: {
            menu: '校本卷库',
          },
          component: () => import('@/views/qlib/school/paper/index.vue'),
        },
        {
          name: 'qlib-school-question',
          path: 'question',
          meta: {
            menu: '校本题库',
          },
          component: () => import('@/views/qlib/school/question/index.vue'),
        },
        {
          name: 'qlib-school-reference-book',
          path: 'reference',
          meta: {
            menu: '定制教辅',
            roles: user => {
              return user.roles.some(r => r == Role.SchoolAdministrator.id) && user.isSystem
            },
          },
          component: () => import('@/views/qlib/school/reference/index.vue'),
          children: [
            {
              name: 'qlib-school-reference-book-list',
              path: 'list',
              meta: {
                menu: '教辅列表',
              },
              component: () => import('@/views/qlib/school/reference/list/index.vue'),
            },
            {
              name: 'qlib-school-reference-book-subscription',
              path: 'subscription',
              meta: {
                menu: '定制试卷',
              },
              component: () => import('@/views/qlib/school/reference/subscription/index.vue'),
            },
            {
              name: 'qlib-school-reference-book-question',
              path: 'question',
              meta: {
                menu: '定制试题',
              },
              component: () => import('@/views/qlib/school/reference/question/index.vue'),
            },
          ],
        },
      ],
    },
    {
      name: 'qlib-records',
      path: 'records',
      meta: {
        menu: '我的卷库',
      },
      component: () => import('@/views/qlib/records/index.vue'),
      children: [
        {
          name: 'qlib-records-composed',
          path: 'composed',
          meta: {
            menu: '组卷记录',
          },
          component: () => import('@/views/qlib/records/composed.vue'),
        },
        {
          name: 'qlib-records-uploaded',
          path: 'uploaded',
          meta: {
            menu: '上传记录',
          },
          component: () => import('@/views/qlib/records/uploaded.vue'),
        },
        {
          name: 'qlib-records-share',
          path: 'share',
          meta: {
            menu: '我的分享',
          },
          component: () => import('@/views/qlib/records/share.vue'),
        },
        {
          name: 'qlib-records-shareToMe',
          path: 'sharetome',
          meta: {
            menu: '分享给我',
          },
          component: () => import('@/views/qlib/records/share_to_me.vue'),
        },
        {
          name: 'qlib-records-favorite-papers',
          path: 'favoritepaper',
          meta: {
            menu: '收藏试卷',
          },
          component: () => import('@/views/qlib/records/favorite_paper.vue'),
        },
        {
          name: 'qlib-records-favorite-questions',
          path: 'favoritequestion',
          meta: {
            menu: '收藏试题',
          },
          component: () => import('@/views/qlib/records/favorite_question.vue'),
        },
      ],
    },
    {
      name: 'qlib-management',
      path: 'management',
      meta: {
        menu: '题库管理',
        roles: [
          Role.QuestionLibraryAdministrator.id,
          Role.QuestionLibraryEditor.id,
          Role.QuestionLibraryKnowledgeEditor.id,
        ],
      },
      component: () => import('@/views/qlib/management/index.vue'),
      children: [
        {
          name: 'qlib-management-paper-edit',
          path: 'paperedit',
          meta: {
            menu: '试卷编辑',
          },
          component: () => import('@/views/qlib/management/paper_edit/index.vue'),
        },
        {
          name: 'qlib-management-paper-review',
          path: 'paperreview',
          meta: {
            menu: '试卷审核',
            roles: [Role.QuestionLibraryAdministrator.id],
            // roles: [Role.QuestionLibraryAdministrator.id, Role.QuestionLibraryEditor.id],
          },
          component: () => import('@/views/qlib/management/paper_review/index.vue'),
        },
        {
          name: 'qlib-management-paper-operation',
          path: 'paperoperation',
          meta: {
            menu: '试卷管理',
            roles: [Role.QuestionLibraryAdministrator.id, Role.QuestionLibraryEditor.id],
          },
          component: () => import('@/views/qlib/management/paper_operation/index.vue'),
        },
        {
          name: 'qlib-management-question-operation',
          path: 'questionoperation',
          meta: {
            menu: '试题管理',
            roles: [Role.QuestionLibraryAdministrator.id, Role.QuestionLibraryEditor.id],
          },
          component: () => import('@/views/qlib/management/question_operation/index.vue'),
        },
        {
          name: 'qlib-management-correct',
          path: 'correct',
          meta: {
            menu: '试题纠错',
            roles: [Role.QuestionLibraryAdministrator.id, Role.QuestionLibraryEditor.id],
          },
          component: () => import('@/views/qlib/management/correct/correct_list.vue'),
        },
        {
          name: 'qlib-management-personnel',
          path: 'personnel',
          meta: {
            menu: '编辑管理',
            roles: [Role.QuestionLibraryAdministrator.id],
          },
          component: () => import('@/views/qlib/management/personnel/index.vue'),
        },
        {
          name: 'qlib-management-supplementaryBook',
          path: 'supplementarybook',
          meta: {
            menu: '教辅管理',
            roles: [Role.QuestionLibraryAdministrator.id, Role.QuestionLibraryEditor.id],
          },
          component: () => import('@/views/qlib/management/coach_book/index.vue'),
        },
        {
          name: 'qlib-management-basis',
          path: 'basis',
          meta: {
            menu: '基础数据',
            roles: [Role.QuestionLibraryAdministrator.id, Role.QuestionLibraryEditor.id],
          },
          component: () => import('@/views/qlib/management/basis/index.vue'),
        },
      ],
    },
    {
      name: 'qlib-paper',
      path: 'paperrecords',
      meta: {
        menu: '试卷日志统计',
        roles: [Role.QuestionLibraryAdministrator.id],
      },
      component: () => import('@/views/qlib/paper/index.vue'),
    },
    {
      name: 'qlib-divide',
      path: 'divide/:paperid',
      meta: {
        showHeader: false,
      },
      component: () => import('@/views/qlib/divide/index.vue'),
    },
    {
      name: 'qlib-examPaperView',
      path: 'exampaperview/:paperid',
      component: () => import('@/views/qlib/exam_paper/view.vue'),
    },
    {
      name: 'qlib-examPaperEdit',
      path: 'exampaperedit/:paperid',
      meta: {
        roles: [Role.QuestionLibraryAdministrator.id, Role.QuestionLibraryEditor.id],
      },
      component: () => import('@/views/qlib/exam_paper/edit.vue'),
    },
    {
      name: 'qlib-composedPaperView',
      path: 'composedpaperview/:paperid',
      component: () => import('@/views/qlib/composed_paper/view.vue'),
    },
    {
      name: 'qlib-composedPaperEdit',
      path: 'composedpaperedit',
      component: () => import('@/views/qlib/composed_paper/edit.vue'),
    },
    {
      name: 'qlib-uploadPaperView',
      path: 'uploadpaperview/:paperid',
      component: () => import('@/views/qlib/exam_paper/view.vue'),
    },
    {
      name: 'qlib-similarQuestion',
      path: 'similar/:questionid',
      component: () => import('@/views/qlib/search/similar_question.vue'),
    },
    {
      name: 'qlib-bindPaperView',
      path: 'bindpaperview/:examid/:examsubjectid',
      component: () => import('@/views/qlib/bind_paper/view.vue'),
    },
    {
      name: 'qlib-coachBookIngestion',
      path: 'coachbookingestion/:coachBookId',
      meta: {
        showHeader: false,
        showFooter: false,
        pageWidth: 'full',
        roles: [Role.QuestionLibraryAdministrator.id, Role.QuestionLibraryEditor.id],
      },
      component: () => import('@/views/qlib/coach_book_ingestion/index.vue'),
    },
  ],
}
