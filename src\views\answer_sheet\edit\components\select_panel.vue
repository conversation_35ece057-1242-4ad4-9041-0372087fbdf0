<template>
  <div class="answer-sheet-select-panel" :style="panelStyle">
    <div style="position: relative">
      <div style="position: absolute; top: 7px; left: 10px; font-weight: bold; font-size: 14px">选考题</div>
      <div
        ref="input"
        style="width: 100%; height: 46px; padding: 8px 10px 2px 70px"
        contenteditable
        @blur="updateSelectDescription"
      >
        {{ questionGroup.selectDescription }}
      </div>
    </div>
    <div>
      <span style="display: inline-block; width: 92px; margin-left: 10px">我选做的题号是</span>
      <template v-for="code in questionCodes" :key="code">
        <span style="display: inline-block; width: 20px; margin-left: 24px">{{ code }}</span
        ><span
          class="select-mark-area"
          style="
            display: inline-block;
            width: 30px;
            height: 18px;
            border: 1px solid var(--base-border-color);
            vertical-align: -4px;
          "
          :data-code="code"
        ></span>
      </template>
    </div>
  </div>
</template>

<script>
  import TemplateSelectMarkArea from '@/helpers/scan_template_define/template_select_mark_area'
  import TemplateRect from '@/helpers/scan_template_define/template_rect'
  import { uniq } from '@/utils/array'
  import { SelectPanelHeight } from '@/const/answer_sheet'

  export default {
    props: {
      questionGroup: Object,
      border: {
        type: Object,
        default: () => ({
          left: true,
          right: true,
          top: true,
          bottom: false,
        }),
      },
    },
    computed: {
      panelStyle() {
        return {
          height: SelectPanelHeight + 'px',
          fontSize: '12px',
          lineHeight: '18px',
          borderTop: `1px solid ${this.border.top ? 'var(--base-border-color)' : 'transparent'}`,
          borderRight: `1px solid ${this.border.right ? 'var(--base-border-color)' : 'transparent'}`,
          borderBottom: `1px solid ${this.border.bottom ? 'var(--base-border-color)' : 'transparent'}`,
          borderLeft: `1px solid ${this.border.left ? 'var(--base-border-color)' : 'transparent'}`,
        }
      },
      questionCodes() {
        return uniq(this.questionGroup.questions.map(q => q.questionCode))
      },
    },
    methods: {
      updateSelectDescription() {
        let el = this.$refs.input
        if (el) {
          this.$store.commit('answerSheet/changeSelectDescription', {
            questionGroupId: this.questionGroup.id,
            selectDescription: el.innerText,
          })
        }
      },
      getSelectMarkAreas(areaLeft, areaTop) {
        let list = []
        let elRect = this.$el.getBoundingClientRect()
        let squares = this.$el.getElementsByClassName('select-mark-area')
        Array.from(squares).forEach(elSquare => {
          let area = new TemplateSelectMarkArea()
          let code = Number(elSquare.dataset.code)
          area.id = `SelectMarkArea-${code}`
          area._extra = {
            questionCode: code,
          }
          let squareRect = elSquare.getBoundingClientRect()
          area.matchArea = TemplateRect.createFromAnswerSheetPageRect(
            squareRect.left - elRect.left + areaLeft,
            squareRect.top - elRect.top + areaTop,
            squareRect.width,
            squareRect.height
          )
          list.push(area)
        })
        return list
      },
    },
  }
</script>
