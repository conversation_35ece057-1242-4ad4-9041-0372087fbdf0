<template>
  <div class="com-user-school">
    <div class="header">
      <div class="title">我所在的学校（{{ userSchools.length }}所）</div>
      <Input class="search" :model-value="keyword" clearable placeholder="搜索学校" @on-change="changeKeyword"></Input>
    </div>
    <Table :columns="tableColumns" :data="tableData"></Table>
    <div class="page">
      <Page
        show-elevator
        show-sizer
        :total="searchedSchool.length"
        :model-value="currentPage"
        :page-size="pageSize"
        :page-size-opts="[10, 20, 50, 100]"
        @on-change="changePage"
        @on-page-size-change="changePageSize"
      ></Page>
    </div>
  </div>
</template>

<script>
  import TextButton from '@/components/text_button'
  import { apiGetUserSchools, apiSetDefaultSchool, apiSwitchSchool } from '@/api/user'

  export default {
    data() {
      return {
        userSchools: [],
        keyword: '',
        pageSize: 10,
        currentPage: 1,
      }
    },
    computed: {
      user() {
        return this.$store.state.user
      },
      searchedSchool() {
        return this.userSchools.filter(x => x.schoolName.includes(this.keyword.trim()))
      },
      tableData() {
        return this.searchedSchool.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize)
      },
      tableColumns() {
        return [
          {
            title: '学校名称',
            key: 'schoolName',
          },
          {
            title: '是否默认',
            width: 120,
            align: 'center',
            render: (h, params) => h('span', {}, params.row.isDefault ? '默认' : ''),
          },
          {
            title: '操作',
            width: 180,
            align: 'center',
            render: (h, params) => {
              let buttons = [
                h(
                  TextButton,
                  {
                    type: 'primary',
                    disabled: params.row.isDefault,
                    onClick: () => {
                      this.setDefaultSchool(params.row)
                    },
                  },
                  () => '设为默认'
                ),
                h(
                  TextButton,
                  {
                    type: 'warning',
                    disabled: params.row.schoolId === this.user.schoolId,
                    onClick: () => {
                      this.switchSchool(params.row)
                    },
                  },
                  () => '切换至该校'
                ),
              ]

              return h('div', {}, buttons)
            },
          },
        ]
      },
    },
    created() {
      this.loadSchools()
    },
    methods: {
      loadSchools() {
        apiGetUserSchools().then(schools => {
          this.userSchools = schools || []
        })
      },
      setDefaultSchool(school) {
        if (school.isDefault) {
          this.$Message.info({
            content: `${school.schoolName}已是默认学校`,
          })
          return
        }

        apiSetDefaultSchool(school.schoolId).then(() => {
          this.$Message.success({
            content: `已将${school.schoolName}设为默认学校`,
          })
          this.loadSchools()
        })
      },
      switchSchool(school) {
        if (school.schoolId === this.user.schoolId) {
          this.$Message.info({
            content: `${school.schoolName}已是当前学校`,
          })
          return
        }

        this.$Spin.show({
          render: h =>
            h(
              'div',
              {
                style: {
                  fontSize: '24px',
                },
              },
              `正在切换至【${school.schoolName}】`
            ),
        })

        apiSwitchSchool(school.schoolId)
          .then(data => {
            // 清缓存
            window.sessionStorage.clear()
            // 设置新token
            let newToken = (data && data.token) || ''
            window.localStorage.setItem('token', newToken)
            // 刷新页面
            window.location.reload()
          })
          .catch(() => {
            this.$Spin.hide()
          })
      },
      changePage(page) {
        this.currentPage = page
      },
      changePageSize(pageSize) {
        this.pageSize = pageSize
        this.currentPage = 1
      },
      changeKeyword(e) {
        this.keyword = e.target.value
        this.currentPage = 1
      },
    },
  }
</script>

<style lang="scss" scoped>
  .com-user-school {
    min-width: 400px;

    .header {
      @include flex(row, space-between, center);

      .title {
        margin-bottom: 10px;
        font-weight: bold;
        line-height: 34px;
      }

      .search {
        width: 250px;
      }
    }

    .page {
      margin-top: 10px;
      text-align: right;
    }
  }
</style>
