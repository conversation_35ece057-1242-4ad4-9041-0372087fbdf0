(function() {
	function addCombo(editor, comboName, styleType, lang, entries, defaultLabel, styleDefinition, order) {
			var config = editor.config,
					style = new CKEDITOR.style(styleDefinition);

			// -- 1. 定义自定义行高对话框 --
			var dialogName = 'lineHeightDialog';
			CKEDITOR.dialog.add(dialogName, function(editor) {
					return {
							title: lang.dialogTitle,
							minWidth: 200,
							minHeight: 80,
							contents: [{
									id: 'info',
									label: '基本信息',
									accessKey: 'I',
									elements: [{
											type: 'hbox',
											widths: ['70%', '30%'],
											children: [{
													type: 'text',
													id: 'value',
													label: lang.valueLabel,
													'default': '',
													validate: CKEDITOR.dialog.validate.number(editor.lang.common.invalidLength),
											}, {
													type: 'select',
													id: 'unit',
													label: lang.unitLabel,
													'default': '',
													items: [
															[lang.unitMultiplier, ''],
															[lang.unitPixel, 'px']
													],
													setup: function(element) {
															// 已在value的setup中处理
													}
											}]
									}]
							}],
							onShow: function() {
									var selection = editor.getSelection();
									var element = selection.getStartElement();
									if (element) {
											this.setupContent(element);
									}
							},
							onOk: function() {
									var dialog = this;
									var value = dialog.getValueOf('info', 'value');
									var unit = dialog.getValueOf('info', 'unit');
									var lineHeight = value + unit;

									// 创建并应用动态样式
									var customStyle = new CKEDITOR.style(styleDefinition, { size: lineHeight });
									editor.applyStyle(customStyle);
							}
					};
			});
			// -- 对话框定义结束 --

			var names = entries.split(';'),
					values = [];
			var styles = {};
			for (var i = 0; i < names.length; i++) {
					var parts = names[i];
					if (parts) {
							parts = parts.split('/');
							var vars = {},
									name = names[i] = parts[0];
							vars[styleType] = values[i] = parts[1] || name;
							styles[name] = new CKEDITOR.style(styleDefinition, vars);
							styles[name]._.definition.name = name;
					} else
							names.splice(i--, 1);
			}

			editor.ui.addRichCombo(comboName, {
					label: lang.label,
					title: lang.panelTitle,
					toolbar: 'styles,' + order,
					allowedContent: style,
					requiredContent: style,
					panel: {
							css: [CKEDITOR.skin.getPath('editor')].concat(config.contentsCss),
							multiSelect: false,
							attributes: { 'aria-label': lang.panelTitle }
					},
					init: function() {
							this.startGroup(lang.panelTitle);
							for (var i = 0; i < names.length; i++) {
									var name = names[i];
									this.add(name, styles[name].buildPreview(), name);
							}
							// -- 2. 在列表末尾添加“自定义”选项 --
							this.add('custom', lang.custom, lang.custom);
					},
					onClick: function(value) {
							editor.focus();
							editor.fire('saveSnapshot');
							
							// -- 3. 判断点击的是否是“自定义” --
							if (value == 'custom') {
									editor.openDialog(dialogName);
									// 打开对话框后，不需要执行后续的 applyStyle
									return; 
							}

							var style = styles[value];
							editor[this.getValue() == value ? 'removeStyle' : 'applyStyle'](style);
							editor.fire('saveSnapshot');
					},
					onRender: function() {
							editor.on('selectionChange', function(ev) {
									var currentValue = this.getValue();
									var elementPath = ev.data.path,
											elements = elementPath.elements;
									for (var i = 0, element; i < elements.length; i++) {
											element = elements[i];
											for (var value in styles) {
													if (styles[value].checkElementMatch(element, true, editor)) {
															if (value != currentValue)
																	this.setValue(value);
															return;
													}
											}
									}

									this.setValue('', defaultLabel);
							}, this);
					},
					refresh: function() {
							if (!editor.activeFilter.check(style))
									this.setState(CKEDITOR.TRISTATE_DISABLED);
					}
			});
	}

	CKEDITOR.plugins.add('lineheight', {
			requires: 'richcombo,dialog',
			lang: 'ar,de,en,es,fr,ko,pt,zh-cn',
			init: function(editor) {
					var config = editor.config;
					addCombo(editor, 'lineheight', 'size', editor.lang.lineheight, config.line_height, editor.lang.lineheight.title, config.lineHeight_style, 40);
			}
	});
})();

CKEDITOR.config.line_height = '1;1.1;1.2;1.3;1.4;1.5;1.6;1.7;1.8;1.9;2;2.5;3;4px;6px;8px;10px;12px';
CKEDITOR.config.lineHeight_style = {
	element: 'p',
	styles:   { 'line-height': '#(size)' },
	overrides: [
			{ element: 'span', styles: { 'line-height': null } }
	]
};