import ajax from '@/api/ajax'

export function apiGetSchoolCoachBooks(params) {
  return ajax.get({
    url: 'user/coachBook/list',
    params: {
      semesterId: params.semesterId,
      gradeId: params.gradeId,
      coachBookName: params.coachBookName,
      pageNum: params.currentPage,
      pageSize: params.pageSize,
    },
  })
}

export function apiAddFeedbackCoachBook(requestParams) {
  return ajax.post({
    url: 'user/coachBook/add',
    params: {
      term: requestParams.term, // 1 - 第一学期 | 2 - 第二学期
      coachBookId: requestParams.coachBookId,
      coachBookName: requestParams.coachBookName,
      gradeId: requestParams.gradeId,
      subjectId: requestParams.subjectId,
    },
    requestName: '添加教辅到错题反馈',
  })
}

export function apiDeleteFeedbackCoachBook(id) {
  return ajax.delete({
    url: 'user/coachBook/delete',
    params: {
      id: id,
    },
    requestName: '删除错题反馈教辅',
  })
}
