import ajax from '@/api/ajax'
import { Question } from '@/helpers/qlib/question'

/**
 * 报错操作
 */
export function apiMarkCorrectQuestion({ questionId, paperId, errorTypeJson, description }) {
  return ajax.put({
    url: `ques/questionCorrect/mark`,
    data: {
      questionId,
      paperId,
      errorTypeJson,
      description,
    },
    requestName: '试题报错',
  })
}

export function apiUnMarkCorrectQuestion(questionId) {
  return ajax.put({
    url: 'ques/questionCorrect/unMark',
    params: {
      questionId,
    },
    requestName: '取消试题报错',
  })
}

// 查某题个人报错记录
export function apiGetQuestionCorrectItem(questionId) {
  return ajax.get({
    url: 'ques/questionCorrect/item',
    params: {
      questionId,
    },
  })
}

export function apiGetCorrectQuestions(params) {
  return ajax
    .get({
      url: 'ques/questionCorrect/list',
      params,
      requestName: '获取报错题目列表',
    })
    .then(res => {
      return {
        total: res.total,
        records: res.records.map(item => {
          return {
            ...item,
            question: Question.import(item.question),
          }
        }),
      }
    })
}

export function apiUpdateCorrectQuestionStatusToHandled(questionId) {
  return ajax.put({
    url: `ques/questionCorrect/updateStatusToHandled`,
    params: {
      questionId,
    },
    requestName: '标记报错题目已处理',
  })
}

export function apiCorrectQuestion(question) {
  return question
    .export()
    .then(data => {
      return ajax.put({
        url: 'ques/questionCorrect/correct',
        data,
        requestName: '纠正试题',
      })
    })
    .then(data => {
      return Question.import(data)
    })
}
