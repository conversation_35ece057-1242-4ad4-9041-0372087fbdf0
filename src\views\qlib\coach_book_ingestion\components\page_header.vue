<template>
  <div class="coach-book-ingestion-header">
    <div class="back">
      <Icon class="icon-back" title="返回" type="ios-arrow-back" @click="back"></Icon>
    </div>
    <div class="title-bar">
      <div class="title">{{ title }}</div>
    </div>
    <div class="step">
      <span class="label">当前阶段：</span>
      <Dropdown @on-click="handleStepClick">
        <span class="current-step">{{ currentStepName }}</span
        ><Icon type="ios-arrow-down"></Icon>
        <template #list>
          <DropdownItem v-for="step in steps" :key="step.id" :name="step.id">{{ step.name }}</DropdownItem>
        </template>
      </Dropdown>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'

  import { useCoachBookIngestionStore } from '@/store/qlib/coach_book_ingestion'

  import IngestionStepEnum from '@/enum/qlib/ingestion/step'

  const emit = defineEmits(['back'])

  const ingestionStore = useCoachBookIngestionStore()

  const currentStepName = computed(() => {
    return IngestionStepEnum.getNameById(ingestionStore.currentStep) || '待定'
  })

  const steps = computed(() => {
    return IngestionStepEnum.getEntries()
  })

  const title = computed(() => {
    let bookName = ingestionStore.coachBookInfo?.bookName
    if (bookName) {
      return `教辅录入——${bookName}`
    } else {
      return '教辅录入'
    }
  })

  function back() {
    emit('back')
  }

  function handleStepClick(step) {
    if (step == ingestionStore.currentStep) {
      return
    }
    ingestionStore.setCurrentStep(step)
  }
</script>

<style lang="scss" scoped>
  .coach-book-ingestion-header {
    @include flex(row, flex-start, center);
    position: relative;
    min-height: 36px;
    padding: 0 16px;
    color: white;
    line-height: 1;
    background-color: $color-primary-dark;

    .back {
      flex-shrink: 0;
      margin-right: 10px;
      font-size: $font-size-large;
      cursor: pointer;
    }

    .title-bar {
      flex-shrink: 1;
      margin-right: 32px;
      font-weight: bold;
      font-size: $font-size-medium-x;
    }

    .step {
      margin-right: 16px;
      margin-left: auto;
    }
  }
</style>
