<template>
  <div class="block-topic">
    <com-box-content
      :content="block.topic.descriptionContent"
      :width="pageBodyWidth"
      :height="block.topic.descriptionHeight"
      :padding-top="6"
      :border="contentBorderStyle"
      @height-change="handleHeightChange"
      @content-change="changeContent"
    ></com-box-content>

    <div v-if="showQuestionContent" class="page-block-toolbar no-print">
      <div class="btn btn-primary" title="设置" @click="showModalSetting = true">
        <Icon class="btn-icon" type="ios-settings"></Icon>
      </div>
    </div>

    <ModalTopicSetting v-model="showModalSetting" :topic="block.topic"></ModalTopicSetting>
  </div>
</template>
<script>
  import comBoxContent from './box_content.vue'
  import ModalTopicSetting from './modal_topic_setting.vue'

  import { mapMutations } from 'vuex'

  export default {
    components: {
      comBoxContent,
      ModalTopicSetting,
    },
    props: {
      block: {
        type: Object,
        required: true,
      },
    },
    emits: ['height-change'],
    data() {
      return {
        showModalSetting: false,
      }
    },
    computed: {
      pageBodyWidth() {
        let pageSize = this.$store.getters['answerSheet/pageSize'](this.block.pageIndex)
        return pageSize.width - pageSize.left - pageSize.right
      },
      contentBorderStyle() {
        return {
          left: false,
          right: false,
          top: false,
          bottom: false,
        }
      },
      objectiveTopicDescriptionShowTopicName() {
        return this.$store.getters['answerSheet/objectiveTopicDescriptionShowTopicName']
      },
      showScoreTypeTrueFalseTip() {
        return this.$store.getters['answerSheet/showScoreTypeTrueFalseTip'](this.block.topic.code)
      },
      showQuestionContent() {
        return this.$store.getters['answerSheet/showQuestionContent']
      },
    },
    watch: {
      objectiveTopicDescriptionShowTopicName: {
        handler() {
          this.$nextTick().then(() => {
            this.$store.commit('answerSheet/changeObjectiveTopicDescriptionShowTopicName', {
              topicCode: this.block.topic.code,
              showTopicName: this.objectiveTopicDescriptionShowTopicName,
            })
          })
        },
        immediate: true,
      },
      showScoreTypeTrueFalseTip: {
        handler() {
          this.$nextTick().then(() => {
            this.$store.commit('answerSheet/changeShowScoreTypeTrueFalseTip', {
              topicCode: this.block.topic.code,
              ...this.showScoreTypeTrueFalseTip,
            })
          })
        },
        immediate: true,
        deep: true,
      },
    },
    created() {
      this.changeInstance()
    },
    updated() {
      this.changeInstance()
    },
    methods: {
      ...mapMutations('answerSheet', ['changePageBlockInstance', 'changePageBlockContent', 'changePageBlockHeight']),
      changeInstance() {
        this.changePageBlockInstance({
          blockId: this.block.id,
          instance: this,
        })
      },
      changeContent(content) {
        this.changePageBlockContent({
          blockId: this.block.id,
          content: content,
        })
      },
      changeHeight(height) {
        this.changePageBlockHeight({
          blockId: this.block.id,
          height: height,
        })
      },
      handleHeightChange(height) {
        if (this.block.topic.descriptionHeight === height) {
          return
        }
        this.changeHeight(height)
        this.$emit('height-change', height)
      },
    },
  }
</script>
