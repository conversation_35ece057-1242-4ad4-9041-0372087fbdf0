<template>
  <transition
    @before-enter="beforeEnter"
    @enter="enter"
    @after-enter="afterEnter"
    @enter-cancelled="enterCancelled"
    @before-leave="beforeLeave"
    @leave="leave"
    @after-leave="afterLeave"
    @leave-cancelled="leaveCancelled"
  >
    <slot></slot>
  </transition>
</template>
<script>
  export default {
    props: {
      duration: {
        type: Number,
        default: 0.5,
      },
    },
    data() {
      return {}
    },
    methods: {
      beforeEnter(el) {
        if (!el.dataset) el.dataset = {}
        el.dataset.oldTransitionDuration = el.style.transitionDuration
        el.dataset.oldPaddingTop = el.style.paddingTop
        el.dataset.oldPaddingBottom = el.style.paddingBottom

        el.classList.add('transition-collapsing')
        el.style.transitionDuration = this.duration + 's'

        el.style.height = 0
        el.style.paddingTop = 0
        el.style.paddingBottom = 0
      },
      enter(el) {
        el.dataset.oldOverflow = el.style.overflow
        if (el.scrollHeight !== 0) {
          el.style.height =
            el.scrollHeight +
            (parseFloat(el.dataset.oldPaddingTop) || 0) +
            (parseFloat(el.dataset.oldPaddingBottom) || 0) +
            'px'
          el.style.paddingTop = el.dataset.oldPaddingTop
          el.style.paddingBottom = el.dataset.oldPaddingBottom
        } else {
          el.style.height = ''
          el.style.paddingTop = el.dataset.oldPaddingTop
          el.style.paddingBottom = el.dataset.oldPaddingBottom
        }

        el.style.overflow = 'hidden'
      },
      afterEnter(el) {
        el.classList.remove('transition-collapsing')
        el.style.transitionDuration = el.dataset.oldTransitionDuration
        el.style.height = ''
        el.style.overflow = el.dataset.oldOverflow
      },
      enterCancelled() {},
      beforeLeave(el) {
        if (!el.dataset) el.dataset = {}
        el.dataset.oldPaddingTop = el.style.paddingTop
        el.dataset.oldPaddingBottom = el.style.paddingBottom
        el.dataset.oldOverflow = el.style.overflow
        el.dataset.oldTransitionDuration = el.style.transitionDuration

        el.style.height = el.scrollHeight + 'px'
        el.style.overflow = 'hidden'
      },
      leave(el) {
        if (el.scrollHeight !== 0) {
          el.classList.add('transition-collapsing')
          el.style.transitionDuration = this.duration + 's'
          el.style.height = 0
          el.style.paddingTop = 0
          el.style.paddingBottom = 0
        }
      },
      afterLeave(el) {
        el.classList.remove('transition-collapsing')
        el.style.height = ''
        el.style.overflow = el.dataset.oldOverflow
        el.style.paddingTop = el.dataset.oldPaddingTop
        el.style.paddingBottom = el.dataset.oldPaddingBottom
        el.style.transitionDuration = el.dataset.oldTransitionDuration
      },
      leaveCancelled() {},
    },
  }
</script>

<style scoped>
  .transition-collapsing {
    transition: height 0.5s ease, padding-top 0.5s ease, padding-bottom 0.5s ease;
  }
</style>
