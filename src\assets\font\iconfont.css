@font-face {
  font-family: "iconfont"; /* Project id 4528513 */
  src: url('iconfont.woff2?t=1716970427470') format('woff2'),
       url('iconfont.woff?t=1716970427470') format('woff'),
       url('iconfont.ttf?t=1716970427470') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-exception:before {
  content: "\e600";
}

.icon-batch-action:before {
  content: "\e7ad";
}

