<template>
  <Modal :model-value="modelValue" title="客观题设置" width="1200" @on-visible-change="handleModalVisibleChange">
    <div class="excel-import">
      <div class="export-import-left">
        <Button size="small" type="primary" @click="readObjectiveSettingFromExcel">从Excel导入</Button>
        <span class="import-excel-explain">
          导入的Excel文件必须包含以下列：题号、分数、答案；多选题其他答案也给分的，请设置【其他答案分数】，格式如：A=2;B=2;AB=3
        </span>
      </div>
      <Button type="primary" size="small" @click="exportToExcel">导出到Excel</Button>
    </div>

    <Table :data="modalObjectives" :columns="tableColumns" :span-method="handleSpan" border></Table>

    <template #footer>
      <div class="modal-footer">
        <Button type="text" @click="handleCloseModal">取消</Button>
        <Button type="primary" @click="handleBtnConfirmChangeObjectives">确定</Button>
      </div>
    </template>

    <component-modal-answer-score-list
      v-model="modalAnswerScoreListShowed"
      :question="questionInModal"
      @change="handleChangeAnswerScoreList"
    ></component-modal-answer-score-list>
  </Modal>
</template>

<script>
  import { InputNumber, Select, Option, Tooltip } from 'view-ui-plus'
  import TextButton from '@/components/text_button'
  import ComponentRadioGroup from '@/components/radio_group'
  import ComponentModalAnswerScoreList from '@/views/emarking/exam/paper/components/modal_answer_score_list'

  import { getObjectiveQuestionAnswerChars } from '@/helpers/emarking/objective_question'
  import excelImport from '@/utils/excel_import'
  import { exportExcel } from '@/utils/excel_export'
  import { numberToChinese } from '@/utils/number'
  import { splitQuestionScore } from '@/helpers/emarking/miscellaneous'
  import { isUniq, isUniqBy } from '@/utils/array'

  import BranchType, { ObjectiveBranchTypeEnum } from '@/enum/qlib/branch_type'

  export default {
    components: {
      'component-modal-answer-score-list': ComponentModalAnswerScoreList,
    },

    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },

      objectives: {
        type: Array,
        default: () => [],
      },

      testName: {
        type: String,
        default: '手阅测试',
      },
    },

    emits: ['update:modelValue', 'change-objectives'],

    data() {
      return {
        modalAnswerScoreListShowed: false,
        questionInModal: null,

        modalObjectives: [],
        tableColumns: [
          {
            title: '大题',
            align: 'center',
            minWidth: 150,
            render: (h, params) => h('span', {}, numberToChinese(params.row.topicCode)),
          },
          {
            title: '题号',
            align: 'center',
            minWidth: 100,
            key: 'questionCode',
          },
          {
            title: '题型',
            align: 'center',
            minWidth: 100,
            render: (h, params) => {
              if (params.row.branchTypeId === BranchType.TrueOrFalse.id) {
                return h('span', {}, BranchType.TrueOrFalse.name)
              } else {
                return h(
                  Select,
                  {
                    size: 'small',
                    transfer: true,
                    modelValue: params.row.branchTypeId,
                    style: {
                      width: '80px',
                    },
                    onOnChange: value => {
                      let question = this.modalObjectives.find(
                        x => x.questionCode === params.row.questionCode && x.topicCode === params.row.topicCode
                      )
                      if (question) {
                        question.branchTypeId = value
                      }
                    },
                  },
                  () =>
                    this.optionalBranchType.map(x =>
                      h(
                        Option,
                        {
                          value: x.id,
                        },
                        () => x.name
                      )
                    )
                )
              }
            },
          },
          {
            title: '选项',
            align: 'center',
            minWidth: 100,
            key: 'optionCount',
          },
          {
            title: '分数',
            align: 'center',
            minWidth: 120,
            render: (h, params) =>
              h(InputNumber, {
                modelValue: Number(params.row.fullScore),
                size: 'small',
                min: 0,
                step: 0.5,
                onOnChange: score => {
                  let target = this.modalObjectives.find(
                    x => x.questionCode === params.row.questionCode && x.topicCode === params.row.topicCode
                  )
                  if (target) {
                    if (target.answerScoreList) {
                      target.answerScoreList = ''
                    }
                    target.fullScore = score
                  }
                },
              }),
          },
          {
            title: '答案',
            align: 'center',
            minWidth: 200,
            render: (h, params) => {
              let isMultipleChoice = params.row.branchTypeId === BranchType.MultipleChoice.id
              return h(ComponentRadioGroup, {
                modelValue: isMultipleChoice ? (params.row.standardAnswer || '').split('') : params.row.standardAnswer,
                radioes: getObjectiveQuestionAnswerChars({
                  ...params.row,
                  branchTypeId: params.row.branchTypeId,
                }).map(x => ({
                  id: x,
                  name: x,
                })),
                multiple: isMultipleChoice,
                button: true,
                style: {
                  display: 'inline-block',
                },
                'onUpdate:modelValue': answer => {
                  let question = this.modalObjectives.find(
                    x => x.questionCode === params.row.questionCode && x.topicCode === params.row.topicCode
                  )

                  if (question) {
                    if (isMultipleChoice) {
                      question.standardAnswer = answer.join('')
                      question.answerScoreList = ''
                    } else {
                      question.standardAnswer = answer
                    }
                  }
                },
              })
            },
          },
          {
            title: '其它答案分数',
            align: 'center',
            minWidth: 200,
            render: (h, params) => {
              if (params.row.branchTypeId === BranchType.MultipleChoice.id) {
                if (params.row.answerScoreList) {
                  return h(
                    Tooltip,
                    {
                      content: params.row.answerScoreList,
                      'max-width': '800',
                      transfer: true,
                    },
                    [
                      h(
                        TextButton,
                        {
                          type: 'default',
                          onClick: () => this.handleSetAnswerScoreList(params.row),
                        },
                        () =>
                          params.row.answerScoreList.length > 21
                            ? params.row.answerScoreList.slice(0, 21) + '……'
                            : params.row.answerScoreList
                      ),
                    ]
                  )
                } else {
                  return h(
                    TextButton,
                    {
                      type: 'primary',
                      onClick: () => {
                        this.handleSetAnswerScoreList(params.row)
                      },
                    },
                    '设置'
                  )
                }
              } else {
                return h('span', {}, () => '-')
              }
            },
          },
        ],
      }
    },

    computed: {
      branchType() {
        return BranchType.getIdNames()
      },

      optionalBranchType() {
        return this.branchType.filter(x => x.id > 10 && x.id < 13)
      },
    },

    watch: {
      modelValue() {
        if (this.modelValue) {
          this.modalObjectives = this.objectives.map(x => x.clone())
        }
      },
    },

    methods: {
      handleModalVisibleChange(visible) {
        if (!visible) {
          this.handleCloseModal()
        }
      },

      handleCloseModal() {
        this.$emit('update:modelValue', false)
      },

      handleBtnConfirmChangeObjectives() {
        this.$emit('change-objectives', this.modalObjectives)
        this.handleCloseModal()
      },

      handleSetAnswerScoreList(row) {
        let q = this.modalObjectives.find(x => x.questionCode === row.questionCode && x.topicCode === row.topicCode)
        if (!q) {
          return
        }
        if (!q.standardAnswer) {
          this.$Message.info({
            content: '请先设置标准答案',
          })
          return
        }

        this.questionInModal = {
          ...q,
          defaultBranchName: q.topicCode + '、' + q.topicName + '（' + q.questionCode + '）',
        }

        this.modalAnswerScoreListShowed = true
      },

      handleChangeAnswerScoreList(answerScoreList) {
        let q = this.modalObjectives.find(
          x =>
            this.questionInModal &&
            x.questionCode === this.questionInModal.questionCode &&
            x.topicCode === this.questionInModal.topicCode
        )
        if (q) {
          q.answerScoreList = answerScoreList
        }
      },

      handleSpan({ row, rowIndex, columnIndex }) {
        let index = this.modalObjectives.findIndex(x => x.topicCode === row.topicCode)
        let count = this.modalObjectives.filter(x => x.topicCode === row.topicCode).length
        if (index === -1) {
          return [1, 1]
        } else {
          if (!columnIndex && rowIndex === index) {
            return [count, 1]
          } else if (!columnIndex && rowIndex > index) {
            return [0, 0]
          }
        }
      },

      readObjectiveSettingFromExcel() {
        excelImport(this.checkExcelData, {
          columns: [
            {
              title: '题号',
              key: 'questionCode',
              required: true,
              unique: true,
              range: this.modalObjectives.map(q => q.questionCode),
            },
            {
              title: '题型',
              key: 'questionType',
            },
            {
              title: '分数',
              key: 'fullScore',
              required: true,
            },
            {
              title: '答案',
              key: 'answer',
              required: true,
            },
            {
              title: '其他答案分数',
              key: 'answerScoreList',
            },
          ],
        })
      },

      checkExcelData(rows) {
        let changeableObjectiveQuestionType = [BranchType.SingleChoice, BranchType.MultipleChoice]
        let newQuestions = rows
          .filter(row => this.modalObjectives.some(x => x.questionCode === row.questionCode))
          .map(row => {
            let target = this.modalObjectives.find(x => x.questionCode === row.questionCode)
            let newQuestion = target.clone()
            newQuestion.fullScore = row.fullScore
            newQuestion.standardAnswer = row.answer.toUpperCase()
            newQuestion.answerScoreList = row.answerScoreList
            row.branchTypeId = (changeableObjectiveQuestionType.find(x => x.name === row.questionType) || { id: '' }).id
            if (row.branchTypeId && row.branchTypeId !== newQuestion.branchTypeId) {
              newQuestion.branchTypeId = row.branchTypeId
            }

            return newQuestion
          })

        let errors = newQuestions
          .map(x => this.checkQuestion(x))
          .filter(Boolean)
          .join('<br>')
        if (errors) {
          this.$Modal.error({
            title: '导入失败',
            content: errors,
          })
          return
        }

        newQuestions.forEach(question => {
          let target = this.modalObjectives.find(x => x.questionCode === question.questionCode)
          target.branchTypeId = question.branchTypeId
          target.fullScore = question.fullScore
          target.standardAnswer = question.standardAnswer
          target.answerScoreList = question.answerScoreList
        })

        this.$Message.info({
          content: `成功从Excel中导入了${newQuestions.length}条数据`,
        })
      },

      checkQuestion(item) {
        let error = ''

        if (Number.isNaN(Number(item.fullScore)) || Number(item.fullScore) < 0) {
          // 分数
          error = '分数输入有误'
        } else {
          let legalCharacters
          if (item.branchTypeId == BranchType.TrueOrFalse.id) {
            legalCharacters = 'TF'
          } else {
            legalCharacters = []
            for (let i = 65; i < 65 + item.optionCount; i++) {
              legalCharacters.push(i)
            }
            legalCharacters = String.fromCharCode(...legalCharacters)
          }
          let answerArray = item.standardAnswer.split('')
          if (answerArray.some(x => !legalCharacters.includes(x))) {
            error = '答案格式错误（非法选项）'
          } else if (
            (answerArray.length > 1 &&
              [BranchType.SingleChoice.id, BranchType.TrueOrFalse.id].includes(item.branchTypeId)) ||
            (answerArray.length === 1 && item.branchTypeId === BranchType.MultipleChoice.id)
          ) {
            error = '答案格式有误（答案个数不符合题型）'
          }

          // 其它答案分数
          if (item.branchTypeId != BranchType.MultipleChoice.id) {
            item.answerScoreList = ''
          } else {
            item.answerScoreList = String(item.answerScoreList || '')
              .replace(' ', '')
              .replace('；', ';')
              .toUpperCase()

            let answerScores = []
            try {
              answerScores = splitQuestionScore(item.answerScoreList)
            } catch {
              error = '其他答案分数格式错误'
            }

            let resultAnswerScores = []
            for (let pair of answerScores) {
              let answer = pair.name
              let score = pair.score
              if (!this.isChoiceAnswerValid(answer, item.optionCount)) {
                error = '其他答案分数格式错误'
              }
              if (!(score > 0 && score <= item.fullScore)) {
                error = '其他答案分数格式错误'
              }
              resultAnswerScores.push({
                answer: Array.from(answer).sort().join(''),
                score,
              })
            }
            if (!isUniqBy(resultAnswerScores, x => x.answer)) {
              error = '其他答案分数格式错误'
            } else if (resultAnswerScores.some(x => x.answer === item.answer && x.score !== item.fullScore)) {
              error = '其他答案分数与标准答案分数不一致'
            } else {
              item.answerScoreList = resultAnswerScores.map(x => `${x.answer}=${x.score}`).join(';')
            }
          }
        }

        return error ? `题号${item.questionCode}：${error}` : ''
      },

      isChoiceAnswerValid(answer, optionCount) {
        let maxAnswerChar = String.fromCharCode(65 + optionCount)
        let chars = Array.from(answer)
        return isUniq(chars) && chars.every(char => char >= 'A' && char < maxAnswerChar)
      },

      exportToExcel() {
        let columns = [
          {
            title: '大题',
            key: dataRow => {
              return numberToChinese(dataRow.topicCode)
            },
            numberFormat: '', // 数字格式
            width: 'auto', // 列宽 = 'auto' | number | function(cellValueString)
            freeze: false, // 是否冻结该列
          },
          {
            title: '题号',
            key: 'questionCode',
            numberFormat: '', // 数字格式
            width: 'auto', // 列宽 = 'auto' | number | function(cellValueString)
            freeze: false, // 是否冻结该列
          },
          {
            title: '题型',
            key: dataRow => {
              for (let k in ObjectiveBranchTypeEnum) {
                if (ObjectiveBranchTypeEnum[k].id === dataRow.branchTypeId) {
                  return ObjectiveBranchTypeEnum[k].name
                }
              }
            },
            numberFormat: '',
            width: 'auto',
            freeze: false,
          },
          {
            title: '选项',
            key: 'optionCount',
            numberFormat: '',
            width: 'auto',
            freeze: false,
          },
          {
            title: '分数',
            key: 'fullScore',
            numberFormat: '',
            width: 'auto',
            freeze: false,
          },
          {
            title: '答案',
            // key: 'answer',
            numberFormat: '',
            width: 'auto',
            freeze: false,
            key: dataRow => {
              let isMultipleChoice = dataRow.branchTypeId === BranchType.MultipleChoice.id
              return isMultipleChoice ? (dataRow.standardAnswer || '').split('') : dataRow.standardAnswer
            },
          },
          {
            title: '其它答案分数',
            key: dataRow => {
              if (dataRow.branchTypeId !== ObjectiveBranchTypeEnum.MultipleChoice.id) {
                return '-'
              } else if (dataRow.answerScoreList) {
                return dataRow.answerScoreList
              }
              return '-'
            },
            numberFormat: '',
            width: 'auto',
            freeze: false,
          },
        ]

        let sheets = [
          {
            sheetName: `${this.testName}客观题`, // sheet名称
            rows: this.modalObjectives, // 数据数组
            freezeHeader: true, // 是否冻结表头
            columns: columns,
          },
        ]

        exportExcel(sheets, `${this.testName}客观题`)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .excel-import {
    @include flex(row, space-between, flex-start);
    margin-bottom: 15px;

    .export-import-left {
      @include flex(row, flex-start, flex-start);
    }

    .import-excel-explain {
      margin-right: 20px;
      margin-left: 10px;
      color: $color-icon;
    }
  }
</style>
