/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'is', {
	alt: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> texti',
	btnUpload: 'Hlaða upp',
	captioned: 'Captioned image', // MISSING
	captionPlaceholder: 'Caption', // MISSING
	infoTab: 'Almennt',
	lockRatio: 'Festa stærðarhlutfall',
	menu: 'Eigindi myndar',
	pathName: 'image', // MISSING
	pathNameCaption: 'caption', // MISSING
	resetSize: '<PERSON><PERSON><PERSON> stær<PERSON>',
	resizer: 'Click and drag to resize', // MISSING
	title: 'Eigindi myndar',
	uploadTab: 'Senda upp',
	urlMissing: 'Image source URL is missing.', // MISSING
	altMissing: 'Alternative text is missing.' // MISSING
} );
