import {
  apiGetExamById,
  apiGetSubjectScanTemplates,
  apiGetObjectiveQuestions,
  apiGetSubjectiveQuestions,
  apiGetBlocks,
} from '@/api/emarking'
import { apiGetScanRange, apiListStationScanners } from '@/api/scan/scan_user'
import { apiGetTemplateContent } from '@/api/scan/template'

import { buildTemplateObjectiveSubjectiveDefine } from '@/helpers/scan'
import ExamScopeEnum from '@/enum/emarking/exam_scope'
import UploadModeEnum from '@/enum/emarking/upload_mode'

export default {
  namespaced: true,

  state: {
    // 考试Id
    examId: '',
    // 考试科目Id
    examSubjectId: '',
    // 考试信息
    exam: null,

    // 科目客观题定义
    objectiveDefine: [],
    // 科目主观题定义
    subjectiveDefine: [],
    // 科目题块定义
    blockDefine: [],
    // 科目选做题定义
    selectDefine: [],

    // 扫描模板
    scanTemplates: [],
    // 扫描模板内容
    templateContent: null,

    // 扫描范围
    scanRange: null,
    // 可见扫描点扫描员
    stationScanners: [],
    // 页面显示模式
    viewMode: {
      isAdmin: false,
      scanStationId: 0,
      scanUserId: '',
    },
  },

  getters: {
    /**
     * 考试、科目
     */
    hasExam(state) {
      return Boolean(state.exam)
    },
    examId(state) {
      return state.examId
    },
    examSubjectId(state) {
      return state.examSubjectId
    },
    examName(state) {
      if (!state.exam) {
        return ''
      }
      return state.exam.examName
    },
    isInSchoolExam(state) {
      if (!state.exam) {
        return false
      }
      return state.exam.examScope.id == ExamScopeEnum.Single.id
    },
    examSubject(state) {
      if (!state.exam) {
        return null
      }
      return state.exam.subjects.find(x => x.examSubjectId == state.examSubjectId)
    },
    subjectName(state, getters) {
      return (getters.examSubject && getters.examSubject.subjectName) || ''
    },

    /**
     * 科目题目
     */
    objectiveDefine(state) {
      return state.objectiveDefine
    },
    subjectiveDefine(state) {
      return state.subjectiveDefine
    },
    blockDefine(state) {
      return state.blockDefine
    },
    selectDefine(state) {
      return state.selectDefine
    },

    /**
     * 扫描模板，暂时只用到主模板
     */
    scanTemplate(state) {
      return state.scanTemplates.find(x => x.isPrimary)
    },
    templateContent(state) {
      return state.templateContent
    },
    isPostScan(state, getters) {
      return getters.examSubject?.uploadMode.id == UploadModeEnum.PostScan.id
    },
    hasObjectiveQuestion(state) {
      return state.objectiveDefine.length > 0
    },
    hasSubjectiveQuestion(state) {
      return state.subjectiveDefine.length > 0
    },
    hasBlock(state) {
      return state.blockDefine.length > 0
    },
    hasSelectQuestion(state) {
      if (!state.templateContent) {
        return false
      }
      let selectGroups = state.templateContent.selectGroups
      return selectGroups != null && selectGroups.length > 0 && state.selectDefine.length > 0
    },
    templatePageCount(state) {
      if (!state.templateContent) {
        return 0
      }
      return state.templateContent.tpl.tplFile.pages.length
    },
    templatePaperCount(state) {
      if (!state.templateContent) {
        return 0
      }
      let pageCount = state.templateContent.tpl.tplFile.pages.length
      let isDoubleSide = state.templateContent.tpl.doubleSide == 'Y'
      return isDoubleSide ? Math.ceil(pageCount / 2) : pageCount
    },
    // 页面包含的题块Id
    pageBlockIds(state) {
      return pageIndex => {
        if (!state.templateContent) {
          return []
        }
        let page = state.templateContent.tpl.tplFile.pages.find(p => p.pageIndex == pageIndex)
        if (!page) {
          return []
        }
        let blockIdSet = new Set()
        let subjectives = state.templateContent.questions.subjectives
        page.subjectiveAreas.forEach(area => {
          let subjective = subjectives.find(s => s.areaIds.includes(area.id))
          if (subjective) {
            blockIdSet.add(subjective.subjectiveId)
          }
        })
        return Array.from(blockIdSet)
      }
    },

    /**
     * 扫描范围
     */
    scanRange(state) {
      return state.scanRange
    },
    hasPermission(state) {
      if (!state.scanRange) {
        return false
      }
      let { allSchool, schoolIds } = state.scanRange
      return allSchool || (schoolIds && schoolIds.length > 0)
    },
    // 扫描员所在扫描点
    userScanStation(state) {
      let { examAdmin, scanStationId } = state.scanRange || {}
      if (!examAdmin && scanStationId) {
        return state.stationScanners.find(x => x.scanStationId == scanStationId) || null
      }
      return null
    },
    stationScanners(state) {
      return state.stationScanners
    },
    viewMode(state) {
      return state.viewMode
    },
    // 扫描点名称
    scanStationName(state) {
      return scanStationId => {
        let station = state.stationScanners.find(x => x.scanStationId == scanStationId)
        return station && station.scanStationName
      }
    },

    /**
     * 权限
     * 0-管理员，1-扫描员，2-处理员，3-纯扫描员
     */
    hasPermissionScan(state) {
      return state.scanRange && [0, 1, 3].includes(state.scanRange.roleId)
    },
    hasPermissionHandleLocateEx(state) {
      return state.scanRange && [0, 1, 3].includes(state.scanRange.roleId)
    },
    hasPermissionHandleOtherEx(state) {
      return state.scanRange && [0, 1, 2].includes(state.scanRange.roleId)
    },
    hasPermissionChangeScanUnit(state, getters, rootState) {
      return scanUnit => {
        let { scanAdmin, allSchool, scanStationId } = state.scanRange || {}
        // 校内考试、考试管理员、联考管理员或处理员可处理所有
        if (scanAdmin && allSchool) {
          return true
        }
        // 扫描点管理员或处理员可处理自己扫描点扫描的
        if (scanAdmin && scanUnit.scanStationId == scanStationId) {
          return true
        }
        // 扫描员或纯扫描员只可处理自己扫描的
        return rootState.user.userId == scanUnit.scanUserId
      }
    },
    isSimpleScanner(state) {
      return !state.scanRange || state.scanRange.roleId == 3
    },

    /**
     * 扫描复核权限：考试管理员，或不限扫描点的扫描管理员、扫描处理员
     */
    hasPermissionReview(state) {
      if (!state.scanRange) {
        return false
      }
      let { examAdmin, roleId, allSchool } = state.scanRange
      return examAdmin || (allSchool && [0, 2].includes(roleId))
    },

    /**
     * 请求一页异常接口参数
     */
    exceptionPageApiParams(state, getters, rootState) {
      if (!state.viewMode.isAdmin) {
        return {
          examSubjectId: state.examSubjectId,
          scanStationId: state.scanRange && state.scanRange.scanStationId,
          scanUserId: rootState.user.userId,
        }
      } else {
        return {
          examSubjectId: state.examSubjectId,
          scanStationId: state.viewMode.scanStationId,
          scanUserId: state.viewMode.scanUserId,
        }
      }
    },
  },

  mutations: {
    clear(state) {
      state.examId = ''
      state.examSubjectId = ''
      state.exam = null

      state.objectiveDefine = []
      state.subjectiveDefine = []
      state.selectDefine = []

      state.scanTemplates = []
      state.templateContent = null

      state.scanRange = null
      state.stationScanners = []
      state.viewMode = {
        isAdmin: false,
        scanStationId: 0,
        scanUserId: '',
      }
    },
    changeViewMode(state, { isAdmin, scanStationId, scanUserId } = {}) {
      // 单校不能切换viewMode
      if (state.exam.examScope.id == ExamScopeEnum.Single.id) {
        state.viewMode = {
          isAdmin: true,
          scanStationId: 0,
          scanUserId: '',
        }
        return
      }

      // 扫描管理员，未明确指定为扫描模式的，都处于管理模式
      if (state.scanRange.scanAdmin && isAdmin !== false) {
        // 如果指定扫描点，则扫描点必须在扫描范围内
        let scanStation = null
        if (scanStationId) {
          scanStation = state.stationScanners.find(x => x.scanStationId == scanStationId)
        }
        if (scanStation) {
          // 传入的scanStationId可能是字符串，要重新赋值
          scanStationId = scanStation.scanStationId
        } else {
          scanStationId = state.scanRange.scanStationId
          scanStation = state.stationScanners.find(x => x.scanStationId == scanStationId)
        }

        let scanUser = null
        if (scanUserId && scanStation) {
          scanUser = scanStation.scanners.find(x => x.userId == scanUserId)
        }
        scanUserId = scanUser ? scanUser.userId : ''

        state.viewMode = {
          isAdmin: true,
          scanStationId,
          scanUserId,
        }
      }
      // 扫描模式
      else {
        state.viewMode = {
          isAdmin: false,
          scanStationId: state.scanRange.scanStationId,
          scanUserId: '',
        }
      }
    },
  },

  actions: {
    // 加载考试、扫描模板、扫描范围、科目题目、模板内容
    loadExamSubjectScanRangeQuestionTemplate(context, { examId, examSubjectId }) {
      context.state.examId = examId
      context.state.examSubjectId = examSubjectId

      return Promise.all([
        apiGetExamById({ examId }),
        apiGetSubjectScanTemplates(examSubjectId),
        apiGetScanRange(examSubjectId),
        apiListStationScanners(examSubjectId),
        apiGetTemplateContent(examSubjectId),
        apiGetObjectiveQuestions({ examId, examSubjectId }),
        apiGetSubjectiveQuestions({ examId, examSubjectId }),
        apiGetBlocks({ examId, examSubjectId }),
      ])
        .then(
          ([exam, scanTemplates, scanRange, stationScanners, template, objectiveDefine, subjectiveDefine, blocks]) => {
            context.state.exam = exam
            context.state.scanTemplates = scanTemplates
            context.state.scanRange = scanRange
            context.state.stationScanners = stationScanners

            let define = buildTemplateObjectiveSubjectiveDefine(template, objectiveDefine, subjectiveDefine)
            context.state.templateContent = define.template
            context.state.objectiveDefine = define.objectiveDefine
            context.state.subjectiveDefine = define.subjectiveDefine
            context.state.selectDefine = define.selectDefine
            context.state.blockDefine = blocks

            context.commit('changeViewMode', {})
          }
        )
        .catch(err => {
          context.commit('clear')
          throw err
        })
    },

    async loadExamSubjectScanRangeQuestionTemplateAsync(context, { examId, examSubjectId }) {
      context.state.examId = examId
      context.state.examSubjectId = examSubjectId

      try {
        await Promise.all([apiGetExamById({ examId }), apiGetSubjectScanTemplates(examSubjectId)]).then(
          ([exam, scanTemplates]) => {
            context.state.exam = exam
            context.state.scanTemplates = scanTemplates
          }
        )

        await Promise.all([
          apiGetScanRange(examSubjectId),
          apiListStationScanners(examSubjectId),
          apiGetTemplateContent(examSubjectId),
          apiGetObjectiveQuestions({ examId, examSubjectId }),
          apiGetSubjectiveQuestions({ examId, examSubjectId }),
          apiGetBlocks({ examId, examSubjectId }),
        ]).then(([scanRange, stationScanners, template, objectiveDefine, subjectiveDefine, blocks]) => {
          context.state.scanRange = scanRange
          context.state.stationScanners = stationScanners

          const Define = buildTemplateObjectiveSubjectiveDefine(template, objectiveDefine, subjectiveDefine)
          context.state.templateContent = Define.template
          context.state.objectiveDefine = Define.objectiveDefine
          context.state.subjectiveDefine = Define.subjectiveDefine
          context.state.selectDefine = Define.selectDefine
          context.state.blockDefine = blocks

          context.commit('changeViewMode', {})
        })

        return Promise.resolve()
      } catch (err) {
        context.commit('clear')
        return Promise.reject(err)
      }
    },
  },
}
