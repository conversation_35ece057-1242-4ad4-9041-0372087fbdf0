import ajax from '@/api/ajax'

export function apiGetUploadStatus(data) {
  return ajax
    .get({
      url: '/imgproc/upload/stats',
      params: {
        examSubjectId: data.examSubjectId,
      },
      requestName: '获取上传答卷各状态统计',
    })
    .then(data => {
      return data
    })
}

export function apiGetUploadPackageByStatus(data) {
  const urls = {
    prepare: '/imgproc/upload/prepare',
    uploading: '/imgproc/upload/uploading',
    success: '/imgproc/upload/success',
    fail: '/imgproc/upload/fail',
  }
  return ajax
    .get({
      url: urls[data.status],
      params: {
        examSubjectId: data.examSubjectId,
        page: data.page,
        size: data.size,
        key: data.key || '',
        roomId: data.roomId || '',
      },
      requestName: '获取上传答卷各状态列表',
    })
    .then(data => {
      return data
    })
}

export function apiReUploadThePaper(data) {
  const urls = {
    success: '/imgproc/upload/success',
    fail: '/imgproc/upload/fail',
    prepare: '/imgproc/upload/prepare',
  }
  return ajax
    .put({
      url: urls[data.type],
      data: data.msg,
      requestName: '重传选定卷',
    })
    .then(data => {
      return data
    })
}

export function apiReCutTheUploadedPaper(data) {
  const urls = {
    success: '/imgproc/upload/reclipSuccess',
    fail: '/imgproc/upload/reclipFail',
  }
  return ajax
    .put({
      url: urls[data.type],
      data: data.msg,
      requestName: '重切选定卷',
    })
    .then(data => {
      return data
    })
}

export function apiFixUploading(data) {
  return ajax.put({
    url: '/imgproc/upload/fixUploading',
    data: {
      examSubjectId: data.examSubjectId,
      roomIds: [],
      scanInfoIds: data.serialNos,
    },
    requestName: '刷新答卷上传',
  })
}
