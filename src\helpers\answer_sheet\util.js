import ObjectiveQuestion from '@/helpers/emarking/objective_question'
import SubjectiveQuestion from '@/helpers/emarking/subjective_question'
import { sortQuestionFunction } from '../emarking/miscellaneous'
import { exportBlock } from './paper_structure'
import MathJaxUtil from '@/utils/mathjax'
import { createHiddenContainer, removeHiddenContainer, getTableHtml } from '@/utils/dom'

// 比较答题卡题目与科目题目定义或题库试卷题目定义是否一致
export function isSameAnswerSheetPaperStructure(
  objectiveQuestionsNew,
  subjectiveQuestionsNew,
  objectiveQuestionsOld,
  subjectiveQuestionsOld,
  checkScore = false
) {
  objectiveQuestionsNew = getSortedQuestions(objectiveQuestionsNew)
  objectiveQuestionsOld = getSortedQuestions(objectiveQuestionsOld)
  subjectiveQuestionsNew = getSortedQuestions(subjectiveQuestionsNew)
  subjectiveQuestionsOld = getSortedQuestions(subjectiveQuestionsOld)
  let objectiveCompareKeys = ['topicCode', 'questionCode', 'branchTypeId', 'optionCount']
  let subjectiveCompareKeys = ['topicCode', 'questionCode', 'branchCode']
  if (checkScore) {
    subjectiveCompareKeys.push('fullScore')
  }
  return (
    isSameArrayCompareByKeys(objectiveQuestionsOld, objectiveQuestionsNew, objectiveCompareKeys) &&
    isSameArrayCompareByKeys(subjectiveQuestionsOld, subjectiveQuestionsNew, subjectiveCompareKeys)
  )
}

// 比较答题卡主观题与科目主观题分数是否一致
export function getScoreChangedSubjectiveQuestionsTableHtml(subjectiveQuestionsNew, subjectiveQuestionsOld) {
  let list = []
  subjectiveQuestionsNew.forEach(subjNew => {
    let subjOld = subjectiveQuestionsOld.find(
      x => x.questionCode == subjNew.questionCode && x.branchCode == subjNew.branchCode
    )
    if (subjOld && subjOld.fullScore != subjNew.fullScore) {
      list.push({
        branchName: subjNew.branchName || subjNew.defaultBranchName,
        oldScore: subjOld.fullScore,
        newScore: subjNew.fullScore,
      })
    }
  })
  if (list.length == 0) {
    return ''
  }

  return getTableHtml({
    columns: [
      {
        title: '题号',
        width: 80,
        key: 'branchName',
      },
      {
        title: '原分数',
        width: 80,
        key: 'oldScore',
      },
      {
        title: '新分数',
        width: 80,
        key: 'newScore',
      },
    ],
    data: list,
  })
}

// 比较答题卡题块与科目题块定义是否一致
export function isSameAnswerSheetBlocks(blocksNew, blocksOld) {
  return isSameArray(blocksNew, blocksOld, (blockA, blockB) => {
    return (
      blockA.blockId == blockB.blockId &&
      ((!blockA.selectGroupName && !blockB.selectGroupName) || blockA.selectGroupName == blockB.selectGroupName) &&
      ((!blockA.selectCount && !blockB.selectCount) || blockA.selectCount == blockB.selectCount) &&
      isSameArrayCompareByKeys(blockA.questions, blockB.questions, ['topicCode', 'questionCode', 'branchCode'])
    )
  })
}

function isSameArray(arr1, arr2, compareFunction) {
  if (arr1.length != arr2.length) {
    return false
  }
  return arr1.every((element1, idx) => compareFunction(element1, arr2[idx]))
}

function isSameArrayCompareByKeys(arr1, arr2, keys) {
  return isSameArray(arr1, arr2, (a, b) => keys.every(key => a[key] == b[key]))
}

function getSortedQuestions(questions) {
  return questions.slice().sort(sortQuestionFunction)
}

// 解析答题卡题目题块
export function parseQuestionsAndBlocks({ objectivesJson, blocksJson, sheet }) {
  let objectiveQuestions = []
  let subjectiveQuestions = []
  let blocks = []
  // 兼容旧版题目题块保存在sheet中
  if (!objectivesJson && !blocksJson) {
    objectiveQuestions = sheet.paperStructure.objectiveQuestions
    subjectiveQuestions = sheet.paperStructure.subjectiveQuestions
    blocks = sheet.blocks
  } else {
    try {
      if (objectivesJson) {
        objectiveQuestions = JSON.parse(objectivesJson)
      }
      if (blocksJson) {
        blocks = JSON.parse(blocksJson)
      }
      blocks.forEach(block => {
        block.questions.forEach(q => {
          subjectiveQuestions.push(q)
        })
      })
    } catch {
      throw '解析题目出错了'
    }
  }
  objectiveQuestions.sort(sortQuestionFunction)
  subjectiveQuestions.sort(sortQuestionFunction)
  blocks.forEach(block => block.questions.sort(sortQuestionFunction))

  objectiveQuestions.forEach(q => Object.setPrototypeOf(q, ObjectiveQuestion.prototype))
  subjectiveQuestions.forEach(q => Object.setPrototypeOf(q, SubjectiveQuestion.prototype))
  blocks.forEach(block => block.questions.forEach(q => Object.setPrototypeOf(q, SubjectiveQuestion.prototype)))

  return {
    objectiveQuestions,
    subjectiveQuestions,
    blocks,
  }
}

// 答题卡题目题块与科目试卷结构对比，更新题名分数填空个数答案题块名
export function updateQuestionNameScoreAnswerBlockName({
  objectiveQuestions,
  subjectiveQuestions,
  blocks,
  objectiveDefines,
  subjectiveDefines,
  blockDefines,
  updateOriginalQuestionId,
}) {
  // 客观题更新题名分数答案
  objectiveQuestions.forEach(sheetObj => {
    let objDefine = objectiveDefines.find(q => q.questionCode == sheetObj.questionCode)
    if (objDefine) {
      sheetObj.questionName = objDefine.questionName
      sheetObj.branchName = objDefine.branchName
      sheetObj.fullScore = objDefine.fullScore
      sheetObj.standardAnswer = objDefine.standardAnswer
      if (updateOriginalQuestionId) {
        sheetObj.originalQuestionId = objDefine.originalQuestionId
        sheetObj.originalBranchId = objDefine.originalBranchId
      }
    }
  })
  // 主观题更新题名分数填空个数（先阅后扫不更新分数）
  subjectiveQuestions.forEach(sheetSubj => {
    let subjDefine = subjectiveDefines.find(
      q => q.questionCode == sheetSubj.questionCode && q.branchCode == sheetSubj.branchCode
    )
    if (subjDefine) {
      sheetSubj.questionName = subjDefine.questionName
      sheetSubj.branchName = subjDefine.branchName
      sheetSubj.fullScore = subjDefine.fullScore
      if (updateOriginalQuestionId) {
        sheetSubj.originalQuestionId = subjDefine.originalQuestionId
        sheetSubj.originalBranchId = subjDefine.originalBranchId
      }
      sheetSubj.blankScores = subjDefine.blankScores.slice()
    }
  })
  // 题块按照包含题名与科目题块对应，更新题块名称
  let blockDefineQuestionCodesMap = new Map()
  blockDefines.forEach(block => {
    let questionCodes = block.questions.map(q => `${q.questionCode}.${q.branchCode}`).join(',')
    blockDefineQuestionCodesMap.set(questionCodes, block)
  })
  blocks.forEach(sheetBlock => {
    let questionCodes = sheetBlock.questions.map(q => `${q.questionCode}.${q.branchCode}`).join(',')
    let blockDefine = blockDefineQuestionCodesMap.get(questionCodes)
    if (blockDefine) {
      sheetBlock.blockName = blockDefine.blockName
    }
    sheetBlock.questions.forEach(sheetSubj => {
      let subjDefine = subjectiveDefines.find(
        q => q.questionCode == sheetSubj.questionCode && q.branchCode == sheetSubj.branchCode
      )
      if (subjDefine) {
        sheetSubj.questionName = subjDefine.questionName
        sheetSubj.branchName = subjDefine.branchName
        sheetSubj.fullScore = subjDefine.fullScore
        if (updateOriginalQuestionId) {
          sheetSubj.originalQuestionId = subjDefine.originalQuestionId
          sheetSubj.originalBranchId = subjDefine.originalBranchId
        }
      }
    })
  })
}

// 答题卡完成制作后blocksJson对象属性顺序、分值字符串会变，导致用户未作修改页面仍提示保存
export function reOrderBlocksJson(blocksJson) {
  let blocks = JSON.parse(blocksJson)
  let newBlocks = blocks.map(exportBlock)
  return JSON.stringify(newBlocks)
}

// 拆分题组时将内容按题号或小题号拆分
export function splitContent(content, separators) {
  let div = document.createElement('div')
  div.innerHTML = content
  let separatorIdx = 0
  let nodesList = []
  let nodes = []
  nodesList.push(nodes)
  for (let node of div.childNodes) {
    // 若元素html以分隔符开头，则分成另一组
    let separator = separators[separatorIdx]
    if (node.nodeType == 1 && separator && node.innerText.startsWith(separator)) {
      nodes = [node]
      nodesList.push(nodes)
      separatorIdx++
    } else {
      nodes.push(node)
    }
  }
  // 每个分隔符都分出一组，则拆分成功
  if (nodesList.length == separators.length + 1) {
    return nodesList.map(nodes => {
      div.innerHTML = ''
      div.append(...nodes)
      return div.innerHTML
    })
  } else {
    return [content]
  }
}

// 显示题目内容前，预处理试卷内容
export async function preProcessPaper(paper, baseStyle) {
  if (paper._processed) {
    return
  }
  let contentList = []
  paper.questionList.forEach(q => {
    if (q.trunk) {
      contentList.push({
        id: `${q.id}-trunk`,
        content: q.trunk,
        setter: newContent => {
          q.trunk = newContent
        },
        needUnifyBrackets: true,
      })
    }
    q.branches.forEach(b => {
      if (b.stem) {
        contentList.push({
          id: `${b.id}-stem`,
          content: b.stem,
          setter: newContent => {
            b.stem = newContent
          },
          needUnifyBrackets: true,
        })
      }
      b.options.forEach(opt => {
        if (opt.content) {
          contentList.push({
            id: `${b.id}-opt-${opt.label}`,
            content: opt.content,
            setter: newContent => {
              opt.content = newContent
            },
          })
        }
      })
    })
  })

  // 英语替换不换行空格为普通空格，以免无法换行
  if (paper.paperInfo.subject.name == '英语') {
    contentList.forEach(item => {
      item.content = item.content.replace(/&nbsp;/g, ' ').replace(/\u00a0/g, ' ')
    })
  }

  let container = createHiddenContainer(baseStyle)
  contentList.forEach(item => {
    let elItem = document.createElement('div')
    elItem.id = item.id
    elItem.innerHTML = item.content
    container.append(elItem)
    if (item.needUnifyBrackets) {
      unifyBrackets(elItem)
    }
  })

  await Promise.all([renderMath(container), setImageSize(container), setTableStyle(container)])

  for (let child of container.children) {
    let target = contentList.find(x => x.id == child.id)
    if (target) {
      target.setter(child.innerHTML)
    }
  }

  removeHiddenContainer(container)
  paper._processed = true
}

export const MathWrapperClass = 'igrade-answer-sheet-math'
window.igradeAnswerSheetRenderMath = renderMath

async function renderMath(el) {
  // \frac改为\dfrac
  let treeWalker = document.createTreeWalker(el, NodeFilter.SHOW_TEXT)
  while (treeWalker.nextNode()) {
    let node = treeWalker.currentNode
    node.nodeValue = node.nodeValue.replace(/\$[^$]+\$$/g, match => {
      match = match.replace(/\\frac/g, '\\dfrac')
      return match
    })
  }
  await MathJaxUtil.render(el)
  // 找渲染后的内容
  let elMathList = el.querySelectorAll('.mjx-chtml')
  elMathList.forEach(elMath => {
    // 补充原始tex
    let next = elMath.nextElementSibling
    if (next.tagName.toLowerCase() == 'script' && next.getAttribute('type') == 'math/tex') {
      let tex = next.innerText
      elMath.setAttribute('data-mathtex', tex)
    }

    // 空节点添加内容，避免被ckeditor删除
    addMathjaxPlaceholder(elMath)

    // 包装节点
    if (!elMath.parentElement.classList.contains(MathWrapperClass)) {
      let wrapper = document.createElement('span')
      wrapper.className = MathWrapperClass
      elMath.before(wrapper)
      wrapper.append(elMath)
    }
  })
  // 删渲染后mathjax标签
  let scripts = el.querySelectorAll('script')
  let assistants = el.querySelectorAll('.MJX_Assistive_MathML')
  let previews = el.querySelectorAll('.MathJax_Preview')
  let removeList = [...scripts, ...assistants, ...previews]
  removeList.forEach(item => {
    item.remove()
  })
}

// mathjax渲染后的dom中存在只有样式没有内容的空节点，向这些空节点中添加内容，避免被ckeditor删除
function addMathjaxPlaceholder(elMath) {
  let placeholder = document.createElement('span')
  placeholder.style.display = 'none'
  placeholder.className = 'placeholder'
  placeholder.innerHTML = ' '
  add(elMath)

  function add(el) {
    for (let child of el.children) {
      if (child.childNodes.length == 0) {
        child.append(placeholder.cloneNode(1))
      } else {
        add(child)
      }
    }
  }
}

function setImageSize(el) {
  // 找图片
  let elImgs = Array.from(el.querySelectorAll('img'))
  return new Promise(resolve => {
    let timer = setInterval(() => {
      for (let i = elImgs.length - 1; i >= 0; i--) {
        let elImg = elImgs[i]
        if (elImg.complete) {
          elImgs.splice(i, 1)
          if (elImg.naturalWidth) {
            elImg.width = elImg.offsetWidth
            elImg.height = elImg.offsetHeight
          }
        }
      }
      if (elImgs.length == 0) {
        clearInterval(timer)
        resolve()
      }
    }, 10)
  })
}

function setTableStyle(el) {
  el.querySelectorAll('table').forEach(elTable => {
    elTable.style.borderCollapse = 'collapse'
    elTable.querySelectorAll('td').forEach(elTd => {
      elTd.style.padding = '3px'
      elTd.style.border = '1px solid #666'
    })
  })
  return Promise.resolve()
}

// 导入后生成页面前先渲染公式
export async function renderTopicsContentMath(topics, baseStyle) {
  let contentList = getTopicsMathContentList(topics)
  if (contentList.length == 0) {
    return
  }

  let container = createHiddenContainer(baseStyle)
  contentList.forEach(item => {
    let elItem = document.createElement('div')
    elItem.id = item.id
    elItem.innerHTML = item.content
    container.append(elItem)
  })

  await renderMath(container)

  for (let child of container.children) {
    let target = contentList.find(x => x.id == child.id)
    if (target) {
      target.setter(child.innerHTML)
    }
  }

  removeHiddenContainer(container)
}

// 导出前替换已渲染公式
export function resetTopicsContentMath(topics) {
  let contentList = getTopicsMathContentList(topics)
  if (contentList.length == 0) {
    return
  }
  let container = document.createElement('div')
  contentList.forEach(item => {
    let elItem = document.createElement('div')
    elItem.id = item.id
    elItem.innerHTML = item.content
    container.append(elItem)
  })
  unRenderMath(container)
  for (let child of container.children) {
    let target = contentList.find(x => x.id == child.id)
    if (target) {
      target.setter(child.innerHTML)
    }
  }
}

function getTopicsMathContentList(topics) {
  let contentList = []
  topics.forEach(topic => {
    topic.questionGroups.forEach(g => {
      if (g.content && g.content.includes(MathWrapperClass)) {
        contentList.push({
          id: `${g.id}-content`,
          content: g.content,
          setter: newContent => {
            g.content = newContent
          },
        })
      }
      if (g.options) {
        g.options.forEach(opt => {
          if (opt.content && opt.content.includes(MathWrapperClass)) {
            contentList.push({
              id: `${g.id}-option-${opt.label}-content`,
              content: opt.content,
              setter: newContent => {
                opt.content = newContent
              },
            })
          }
        })
      }
    })
  })
  return contentList
}

function unRenderMath(el) {
  let wrappers = el.querySelectorAll(`.${MathWrapperClass}`)
  wrappers.forEach(wrapper => {
    let elMath = wrapper.querySelector('.mjx-chtml')
    if (!elMath) {
      return
    }
    let { mathtex, mathml } = elMath.dataset
    let replaceHtml = mathtex ? `$${mathtex}$` : mathml
    if (!replaceHtml) {
      return
    }
    wrapper.innerHTML = replaceHtml
  })
}

// 统一选择题括号
function unifyBrackets(container) {
  let bracket = `（${String.fromCharCode(160).repeat(8)}）`
  let treeWalker = document.createTreeWalker(container, NodeFilter.SHOW_TEXT)
  while (treeWalker.nextNode()) {
    let node = treeWalker.currentNode
    node.nodeValue = node.nodeValue.replace(/[(（]\s*[)）]?\s*[.。]?\s*$/, bracket)
  }
}
