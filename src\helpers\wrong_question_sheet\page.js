/**
 * 页面
 */

import TemplatePage from '@/helpers/scan_template_define/template_page'
import TemplatePageParam from '@/helpers/scan_template_define/template_page_param'
import TemplateStudentInfo from '@/helpers/scan_template_define/template_student_info'

export default class Page {
  constructor() {
    // 页面序号，从0开始
    this.pageIndex = 0
    // 页头，Vue实例
    this.header = null
    // 页脚，Vue实例
    this.footer = null
    // 页面块
    this.blocks = []
    // 最后一个页面块与顶部的距离
    this.top = 0
    // Id，保证每次更新后不一样
    this.pageId = ''
  }

  addBlock(block) {
    block.id = `${block.id}_pageIndex-${this.pageIndex}_pageId${this.pageId}`
    block.pageIndex = this.pageIndex
    block.top = this.top
    this.blocks.push(block)
    this.top += block.height
  }

  exportScanTemplatePage() {
    let page = new TemplatePage()
    page.pageIndex = this.pageIndex + 1
    page.pageParam = new TemplatePageParam()
    page.pageParam.correctType = 'ANCHORMARK'
    page.pageParam.anchorMarks = [null, null, null, null]
    page.studentInfo = new TemplateStudentInfo()
    page.studentInfo.admissionType = 'NONE'

    // header
    if (!this.header) {
      throw new Error('没有header实例')
    }
    let { anchorTopLeft, anchorTopRight } = this.header.getRects()
    if (anchorTopLeft) {
      page.pageParam.anchorMarks[0] = anchorTopLeft
    }
    if (anchorTopRight) {
      page.pageParam.anchorMarks[1] = anchorTopRight
    }

    // footer
    if (!this.footer) {
      throw new Error('没有footer实例')
    }
    let { anchorBottomLeft, anchorBottomRight, titleArea, pageMark } = this.footer.getRects()
    if (anchorBottomLeft) {
      page.pageParam.anchorMarks[3] = anchorBottomLeft
    }
    if (anchorBottomRight) {
      page.pageParam.anchorMarks[2] = anchorBottomRight
    }
    if (titleArea) {
      page.pageParam.titleArea = titleArea
    }
    if (pageMark) {
      page.pageParam.pageMark = pageMark
    }

    this.blocks.forEach(block => {
      // examInfo
      if (block.type == 'ExamInfo' && this.pageIndex == 0) {
        let examInfoBlock = this.blocks.find(x => x.type == 'ExamInfo')
        if (!examInfoBlock || !examInfoBlock.instance) {
          throw new Error('没有examInfo实例')
        }
        let { admissionOmrArea, pageMark, border } = examInfoBlock.instance.getRects()
        if (admissionOmrArea) {
          page.studentInfo.admissionOmrArea = admissionOmrArea
          page.studentInfo.admissionType = 'OMR'
        }
        if (pageMark) {
          page.pageParam.pageMark = pageMark
        }
        if (border) {
          addPageParamBorder(page.pageParam, border)
        }
      }
      // 题目块
      else if (block.type == 'Question') {
        let { omrAreas, border } = block.instance.getRects()
        page.objectiveAreas.push(...omrAreas)
        if (border) {
          addPageParamBorder(page.pageParam, border)
        }
      }
    })

    return page

    function addPageParamBorder(pageParam, border) {
      if (!pageParam.border) {
        pageParam.border = { ...border }
      } else {
        pageParam.border.height += border.height
      }
    }
  }
}
