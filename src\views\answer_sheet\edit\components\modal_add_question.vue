<template>
  <Modal
    class="modal-add-question"
    :model-value="modelValue"
    :title="modalTitle"
    :mask-closable="false"
    @on-visible-change="handleVisibleChange"
  >
    <component
      :is="component"
      v-if="modelValue"
      ref="component"
      :branch-type-id="branchTypeId"
      :topic-chinese-codes="topicChineseCodes"
    ></component>
    <template #footer>
      <div>
        <Button type="text" @click="handleModalCancel">取消</Button>
        <Button type="primary" @click="handleModalOK">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import AddObjective from './add_objective'
  import AddFillBlank from './add_fill_blank'
  import AddEssay from './add_essay'
  import AddChineseWriting from './add_chinese_writing'
  import AddEnglishWriting from './add_english_writing'

  import { showScrollbarAfterHideModal } from '@/utils/iview'
  import { deepCopy } from '@/utils/object'
  import BranchTypeEnum from '@/enum/qlib/branch_type'
  import { TopicChineseCodes } from '@/const/answer_sheet'

  export default {
    components: {
      AddObjective,
      AddFillBlank,
      AddEssay,
      AddChineseWriting,
      AddEnglishWriting,
    },
    props: {
      modelValue: Boolean,
      branchTypeId: Number,
    },
    emits: ['update:modelValue'],
    data() {
      return {
        topicChineseCodes: deepCopy(TopicChineseCodes),
      }
    },
    computed: {
      modalTitle() {
        if (!this.branchTypeId) {
          return '添加题目'
        }
        return `添加${BranchTypeEnum.getNameById(this.branchTypeId)}`
      },
      isObjective() {
        return [
          BranchTypeEnum.SingleChoice.id,
          BranchTypeEnum.MultipleChoice.id,
          BranchTypeEnum.TrueOrFalse.id,
        ].includes(this.branchTypeId)
      },
      component() {
        if (this.isObjective) {
          return AddObjective
        } else if (this.branchTypeId == BranchTypeEnum.FillBlank.id) {
          return AddFillBlank
        } else if (this.branchTypeId == BranchTypeEnum.Essay.id) {
          return AddEssay
        } else if (this.branchTypeId == BranchTypeEnum.ChineseWriting.id) {
          return AddChineseWriting
        } else if (this.branchTypeId == BranchTypeEnum.EnglishWriting.id) {
          return AddEnglishWriting
        } else {
          return null
        }
      },
    },
    methods: {
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.handleModalCancel()
        }
      },
      handleModalCancel() {
        this.$emit('update:modelValue', false)
        showScrollbarAfterHideModal()
      },
      handleModalOK() {
        let component = this.$refs['component']
        let result = component.check()
        if (result.message) {
          this.$Message.info({
            content: result.message,
          })
          return
        }

        let mutationName = ''
        if (this.isObjective) {
          mutationName = 'addObjectiveQuestions'
        } else if (this.branchTypeId == BranchTypeEnum.FillBlank.id) {
          mutationName = 'addFillBlankQuestions'
        } else if (this.branchTypeId == BranchTypeEnum.Essay.id) {
          mutationName = 'addEssayQuestions'
        } else if (this.branchTypeId == BranchTypeEnum.ChineseWriting.id) {
          mutationName = 'addChineseWritingQuestion'
        } else if (this.branchTypeId == BranchTypeEnum.EnglishWriting.id) {
          mutationName = 'addEnglishWritingQuestion'
        }
        if (mutationName) {
          this.$store.commit(`answerSheet/${mutationName}`, result.data)
          this.$store.commit('answerSheet/generatePages')
        }

        this.handleModalCancel()
      },
    },
  }
</script>
