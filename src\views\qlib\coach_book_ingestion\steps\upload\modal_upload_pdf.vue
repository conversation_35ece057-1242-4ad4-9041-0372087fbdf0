<template>
  <Modal
    :model-value="props.modelValue"
    title="上传pdf文件"
    width="600"
    :closable="!uploading"
    :mask-closable="false"
    @on-visible-change="handleVisibleChange"
  >
    <div class="select-file">
      <ComUpload
        :accepts="['.pdf']"
        :max-size="50"
        :file-name="selectedFile?.name"
        :file-size="selectedFile?.size"
        :show-file-size="true"
        @on-selected="onFileSelected"
      ></ComUpload>
    </div>
    <div class="section-progress">
      <div class="label">上传进度</div>
      <Progress :percent="uploadProgress" :stroke-width="10">{{ progressText }}</Progress>
    </div>
    <template #footer>
      <div class="footer">
        <div class="footer-left">
          <Checkbox v-model="autoConvertImage">上传后自动转换为图片</Checkbox>
        </div>
        <div class="footer-right">
          <Button type="text" :disabled="uploading" @click="handleCancel">取消</Button>
          <Button type="primary" :disabled="!selectedFile" :loading="uploading" @click="onBtnUploadClick">{{
            uploading ? '上传中' : '上传'
          }}</Button>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import iView from '@/iview'

  import ComUpload from '@/components/upload'

  import { useCoachBookIngestionStore } from '@/store/qlib/coach_book_ingestion'

  import { apiCoachBookIngestionUploadPdf, apiCoachBookIngestionChangeFileType } from '@/api/qlib/coach_book_ingestion'

  import FileTypeEnum from '@/enum/qlib/ingestion/file_type'

  const props = defineProps({
    modelValue: Boolean,
  })

  const emits = defineEmits(['update:modelValue', 'uploaded'])

  const ingestionStore = useCoachBookIngestionStore()

  const selectedFile = ref(null)
  const uploading = ref(false)
  const uploadProgress = ref(0)
  const autoConvertImage = ref(false)

  function onFileSelected(file) {
    if (!uploading.value) {
      selectedFile.value = file
      uploadProgress.value = 0
    }
  }

  const progressText = computed(() => {
    if (uploadProgress.value >= 100) {
      if (uploading.value) {
        return '处理中'
      } else {
        return ''
      }
    } else {
      return `${uploadProgress.value}%`
    }
  })

  function handleVisibleChange(visible) {
    if (!visible) {
      handleCancel()
    }
  }

  function handleCancel() {
    emits('update:modelValue', false)
  }

  watch(
    () => props.modelValue,
    () => {
      if (props.modelValue) {
        selectedFile.value = null
        uploadProgress.value = 0
        uploading.value = false
        autoConvertImage.value = true
      }
    }
  )

  async function onBtnUploadClick() {
    if (!selectedFile.value) {
      return
    }
    uploading.value = true
    try {
      let ingestionId = ingestionStore.ingestionInfo.id

      let fileType = FileTypeEnum.Pdf.id
      if (ingestionStore.ingestionInfo.fileType != fileType) {
        ingestionStore.ingestionInfo.fileType = fileType
        await apiCoachBookIngestionChangeFileType({
          ingestionId,
          fileType,
        })
      }

      await apiCoachBookIngestionUploadPdf({
        ingestionId,
        file: selectedFile.value,
        autoConvertImage: autoConvertImage.value,
        onUploadProgress: e => {
          uploadProgress.value = Math.floor((e.loaded / e.total) * 100)
        },
      })
      iView.Message.success('上传成功')
      emits('uploaded')
      handleCancel()
    } catch (err) {
      iView.Modal.error({
        title: '上传失败',
        content: err?.msg || '上传失败',
        onOk: () => {
          emits('uploaded')
          handleCancel()
        },
      })
    } finally {
      uploading.value = false
    }
  }
</script>

<style lang="scss" scoped>
  .section-progress {
    @include flex(row, flex-start, center);
    margin-top: 16px;
    margin-bottom: 16px;

    .label {
      flex-grow: 0;
      flex-shrink: 0;
      width: 88px;
      margin-right: 10px;
      text-align: right;
    }
  }

  .footer {
    @include flex(row, flex-end, center);

    .footer-right {
      margin-left: auto;
    }
  }
</style>
