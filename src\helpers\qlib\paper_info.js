import STORE from '@/store/index'
import { deepCopy } from '@/utils/object'
import { normalizeNumber } from '@/utils/math'

/**
 * 试卷信息
 */
export class PaperInfo {
  constructor() {
    Object.assign(this, {
      id: '',
      name: '',
      stage: {},
      subject: {},
      grade: {},
      term: {},
      paperType: {},
      region: [],
      year: '',
      viewTimes: 0,
      downloadTimes: 0,
      createTime: null,
      updateTime: null,
      status: 0,
      school: {},
      sourceName: '',
      onDividing: false,
      favorite: false,
      editorId: 0,
      structureJSON: '',
      shareUser: '',
      sharePaperId: '',
      feedbackSheetId: '',
    })
  }

  static copy(p) {
    let newPaperInfo = new PaperInfo()
    Object.assign(newPaperInfo, deepCopy(p))

    return newPaperInfo
  }

  static import(p) {
    let paperInfo = new PaperInfo()
    // QuestionBasket 创建时尚无STORE
    if (!STORE) {
      return paperInfo
    }

    if (!p) {
      return paperInfo
    }

    let stages = STORE.state.qlib.stages || []
    let stage = stages.find(x => x.id === Number(p.gradeLevel || p.stage)) || {}
    let subject = (stage.subjects || []).find(x => x.id === Number(p.subjectId || p.subject)) || {}
    let grade = (stage.grades || []).find(x => x.id === Number(p.gradeId)) || {}
    let term = (stage.terms || []).find(x => x.id === Number(p.term)) || {}
    let paperType = (stage.paperTypes || []).find(x => x.id === Number(p.paperTypeId || p.type || p.source)) || {}
    let region = STORE.getters['common/regionsById'](Number(p.regionId || p.region)) || []

    paperInfo.id = p.id
    paperInfo.name = p.paperName || p.name
    paperInfo.stage = {
      id: stage.id || 0,
      name: stage.name || '',
    }
    paperInfo.subject = {
      id: subject.id || 0,
      name: subject.name || '',
    }
    paperInfo.grade = {
      id: grade.id || 0,
      name: grade.name || '',
    }
    paperInfo.term = {
      id: term.id || 0,
      name: term.name || '',
    }
    paperInfo.paperType = paperType
    paperInfo.region = region
    paperInfo.year = Number(p.year) || new Date().getFullYear()
    paperInfo.viewTimes = Number(p.browseCount || p.viewCount) || 0
    paperInfo.downloadTimes = Number(p.downloadCount) || 0
    paperInfo.createTime = new Date(Number(p.createTime))
    paperInfo.updateTime = new Date(Number(p.updateTime))
    paperInfo.status = Number(p.shareStatus)
    paperInfo.school = {
      id: p.schoolId || '',
      name: p.schoolName || '',
    }
    paperInfo.favorite = !!p.ifFavorite
    paperInfo.sourceName = `${paperInfo.year}${paperInfo.region.map(r => r.label).join('')}${
      paperInfo.school.name || ''
    }${paperInfo.paperType.name || ''}`
    paperInfo.onDividing = p.editStatus == 0 ? true : false
    paperInfo.editorId = p.editorId && p.editorId.toLowerCase()
    paperInfo.structureJSON = p.paperStyleJson || ''
    paperInfo.shareUser = p.shareUser || ''
    paperInfo.sharePaperId = p.sharePaperId || ''
    paperInfo.feedbackSheetId = p.feedbackSheetId || ''

    paperInfo.referenceBookName = p.bookName || ''
    paperInfo.pressName = p.pressName || ''

    paperInfo.answerSheetDownloadCount = p.answerSheetDownloadCount
    paperInfo.feedbackSheetDownloadCount = p.feedbackSheetDownloadCount

    paperInfo.paperBank = (p.paperBank && Number(p.paperBank)) || undefined

    paperInfo.coachbookId = p.flagCoachBookId || ''
    paperInfo.coachbookName = p.flagCoachBookName || ''

    paperInfo.paperGroupId = p.paperGroupId || ''
    paperInfo.paperGroupName = p.paperGroupName || ''

    return paperInfo
  }

  export() {
    let region = this.region[this.region.length - 1]
    return {
      id: this.id,
      paperName: this.name,
      gradeLevel: this.stage.id || null,
      subjectId: this.subject.id || null,
      gradeId: this.grade.id || null,
      term: this.term.id || null,
      paperTypeId: this.paperType.id || null,
      year: this.year,
      regionId: (region && region.value) || null,
    }
  }

  changeName(name) {
    if (!(name || name.length < 100)) {
      return
    }

    this.name = name
  }

  changeStage(stageId) {
    let stages = STORE.state.qlib.stages || []
    let finding = stages.find(x => x.id === stageId)
    if (!finding) {
      return
    }

    this.stage = {
      id: finding.id,
      name: finding.name,
    }

    let findingSubject = finding.subjects.find(x => x.id === this.subject.id)
    if (!findingSubject) {
      this.subject = {
        id: 0,
        name: '',
      }
    }

    let findingGrade = finding.grades.find(x => x.id === this.grade.id)
    if (!findingGrade) {
      this.grade = {
        id: 0,
        name: '',
      }
    }

    let findingTerm = finding.terms.find(x => x.id === this.term.id)
    if (!findingTerm) {
      this.term = {
        id: 0,
        name: '',
      }
    }

    let findingPaperType = finding.paperTypes.find(x => x.id === this.paperType.id)
    if (!findingPaperType) {
      this.paperType = {
        id: 0,
        name: '',
      }
    }
  }

  changeSubject(subjectId) {
    let stage = (STORE.state.qlib.stages || []).find(x => x.id === this.stage.id)
    if (!stage) {
      this.changeStage(0)
    }

    let subject = stage.subjects.find(x => x.id === subjectId)
    if (!subject) {
      this.subject = {
        id: 0,
        name: '',
      }
      return
    }

    this.subject = {
      id: subject.id,
      name: subject.name,
    }
  }

  changeGrade(gradeId) {
    let stage = (STORE.state.qlib.stages || []).find(x => x.id === this.stage.id)
    if (!stage) {
      this.changeStage(0)
    }

    let grade = stage.grades.find(x => x.id === gradeId)
    if (!grade) {
      this.grade = {
        id: 0,
        name: '',
      }
      return
    }

    this.grade = {
      id: grade.id,
      name: grade.name,
    }
  }

  changeTerm(termId) {
    let stage = (STORE.state.qlib.stages || []).find(x => x.id === this.stage.id)
    if (!stage) {
      this.changeStage(0)
    }

    let term = stage.terms.find(x => x.id === termId)
    if (!term) {
      this.term = {
        id: 0,
        name: '',
      }
      return
    }

    this.term = {
      id: term.id,
      name: term.name,
    }
  }

  changePaperType(paperTypeId) {
    let stage = (STORE.state.qlib.stages || []).find(x => x.id === this.stage.id)
    if (!stage) {
      this.changeStage(0)
    }

    let paperType = stage.paperTypes.find(x => x.id === paperTypeId)
    if (!paperType) {
      this.paperType = {
        id: 0,
        name: '',
      }
      return
    }

    this.paperType = {
      id: paperType.id,
      name: paperType.name,
    }
  }

  changeYear(year) {
    this.year = normalizeNumber(year, 2000, new Date().getFullYear() + 1, 0)
  }

  changeRegion(regionId) {
    let region = regionId === 0 ? [{ label: '不限', value: 0 }] : STORE.getters['common/regionsById'](regionId)
    this.region = region
  }

  checkIntegrity() {
    if (!this.name) {
      return '未输入试卷名'
    } else if (this.name.length > 100) {
      return '试卷名超长'
    }

    if (!(this.stage.id > 0)) {
      return '未选择学段'
    }

    if (!(this.subject.id > 0)) {
      return '未选择学科'
    }

    if (!(this.paperType.id > 0)) {
      return '未选择试卷类型'
    }
  }

  favor() {
    this.favorite = true
  }

  unFavor() {
    this.favorite = false
  }
}
