import ajax from '../ajax'

export function apiAddDownloadAssetConfig(teacherIds) {
  return ajax.post({
    url: 'user/coachBookDownloadAssetConfig/add',
    data: teacherIds, // *Array[String]
    requestName: '添加教师下载教辅试卷相关文件权限',
  })
}

export function apiDeleteDownloadAssetConfig(teacherIds) {
  return ajax.delete({
    url: 'user/coachBookDownloadAssetConfig/delete',
    data: teacherIds, // *Array[String]
    requestName: '删除教师下载教辅试卷相关文件权限',
  })
}

export function apiGetDownloadAssetConfigs() {
  return ajax.get({
    url: 'user/coachBookDownloadAssetConfig/list',
    requestName: '获取教师下载教辅试卷相关文件权限列表',
  })
}

export function apiCheckUserDownloadPaperFilePermission() {
  return ajax.get({
    url: 'user/coachBookDownloadAssetConfig/checkHasPermission',
    requestName: '检查当前用户是否有下载教辅试卷相关文件权限',
  })
}
