import MathJaxUtil from '@/utils/mathjax'
import BranchTypeEnum from '@/enum/qlib/branch_type'

/**
 * 试卷转成html以导出word --------------------------------------------------------------------------------
 */
export async function paperToHTML(
  paper,
  { size, answerLocation, leaveAnswerSpace, showAnswer, showExplanation, showKnowledge, showListening }
) {
  let doc = getEmptyHTMLDoc()
  addPaperDocContent(doc, paper, {
    answerLocation,
    leaveAnswerSpace,
    showAnswer,
    showExplanation,
    showKnowledge,
    showListening,
  })
  let leaveAnswerSpaceQuestions = getPaperLeaveAnswerSpaceQuestions(
    paper,
    answerLocation == 'afterPaper' && leaveAnswerSpace
  )
  return await handleDocContent(doc, size, leaveAnswerSpaceQuestions)
}

function addPaperDocContent(doc, paper, { answerLocation, showAnswer, showExplanation, showKnowledge, showListening }) {
  // 样式
  addPaperStyle(doc, paper)
  // 正文
  let answerAfterPaper = answerLocation == 'afterPaper'
  paper.paperStructure.volumes.forEach(v => {
    // 卷信息
    if (paper.paperStyle.volume.enabled) {
      doc.appendChild(
        createElement('div', v.content, {
          textAlign: 'center',
          fontSize: '16px',
        })
      )
    }
    // 大题
    v.topics.forEach(t => {
      // 大题说明
      doc.appendChild(
        createElement('div', t.content, {
          marginTop: '10px',
          fontWeight: 'bold',
        })
      )
      // 题目
      t.questions.forEach(q => {
        addQuestion(doc, q, {
          showQuestionScore: t.showQuestionScore,
          showAnswer: !answerAfterPaper && showAnswer,
          showExplanation: !answerAfterPaper && showExplanation,
          showKnowledge: !answerAfterPaper && showKnowledge,
          showListening: !answerAfterPaper && showListening,
        })
      })
    })
  })
  // 参考答案与解析
  if (answerAfterPaper && (showAnswer || showExplanation || showKnowledge || showListening)) {
    // 标题，接口转换时会将@@page-break@@替换为分页
    doc.appendChild(
      createElement('div', '@@page-break@@【参考答案与解析】', {
        textAlign: 'center',
        fontSize: '20px',
        fontWeight: 'bold',
      })
    )

    paper.paperStructure.topics.forEach(t => {
      // 大题说明
      doc.appendChild(
        createElement('div', t.title, {
          marginTop: '10px',
          fontWeight: 'bold',
        })
      )
      t.questions.forEach(q => {
        addAloneQuestionAnswerExplanation(doc, q, {
          showBranchScore: t.showQuestionScore,
          showAnswer,
          showExplanation,
          showKnowledge,
          showListening,
        })
      })
    })
  }
}

function addPaperStyle(doc, paper) {
  let { secretMark, title, subTitle, examInfo, studentInfo, scoreBox, cautions, situation } = paper.paperStyle
  // 保密标记
  if (secretMark.enabled) {
    doc.appendChild(createElement('div', secretMark.content))
  }
  // 标题
  if (title.enabled) {
    doc.appendChild(
      createElement('div', title.content, {
        textAlign: 'center',
        fontSize: '20px',
        fontWeight: 'bold',
      })
    )
  }
  // 副标题
  if (subTitle.enabled) {
    doc.appendChild(
      createElement('div', subTitle.content, {
        textAlign: 'center',
        fontSize: '16px',
        marginTop: '10px',
      })
    )
  }
  // 考试信息
  if (examInfo.enabled) {
    doc.appendChild(
      createElement('div', examInfo.content, {
        textAlign: 'center',
        marginTop: '10px',
      })
    )
  }
  // 学生信息
  if (studentInfo.enabled) {
    doc.appendChild(
      createElement('div', studentInfo.content, {
        textAlign: 'center',
        marginTop: '10px',
      })
    )
  }
  // 打分框
  if (scoreBox.enabled) {
    let tableWrap = createElement('div', scoreBox.content)
    let tds = tableWrap.querySelectorAll('td')
    for (let i = 0; i < tds.length; i++) {
      tds[i].style.width = '40pt'
      tds[i].style.textAlign = 'center'
    }
    let table = tableWrap.firstElementChild
    table.style.marginTop = '10px'
    doc.appendChild(table)
  }
  // 注意事项
  if (cautions.enabled) {
    doc.appendChild(
      createElement('div', '注意事项', {
        fontWeight: 'bold',
        marginTop: '10px',
      })
    )
    doc.appendChild(createElement('div', cautions.content.replace(/\n/g, '<br>')))
  }
  // 试卷情境
  if (situation.enabled) {
    doc.appendChild(createElement('div', situation.content, { marginTop: '20px' }))
  }
}

function getPaperLeaveAnswerSpaceQuestions(paper, leaveAnswerSpace) {
  if (!leaveAnswerSpace) {
    return []
  }
  let questions = []
  paper.paperStructure.volumes.forEach(v => {
    v.topics.forEach(t => {
      t.questions.forEach(q => {
        questions.push(q)
      })
    })
  })
  return getLeaveAnswerSpaceQuestions(questions)
}

function getLeaveAnswerSpaceQuestions(questions) {
  return questions.filter(q =>
    q.branches.some(b =>
      [BranchTypeEnum.Essay.id, BranchTypeEnum.ChineseWriting.id, BranchTypeEnum.EnglishWriting.id].includes(
        b.branchType.id
      )
    )
  )
}

/**
 * 错题转成html以导出word --------------------------------------------------------------------------------
 */
export async function wrongQuestionToHTML(
  title,
  exams,
  { subInfos, size, answerLocation, leaveAnswerSpace, showAnswer, showExplanation, showKnowledge, showListening }
) {
  let doc = getEmptyHTMLDoc()
  addWrongQuestionDocContent(doc, title, exams, {
    subInfos,
    answerLocation,
    showAnswer,
    showExplanation,
    showKnowledge,
    showListening,
  })
  let leaveAnswerSpaceQuestions = getLeaveAnswerSpaceWrongQuestions(
    exams,
    answerLocation == 'afterPaper' && leaveAnswerSpace
  )
  return await handleDocContent(doc, size, leaveAnswerSpaceQuestions)
}

function addWrongQuestionDocContent(
  doc,
  title,
  exams,
  { subInfos, answerLocation, showAnswer, showExplanation, showKnowledge, showListening }
) {
  // 标题
  doc.appendChild(
    createElement('div', title, {
      textAlign: 'center',
      fontSize: '20px',
      fontWeight: 'bold',
    })
  )

  // 学生信息
  if (subInfos && subInfos.show) {
    let info = subInfos.className
    if (subInfos.studentName) {
      info += ' - ' + subInfos.studentName
    }
    doc.appendChild(
      createElement('div', info, {
        fontSize: '15px',
        textAlign: 'center',
        marginTop: '10px',
      })
    )
  }
  // 正文
  let answerAfterPaper = answerLocation == 'afterPaper'
  exams.forEach(exam => {
    // 考试项目标题
    doc.appendChild(
      createElement('div', exam.examName, {
        fontSize: '16px',
        fontWeight: 'bold',
        marginTop: '1em',
      })
    )
    // 题目
    exam.wrongQuestionList.forEach(q => {
      addQuestion(doc, q, {
        showQuestionScore: true,
        showAnswer: !answerAfterPaper && showAnswer,
        showExplanation: !answerAfterPaper && showExplanation,
        showKnowledge: !answerAfterPaper && showKnowledge,
        showListening: !answerAfterPaper && showListening,
      })
    })
  })
  // 参考答案与解析
  if (answerAfterPaper && (showAnswer || showExplanation || showKnowledge || showListening)) {
    // 标题，接口转换时会将@@page-break@@替换为分页
    doc.appendChild(
      createElement('div', '@@page-break@@【参考答案与解析】', {
        textAlign: 'center',
        fontSize: '20px',
        fontWeight: 'bold',
      })
    )

    exams.forEach(exam => {
      // 考试项目标题
      doc.appendChild(
        createElement('div', exam.examName, {
          fontSize: '16px',
          fontWeight: 'bold',
          marginTop: '1em',
        })
      )
      // 题目
      exam.wrongQuestionList.forEach(q => {
        addAloneQuestionAnswerExplanation(doc, q, {
          showBranchScore: q.branches.length > 1,
          showAnswer,
          showExplanation,
          showKnowledge,
          showListening,
        })
      })
    })
  }
}

function getLeaveAnswerSpaceWrongQuestions(exams, leaveAnswerSpace) {
  if (!leaveAnswerSpace) {
    return []
  }
  let questions = []
  exams.forEach(exam => {
    exam.wrongQuestionList.forEach(q => {
      questions.push(q)
    })
  })
  return getLeaveAnswerSpaceQuestions(questions)
}

/**
 * 文档与元素的创建 ---------------------------------------------------------------------------------------
 */
// 创建空文档
function getEmptyHTMLDoc() {
  return createElement('div', null, {
    position: 'fixed',
    zIndex: '-9999',
    fontSize: '14px',
    fontFamily: '"Times New Roman", 宋体',
  })
}

// 创建元素
function createElement(tagName, innerHTML, style, classStr) {
  let el = window.document.createElement(tagName)
  if (innerHTML) {
    el.innerHTML = innerHTML
  }
  if (style) {
    Object.keys(style).forEach(key => {
      el.style[key] = style[key]
    })
  }
  if (classStr) {
    el.classList.add(classStr)
  }
  return el
}

// 去掉包裹<p></p>
function removeWrap(content) {
  if (!content) {
    return ''
  }
  content = content.trim()

  // 部分latex公式不能正常转换为word公式，需改写法
  content = content.replace(/\$[^$]*\$/g, match => {
    return (
      match
        // 撇号
        .replace(/{\s*'\s*}/g, `'`)
        // 摄氏度
        .replace(/\^{\s*\\circ\s*}\s*C/g, `°C`)
        // 分式
        .replace(/\\dfrac/g, `\\frac`)
    )
  })

  let container = createElement('div', content)
  let nodes = container.childNodes
  for (let i = 0; i < nodes.length; i++) {
    let node = nodes[i]
    if (node.nodeType === 3) {
      break
    } else if (node.nodeType === 1 && node.nodeName.toUpperCase() === 'P') {
      let newNodes = node.childNodes
      for (let newNode of newNodes) {
        container.insertBefore(newNode.cloneNode(true), node)
      }
      container.removeChild(node)
      break
    }
  }
  return container.innerHTML
}

/**
 * 添加题目 -------------------------------------------------------------------------------------------
 */
// 加题目内容
function addQuestion(doc, q, { showQuestionScore, showAnswer, showExplanation, showKnowledge, showListening }) {
  let elQuestion = createElement(
    'div',
    null,
    {
      marginTop: '10px',
    },
    'wrapper-question'
  )
  elQuestion.dataset.questionId = q.id
  doc.appendChild(elQuestion)

  // 小题是否展开
  let branchesExpanded = q.getIsBranchesExpanded()

  // 材料
  if (q.trunk) {
    let trunkContent = q.getTrunkContent()
    let questionCodeText = branchesExpanded ? '' : q.getQuestionCodeString(false) + '. '
    let questionScoreText = showQuestionScore ? `（${q.score}分）` : ''
    elQuestion.appendChild(createElement('div', `${questionCodeText}${questionScoreText}${removeWrap(trunkContent)}`))
  }

  // 小题
  q.branches.forEach((b, idx) => {
    let elBranch = createElement('div', null, {
      marginTop: idx > 0 ? '5px' : '0',
    })
    elQuestion.appendChild(elBranch)

    let prefix = getBranchCodeText(q, b, true)
    if (showQuestionScore) {
      prefix += `（${b.score}分）`
    }

    // 题干
    if (b.stem) {
      let elStem = createElement('div', `${prefix}${removeWrap(b.stem)}`)
      elBranch.appendChild(elStem)
    }

    // 选项
    if (b.options && b.options.length && b.options.some(opt => opt.content)) {
      let elOptionsWrapper = createElement('div', null, null, 'q-options-wrapper')
      elBranch.appendChild(elOptionsWrapper)
      b.options.forEach((opt, idx) => {
        let content = `${opt.label}. ${removeWrap(opt.content)}`
        if (!b.stem && idx === 0) {
          content = `${prefix}${content}`
        }
        let elOptionItem = createElement('span', content, null, 'q-option-item')
        elOptionsWrapper.appendChild(elOptionItem)
      })
    }
  })

  // 答案
  if (showAnswer) {
    addAnswer(elQuestion, q)
  }

  // 解析
  if (showExplanation) {
    addExplanation(elQuestion, q)
  }

  // 知识点
  if (showKnowledge) {
    addKnowledge(elQuestion, q)
  }

  // 听力
  if (showListening) {
    addListeningMaterial(elQuestion, q)
  }
}

// 加独立的题目答案解析
function addAloneQuestionAnswerExplanation(
  doc,
  q,
  { showBranchScore, showAnswer, showExplanation, showKnowledge, showListening }
) {
  let elQuestion = createElement('div', null, {
    marginTop: '10px',
  })
  doc.appendChild(elQuestion)

  // 题号分数
  elQuestion.appendChild(createElement('div', `${q.getQuestionCodeString()}. （${q.score}分）`))

  if (showAnswer) {
    addAnswer(elQuestion, q, showBranchScore)
  }
  if (showExplanation) {
    addExplanation(elQuestion, q)
  }
  if (showKnowledge) {
    addKnowledge(elQuestion, q)
  }
  if (showListening) {
    addListeningMaterial(elQuestion, q)
  }
}

// 加答案
function addAnswer(elQuestion, q, showBranchScore) {
  let elAnswer = createElement(
    'div',
    null,
    {
      marginTop: '5px',
    },
    'wrapper-question-answer'
  )
  elAnswer.dataset.questionId = q.id
  elQuestion.appendChild(elAnswer)

  if (q.branches.length > 1) {
    elAnswer.appendChild(createElement('div', '【答案】'))
  }
  q.branches.forEach((b, idx) => {
    let prefix = getBranchCodeText(q, b)
    if (idx === 0 && q.branches.length == 1) {
      prefix = '【答案】' + prefix
    }
    if (showBranchScore) {
      prefix += `（${b.score}分）`
    }

    let content = ''
    if (b.branchType.id === BranchTypeEnum.SingleChoice.id) {
      content = b.answer.singleChoice
    } else if (b.branchType.id === BranchTypeEnum.MultipleChoice.id) {
      content = b.answer.multipleChoice.join('')
    } else if (b.branchType.id === BranchTypeEnum.TrueOrFalse.id) {
      content = b.answer.trueOrFalse
    } else if (b.branchType.id === BranchTypeEnum.FillBlank.id) {
      content = b.answer.fillBlank.join('&nbsp;&nbsp;&nbsp;&nbsp;')
    } else {
      content = b.answer.essay
    }

    elAnswer.appendChild(createElement('div', `${prefix}${removeWrap(content)}`))
  })
}

// 加解析
function addExplanation(elQuestion, q) {
  if (q.branches.every(b => !b.explanation)) {
    return
  }

  let elExplanation = createElement('div', null, {
    marginTop: '5px',
  })
  elQuestion.appendChild(elExplanation)

  // 判断是否只有第一小题有解析（不分小题写解析）
  let onlyFirstBranchHasExplanation = true
  for (let i = 1; i < q.branches.length; i++) {
    if (q.branches[i].explanation) {
      onlyFirstBranchHasExplanation = false
    }
  }
  if (onlyFirstBranchHasExplanation) {
    elExplanation.appendChild(createElement('div', `【解析】${removeWrap(q.branches[0].explanation)}`))
  } else {
    if (q.branches.length > 1) {
      elExplanation.appendChild(createElement('div', '【解析】'))
    }
    q.branches.forEach((b, idx) => {
      let prefix = getBranchCodeText(q, b)
      if (idx === 0 && q.branches.length == 1) {
        prefix = '【解析】' + prefix
      }
      if (b.explanation) {
        elExplanation.appendChild(createElement('div', `${prefix}${removeWrap(b.explanation)}`))
      }
    })
  }
}

// 加知识点
function addKnowledge(elQuestion, q) {
  let elKnowledge = createElement('div', null, {
    marginTop: '5px',
  })
  elQuestion.appendChild(elKnowledge)

  if (q.branches.length > 1) {
    elKnowledge.appendChild(createElement('div', '【知识点】'))
  }
  q.branches.forEach((b, idx) => {
    let prefix = getBranchCodeText(q, b)
    if (idx === 0 && q.branches.length == 1) {
      prefix = '【知识点】' + prefix
    }

    let content = b.knowledges.map(k => k.name).join('；')

    elKnowledge.appendChild(createElement('div', `${prefix}${content}`))
  })
}

// 加听力材料
function addListeningMaterial(elQuestion, q) {
  let questionListeingMaterial = q.listening && q.listening.material
  let hasMaterialBranches = (q.branches || []).filter(b => b.listening && b.listening.material)
  if (!questionListeingMaterial && hasMaterialBranches.length == 0) {
    return
  }

  let listeningContents = []
  if (questionListeingMaterial) {
    listeningContents.push(removeWrap(questionListeingMaterial))
  }
  hasMaterialBranches.forEach(b => {
    listeningContents.push(getBranchCodeText(q, b) + removeWrap(b.listening.material))
  })
  listeningContents[0] = '【听力材料】' + listeningContents[0]

  let elListening = createElement('div', null, {
    marginTop: '5px',
  })
  listeningContents.forEach(content =>
    elListening.appendChild(
      createElement('div', content, {
        'white-space': 'pre-wrap',
      })
    )
  )
  elQuestion.appendChild(elListening)
}

// 小题号文本
function getBranchCodeText(question, branch, showQuestionCode = false) {
  let code = question.getBranchCodeString(branch, false)
  if (question.getIsBranchesExpanded()) {
    return `${code}. `
  } else if (question.branches.length > 1) {
    if (!question.trunk && branch.code == 1 && showQuestionCode) {
      return `${question.getQuestionCodeString(false)}.（${code}）`
    } else {
      return `（${code}）`
    }
  } else {
    return showQuestionCode ? question.getQuestionCodeString(false) + '. ' : ''
  }
}

/**
 * 处理文档内容 -----------------------------------------------------------------------------------------
 */
async function handleDocContent(doc, size, leaveAnswerSpaceQuestions) {
  // 表格加实线边框、设置上下边距
  setTableBorderAndMargin(doc)

  // 添加到页面中并渲染公式
  window.document.body.appendChild(doc)
  await MathJaxUtil.render(doc)

  // 排列选择题选项、处理数学公式
  layoutOptions(doc, size)
  handleMathFormula(doc)

  // 留作答空白
  if (leaveAnswerSpaceQuestions.length > 0) {
    setAnswerSpace(doc, leaveAnswerSpaceQuestions)
  }

  // 从页面中移除
  window.document.body.removeChild(doc)

  // 拼接html
  let html = `<!DOCTYPE html><html lang="zh-CN"><body>${doc.innerHTML}</body></html>`

  // 替换特殊字符
  html = html.replace(/[\u2060-\u206F]/g, '')

  return html
}

// 表格加实线边框、设置上下边距
function setTableBorderAndMargin(doc) {
  Array.from(doc.querySelectorAll('table')).forEach(table => {
    table.style.borderCollapse = 'collapse'
    table.style.margin = '0 auto'

    Array.from(table.querySelectorAll('td')).forEach(td => {
      td.style.border = '0.1pt solid var(--base-border-color)'
      td.style.padding = '5pt 8pt'
    })

    // 上下插入空行
    table.insertAdjacentHTML('beforebegin', '<div style="font-size:5px;line-height:5px;height:5px;"><br></div>')
    table.insertAdjacentHTML('afterend', '<div style="font-size:5px;line-height:5px;height:5px;"><br></div>')
  })
}

// 排列选择题选项
function layoutOptions(doc, size) {
  let optionsWrappers = doc.querySelectorAll('.q-options-wrapper')
  optionsWrappers.forEach(wrapper => {
    let optionItems = wrapper.querySelectorAll('.q-option-item')
    let options = Array.from(optionItems).map(optionItem => ({
      content: optionItem.innerHTML,
      width: optionItem.offsetWidth,
    }))
    wrapper.innerHTML = ''

    // 每个制表位的宽度，接口转换时会将@@tab-stop@@替换为制表符
    // 制表符宽度由接口告知，计算方法：(页面宽度inch - 左边距inch - 右边距inch - 栏间距inch) / 栏数(A4一栏，A3、K8两栏) / 制表位数4 * 72pt/inch / 10.5pt * 14px
    let tabStopWidth = size == 'A3' ? 177 : size == 'K8' ? 153 : 174
    let maxOptionWidth = Math.max(...options.map(opt => opt.width))
    let optionsPerRow = maxOptionWidth < tabStopWidth - 20 ? 4 : maxOptionWidth < tabStopWidth * 2 - 20 ? 2 : 1
    let rows = Math.ceil(options.length / optionsPerRow)
    for (let r = 0; r < rows; r++) {
      let rowContent = ''
      for (let i = 0; i < optionsPerRow; i++) {
        let index = r * optionsPerRow + i
        let opt = options[index]
        if (!opt) {
          continue
        }
        if (i > 0) {
          let tabStopCount = optionsPerRow == 2 && options[index - 1].width < tabStopWidth ? 2 : 1
          rowContent += '@@tab-stop@@'.repeat(tabStopCount)
        }
        rowContent += opt.content
      }
      let row = createElement('div', rowContent)
      wrapper.appendChild(row)
    }
  })
}

// 处理数学公式
export function handleMathFormula(doc) {
  // 删渲染后mathjax标签
  let scripts = doc.querySelectorAll('script')
  let assistants = doc.querySelectorAll('.MJX_Assistive_MathML')
  let previews = doc.querySelectorAll('.MathJax_Preview')
  let removeList = [...scripts, ...assistants, ...previews]
  removeList.forEach(item => {
    item.remove()
  })

  //替换mathml
  let elMathjaxes = doc.querySelectorAll('.mjx-chtml')
  Array.from(elMathjaxes).forEach(el => {
    let mml = el.getAttribute('data-mathml')
    let mathWrapper = createElement('span', mml)
    let mathEl = mathWrapper.firstElementChild
    // 删除不必要的<mrow></mrow>,<mstyle></mstyle>
    Array.from(mathEl.querySelectorAll('mrow,mstyle')).forEach(node => {
      if (node.children.length < 2) {
        node.before(...node.children)
        node.remove()
      }
    })
    // word公式数组前后括号写法：mfenced
    let children = mathEl.children
    if (children.length == 1 && children[0].tagName == 'mrow') {
      children = children[0].children
    }
    if (children.length == 3) {
      let [first, middle, last] = children
      if (first.tagName == 'mo' && middle.tagName == 'mtable' && last.tagName == 'mo') {
        let firstInnerHTML = first.innerHTML
        let lastInnerHTML = last.innerHTML
        if (['', '(', '{'].includes(firstInnerHTML) && ['', ')', '}'].includes(lastInnerHTML)) {
          let mfenced = document.createElement('mfenced')
          mfenced.setAttribute('open', firstInnerHTML)
          mfenced.setAttribute('close', lastInnerHTML)
          mfenced.appendChild(middle)
          Array.from(mathEl.children).forEach(child => {
            child.remove()
          })
          mathEl.appendChild(mfenced)
        }
      }
    }
    el.parentElement.insertBefore(mathEl, el)
    el.parentElement.removeChild(el)
  })
}

// 留答题空白
function setAnswerSpace(doc, leaveAnswerSpaceQuestions) {
  let elQuestions = Array.from(doc.querySelectorAll('.wrapper-question'))
  let elAnswers = Array.from(doc.querySelectorAll('.wrapper-question-answer'))
  for (let q of leaveAnswerSpaceQuestions) {
    let elQuestion = elQuestions.find(el => el.dataset.questionId == q.id)
    if (!elQuestion) {
      continue
    }
    let emptyRows
    if (q.branches.some(b => b.branchTypeId == BranchTypeEnum.ChineseWriting.id)) {
      emptyRows = 32
      if (q.stage.id == 1) {
        emptyRows = 24
      } else if (q.stage.id == 4) {
        emptyRows = 40
      }
    } else if (q.branches.some(b => b.branchTypeId == BranchTypeEnum.EnglishWriting.id)) {
      emptyRows = 16
      if (q.stage.id == 1) {
        emptyRows = 12
      } else if (q.stage.id == 4) {
        emptyRows = 20
      }
    } else {
      let elAnswer = elAnswers.find(el => el.dataset.questionId == q.id)
      if (!elAnswer) {
        emptyRows = 8
      } else {
        let oldWidth = elAnswer.style.width
        elAnswer.style.width = '600px'
        emptyRows = Math.ceil(elAnswer.offsetHeight / 14 / 4) * 4
        elAnswer.style.width = oldWidth
      }
    }
    emptyRows = Math.max(4, Math.min(40, emptyRows))
    let div = document.createElement('div')
    div.innerHTML = '<br>'.repeat(emptyRows)
    elQuestion.appendChild(div)
  }
}
