import ajax from '@/api/ajax'

export function apiGetTheTeachingInfo() {
  return ajax
    .get({
      url: 'homework/teaapp/main/getTeaBaseInfo/list',
      params: {},
      requestName: '获取任教老师任课信息',
    })
    .then(data => {
      return data
    })
}

export function apiCreateLearningTask(params) {
  return ajax
    .post({
      url: 'homework/teaapp/createLearningTasks/add',
      data: params,
      requestName: '创建学习任务',
    })
    .then(data => {
      return data
    })
}

export function apiUploadFile(params) {
  return ajax
    .upload({
      url: 'homework/teaapp/addNewUploadToAliyun',
      data: params,
      requestName: '上传文件',
    })
    .then(data => {
      return data
    })
}

export function apiDeleteFile(params) {
  return ajax
    .delete({
      url: 'homework/teaapp/deleteHomeworkSource/delete',

      data: params,
      requestName: '删除文件',
    })
    .then(data => {
      return data
    })
}

export function apiGetStudentInfo(params) {
  return ajax
    .get({
      url: 'homework/teaapp/getStudentInfo',
      params: params,
      requestName: '获取学生信息',
    })
    .then(data => {
      return data
    })
}

export function apiPublishLearningTask(params) {
  return ajax
    .post({
      url: 'homework/teaapp/publishLearningTask/add',
      data: params,
      requestName: '发布学习任务',
    })
    .then(data => {
      return data
    })
}

export function apiGetHomeworkList(params) {
  return ajax
    .get({
      url: 'homework/teaapp/homeworkList/page',
      params: params,
      requestName: '获取作业列表',
    })
    .then(data => {
      return data
    })
}

export function apiCreateHomeworkGroup(params) {
  return ajax
    .post({
      url: 'homework/teaapp/createHomeworkGroup/add',
      data: params,
      requestName: '创建作业分组',
    })
    .then(data => {
      return data
    })
}

export function apiDeleteHomeworkGroup(params) {
  return ajax
    .delete({
      url: 'homework/teaapp/deleteHomeworkGroup/delete',
      data: params,
      requestName: '删除作业分组',
    })
    .then(data => {
      return data
    })
}

export function apiDeleteHomework(params) {
  return ajax
    .delete({
      url: 'homework/teaapp/deleteHomework/delete',
      data: params,
      requestName: '删除作业',
    })
    .then(data => {
      return data
    })
}

export function apiGetHomeworkSetting(params) {
  return ajax
    .get({
      url: 'homework/teaapp/getHomeworkSetting',
      params: params,
      requestName: '获取作业设置信息',
    })
    .then(data => {
      return data
    })
}

export function apiUpdateHomework(params) {
  return ajax
    .post({
      url: 'homework/teaapp/editHomework/edit',
      data: params,
      requestName: '更新作业设置',
    })
    .then(data => {
      return data
    })
}

export function apiGetHomeworkBaseInfo(params) {
  return ajax
    .get({
      url: 'homework/teaapp/getAssignmentCorrectionInfo',
      params: params,
      requestName: '获取作业信息',
    })
    .then(data => {
      return data
    })
}

export function apiGetCorrectBaseInfo(params) {
  return ajax
    .get({
      url: 'homework/teaapp/getCorrectionBaseInfo',
      params: params,
      requestName: '获取作业信息',
    })
    .then(data => {
      return data
    })
}

export function apiGetStudentAssignment(params) {
  return ajax
    .get({
      url: 'homework/teaapp/getNextStudentAssignment',
      params: params,
      requestName: '获取待批改数据',
    })
    .then(data => {
      return data
    })
}

export function apiGetStudentCorrectedAssignment(params) {
  return ajax
    .get({
      url: 'homework/teaapp/getStudentCorrectedAssignment/list',
      params: params,
      requestName: '获取已批改数据',
    })
    .then(data => {
      return data
    })
}

export function apiMarkPoint(params) {
  return ajax
    .post({
      url: 'homework/teaapp/givePoint/edit',
      data: params,
      requestName: '给分',
    })
    .then(data => {
      return data
    })
}

export function apiCreateFreeTopic(params) {
  return ajax
    .post({
      url: 'homework/teaapp/createHomework/add',
      data: params,
      requestName: '创建自由出题',
    })
    .then(data => {
      return data
    })
}

export function apiPublishFreeTopic(params) {
  return ajax
    .post({
      url: 'homework/teaapp/publishHomework/add',
      data: params,
      requestName: '布置自由出题',
    })
    .then(data => {
      return data
    })
}

export function apiGetAssignmentOverview(params) {
  return ajax
    .get({
      url: 'homework/teaapp/assignmentOverview',
      params: params,
      requestName: '获取作业概览',
    })
    .then(data => {
      return data
    })
}

export function apiGetAssignmentScore(params) {
  return ajax
    .get({
      url: 'homework/teaapp/viewStudentGrade/list',
      params: params,
      requestName: '获取学生作业成绩',
    })
    .then(data => {
      return data
    })
}

export function apiGetStudentAssignmentDetails(params) {
  return ajax
    .get({
      url: 'homework/teaapp/viewStudentGradeDetail',
      params: params,
      requestName: '获取学生作业成绩详情',
    })
    .then(data => {
      return data
    })
}

export function apiGetHomeworkStudentInfo(params) {
  return ajax
    .get({
      url: 'homework/teaapp/getHomeworkStudentInfo/list',
      params: params,
      requestName: '获取作业全部学生',
    })
    .then(data => {
      return data
    })
}

export function apiGetHomeworkReview(params) {
  return ajax
    .get({
      url: 'homework/teaapp/homeworkReview',
      params: params,
      requestName: '获取作业讲评',
    })
    .then(data => {
      return data
    })
}

export function apiEditHomeworkSubmitStatus(params) {
  return ajax
    .post({
      url: 'homework/teaapp/editHomeworkSubmitStatus/edit',
      data: params,
      requestName: '修改学生提交状态',
    })
    .then(data => {
      return data
    })
}

export function apiReleaseScore(params) {
  return ajax
    .post({
      url: 'homework/teaapp/releaseScore/edit',
      data: params,
    })
    .then(data => {
      return data
    })
}

export function apiGetHomeworkQuesList(params) {
  return ajax
    .get({
      url: 'homework/teaapp/homeworkQues/list',
      params: params,
      requestName: '获取作业题目结构',
    })
    .then(data => {
      return data
    })
}

export function apiEditHomeworkQuesAnswer(params) {
  return ajax
    .post({
      url: 'homework/teaapp/homeworkQuesAnswer/edit',
      data: params,
      requestName: '修改作业题目客观题答案',
    })
    .then(data => {
      return data
    })
}

export function apiRevokeHomeworkStat(params) {
  return ajax
    .post({
      url: 'homework/teaapp/revokeHomeworkStat/edit',
      data: params,
      requestName: '撤销发布成绩',
    })
    .then(data => {
      return data
    })
}

export function apiGetHomeworkStruture(params) {
  return ajax
    .get({
      url: 'homework/teaapp/getHomeworkStruture',
      params: params,
      requestName: '获取题库作业题目结构',
    })
    .then(data => {
      return data
    })
}
