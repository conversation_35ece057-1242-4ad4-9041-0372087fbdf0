/**
 * 动画
 * @param {number} duration
 * @param {(function | string | Object)} timing  timing函数或预定义timing函数名或对象{timing: function || 'string', transform: 'easeout' || 'easeinout'}
 * @param {function} callback
 */
function animate(duration = 1000, timing, callback = () => {}) {
  let timingFun

  if (typeof timing == 'object') {
    let unTransFormedFunction = getUnTransFormedFunction(timing.timing)
    let transformName = String(timing.transform).toLowerCase()
    if (transformName == 'easeout') {
      timingFun = makeEaseOut(unTransFormedFunction)
    } else if (transformName == 'easeinout') {
      timingFun = makeEaseInOut(unTransFormedFunction)
    } else {
      timingFun = unTransFormedFunction
    }
  } else {
    timingFun = getUnTransFormedFunction(timing)
  }

  _animate(duration, timingFun, callback)

  function getUnTransFormedFunction(f) {
    if (typeof f == 'function') {
      return f
    } else if (typeof f == 'string') {
      return TimingFunction[f] || TimingFunction.linear
    } else {
      return TimingFunction.linear
    }
  }
}

// 预定义timing函数
const TimingFunction = {
  linear(x) {
    return x
  },
  arc(x) {
    return 1 - Math.sqrt(1 - x * x)
  },
  back(x, elasticity = 1) {
    return x * x * ((elasticity + 1) * x - elasticity)
  },
}

function makeEaseOut(timing) {
  return function (x) {
    return 1 - timing(1 - x)
  }
}

function makeEaseInOut(timing) {
  return function (x) {
    if (x <= 0.5) {
      return timing(2 * x) * 0.5
    } else {
      return 1 - timing(2 - 2 * x) * 0.5
    }
  }
}

function _animate(duration, timing, callback) {
  let startTime = 0
  window.requestAnimationFrame(function doWork(currentTime) {
    if (startTime == 0) {
      startTime = currentTime
    }
    let timeFraction = (currentTime - startTime) / (duration || 1)
    if (timeFraction < 0) {
      timeFraction = 0
    }

    if (timeFraction > 1) {
      timeFraction = 1
    }

    let progress = timing(timeFraction)
    callback(progress)

    if (timeFraction < 1) {
      window.requestAnimationFrame(doWork)
    }
  })
}

export { animate }
