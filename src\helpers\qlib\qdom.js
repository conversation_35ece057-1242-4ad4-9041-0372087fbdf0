import { deepCopy } from '@/utils/object'
import { groupArray, groupArrayAdjacent } from '@/utils/array'
import { Question } from '@/helpers/qlib/question'
import { getOptionLabelByIndex, getQuestionTypeValidBranchTypeIds } from '@/helpers/qlib/miscellaneous'
import BranchTypeEnum from '@/enum/qlib/branch_type'

import STORE from '@/store/index'

// 最大题号限制
const MaxQuestionCode = 200

// 圈码
const CircleNumbers = '①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳'

// 标签转换
const TagTransForm = {
  h1: 'p',
  h2: 'p',
  h3: 'p',
  h4: 'p',
  h5: 'p',
  h6: 'p',
  address: 'p',
  article: 'p',
  aside: 'p',
  footer: 'p',
  header: 'p',
  hgroup: 'p',
  main: 'p',
  nav: 'p',
  section: 'p',
  blockquote: 'p',
  dd: 'span',
  dir: 'p',
  div: 'p',
  dl: 'p',
  dt: 'span',
  figcaption: 'span',
  figure: 'p',
  label: 'span',
  li: 'p',
  ol: 'p',
  p: 'p',
  pre: 'span',
  ul: 'p',
  a: 'span',
  abbr: 'span',
  b: 'strong',
  bdi: 'span',
  bdo: 'span',
  br: 'br',
  cite: 'span',
  code: 'p',
  data: 'p',
  dfn: 'span',
  em: 'em',
  i: 'em',
  kbd: 'span',
  mark: 'span',
  q: 'span',
  s: 'span',
  samp: 'span',
  small: 'span',
  span: 'span',
  strong: 'strong',
  sub: 'sub',
  sup: 'sup',
  time: 'span',
  tt: 'span',
  u: 'u',
  del: 'u',
  var: 'em',
  wbr: 'span',
  caption: 'caption',
  table: 'table',
  thead: 'thead',
  tbody: 'tbody',
  tfoot: 'tfoot',
  tr: 'tr',
  td: 'td',
  th: 'th',
  img: 'img',
  font: 'span',
}

// 标签可保留的样式
const ReserveStyles = {
  br: [],
  strong: [],
  em: [],
  u: [],
  sub: [],
  sup: [],
  table: [],
  caption: [],
  thead: [],
  tbody: [],
  tfoot: [],
  tr: [],
  td: [
    {
      name: 'text-align',
      value: /^(left|right|center)$/,
    },
  ],
  th: [
    {
      name: 'text-align',
      value: /^(left|right|center)$/,
    },
  ],
  // img: ['width', 'height', 'float', 'margin-left', 'margin-right', 'display'],
  img: ['width', 'height', 'float', 'display'],
  p: [
    {
      name: 'text-align',
      value: /^(left|right|center)$/,
    },
  ],
  span: ['font-weight', 'text-decoration', 'vertical-align'],
}

// 具有某些特定样式的标签转换成本身具有相同显示效果的标签
const StyleTagTransform = {
  span: [
    {
      name: 'font-weight',
      value: 'bold,700,800,900',
      toName: 'strong',
    },
    {
      name: 'text-decoration',
      value: 'underline',
      toName: 'u',
    },
    {
      name: 'vertical-align',
      value: 'sub',
      toName: 'sub',
    },
    {
      name: 'vertical-align',
      value: 'super',
      toName: 'sup',
    },
  ],
  p: [
    {
      name: 'font-weight',
      value: 'bold,700,800,900',
      toName: 'strong',
    },
    {
      name: 'text-decoration',
      value: 'underline',
      toName: 'u',
    },
    {
      name: 'vertical-align',
      value: 'sub',
      toName: 'sub',
    },
    {
      name: 'vertical-align',
      value: 'super',
      toName: 'sup',
    },
  ],
}

// 标签可保留的属性
const ReserveAttributes = {
  img: [
    {
      name: 'src',
      required: true,
    },
  ],
  td: [
    {
      name: 'colspan',
      required: false,
    },
    {
      name: 'rowspan',
      required: false,
    },
  ],
  th: [
    {
      name: 'colspan',
      required: false,
    },
    {
      name: 'rowspan',
      required: false,
    },
  ],
}

export class QDOM {
  constructor() {
    this.DOMTree = null
  }

  // 克隆dom片段
  static clone(fragment) {
    let qDom = new QDOM()

    cleanMathJax(fragment)

    let p = document.createElement('p')
    p.append(fragment || '')
    qDom.DOMTree = _clone(p)

    qDom.normalize()

    return qDom

    function cleanMathJax(fragment) {
      // 删除preview
      let mathjaxPreviews = fragment.querySelectorAll('.MathJax_Preview')
      for (let item of mathjaxPreviews) {
        // 去除公式前多余的空格
        let previousSibling = item.previousSibling
        if (previousSibling && previousSibling.nodeType == 3) {
          let nodeValue = previousSibling.nodeValue.trim()
          if (nodeValue == '') {
            previousSibling.remove()
          }
        }
        item.remove()
      }

      // 替换成公式文本；若为序号，则替换成纯文本，暂不考虑序号是否在行首
      let mathjaxCHTMLs = fragment.querySelectorAll('.MathJax_CHTML')
      for (let item of mathjaxCHTMLs) {
        let mathText = ''
        let isTex = false
        // 优先提取tex公式
        let nextSibling = item.nextSibling
        if (
          nextSibling &&
          nextSibling.nodeType == 1 &&
          nextSibling.tagName.toLowerCase() == 'script' &&
          (nextSibling.type || '').toLowerCase() == 'math/tex'
        ) {
          mathText = '$' + nextSibling.innerText + '$'
          isTex = true
        }
        if (!isTex) {
          mathText = item.dataset['mathml'] || ''
        }

        let order
        // 小题号为公式
        if (mathText) {
          if (isTex) {
            let mathContent = mathText.substring(1, mathText.length - 1)
            let matchOrder = mathContent.match(/^\s*(\\left)?\s*[(（]\s*([1-9][0-9]?)\s*(\\right)?\s*[)）]/)
            if (matchOrder) {
              order = matchOrder[1] == '\\left' ? matchOrder[2] : matchOrder[1]
              mathText = '$' + mathContent.slice(matchOrder[0].length) + '$'
            }
          } else {
            // 提取<math>XXXX</math>标签的内容，得到XXXX
            let mathContent = ''
            let start = mathText.indexOf('>')
            let end = mathText.lastIndexOf('<')
            if (start >= 0 && end >= 0) {
              mathContent = mathText.slice(start + 1, end)
            }
            let matchOrder = mathContent.match(/^<mo>[(（]<\/mo><mn>([1-9][0-9]?)<\/mn><mo>[)）]<\/mo>/)
            if (matchOrder) {
              order = matchOrder[1]
              mathText =
                '<math xmlns="http://www.w3.org/1998/Math/MathML">' +
                mathContent.slice(matchOrder[0].length) +
                '</math>'
            }
          }
        }

        // 替换为文本节点
        if (order) {
          item.replaceWith('(' + order + ')' + mathText)
        } else {
          item.replaceWith(mathText)
        }
      }
    }

    function _clone(node) {
      // 文本节点
      if (node.nodeType === 3) {
        let textValue = node.nodeValue
        if (!textValue) {
          return
        }

        return {
          type: 3,
          value: textValue,
        }
      }
      // 元素节点
      else if (node.nodeType === 1) {
        // 匹配标签名
        let nodeName = TagTransForm[node.nodeName.toLowerCase()]
        if (!nodeName) {
          return
        }
        let childNodes = Array.from(node.childNodes)

        let thisNode = {
          type: 1,
          name: nodeName,
          styles: [],
          attributes: [],
          children: [],
        }

        // 自动序号转成文本
        if (node.nodeName.toLowerCase() === 'ol') {
          let start = Number(node.start) || 1
          for (let child of node.childNodes) {
            if (child.nodeType === 1 && child.nodeName.toLowerCase() === 'li') {
              child.setAttribute('order', start++)
            }
          }
        } else if (node.nodeName.toLowerCase() === 'li') {
          let order = node.getAttribute('order')
          if (order) {
            thisNode.children.push({
              type: 3,
              value: order + '. ',
              parent: thisNode,
            })
          }
        }

        // 若选择题选项是表格，则拆开
        if (node.nodeName.toLowerCase() === 'table') {
          let isOptions = true
          let tds = node.querySelectorAll('td')
          for (let i = 0; i < tds.length; i++) {
            let text = (tds[i].textContent || '')
              .replace(new RegExp(String.fromCharCode(160), 'g'), ' ')
              .replace(new RegExp(String.fromCharCode(12288), 'g'), '  ')
              .replace(/&nbsp;/g, ' ')
            if (i === 0) {
              if (!/^\s*[(（]?\s*(\d+)?\s*[)）]?\s*A\s*[.．]/.test(text) && !/^\s*(\d+[.．])?\s*A\s*[.．]/.test(text)) {
                isOptions = false
                break
              }
            } else {
              let reg = new RegExp(`^\\s*${String.fromCharCode(65 + i)}\\s*[.．]`)
              if (!reg.test(text)) {
                isOptions = false
                break
              }
            }
          }

          if (isOptions) {
            nodeName = 'p'
            thisNode.name = 'p'
            childNodes = []
            for (let td of tds) {
              childNodes.push(...td.childNodes)
            }
          }
        }

        // 获取可保留的属性，若缺失必需属性则抛弃此节点
        let mayAttributes = ReserveAttributes[nodeName] || []
        for (let attr of mayAttributes) {
          let attrValue = node.getAttribute(attr.name)
          if (attrValue != null) {
            thisNode.attributes.push({
              name: attr.name,
              value: attrValue,
            })
          } else {
            if (attr.required) {
              return
            }
          }
        }

        // 获取可保留的样式
        let mayStyles = ReserveStyles[nodeName] || []
        if (mayStyles.length > 0) {
          let styleStr = node.getAttribute('style')
          if (styleStr) {
            let styleItems = styleStr.split(';')
            styleItems.forEach(item => {
              let pair = item.split(':')
              if (pair && pair.length === 2) {
                let styleName = pair[0].trim(),
                  styleValue = pair[1].trim()
                if (
                  mayStyles.some(s =>
                    typeof s === 'string' ? s === styleName : s.name === styleName && s.value.test(styleValue)
                  )
                ) {
                  // 特殊处理，将其他单位转为像素
                  if (['margin', 'width', 'height'].some(x => styleName.match(x))) {
                    let styleValueNumber = parseFloat(styleValue)
                    let unit = styleValue.slice(-2).toLowerCase()
                    if (unit === 'pt') {
                      styleValue = (styleValueNumber / 72) * 96 + 'px'
                    } else if (unit === 'mm') {
                      styleValue = (styleValueNumber / 25.4) * 96 + 'px'
                    } else if (unit === 'cm') {
                      styleValue = (styleValueNumber / 2.54) * 96 + 'px'
                    } else if (unit === 'in') {
                      styleValue = styleValueNumber * 96 + 'px'
                    }
                  }

                  thisNode.styles.push({
                    name: styleName,
                    value: styleValue,
                  })
                }
              }
            })
          }
        }

        // 图片元素的宽高可从属性中获取
        if (nodeName === 'img' && node.width && node.height) {
          let styleWidth = thisNode.styles.find(x => x.name === 'width')
          if (styleWidth) {
            styleWidth.value = `${node.width}px`
          } else {
            thisNode.styles.push({
              name: 'width',
              value: `${node.width}px`,
            })
          }

          let styleHeight = thisNode.styles.find(x => x.name === 'height')
          if (styleHeight) {
            styleHeight.value = `${node.height}px`
          } else {
            thisNode.styles.push({
              name: 'height',
              value: `${node.height}px`,
            })
          }
        }

        // 转换带有某些样式的标签至特定标签
        if (StyleTagTransform[nodeName] && thisNode.styles.length) {
          let rule = StyleTagTransform[nodeName].find(x =>
            thisNode.styles.some(s => x.name === s.name && x.value.indexOf(s.value) >= 0)
          )
          if (rule) {
            thisNode.name = rule.toName
            thisNode.styles = thisNode.styles.filter(s => s.name !== rule.name)
          }
        }

        // 递归复制
        for (let child of childNodes) {
          let childCopy = _clone(child)
          if (childCopy) {
            childCopy.parent = thisNode
            thisNode.children.push(childCopy)
          }
        }

        return thisNode
      }
    }
  }

  static copy(nodes) {
    let qDom = new QDOM()
    if (nodes) {
      if (!(nodes instanceof Array)) {
        nodes = [nodes]
      }

      let p = {
        type: 1,
        name: 'p',
        styles: [],
        attributes: [],
        parent: null,
        children: [],
      }
      nodes.forEach(node => {
        node.parent = p
      })
      p.children = nodes
      qDom.DOMTree = p
      qDom.normalize()
    }

    return qDom
  }

  static deepCopy(nodes) {
    let qDom = new QDOM()
    if (nodes) {
      if (!(nodes instanceof Array)) {
        nodes = [nodes]
      }

      let p = {
        type: 1,
        name: 'p',
        styles: [],
        attributes: [],
        parent: null,
        children: [],
      }
      let newChildren = nodes.map(function _copy(node) {
        let newNode = Object.assign({}, node)
        if (node.children && node.children.length > 0) {
          let newNodeChildren = node.children.map(child => _copy(child))
          newNodeChildren.forEach(child => {
            child.parent = newNode
          })
          newNode.children = newNodeChildren
        }
        return newNode
      })
      newChildren.forEach(child => {
        child.parent = p
      })
      p.children = newChildren
      qDom.DOMTree = p
      qDom.normalize()
    }

    return qDom
  }

  /**
   * 规范化
   * 1. 所有块级元素均在第一层，且第一层只包含块级元素
   * 2. 合并内联元素或文本
   * 3. 删除空标签
   * 4. 删除空行
   */
  normalize() {
    if (!this.DOMTree) {
      return
    }

    _normalize(this.DOMTree)

    // 删除空行
    this.DOMTree.children = this.DOMTree.children.filter(el => {
      if (el.name === 'p') {
        if (el.children.length === 0) {
          return false
        }
        if (el.children.length === 1 && el.children[0].type === 3) {
          let textValue = el.children[0].value
          if (!textValue) {
            return false
          } else if (textValue === '&nbsp;') {
            return false
          } else if (textValue.split('').every(char => char === ' ')) {
            return false
          }
        }
      }

      return true
    })

    function _normalize(node) {
      // 文本节点替换特殊字符
      if (node.type === 3) {
        // 空格统一替换成char(32)
        node.value = node.value
          .replace(new RegExp(String.fromCharCode(160), 'g'), ' ')
          .replace(new RegExp(String.fromCharCode(12288), 'g'), '  ')
          .replace(/&nbsp;/g, ' ')

        // 删除换行符、制表符
        node.value = node.value
          .replace(new RegExp(String.fromCharCode(8629), 'g'), '')
          .replace(/\n/g, '')
          .replace(/\r/g, '')
          .replace(/\t/g, '')
        return
      }

      // children遍历
      node.children.forEach(child => {
        _normalize(child)
      })

      // 若span只含一个子节点，则用子节点替换本身
      node.children.forEach((child, idx) => {
        if (child.type === 3) {
          return
        }

        if (
          child.children.length === 1 &&
          child.styles.length === 0 &&
          child.attributes.length === 0 &&
          child.name === 'span'
        ) {
          let grandson = child.children[0]
          grandson.parent = node
          node.children[idx] = grandson
        }
      })

      // 块级元素只能在第一层（p和table）
      if (node.children.length > 0) {
        let i = 0,
          child
        while ((child = node.children[i])) {
          if (
            ['p', 'span', 'strong', 'em', 'u', 'sub', 'sup'].includes(child.name) &&
            child.children.some(grandson => grandson.name === 'p' || grandson.name === 'table')
          ) {
            child.children.forEach(grandson => (grandson.parent = node))
            node.children.splice(i, 1, ...child.children)
            i += child.children.length
          } else {
            i++
          }
        }
      }

      // 删除空文本节点
      node.children = (node.children || []).filter(child => !(child.type === 3 && !child.value))

      // 删除空标签
      let mayEmptyTags = ['br', 'td', 'th', 'img']
      node.children = (node.children || []).filter(
        child => !(child.type === 1 && child.children.length === 0 && !mayEmptyTags.includes(child.name))
      )

      // 合并文本节点
      if (node.children.length > 1 && node.children.filter(child => child.type === 3).length > 1) {
        let notTextNodeIndex = -1
        let childrenByType = groupArrayAdjacent(node.children, child => (child.type === 3 ? 3 : notTextNodeIndex--))
        let newChildren = childrenByType.map(childGroup => {
          if (childGroup.group.length > 1) {
            let newChild = {
              type: 3,
              parent: node,
              value: childGroup.group.map(x => x.value).join(''),
            }
            return newChild
          } else {
            return childGroup.group[0]
          }
        })
        node.children = newChildren
      }

      // 合并填空题空格
      if (node.children.length > 1 && node.children.filter(child => child.type == 1 && child.name == 'u').length > 1) {
        let notUnderlineNodeIndex = -1
        let childrenByBlank = groupArrayAdjacent(node.children, child =>
          child.type == 1 &&
          child.name == 'u' &&
          child.children.length == 1 &&
          child.children[0].type == 3 &&
          child.children[0].value.trim() == ''
            ? 1
            : notUnderlineNodeIndex--
        )
        let newChildren = childrenByBlank.map(childGroup => {
          if (childGroup.group.length > 1) {
            let newChild = {
              type: 1,
              name: 'u',
              styles: [],
              attributes: [],
              parent: node,
            }
            newChild.children = [
              {
                type: 3,
                parent: newChild,
                value: childGroup.group.map(x => x.children[0].value).join(''),
              },
            ]
            return newChild
          } else {
            return childGroup.group[0]
          }
        })
        node.children = newChildren
      }

      // 文本和内联元素必须包含在p元素内
      if (node.children.length > 0) {
        let blockNodeIndex = -1
        let childrenByType = groupArrayAdjacent(node.children, child =>
          child.type === 3 || ['strong', 'em', 'u', 'sub', 'sup', 'img', 'span'].includes(child.name)
            ? 3
            : blockNodeIndex--
        )
        // 子节点中同时包含内联元素和块级元素；或为根节点时，子节点中包含内联元素
        if (childrenByType.some(g => g.key === 3) && (childrenByType.some(g => g.key !== 3) || !node.parent)) {
          let newChildren = childrenByType.map(childGroup => {
            if (childGroup.key === 3) {
              let newChild = {
                type: 1,
                name: 'p',
                styles: [],
                attributes: [],
                parent: node,
              }
              let newChildChildren = childGroup.group.map(x => {
                x.parent = newChild
                return x
              })
              newChild.children = newChildChildren
              return newChild
            } else {
              return childGroup.group[0]
            }
          })
          node.children = newChildren
        }
      }

      // 删除换行br
      if (node.children.length > 0) {
        node.children = node.children.filter(child => child.name !== 'br')
      }

      // 删除表格周围的空行
      if (node.name == 'table') {
        node.children = node.children.filter(child => ['thead', 'tbody', 'tr'].includes(child.name))
      }
      if (['thead', 'tbody'].includes(node.name)) {
        node.children = node.children.filter(child => child.name == 'tr')
      }
      if (node.name == 'tr') {
        node.children = node.children.filter(child => ['td', 'th'].includes(child.name))
      }
    }
  }

  get innerHTML() {
    if (!this.DOMTree || !this.DOMTree.children || this.DOMTree.children.length === 0) {
      return ''
    }

    return this.DOMTree.children.map(child => QDOM.nodeToString(child)).join('')
  }

  static nodeToString(node) {
    if (node.type === 3) {
      // 连续空格替换成&nbsp;
      return node.value.replace(/\s{2,}/g, match => '&nbsp;'.repeat(match.length))
    } else {
      if (node.name === 'br') {
        return '<br>'
      }

      let attributeStr = node.attributes.map(item => ` ${item.name}="${item.value}"`).join()
      let styleStr = node.styles.map(s => `${s.name}: ${s.value}`).join(';')
      if (styleStr) {
        styleStr = ` style="${styleStr}"`
      }

      if (node.name === 'img') {
        return `<${node.name}${attributeStr}${styleStr}>`
      }

      let childrenStr = ''
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
          childrenStr += QDOM.nodeToString(child)
        })
      }

      return `<${node.name}${attributeStr}${styleStr}>${childrenStr}</${node.name}>`
    }
  }

  get textContent() {
    if (!this.DOMTree || !this.DOMTree.children || this.DOMTree.children.length === 0) {
      return ''
    }

    return this.DOMTree.children.map(child => nodeToText(child)).join('')

    function nodeToText(node) {
      if (node.type === 3) {
        return node.value
      } else {
        let childrenStr = ''
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => {
            childrenStr += nodeToText(child)
          })
        }
        return childrenStr
      }
    }
  }

  extractTopic(paragraph) {
    let matchTopicResult = paragraph.firstText.match(/^\s*[一二三四五六七八九十]{1,2}\s*、\s*([^\s(（$<]+)\s*/)
    return (matchTopicResult && matchTopicResult[1]) || ''
  }

  extractCode(paragraph, mayContainsAnswerSolution) {
    let matchCodeResult = paragraph.firstText.match(/^\s*(\d{1,3})\s*[.．]\s*/)
    let code = (matchCodeResult && Number(matchCodeResult[1])) || null

    let containsAnswer = false,
      containsSolution = false
    if (!code && mayContainsAnswerSolution) {
      matchCodeResult = paragraph.firstText.match(/^\s*[【[]\s*(答\s*案|解\s*析)\s*[】\]]\s*(\d{1,3})\s*[.．]\s*/)
      if (matchCodeResult && Number(matchCodeResult[2])) {
        code = Number(matchCodeResult[2]) || null
        if (matchCodeResult[1][0] === '答') {
          containsAnswer = true
        } else if (matchCodeResult[1][0] === '解') {
          containsSolution = true
        }
      }
    }

    // 避开小数，年份除外
    let firstText = paragraph.firstText
    if (containsAnswer || containsSolution) {
      firstText = firstText.replace(/^\s*[【[]\s*(答\s*案|解\s*析)\s*[】\]]\s*/, '')
    }
    if (firstText.match(/^\s*\d+\.\d+/) && !firstText.match(/^\s*\d+\.\d+年/)) {
      code = null
    }

    // 题号限定在1-200范围内
    if (code > 0 && code <= MaxQuestionCode) {
      if (containsAnswer) {
        paragraph.firstTextNode.value = paragraph.firstText.replace(
          /^\s*[【[]\s*(答\s*案|解\s*析)\s*[】\]]\s*(\d{1,3})\s*[.．]\s*/,
          '【答案】'
        )
      } else if (containsSolution) {
        paragraph.firstTextNode.value = paragraph.firstText.replace(
          /^\s*[【[]\s*(答\s*案|解\s*析)\s*[】\]]\s*(\d{1,3})\s*[.．]\s*/,
          '【解析】'
        )
      } else {
        paragraph.firstTextNode.value = paragraph.firstText.replace(/^\s*\d{1,3}\s*[.．]\s*/, '')
      }
    } else {
      code = null
    }

    return code
  }

  extractTopicsAndCodes(paragraphs, option) {
    // 匹配大题号与题号
    let paragraphCodes = paragraphs.map(x => {
      return {
        index: x.index,
        firstText: x.firstText,
        code: this.extractCode(x),
        topic: this.extractTopic(x),
      }
    })

    // 无需大题名称
    if (option && option.ignoreTopic) {
      paragraphCodes.forEach((c, idx) => {
        if (c.topic) {
          c.code = null
        } else if (c.code === null && idx > 0) {
          c.code = paragraphCodes[idx - 1].code
        }
      })

      let validParagraphCodes = paragraphCodes.filter(c => c.code > 0 && c.code <= MaxQuestionCode)
      if (validParagraphCodes.length > 0) {
        paragraphCodes = validParagraphCodes
      } else {
        // 需要至少保留一个题的内容
        if (option && option.reserveOnlyOne) {
          paragraphCodes.forEach(c => (c.code = -1))
        } else {
          paragraphCodes = []
        }
      }
    } else {
      // 删除大题内容与无题号内容
      let code = 0,
        topic = ''
      paragraphCodes.forEach(c => {
        if (c.topic) {
          topic = c.topic
          c.code = 0
          code = 0
        } else {
          c.topic = topic
        }

        if (c.code) {
          code = c.code
        } else {
          c.code = code
        }
      })
      paragraphCodes = paragraphCodes.filter(c => c.code > 0 && c.code <= MaxQuestionCode)
    }

    // 按题号分组，去掉题号不连续的
    let allGroups = groupArrayAdjacent(paragraphCodes, x => x.code).map(g => {
      let code = g.group[0].code
      let topic = g.group[0].topic
      let indexes = g.group.map(item => item.index)

      return {
        code,
        topic,
        indexes,
      }
    })
    allGroups.forEach(x => {
      if (x.code === -1) {
        x.code = null
      }
    })

    let validGroups = []
    if (allGroups.length > 1) {
      validGroups = [allGroups[0]]
      for (let i = 1; i < allGroups.length; i++) {
        if (allGroups[i].code === validGroups[i - 1].code + 1) {
          validGroups.push(allGroups[i])
        } else {
          break
        }
      }
    } else {
      validGroups = allGroups
    }

    return validGroups
  }

  // 自动划题，提取大题及题目
  extractTopicQuestions() {
    // 提取段首文字
    let childrenFirstTextRaw = this.DOMTree.children.map((child, idx) => {
      let firstTextNode = this.getFirstTextNode(child)
      return {
        index: idx,
        firstTextNode,
        firstText: (firstTextNode && firstTextNode.value) || '',
      }
    })

    // 分正文和答案两部分，以【参考答案与解析】字样分割开
    let splitorIndex = childrenFirstTextRaw.findIndex(x => /[【[]参考答案[与和及]解析[】\]]/.test(x.firstText))
    let sectionMain = childrenFirstTextRaw,
      sectionAnswer = []
    if (splitorIndex > 0) {
      sectionMain = childrenFirstTextRaw.slice(0, splitorIndex)
      sectionAnswer = childrenFirstTextRaw.slice(splitorIndex + 1)
    }

    let resultMain = this.extractTopicsAndCodes(sectionMain)
    let resultAnswer = this.extractTopicsAndCodes(sectionAnswer, {
      ignoreTopic: true,
    })

    let questionContents = resultMain.map(g => {
      let questionParagraphs = this.DOMTree.children.filter((child, idx) => g.indexes.includes(idx))

      // 找对应题目答案
      let answer = resultAnswer.find(x => x.code === g.code)
      if (answer) {
        let answerFirstTextNodeIndex = answer.indexes.findIndex(childIndex => {
          let firstTextNode = this.getFirstTextNode(this.DOMTree.children[childIndex])
          let firstText = (firstTextNode && firstTextNode.value) || ''
          return /^\s*[【[]\s*(答\s*案|解\s*析|知\s*识\s*点)\s*[】\]]\s*/.test(firstText)
        })
        // 剔除答案前的内容
        if (answerFirstTextNodeIndex > 0) {
          answer.indexes = answer.indexes.slice(answerFirstTextNodeIndex)
        }
        // 若无【答案】字样，则添加进去
        if (answerFirstTextNodeIndex < 0) {
          let pNode = {
            type: 1,
            name: 'p',
            styles: [],
            attributes: [],
            parent: null,
            children: [],
          }

          let textNode = {
            type: 3,
            value: '【答案】',
            parent: pNode,
          }

          pNode.children = [textNode]

          questionParagraphs.push(pNode)
        }
        answer.indexes.forEach(idx => {
          questionParagraphs.push(this.DOMTree.children[idx])
        })
      }

      return {
        code: g.code,
        topic: g.topic,
        content: QDOM.copy(questionParagraphs),
      }
    })

    return questionContents
  }

  // 手动划题，提取同一大题题目
  extractQuestions() {
    // 提取段首文字
    let childrenFirstTextRaw = this.DOMTree.children.map((child, idx) => {
      let firstTextNode = this.getFirstTextNode(child)
      return {
        index: idx,
        firstTextNode,
        firstText: (firstTextNode && firstTextNode.value) || '',
      }
    })

    let result = this.extractTopicsAndCodes(childrenFirstTextRaw, {
      ignoreTopic: true,
      reserveOnlyOne: true,
    })

    return result.map(x => {
      let questionParagraphs = this.DOMTree.children.filter((child, idx) => x.indexes.includes(idx))
      let content = QDOM.copy(questionParagraphs)

      return {
        code: x.code,
        content,
      }
    })
  }

  // 手动划题，所选内容归为一题，题号降级成小题
  extractQuestionCollapseBranches(option) {
    // 提取段首文字
    let nodeItemsAll = this.DOMTree.children
      .map((child, idx) => {
        let firstTextNode = this.getFirstTextNode(child)
        let firstText = (firstTextNode && firstTextNode.value) || ''

        return {
          index: idx,
          firstTextNode,
          firstText,
        }
      })
      .map(x => ({
        index: x.index,
        firstTextNode: x.firstTextNode,
        firstText: x.firstText,
        code: this.extractCode(x, true),
      }))
    let nodeItemsCode = nodeItemsAll.filter(x => x.code)

    // 题号去重排序
    let distinctCodes = Array.from(new Set(nodeItemsCode.map(x => x.code))).sort((a, b) => a - b)

    // 题号连续
    let minCode = null
    if (
      distinctCodes.length > 0 &&
      distinctCodes.every((code, idx) => idx === 0 || (idx > 0 && code === distinctCodes[idx - 1] + 1))
    ) {
      // 原小题号改为圈码
      toCircleNumber(this.DOMTree.children.slice(nodeItemsCode[0].index))

      // 题号改为小题号
      minCode = distinctCodes[0]
      nodeItemsCode.forEach(item => {
        let prefix = ''
        if (item.firstTextNode.value.startsWith('【答案】')) {
          item.firstTextNode.value = item.firstTextNode.value.substring(4)
          prefix = '【答案】'
        } else if (item.firstTextNode.value.startsWith('【解析】')) {
          item.firstTextNode.value = item.firstTextNode.value.substring(4)
          prefix = '【解析】'
        }

        item.firstTextNode.value = prefix + `(${item.code - minCode + 1})` + item.firstTextNode.value
      })

      if (option.replaceStemCodes) {
        replaceStemCodes(this.DOMTree.children.slice(0, nodeItemsCode[0].index), distinctCodes)
      }
    }

    return [
      {
        code: minCode,
        content: QDOM.copy(this.DOMTree.children),
      },
    ]

    function toCircleNumber(nodes) {
      nodes.forEach(node => {
        if (node.type === 3) {
          // 括号序号
          node.value = node.value.replace(/[\u2474-\u2487]/g, match => {
            let circleNumber = CircleNumbers[match.charCodeAt(0) - 9332]
            return circleNumber || match
          })

          // 括号加数字
          node.value = node.value.replace(/[(（]\s*(\d{1,2})\s*[)）]/g, (match, p1) => {
            let circleNumber = CircleNumbers[p1 - 1]
            return circleNumber || match
          })
        } else if (node.children && node.children.length > 0) {
          toCircleNumber(node.children)
        }
      })
    }

    function replaceStemCodes(nodes, codes) {
      let minCode = codes[0],
        codeIndex = 0
      nodes.forEach(_replace)

      function _replace(node) {
        if (node.type === 3) {
          // 改序号
          node.value = node.value.replace(/[(（]*\s*(\d+)\s*[)）]*/g, (match, p1) => {
            if (p1 == codes[codeIndex]) {
              if (codeIndex < codes.length - 1) {
                codeIndex++
              }
              return `  (${p1 - minCode + 1})  `
            } else {
              return match
            }
          })
        } else if (node.children && node.children.length > 0) {
          node.children.forEach(_replace)
        }
      }
    }
  }

  getFirstTextNode(node) {
    if (node.type === 3) {
      return node
    }

    if (!node.children || node.children.length === 0) {
      return
    }

    for (let child of node.children) {
      let textNode = this.getFirstTextNode(child)
      if (textNode) {
        return textNode
      }
    }
  }

  async extractQuestion(options) {
    let { stage, subject, questionType, code } = options

    let newQuestion = await Question.createNewQuestion()
    newQuestion.code = code
    newQuestion.stage = deepCopy(stage)
    newQuestion.subject = deepCopy(subject)
    newQuestion.questionType = deepCopy(questionType)
    newQuestion.rawContent = this.innerHTML
    newQuestion.saved = false

    // 1. 分拆材料题干、答案、解析
    let { body, answer, explanation, knowledge } = splitBodyAnswerExplanationKnowledge(this)

    // 2. 题干、材料、解析分小题
    let contentBranches = buildContentBranches()

    // 3. 每个小题内整理小题题干、选项、答案、解析
    for (let branch of contentBranches) {
      await buildNewQuestionBranch(branch)
    }

    return newQuestion

    function splitBodyAnswerExplanationKnowledge(dom) {
      let childrenFirstText = dom.DOMTree.children.map((child, idx) => {
        let firstTextNode = dom.getFirstTextNode(child)
        return {
          index: idx,
          firstTextNode,
          firstText: (firstTextNode && firstTextNode.value) || '',
        }
      })

      let childrenWithContentType = childrenFirstText.map(child => {
        let isAnswer = false
        let matchAnswerResult = child.firstText.match(/^\s*[【[]\s*答\s*案\s*[】\]]\s*/)
        if (matchAnswerResult) {
          child.firstTextNode.value = child.firstText.replace(/^\s*[【[]\s*答\s*案\s*[】\]]\s*/, '')
          isAnswer = true
        }

        let isExplanation = false
        let matchExplanationResult = child.firstText.match(/^\s*[【[]\s*解\s*析\s*[】\]]\s*/)
        if (matchExplanationResult) {
          child.firstTextNode.value = child.firstText.replace(/^\s*[【[]\s*解\s*析\s*[】\]]\s*/, '')
          isExplanation = true
        }

        let isKnowledge = false
        let matchKnowledgeResult = child.firstText.match(/^\s*[【[]\s*((知\s*识)|考)\s*点\s*[】\]]\s*/)
        if (matchKnowledgeResult) {
          child.firstTextNode.value = child.firstText.replace(/^\s*[【[]\s*((知\s*识)|考)\s*点\s*[】\]]\s*/, '')
          isKnowledge = true
        }

        return {
          index: child.index,
          firstTextNode: child.firstTextNode,
          firstText: child.firstText,
          isAnswer,
          isExplanation,
          isKnowledge,
        }
      })

      let isAnswer = false,
        isExplanation = false,
        isKnowledge = false
      childrenWithContentType.forEach(child => {
        if (child.isAnswer && !child.isExplanation && !child.isKnowledge) {
          isAnswer = true
          isExplanation = false
          isKnowledge = false
        } else if (!child.isAnswer && child.isExplanation && !child.isKnowledge) {
          isAnswer = false
          isExplanation = true
          isKnowledge = false
        } else if (!child.isAnswer && !child.isExplanation && child.isKnowledge) {
          isAnswer = false
          isExplanation = false
          isKnowledge = true
        } else {
          child.isAnswer = isAnswer
          child.isExplanation = isExplanation
          child.isKnowledge = isKnowledge
        }
      })

      let bodyParagraphs = childrenWithContentType
        .filter(child => !child.isAnswer && !child.isExplanation && !child.isKnowledge)
        .map(item => dom.DOMTree.children[item.index])
      let answerParagraphs = childrenWithContentType
        .filter(child => child.isAnswer && !child.isExplanation && !child.isKnowledge)
        .map(item => dom.DOMTree.children[item.index])
      let explanationParagraphs = childrenWithContentType
        .filter(child => !child.isAnswer && child.isExplanation && !child.isKnowledge)
        .map(item => dom.DOMTree.children[item.index])
      let knowledgeParagraphs = childrenWithContentType
        .filter(child => !child.isAnswer && !child.isExplanation && child.isKnowledge)
        .map(item => dom.DOMTree.children[item.index])

      return {
        body: QDOM.copy(bodyParagraphs),
        answer: QDOM.copy(answerParagraphs),
        explanation: QDOM.copy(explanationParagraphs),
        knowledge: QDOM.copy(knowledgeParagraphs),
      }
    }

    function splitBranches(dom) {
      let childrenFirstText = dom.DOMTree.children.map((child, idx) => {
        let firstTextNode = dom.getFirstTextNode(child)
        return {
          index: idx,
          firstTextNode,
          firstText: (firstTextNode && firstTextNode.value) || '',
        }
      })

      let childrenWithBranchCode = childrenFirstText.map(child => {
        let code = 0
        if (child.firstText.match(/^\s*(解|证明)\s*[:：]\s*[（(]\s*1\s*[)）]\s*/)) {
          code = 1
          child.firstTextNode.value = child.firstText.replace(/^\s*(解|证明)\s*[:：]\s*[（(]\s*1\s*[)）]\s*/, '')
        } else {
          let matchBranchCodeResult = child.firstText.match(/^\s*[(（]\s*(\d{1,2})\s*[)）]\s*/)
          if (matchBranchCodeResult) {
            code = Number(matchBranchCodeResult[1])
            if (!(code > 0 && code < MaxQuestionCode)) {
              code = 0
            } else {
              child.firstTextNode.value = child.firstText.replace(/^\s*[(（]\s*(\d{1,2})\s*[)）]\s*/, '')
            }
          }
        }

        return {
          index: child.index,
          firstTextNode: child.firstTextNode,
          firstText: child.firstText,
          code,
        }
      })

      let code = 0
      childrenWithBranchCode.forEach(child => {
        if (child.code) {
          code = child.code
        } else {
          child.code = code
        }
      })

      let branchParagraphs = groupArray(childrenWithBranchCode, x => x.code)
        .map(g => {
          let paragraphs = g.group.map(item => dom.DOMTree.children[item.index])
          return {
            code: g.group[0].code,
            content: QDOM.copy(paragraphs),
          }
        })
        .sort((a, b) => a.code - b.code)

      return branchParagraphs
    }

    function buildContentBranches() {
      let bodyBranches = []
      let validBranchTypeIds = getQuestionTypeValidBranchTypeIds(questionType)
      let branchType = BranchTypeEnum.getEntryById(validBranchTypeIds[0])

      // 英语特殊情况，选择题无小题题干选项（如信息匹配）或填空题无小题题干（如短文填空）
      if (
        validBranchTypeIds.length === 1 &&
        !questionType.branchHasStem &&
        ((branchType.isChoice && !questionType.branchHasOptions) || branchType.id === BranchTypeEnum.FillBlank.id)
      ) {
        let bodyText = body.textContent

        // 从材料中提取小题号
        let branchCodes = (bodyText.match(/[(（]\s*(\d{1,2})\s*[)）]/g) || []).map(str => Number(str.match(/\d+/)))
        // 暂不要求从1开始
        // let code1Index = branchCodes.findIndex(x => x === 1)
        // if (code1Index < 0) {
        //   branchCodes = []
        // }
        // else {
        //   branchCodes = branchCodes.slice(code1Index)
        // }
        let codeBreakIndex = branchCodes.findIndex((x, idx) => idx > 0 && x !== branchCodes[idx - 1] + 1)
        if (codeBreakIndex >= 0) {
          branchCodes = branchCodes.slice(0, codeBreakIndex)
        }

        bodyBranches = [
          {
            code: 0,
            content: body,
          },
          ...branchCodes.map(x => ({
            code: x,
            content: null,
          })),
        ]

        // 从材料中提取选项
        if (branchType.isChoice) {
          let optionLabels = (bodyText.match(/([A-J])\s*[.．]/g) || []).map(str => str[0])
          let labelAIndex = optionLabels.findIndex(x => x === 'A')
          if (labelAIndex < 0) {
            optionLabels = []
          } else {
            optionLabels = optionLabels.slice(labelAIndex)
          }
          let labelBreakIndex = optionLabels.findIndex(
            (x, idx) => idx > 0 && x.charCodeAt(0) !== optionLabels[idx - 1].charCodeAt(0) + 1
          )
          if (labelBreakIndex >= 0) {
            optionLabels = optionLabels.slice(0, labelBreakIndex)
          }

          let labelsNode = {
            type: 3,
            value: optionLabels.map(x => `${x}.`).join(''),
          }

          for (let i = 1; i < bodyBranches.length; i++) {
            bodyBranches[i].content = QDOM.copy(labelsNode)
          }
        }
      } else {
        bodyBranches = splitBranches(body)
      }

      let answerBranches = splitBranches(answer)
      let explanationBranches = splitBranches(explanation)
      let knowledgeBranches = splitBranches(knowledge)

      if (bodyBranches.length > 1 && bodyBranches[0].code === 0) {
        newQuestion.trunk = bodyBranches[0].content.innerHTML
        bodyBranches.splice(0, 1)
      }

      if (bodyBranches.length === 1 && bodyBranches[0].code === 0) {
        bodyBranches[0].code = 1
      }

      if (answerBranches.length === 1 && answerBranches[0].code === 0) {
        answerBranches[0].code = 1
      }

      if (explanationBranches.length === 1 && explanationBranches[0].code === 0) {
        explanationBranches[0].code = 1
      }

      if (knowledgeBranches.length === 1 && knowledgeBranches[0].code === 0) {
        knowledgeBranches[0].code = 1
      }

      let branches = []
      //let maxLength = Math.max(bodyBranches.length, answerBranches.length, explanationBranches.length)
      for (let i = 0; i < bodyBranches.length; i++) {
        let code = bodyBranches[i].code
        let answer = answerBranches.find(x => x.code === code)
        let explanation = explanationBranches.find(x => x.code === code)
        let knowledge = knowledgeBranches.find(x => x.code === code)

        branches.push({
          stem: bodyBranches[i].content || QDOM.copy([]),
          answer: (answer && answer.content) || QDOM.copy([]),
          explanation: (explanation && explanation.content) || QDOM.copy([]),
          knowledge: (knowledge && knowledge.content) || QDOM.copy([]),
        })
      }

      return branches
    }

    async function buildNewQuestionBranch(branch) {
      await newQuestion.addBranch()
      let newQuestionBranch = newQuestion.branches[newQuestion.branches.length - 1]

      // 解析、知识点（考点），与题型无关
      let knowledgeList = STORE.getters['qlib/currentStageSubjectKnowledgesForDevideQuestion'] || []
      newQuestionBranch.explanation = branch.explanation.innerHTML
      newQuestionBranch.knowledges = branch.knowledge.textContent
        .split(/[;； \s]/)
        .map(x => {
          let targetKnowledge = knowledgeList.find(y => y.title === x)
          if (targetKnowledge) {
            return {
              id: targetKnowledge.id,
              name: targetKnowledge.title,
            }
          } else {
            return null
          }
        })
        .filter(Boolean)

      let minOptionCount = stage && stage.name == '小学' && subject && subject.name == '英语' ? 2 : 3

      let validBranchTypeIds = getQuestionTypeValidBranchTypeIds(questionType)
      // （1）指定题型
      if (validBranchTypeIds.length == 1) {
        let branchType = BranchTypeEnum.getEntryById(validBranchTypeIds[0])
        newQuestionBranch.branchType = branchType

        if (branchType.isChoice) {
          let { stem, options } = extractStemAndOptions(branch.stem, minOptionCount)
          newQuestionBranch.stem = stem

          // 如果选项过少，则添加到4个选项
          if (options.length < minOptionCount) {
            for (let i = options.length; i < 4; i++) {
              options.push({
                label: getOptionLabelByIndex(i),
                content: '',
              })
            }
          }
          newQuestionBranch.options = options

          let answer = extractAnswerChoises(branch.answer, newQuestionBranch.options.length)
          if (branchType.id === BranchTypeEnum.SingleChoice.id) {
            newQuestionBranch.answer.singleChoice = answer[0] || ''
          } else {
            newQuestionBranch.answer.multipleChoice = answer
          }
        } else if (branchType.id === BranchTypeEnum.TrueOrFalse.id) {
          newQuestionBranch.stem = branch.stem.innerHTML
          newQuestionBranch.answer.trueOrFalse = extractAnswerTrueOrFalse(branch.answer)
        } else if (branchType.id === BranchTypeEnum.FillBlank.id) {
          let stemBlankCount = getStemBlankCount(branch.stem)
          if (stemBlankCount === 0) {
            stemBlankCount = 1
          }

          let extractAnswerResult = extractAnswerFillBlank(branch.answer, stemBlankCount)
          newQuestionBranch.stem = branch.stem.innerHTML
          newQuestionBranch.answer.fillBlank = extractAnswerResult
        } else {
          newQuestionBranch.stem = branch.stem.innerHTML
          newQuestionBranch.answer.essay = branch.answer.innerHTML
        }
      }
      // （2）未指定题型，按选择题 -> 填空题 -> 判断题 -> 解答题优先级顺序依次匹配
      else {
        let branchType = null

        // 选择题
        if (
          !branchType &&
          validBranchTypeIds.some(x => [BranchTypeEnum.SingleChoice.id, BranchTypeEnum.MultipleChoice.id].includes(x))
        ) {
          let { stem, options } = extractStemAndOptions(branch.stem, minOptionCount)
          if (options.length >= minOptionCount) {
            newQuestionBranch.stem = stem
            newQuestionBranch.options = options

            let onlySingle = !validBranchTypeIds.includes(BranchTypeEnum.MultipleChoice.id)
            let onlyMultiple = !validBranchTypeIds.includes(BranchTypeEnum.SingleChoice.id)

            let answerChoises = extractAnswerChoises(branch.answer, options.length)

            if (onlyMultiple || (!onlySingle && answerChoises.length >= 2)) {
              branchType = deepCopy(BranchTypeEnum.MultipleChoice)
              newQuestionBranch.answer.multipleChoice = answerChoises
            } else {
              branchType = deepCopy(BranchTypeEnum.SingleChoice)
              newQuestionBranch.answer.singleChoice = answerChoises[0] || ''
            }

            newQuestionBranch.branchType = branchType
          }
        }

        // 填空题
        if (!branchType && validBranchTypeIds.includes(BranchTypeEnum.FillBlank.id)) {
          let stemBlankCount = getStemBlankCount(branch.stem)
          if (stemBlankCount > 0) {
            branchType = deepCopy(BranchTypeEnum.FillBlank)
            newQuestionBranch.branchType = branchType

            let extractAnswerResult = extractAnswerFillBlank(branch.answer, stemBlankCount)
            newQuestionBranch.stem = branch.stem.innerHTML
            newQuestionBranch.answer.fillBlank = extractAnswerResult
          }
        }

        // 判断题
        if (!branchType && validBranchTypeIds.includes(BranchTypeEnum.TrueOrFalse.id)) {
          let answer = extractAnswerTrueOrFalse(branch.answer)
          if (answer === 'T' || answer === 'F') {
            branchType = deepCopy(BranchTypeEnum.TrueOrFalse)
            newQuestionBranch.branchType = branchType
            newQuestionBranch.stem = branch.stem.innerHTML
            newQuestionBranch.answer.trueOrFalse = answer
          }
        }

        // 解答题
        if (!branchType) {
          let branchTypeId = [
            BranchTypeEnum.Essay.id,
            BranchTypeEnum.ChineseWriting.id,
            BranchTypeEnum.EnglishWriting.id,
          ].find(x => validBranchTypeIds.includes(x))
          branchType = BranchTypeEnum.getEntryById(branchTypeId)
          newQuestionBranch.branchType = branchType
          newQuestionBranch.stem = branch.stem.innerHTML
          newQuestionBranch.answer.essay = branch.answer.innerHTML
        }
      }
    }

    function extractStemAndOptions(dom, minOptionCount = 3) {
      let labelAIndex = -1
      let domChildren = dom.DOMTree.children
      for (let i = 0; i < domChildren.length; i++) {
        let firstText = (dom.getFirstTextNode(domChildren[i]) || { value: '' }).value
        if (firstText.match(/^\s*A\s*[.．]/)) {
          labelAIndex = i
          break
        }
      }
      if (labelAIndex < 0) {
        return {
          stem: unifyChoiceStemBracket(dom.DOMTree.children),
          options: [],
        }
      }

      let optionsDom = QDOM.deepCopy(domChildren.slice(labelAIndex))
      let textNodes = []
      optionsDom.DOMTree.children.forEach(function getTextNodes(child) {
        if (child.type === 3) {
          textNodes.push(child)
          return
        }
        if (child.children && child.children.length > 0) {
          child.children.forEach(grandSon => getTextNodes(grandSon))
        }
      })

      let matchedLabels = []
      let lastLabel = ''
      let errorFlag = false
      for (let i = 0; i < textNodes.length; ) {
        let textNode = textNodes[i]
        let match = textNode.value.match(/\s*([A-J])\s*[.．]\s*/)
        let label = (match && match[1]) || ''
        if (label) {
          // 选项label（ABCD等）必须连续
          if ((!lastLabel && label === 'A') || (lastLabel && label.charCodeAt(0) - lastLabel.charCodeAt(0) === 1)) {
            lastLabel = label
            matchedLabels.push(label)

            // 文本节点需要拆分，并添加到原节点树上
            let paragraphBeforeLabel = {
              type: 1,
              name: 'span',
              attributes: [],
              styles: [],
              children: [],
              parent: textNode.parent,
            }
            let paragraphLabel = {
              type: 1,
              name: 'p',
              attributes: [
                {
                  name: 'label',
                  value: label,
                },
              ],
              styles: [],
              children: [],
              parent: textNode.parent,
            }
            let paragraphAfterLabel = {
              type: 1,
              name: 'span',
              attributes: [],
              styles: [],
              children: [],
              parent: textNode.parent,
            }
            let startIndex = match.index
            let endIndex = startIndex + match[0].length
            paragraphBeforeLabel.children = [
              {
                type: 3,
                value: textNode.value.slice(0, startIndex),
                parent: paragraphBeforeLabel,
              },
            ]
            paragraphLabel.children = [
              {
                type: 3,
                value: label,
                parent: paragraphLabel,
              },
            ]
            paragraphAfterLabel.children = [
              {
                type: 3,
                value: textNode.value.slice(endIndex),
                parent: paragraphAfterLabel,
              },
            ]

            let textNodeIndex = textNode.parent.children.indexOf(textNode)
            textNode.parent.children.splice(textNodeIndex, 1, paragraphBeforeLabel, paragraphLabel, paragraphAfterLabel)
            textNodes[i] = paragraphAfterLabel.children[0]
          } else {
            errorFlag = true
            break
          }
        } else {
          i++
        }
      }

      if (errorFlag || matchedLabels.length < minOptionCount) {
        return {
          stem: unifyChoiceStemBracket(dom.DOMTree.children),
          options: [],
        }
      }

      // 成功匹配到选项
      optionsDom.normalize()
      let childrenWithLabel = optionsDom.DOMTree.children.map((child, idx) => {
        // 避免空child 导致attributes无法读取
        let label = (child && child.attributes && child.attributes.find(x => x.name === 'label')) || {}
        label = (label && label.value) || ''

        return {
          index: idx,
          label,
        }
      })
      childrenWithLabel.forEach((child, idx) => {
        if (!child.label) {
          let previousChild = childrenWithLabel[idx - 1]
          child.label = (previousChild && previousChild.label) || ''
        }
      })
      childrenWithLabel = childrenWithLabel.filter(child => child.label)
      let options = groupArray(childrenWithLabel, x => x.label).map(g => {
        let paragraphs = g.group.slice(1).map(item => optionsDom.DOMTree.children[item.index])
        let optionDom = QDOM.copy(paragraphs)
        return {
          label: g.group[0].label,
          content: optionDom.innerHTML,
        }
      })

      let stem = unifyChoiceStemBracket(domChildren.slice(0, labelAIndex))

      return {
        stem,
        options,
      }
    }

    // 统一选择题题干末尾括号大小
    function unifyChoiceStemBracket(nodes) {
      let stemDom = QDOM.deepCopy(nodes)
      let lastTextNode = getChoiceStemBracketNode(stemDom.DOMTree)
      if (lastTextNode) {
        let bracket = `（${String.fromCharCode(160).repeat(4)}）`
        let match = lastTextNode.value.match(/[(（]\s*[)）]?\s*[.。]?\s*$/)
        if (match) {
          lastTextNode.value = lastTextNode.value.substring(0, lastTextNode.value.length - match[0].length) + bracket
        } else {
          lastTextNode.value = lastTextNode.value.trimEnd() + bracket
        }
      }
      return stemDom.innerHTML
    }

    function getChoiceStemBracketNode(node) {
      // 分段落，p和table单独成段，其他节点相邻成段
      let paragraphs = []
      let current = null
      node.children.forEach(child => {
        if (child.name == 'p' || child.name == 'table') {
          paragraphs.push([child])
          current = null
        } else {
          if (!current) {
            current = []
            paragraphs.push(current)
          }
          current.push(child)
        }
      })
      // 从最后一段往前检查
      for (let i = paragraphs.length - 1; i >= 0; i--) {
        current = paragraphs[i]
        // 跳过表格
        if (current.length == 1 && current[0].name == 'table') {
          continue
        }
        // p节点递归检查
        else if (current.length == 1 && current[0].name == 'p') {
          let result = getChoiceStemBracketNode(current[0])
          if (result) {
            return result
          }
        }
        // 行内元素及文本节点
        else {
          let lastNode = current[current.length - 1]
          // 末尾节点必须是文本节点
          if (lastNode.type != 3) {
            continue
          }
          // 段落文本不能为空白
          if (!current.some(node => node.type == 3 && node.value.trim())) {
            continue
          }
          // 段落不能以圈码序号开头
          if (current[0].type == 3) {
            let firstText = current[0].value.trim()
            let startsWithIndex = CircleNumbers.split('').some(x => firstText.startsWith(x))
            if (startsWithIndex) {
              continue
            }
          }
          return lastNode
        }
      }
    }

    function getStemBlankCount(dom) {
      let domStr = dom.innerHTML

      // 三种空格类型：<u>，下划线，小括号，下划线长度不超过30
      let patterns = [/<u[^>]*>((&nbsp;)|(\s)){4,}<\/u>/g, /(?<!_)_{4,30}(?!_)/g, /[(（]((&nbsp;)|(\s)){4,}[）)]/g]
      let results = patterns.map(pattern => {
        let result = domStr.match(pattern)
        return (result && result.length) || 0
      })

      return Math.max(...results)
    }

    function extractAnswerChoises(dom, optionsLength) {
      let domStr = dom.textContent.trim()

      // 不超长
      if (domStr.length > optionsLength || domStr.length === 0) {
        return []
      }

      let chars = domStr.split('')

      // 不超范围
      if (
        !chars.every(char => {
          let code = char.charCodeAt(0)
          return code >= 65 && code < 65 + optionsLength
        })
      ) {
        return []
      }

      // 按顺序
      if (
        !chars.some((char, idx) => {
          let previousChar = chars[idx - 1]
          return previousChar ? previousChar < char : true
        })
      ) {
        return []
      }

      return chars
    }

    function extractAnswerFillBlank(dom, blankCount) {
      dom = QDOM.deepCopy(dom.DOMTree.children)

      // 替换图片
      let imgs = []
      let imgIndex = 0
      let replaceImgNode = node => {
        if (node.type == 1 && node.name == 'img') {
          let attributeStr = node.attributes.map(item => ` ${item.name}="${item.value}"`).join()
          let styleStr = node.styles.map(s => `${s.name}: ${s.value}`).join(';')
          if (styleStr) {
            styleStr = ` style="${styleStr}"`
          }
          imgs.push(`<${node.name}${attributeStr}${styleStr}>`)
          node.type = 3
          node.value = `@@replace-img@@${imgIndex++}@@replace-img@@`
        }
        if (node.children && node.children.length > 0) {
          node.children.forEach(replaceImgNode)
        }
      }
      replaceImgNode(dom.DOMTree)

      // 替换上下标
      let subSups = []
      let subSupIndex = 0
      let replaceSubSupNode = node => {
        if (node.type == 1 && (node.name == 'sub' || node.name == 'sup')) {
          subSups.push(QDOM.nodeToString(node))
          node.type = 3
          node.value = `@@replace-sub-sup@@${subSupIndex++}@@replace-sub-sup@@`
          node.children = []
        }
        if (node.children && node.children.length > 0) {
          node.children.forEach(replaceSubSupNode)
        }
      }
      replaceSubSupNode(dom.DOMTree)

      // 公式中的分隔符会干扰拆分，故需替换公式
      let mathFormulas = []
      let mathFormulaIndex = 0
      let domText = dom.textContent.trim().replace(/(\$[^$]+\$)|(<math.+?\/math>)/g, match => {
        mathFormulas.push(match)
        return `@@replace-formula@@${mathFormulaIndex++}@@replace-formula@@`
      })

      // 用三种分隔符测试，选能拆分出最多项的
      let strs = []
      let strs1 = domText.split(/\s{2,}/g).filter(x => x)
      let strs2 = domText.split(/[,，]/g).filter(x => x)
      let strs3 = domText.split(/[;；]/g).filter(x => x)
      if (strs1.length >= strs2.length && strs1.length >= strs3.length) {
        strs = strs1
      } else if (strs2.length >= strs1.length && strs2.length >= strs3.length) {
        strs = strs2
      } else if (strs3.length >= strs1.length && strs3.length >= strs2.length) {
        strs = strs3
      }

      // 图片还原
      imgIndex = 0
      strs = strs.map(str => {
        str = str.trim()
        str = str.replace(/@@replace-img@@(\d+)@@replace-img@@/g, () => {
          return imgs[imgIndex++] || ''
        })
        return str
      })

      // 上下标还原
      subSupIndex = 0
      strs = strs.map(str => {
        str = str.trim()
        str = str.replace(/@@replace-sub-sup@@(\d+)@@replace-sub-sup@@/g, () => {
          return subSups[subSupIndex++] || ''
        })
        return str
      })

      // 公式还原
      mathFormulaIndex = 0
      strs = strs.map(str => {
        str = str.trim()
        str = str.replace(/@@replace-formula@@(\d+)@@replace-formula@@/g, () => {
          return mathFormulas[mathFormulaIndex++] || ''
        })
        return str
      })

      let answer = []
      for (let i = 0; i < blankCount; i++) {
        answer.push(strs.shift() || '')
      }

      // 若拆出来的答案比指定的个数多，则把多出来的答案附加到最后一个答案后面
      if (strs.length) {
        answer[answer.length - 1] += '&nbsp;&nbsp;&nbsp;&nbsp;' + strs.join('&nbsp;&nbsp;&nbsp;&nbsp;')
      }

      return answer
    }

    function extractAnswerTrueOrFalse(dom) {
      let domStr = dom.textContent.trim()
      if (domStr === 'T' || domStr === '√') {
        return 'T'
      } else if (domStr === 'F' || domStr === '×') {
        return 'F'
      } else {
        return ''
      }
    }
  }
}
