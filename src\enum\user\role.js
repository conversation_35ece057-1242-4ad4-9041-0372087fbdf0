/**
 * 用户角色
 */

import Enum from '@/enum/enum'

const Role = new Enum({
  SchoolAdministrator: {
    id: 1,
    name: '学校管理员',
  },
  SchoolLeader: {
    id: 2,
    name: '学校领导',
  },
  GradeLeader: {
    id: 4,
    name: '年级主任',
  },
  SubjectLeader: {
    id: 5,
    name: '学科组长',
  },
  GradeSubjectLeader: {
    id: 6,
    name: '备课组长',
  },
  ClassLeader: {
    id: 7,
    name: '班主任',
  },
  Teacher: {
    id: 3,
    name: '科任老师',
  },
  Parent: {
    id: 8,
    name: '家长',
  },
  Student: {
    id: 9,
    name: '学生',
  },
  ResearcherLeader: {
    id: 50,
    name: '教研领导',
  },
  Researcher: {
    id: 51,
    name: '教研员',
  },
  QuestionLibraryAdministrator: {
    id: 101,
    name: '题库管理员',
  },
  QuestionLibraryEditor: {
    id: 102,
    name: '题库编辑',
  },
  QuestionLibraryKnowledgeEditor: {
    id: 104,
    name: '题库知识点编辑',
  },
  Agent: {
    id: 190,
    name: '代理商',
  },
  SystemAdministrator: {
    id: 200,
    name: '系统管理员',
  },
})

export default Role

// 校内职务
export const SchoolPositions = Role.subEnum([
  'SchoolLeader',
  'GradeLeader',
  'SubjectLeader',
  'GradeSubjectLeader',
  'ClassLeader',
])

// 教育局职务
export const EduSchoolPositions = Role.subEnum(['ResearcherLeader', 'Researcher'])

// 报表角色
export const ReportRoles = [
  {
    ...Role.getEntryByKey('ResearcherLeader'),
    levelName: 'union',
    eduSchoolLevel: 0,
    default: true,
  },
  {
    ...Role.getEntryByKey('Researcher'),
    levelName: 'union',
    eduSchoolLevel: 0,
    default: true,
  },
  {
    ...Role.getEntryByKey('ResearcherLeader'),
    levelName: 'union',
    eduSchoolLevel: 1,
    default: false,
  },
  {
    ...Role.getEntryByKey('Researcher'),
    levelName: 'union',
    eduSchoolLevel: 1,
    default: false,
  },
  {
    ...Role.getEntryByKey('ResearcherLeader'),
    levelName: 'union',
    eduSchoolLevel: 2,
    default: false,
  },
  {
    ...Role.getEntryByKey('Researcher'),
    levelName: 'union',
    eduSchoolLevel: 2,
    default: false,
  },
  {
    ...Role.getEntryByKey('SchoolAdministrator'),
    levelName: 'sch',
    eduSchoolLevel: 0,
    default: true,
  },
  {
    ...Role.getEntryByKey('SchoolLeader'),
    levelName: 'sch',
    eduSchoolLevel: 0,
    default: true,
  },
  {
    ...Role.getEntryByKey('GradeLeader'),
    levelName: 'sch',
    eduSchoolLevel: 0,
    default: true,
  },
  {
    ...Role.getEntryByKey('SubjectLeader'),
    levelName: 'sch',
    eduSchoolLevel: 0,
    default: true,
  },
  {
    ...Role.getEntryByKey('GradeSubjectLeader'),
    levelName: 'sch',
    eduSchoolLevel: 0,
    default: true,
  },
  {
    ...Role.getEntryByKey('ClassLeader'),
    levelName: 'cls',
    eduSchoolLevel: 0,
    default: true,
  },
  {
    ...Role.getEntryByKey('Teacher'),
    levelName: 'cls',
    eduSchoolLevel: 0,
    default: true,
  },
]
