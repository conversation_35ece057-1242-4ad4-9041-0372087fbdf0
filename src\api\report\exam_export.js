import ajax from '@/api/ajax'

// 获取 学生原卷 和 作答情况 (答卷查询)
export function apiGetExamPaperPicAndMarkSituation(params) {
  return ajax.get({
    url: 'report/examexport/stuPaperAndMark',
    params: {
      studentId: params.studentId,
      examSubjectId: params.examSubjectId,
      templateId: params.templateId,
      reportName: params.reportName,
    },
    requestName: '获取答卷查询信息',
  })
}

// 获取整个班级评卷批注
export function apiGetClassTeacherComments(params) {
  return ajax.get({
    url: 'report/examexport/classTeacherComments',
    params: {
      examSubjectId: params.examSubjectId,
      classId: params.classId,
    },
  })
}
