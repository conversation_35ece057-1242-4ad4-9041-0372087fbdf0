<template>
  <div class="ingestion-step-structure">
    <ImageContainer v-model:images="displayImages" :enable-select="true" class="image-container"></ImageContainer>
    <div class="structure-panel">
      <div class="actions">
        <TextButton type="primary" icon="md-add" @click="handleBtnAddStructureClick">添加</TextButton>
        <TextButton
          type="primary"
          icon="md-add"
          :disabled="!existsToc"
          :title="!existsToc ? '请先添加目录' : ''"
          @click="handleBtnRecognizeTocClick"
          >识别目录</TextButton
        >
      </div>
      <div class="structure-tree">
        <Tree :data="structureTree" :render="treeRender"></Tree>
      </div>
    </div>

    <ModalAdd
      v-model="showModalAdd"
      :allow-structure-types="allowStructureTypes"
      :selected-pages="selectedPages"
      @add="handleAddStructure"
    ></ModalAdd>

    <ModalRecognizeToc v-model="showModalRecognizeToc" @add="refreshStructures"></ModalRecognizeToc>
  </div>
</template>

<script setup>
  import { computed, onBeforeMount, ref, watch } from 'vue'
  import iView from '@/iview'

  import ImageContainer from '../../components/image_container.vue'
  import ModalAdd from './modal_add.vue'
  import ModalRecognizeToc from './modal_recognize_toc.vue'

  import { useCoachBookIngestionStore } from '@/store/qlib/coach_book_ingestion'

  import { apiCoachBookIngestionAddStructure } from '@/api/qlib/coach_book_ingestion'

  import StructureTypeEnum from '@/enum/qlib/ingestion/structure_type'

  const ingestionStore = useCoachBookIngestionStore()

  const structureTree = ref([])
  const displayImages = ref([])
  const showModalAdd = ref(false)
  const showModalRecognizeToc = ref(false)

  const selectedPages = computed(() => {
    return displayImages.value.filter(x => x.selected)
  })

  const allowStructureTypes = computed(() => {
    return StructureTypeEnum.getEntries()
  })

  const existsToc = computed(() => {
    return ingestionStore.structures.some(x => x.structureType == StructureTypeEnum.Toc.id)
  })

  watch(() => ingestionStore.ingestionInfo?.pageImages, setDisplayImages, { immediate: true })
  watch(() => ingestionStore.structures, buildStructureTree, { immediate: true })

  function setDisplayImages() {
    let pageImages = ingestionStore.ingestionInfo?.pageImages
    if (!pageImages) {
      return []
    }
    displayImages.value = pageImages.map((x, idx) => ({
      ...x,
      index: idx,
      selected: false,
    }))
  }

  // 构建结构树
  function buildStructureTree() {
    let structures = ingestionStore.structures

    let tree = []
    structures.forEach(s => {
      s.expand = true
      s.selected = false
      s.checked = false
      s.children = []

      let parent = null
      if (s.structureParentId) {
        parent = structures.find(x => x.id == s.structureParentId)
      }
      if (parent) {
        s.parent = parent
        parent.children = parent.children || []
        parent.children.push(s)
      } else {
        s.parent = null
        tree.push(s)
      }
    })

    structureTree.value = tree
  }

  function treeRender(h, { root, node, data }) {
    let icon = h(iView.Icon, {
      type: data.children.length > 0 ? 'ios-folder-outline' : 'ios-document-outline',
      style: {
        marginRight: '5px',
      },
    })
    let title = h('span', {}, data.structureName)
    let pageRange = h(
      'span',
      {
        style: {
          marginLeft: 'auto',
        },
      },
      `（${data.pageIndexes[0] + 1} - ${data.pageIndexes[data.pageIndexes.length - 1] + 1}）`
    )
    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
        },
      },
      [icon, title, pageRange]
    )
  }

  onBeforeMount(async () => {
    await ingestionStore.refreshStructures()
  })

  function handleBtnAddStructureClick() {
    if (selectedPages.value.length == 0) {
      iView.Message.warning('请先选择页面')
      return
    }
    showModalAdd.value = true
  }

  async function handleAddStructure(structure) {
    try {
      await apiCoachBookIngestionAddStructure({
        ingestionId: ingestionStore.ingestionInfo.id,
        structures: [structure],
      })
      displayImages.value.forEach(x => {
        x.selected = false
      })
      iView.Message.success('已添加')
    } finally {
      refreshStructures()
      showModalAdd.value = false
    }
  }

  function refreshStructures() {
    ingestionStore.refreshStructures()
  }

  function handleBtnRecognizeTocClick() {
    if (!existsToc.value) {
      iView.Message.warning('请先添加目录')
      return
    }
    showModalRecognizeToc.value = true
  }
</script>

<style lang="scss" scoped>
  .ingestion-step-structure {
    @include flex(row, flex-start, stretch);
  }

  .image-container {
    flex-grow: 1;
    flex-shrink: 1;
  }

  .structure-panel {
    flex-grow: 0;
    flex-shrink: 0;
    width: 400px;
    border-left: 1px solid $color-border;
  }
</style>
