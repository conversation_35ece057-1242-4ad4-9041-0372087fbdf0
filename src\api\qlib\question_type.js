import ajax from '@/api/ajax'

export function apiListQuestionTypes({ stageId, subjectId }) {
  return ajax.get({
    url: 'ques/questionType/bySidAndGid',
    params: {
      stage: stageId,
      subject: subjectId,
    },
    requestName: '查题型',
  })
}

export function apiAddQuestionType(params) {
  let sendData = buildSendQuestionTypeData(params)
  return ajax.post({
    url: 'ques/questionType/add',
    data: sendData,
    requestName: '增加题型',
  })
}

export function apiChangeQuestionType(params) {
  let sendData = buildSendQuestionTypeData(params)
  return ajax.put({
    url: 'ques/questionType/update',
    data: sendData,
    requestName: '修改题型',
  })
}

function buildSendQuestionTypeData({
  id,
  name,
  stageId,
  subjectId,
  branchTypeId,
  groupName,
  branchHasStem,
  branchHasOptions,
  order,
}) {
  let sendData = {
    id,
    questionTypeName: name,
    gradeLevel: stageId,
    subjectId,
    categoryId: branchTypeId,
    groupName,
    extra: null,
    orderId: order,
    status: 1,
  }
  if (subjectId == 3) {
    sendData.extra = JSON.stringify({
      branchHasStem,
      branchHasOptions,
    })
  }
  return sendData
}

export function apiDeleteQuestionType({ stageId, subjectId, questionTypeId }) {
  return ajax.delete({
    url: `ques/questionType/delete`,
    params: {
      gradeLevel: stageId,
      subjectId: subjectId,
      id: questionTypeId,
    },
    requestName: '删除题型',
  })
}
