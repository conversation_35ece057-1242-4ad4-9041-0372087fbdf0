# 树结构算法工具库

这是一个全面的JavaScript树结构算法工具库，提供了丰富的树操作功能。

## 功能特性

### 🔍 遍历算法
- **深度优先遍历 (DFS)** - `traverse(tree, callback, getChildren)`
- **广度优先遍历 (BFS)** - `traverseBFS(tree, callback, getChildren)`

### 🎯 查找算法
- **查找单个节点** - `findNode(tree, predicate, getChildren)`
- **查找所有匹配节点** - `findAllNodes(tree, predicate, getChildren)`
- **获取节点路径** - `getNodePath(tree, predicate, getChildren)`
- **根据路径获取节点** - `getNodeByPath(tree, path, getChildren, getId)`

### 📏 深度和结构分析
- **获取最大深度** - `getMaxDepth(tree, getChildren)`
- **获取节点深度** - `getNodeDepth(tree, predicate, getChildren)`
- **计算节点总数** - `countNodes(tree, getChildren)`
- **验证树结构** - `validateTree(tree, getChildren, getId)`

### 🔄 树转换操作
- **过滤树节点** - `filterTree(tree, predicate, getChildren)`
- **映射树节点** - `mapTree(tree, mapper, getChildren)`
- **扁平化树结构** - `flattenTree(tree, getChildren)`
- **从扁平数组构建树** - `buildTreeFromFlat(flatArray, idKey, parentIdKey, childrenKey)`

### 🌿 节点关系操作
- **获取叶子节点** - `getLeafNodes(tree, getChildren)`
- **获取父节点** - `getParentNode(tree, predicate, getChildren)`
- **获取兄弟节点** - `getSiblingNodes(tree, predicate, getChildren)`
- **获取所有路径** - `getAllPaths(tree, getChildren)`

### 🛠️ 实用工具
- **排序树节点** - `sortTree(tree, compareFn, getChildren)`
- **深度克隆树** - `cloneTree(tree, getChildren)`
- **比较树是否相等** - `isTreeEqual(tree1, tree2, getChildren, compareFn)`

## 使用示例

### 基础用法

```javascript
import { traverse, findNode, getMaxDepth } from './src/utils/tree.js'

const tree = [
  {
    id: 1,
    name: 'Root',
    children: [
      { id: 2, name: 'Child 1', children: [] },
      { id: 3, name: 'Child 2', children: [] }
    ]
  }
]

// 遍历所有节点
traverse(tree, node => console.log(node.name))

// 查找特定节点
const node = findNode(tree, n => n.id === 2)

// 获取树的深度
const depth = getMaxDepth(tree)
```

### 高级用法

```javascript
// 过滤树（只保留包含特定文本的节点）
const filteredTree = filterTree(tree, node => node.name.includes('Child'))

// 从扁平数组构建树
const flatData = [
  { id: 1, name: 'Root', parentId: null },
  { id: 2, name: 'Child', parentId: 1 }
]
const builtTree = buildTreeFromFlat(flatData)

// 获取所有叶子节点
const leaves = getLeafNodes(tree)

// 验证树结构（检查循环引用）
const isValid = validateTree(tree)
```

### 自定义子节点获取函数

所有函数都支持自定义的子节点获取函数：

```javascript
// 对于使用不同字段名的树结构
const customTree = [
  {
    id: 1,
    title: 'Root',
    items: [  // 使用 'items' 而不是 'children'
      { id: 2, title: 'Child', items: [] }
    ]
  }
]

// 自定义获取子节点的函数
const getItems = node => node.items || []

traverse(customTree, node => console.log(node.title), getItems)
```

## 测试和演示

### 运行测试
```bash
# 在Node.js环境中运行测试
node src/utils/tree.test.js
```

### 浏览器演示
打开 `tree-demo.html` 文件在浏览器中查看交互式演示。

## API 参考

### 通用参数说明

- `tree`: 树节点对象或树节点数组
- `callback`: 对每个节点执行的回调函数
- `predicate`: 用于匹配节点的条件函数
- `getChildren`: 获取子节点的函数，默认为 `node => (node && node.children) || []`
- `getId`: 获取节点ID的函数，默认为 `node => node.id`

### 返回值类型

- 查找函数返回节点对象或 `null`
- 遍历函数无返回值
- 转换函数返回新的树结构
- 验证函数返回布尔值

## 性能考虑

- 所有算法都经过优化，适合处理中等规模的树结构
- 对于大型树结构（>10000节点），建议使用分页或虚拟化技术
- 深度克隆和比较操作相对耗时，请谨慎使用

## 兼容性

- 支持现代浏览器（ES6+）
- 支持Node.js环境
- 无外部依赖

## 贡献

欢迎提交问题和改进建议！
