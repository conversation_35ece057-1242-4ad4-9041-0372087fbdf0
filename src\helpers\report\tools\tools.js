import { roundNumber } from '@/utils/math'

import { UUID_ZERO } from '@/const/string'

import Store from '@/store/index'

// 获取机构（学校）名
export function getInstitutionName(schoolId, classId, schoolName, className) {
  const ExamSchools = Store.getters['report/examSchools'] || []

  if ((schoolId === UUID_ZERO || !schoolId) && (classId === UUID_ZERO || !classId)) {
    return '联考全体'
  } else if (classId === UUID_ZERO || !classId) {
    let name =
      (
        (Store.getters['report/tiledInstitutions'] || []).find(
          institution => institution.institutionId === schoolId
        ) || { institutionName: '' }
      ).institutionName ||
      schoolName ||
      (
        ExamSchools.find(school => school.schoolId === schoolId) || {
          schoolName: '',
        }
      ).schoolName ||
      '-'

    return name
  } else {
    if (className) {
      return className
    }
    let operSchool = ExamSchools.find(school => school.schoolId === schoolId)
    if (operSchool) {
      return (operSchool.classes.find(cls => cls.classId === classId) || { className: '-' }).className
    } else {
      return '-'
    }
  }
}

// 等级结构分析 score ranges
export function getScoreRanges() {
  let template = Store.getters['report/template']

  return (
    template &&
    template.parameters &&
    template.parameters.scoreLevels &&
    template.parameters.scoreLevels.map(x => ({
      subjectId: x.subjectId,
      subjectName: x.subjectName,
      statsWay: x.statsWay,
      range: x.items.map(y => {
        let leftValue = 0
        let rightValue = 0

        if ([2, 4].includes(x.statsWay)) {
          leftValue = roundNumber(y.minValue * 100, 2) + '%'
          rightValue = roundNumber(y.maxValue * 100, 2) + '%'
        } else {
          leftValue = roundNumber(y.minValue, 2)
          rightValue = roundNumber(y.maxValue, 2)
        }

        return {
          value: (y.minValueIncluded ? '[' : '(') + leftValue + ', ' + rightValue + (y.maxValueIncluded ? ']' : ')'),
          name: y.name,
        }
      }),
    }))
  )
}
