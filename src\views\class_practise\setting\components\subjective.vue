<template>
  <div class="tab-subjective">
    <div class="subjective-description" style="margin-bottom: 10px">
      主观题满分<span class="span-color-primary">{{ subjectivesTotalScore }}</span
      >分
      <TextButton
        v-if="enableChangeScore"
        type="primary"
        style="margin-left: 10px"
        @click="modalSubjectivesShowed = true"
        >修改分数</TextButton
      >
    </div>

    <Table :data="subjectives" :columns="subjectivesTableColumns" :span-method="handleSubjectivesSpan" border></Table>

    <ModalSubjectives
      v-model="modalSubjectivesShowed"
      :subjectives="subjectives"
      :enable-change-score="enableChangeScore"
      @change-subjectives="updateSubjectives"
    ></ModalSubjectives>
  </div>
</template>

<script>
  import ModalSubjectives from '../../components/modal_subjectives'

  import { apiSaveTestSubjectiveQuestions } from '@/api/emarking'

  import { numberToChinese } from '@/utils/number'

  export default {
    components: {
      ModalSubjectives,
    },
    props: {
      examId: String,
      examSubjectId: String,
      subjectives: Array,
      subjectivesTotalScore: Number,
      enableChangeScore: Boolean,
    },
    emits: ['update-subjectives'],
    data() {
      return {
        modalSubjectivesShowed: false,
      }
    },
    computed: {
      subjectivesTableColumns() {
        let columns = [
          {
            title: '大题',
            key: 'topicName',
            align: 'center',
            render: (h, params) => h('span', {}, numberToChinese(params.row.topicCode)),
          },
          {
            title: '题号',
            key: 'questionCode',
            align: 'center',
          },
          {
            title: '题名',
            key: 'questionName',
            align: 'center',
          },
          {
            title: '小题名',
            key: 'branchName',
            align: 'center',
          },
          {
            title: '分数',
            key: 'fullScore',
            align: 'center',
          },
        ]
        return columns
      },
    },
    methods: {
      handleSubjectivesSpan({ row, rowIndex, columnIndex }) {
        let index = this.subjectives.findIndex(x => x.topicCode === row.topicCode)
        let count = this.subjectives.filter(x => x.topicCode === row.topicCode).length
        if (index === -1) {
          return [1, 1]
        } else {
          if (!columnIndex && rowIndex === index) {
            return [count, 1]
          } else if (!columnIndex && rowIndex > index) {
            return [0, 0]
          }
        }
      },
      updateSubjectives(newSubjectives) {
        apiSaveTestSubjectiveQuestions({
          examId: this.examId,
          examSubjectId: this.examSubjectId,
          subjectives: newSubjectives.map(x => x.export()),
        })
          .then(() => {
            this.$Message.success({
              duration: 3,
              content: '设置成功',
            })
          })
          .finally(() => {
            this.$emit('update-subjectives')
          })
      },
    },
  }
</script>
