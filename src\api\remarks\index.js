import ajax from '@/api/ajax'

// export function apiGetTheTeachingInfo() {
//   return ajax
//     .get({
//       url: 'homework/teaapp/main/getTeaBaseInfo/list',
//       params: {},
//       requestName: '获取任教老师任课信息',
//     })
//     .then(data => {
//       return data
//     })
// }

export function apiGetRemarkList(params) {
  return ajax
    .get({
      url: 'mark/teacherComment/getCommentList',
      params: params,
      requestName: '获取寄语列表',
    })
    .then(data => {
      return data
    })
}

export function apiGetStudents(params) {
  return ajax
    .get({
      url: 'mark/teacherComment/listStudent',
      params: params,
      requestName: '获取班级学生列表',
    })
    .then(data => {
      return data
    })
}

export function apiCreateComments(params) {
  return ajax
    .post({
      url: 'mark/teacherComment/createComment',
      params: params,
      requestName: '创建寄语',
    })
    .then(data => {
      return data
    })
}

export function apiGetComments(params) {
  return ajax
    .get({
      url: 'mark/teacherComment/getComment',
      params: params,
      requestName: '获取寄语',
    })
    .then(data => {
      return data
    })
}

export function apiGetCommentDetail(params) {
  return ajax
    .get({
      url: 'mark/teacherComment/getCommentDetail',
      params: params,
      requestName: '获取学生寄语详情',
    })
    .then(data => {
      return data
    })
}

export function apiGetCommentLibList(params) {
  return ajax
    .get({
      url: 'mark/teacherComment/getCommentLibList',
      params: params,
      requestName: '获取学生寄语模板列表',
    })
    .then(data => {
      return data
    })
}

export function apiFavourComment(params) {
  return ajax
    .post({
      url: 'mark/teacherComment/copyComment',
      params: params,
      requestName: '收藏寄语',
    })
    .then(data => {
      return data
    })
}

export function apiDeleteCommentLib(params) {
  return ajax
    .delete({
      url: 'mark/teacherComment/deleteCommentLib',
      params: params,
      requestName: '删除寄语模板',
    })
    .then(data => {
      return data
    })
}

export function apiCreateCommentLib(params) {
  return ajax
    .post({
      url: 'mark/teacherComment/createCommentLib',
      params: params,
      requestName: '创建寄语模板',
    })
    .then(data => {
      return data
    })
}

export function apiUpdateCommentLib(params) {
  return ajax
    .post({
      url: 'mark/teacherComment/updateCommentLib',
      params: params,
      requestName: '更新寄语模板',
    })
    .then(data => {
      return data
    })
}

export function apiDeleteTheComment(params) {
  return ajax
    .delete({
      url: 'mark/teacherComment/deleteComment',
      params: params,
      requestName: '删除寄语',
    })
    .then(data => {
      return data
    })
}

export function apiUpdateCommentDetails(params) {
  return ajax
    .post({
      url: 'mark/teacherComment/createCommentDetail',
      data: {
        content: params.content,
        commentId: params.commentId,
        studentId: params.studentId,
      },
      requestName: '更新寄语详情',
    })
    .then(data => {
      return data
    })
}

export function apiPublishComment(params) {
  return ajax
    .post({
      url: 'mark/teacherComment/publishComment',
      params: params,
    })
    .then(data => {
      return data
    })
}

export function apiUnPublishComment(params) {
  return ajax
    .post({
      url: 'mark/teacherComment/unPublishComment',
      params: params,
    })
    .then(data => {
      return data
    })
}

export function apiSendComment(params) {
  return ajax
    .post({
      url: 'mark/teacherComment/sendComment',
      params: params,
    })
    .then(data => {
      return data
    })
}

export function apiUpdateComment(params) {
  return ajax
    .post({
      url: 'mark/teacherComment/updateComment',
      params: params,
      requestName: '修改寄语名称',
    })
    .then(data => {
      return data
    })
}

// export function apiUploadFile(params) {
//   return ajax
//     .upload({
//       url: 'homework/teaapp/addNewUploadToAliyun',
//       data: params,
//       requestName: '上传文件',
//     })
//     .then(data => {
//       return data
//     })
// }

// export function apiDeleteFile(params) {
//   return ajax
//     .delete({
//       url: 'homework/teaapp/deleteHomeworkSource/delete',

//       data: params,
//       requestName: '删除文件',
//     })
//     .then(data => {
//       return data
//     })
// }
