<template>
  <div class="container-setting-new-test">
    <div class="header">
      <div v-if="test || !loading" class="nav">
        <Button type="info" icon="md-arrow-back" @click="handleBtnBackToEmarkingHome">返回</Button>
        <span class="test-name">{{ (test && test.examName) || '' }}</span>
      </div>
      <div v-if="test" class="action">
        <TextButton v-if="canEditExam" type="warning" icon="md-create" @click="showModalEditInfo = true"
          >修改名称</TextButton
        >
        <TextButton type="error" icon="md-trash" @click="handleTBtnDeleteTestDetailClick">删除项目</TextButton>
      </div>
    </div>

    <div v-if="test" class="main">
      <TestInfo class="section section-info" :test="test" :total-full-score="totalFullScore"></TestInfo>

      <Tabs class="section section-question" :animated="false">
        <TabPane label="客观题">
          <TabObjective
            :exam-id="test.examId"
            :exam-subject-id="test.examSubjectId"
            :objectives="objectives"
            :objectives-total-score="objectivesTotalScore"
            @update-objectives="fetchObjectives"
          ></TabObjective>
        </TabPane>
        <TabPane label="主观题">
          <TabSubjective
            :exam-id="test.examId"
            :exam-subject-id="test.examSubjectId"
            :subjectives="subjectives"
            :subjectives-total-score="subjectivesTotalScore"
            :enable-change-score="test.needMark"
            @update-subjectives="fetchSubjectives"
          ></TabSubjective>
        </TabPane>
        <TabPane v-if="test.needMark" label="评卷题块">
          <TabBlock
            :exam-id="examId"
            :exam-subject-id="examSubjectId"
            :subject-status="subjectStatus"
            :blocks="blocks"
            :subjectives="subjectives"
            :can-edit-exam="canEditExam"
            :enable-ai-score="enableAiScore"
            @refresh-blocks="fetchBlocks"
            @refresh-subject="refreshSubject"
          ></TabBlock>
        </TabPane>
      </Tabs>

      <div class="section section-class">
        <div class="tabs-actions">
          <Tabs v-model="activeClassId" class="tabs" :animated="false">
            <TabPane v-for="cls in classList" :key="cls.classId" :name="cls.classId" :label="cls.className"></TabPane>
          </Tabs>
          <span v-if="subjectUnfinish" class="actions">
            <TextButton type="primary" @click="syncStudents">同步考生</TextButton>
            <TextButton v-if="test.needMark" type="primary" @click="syncTeachers">同步评卷老师</TextButton>
          </span>
        </div>

        <TabClass v-if="activeClass" :cls="activeClass" :need-mark="test.needMark"></TabClass>
      </div>
    </div>

    <ModalEditInfo v-model="showModalEditInfo" :exam="test" @on-refresh="fetchTestDetailAndQuestions" />
  </div>
</template>

<script>
  import TestInfo from './components/info.vue'
  import TabObjective from './components/objective.vue'
  import TabSubjective from './components/subjective.vue'
  import TabBlock from './components/block.vue'
  import TabClass from './components/class.vue'
  import ModalEditInfo from './components/modal_edit_info.vue'

  import {
    apiDeleteExam,
    apiGetObjectiveQuestions,
    apiGetSubjectiveQuestions,
    apiGetBlocks,
    apiGetExamStudents,
    apiGetMarkersByClass,
  } from '@/api/emarking'
  import { apiGetThePractise, apiSyncStudents, apiSyncTeachers } from '@/api/emarking/practise'

  import SubjectStatusEnum from '@/enum/emarking/subject_status'

  export default {
    components: {
      TestInfo,
      TabObjective,
      TabSubjective,
      TabBlock,
      TabClass,
      ModalEditInfo,
    },

    data() {
      return {
        // 加载中
        loading: false,
        // 测验信息
        test: null,
        // 客观题
        objectives: [],
        // 主观题
        subjectives: [],
        // 题块
        blocks: [],
        // 当前班级
        activeClassId: '',
        // 各班考生、评卷员
        classList: [],
        // 修改名称弹框
        showModalEditInfo: false,
      }
    },

    computed: {
      isSchoolAdministrator() {
        return this.$store.getters['user/isSchoolAdministrator']
      },
      userInfo() {
        return this.$store.getters['user/info']
      },
      isExamCreator() {
        return this.userInfo.userId === this.test.creatorId
      },
      canEditExam() {
        return this.isExamCreator || this.isSchoolAdministrator
      },
      examId() {
        return this.$route.params.examId
      },
      examSubjectId() {
        return this.$route.params.examSubjectId
      },
      subjectStatus() {
        return this.test?.subjectStatus
      },
      objectivesTotalScore() {
        return this.objectives && this.objectives.length
          ? this.objectives.reduce((acc, cur) => acc + cur.fullScore, 0)
          : 0
      },
      subjectivesTotalScore() {
        return this.subjectives && this.subjectives.length
          ? this.subjectives.reduce((acc, cur) => acc + cur.fullScore, 0)
          : 0
      },
      totalFullScore() {
        return this.objectivesTotalScore + this.subjectivesTotalScore
      },
      activeClass() {
        return this.classList.find(x => x.classId == this.activeClassId)
      },
      subjectUnfinish() {
        return this.test.subjectStatus != SubjectStatusEnum.ReportSucceeded.id
      },
      enableAiScore() {
        return [
          '4e606b03-a014-4df7-a7af-db2ffdd02ab7',
          '93a37d47-049f-4102-aa7e-b4d8a93a018c',
          '4dac4f93-50b6-43d9-8109-51c4cc40748f',
        ].includes(this.userInfo.schoolId)
      },
    },

    created() {
      this.refreshSubject()
    },

    methods: {
      /**
       * 加载数据
       */
      fetchTestDetailAndQuestions() {
        this.loading = true
        return Promise.all([
          apiGetThePractise({
            examId: this.examId,
          }),
          this.fetchObjectives(),
          this.fetchSubjectives(),
          this.fetchBlocks(),
        ])
          .then(([test]) => {
            this.test = test
            this.activeClassId = this.test.classList[0].classId
            this.classList = this.test.classList.map(cls => ({
              ...cls,
              students: [],
              markers: [],
            }))
          })
          .finally(() => {
            this.loading = false
          })
      },
      fetchObjectives() {
        return apiGetObjectiveQuestions({
          examId: this.examId,
          examSubjectId: this.examSubjectId,
        }).then(response => {
          this.objectives = response
        })
      },
      fetchSubjectives() {
        return apiGetSubjectiveQuestions({
          examId: this.examId,
          examSubjectId: this.examSubjectId,
        }).then(response => {
          this.subjectives = response
        })
      },
      fetchBlocks() {
        return apiGetBlocks({
          examId: this.examId,
          examSubjectId: this.examSubjectId,
        }).then(response => {
          this.blocks = response
        })
      },
      fetchStudents() {
        return apiGetExamStudents({
          examId: this.examId,
          examSubjectId: this.examSubjectId,
          pageSize: 10000,
          currentPage: 1,
        })
          .then(data => {
            this.classList.forEach(cls => {
              cls.students = []
            })
            data.students.forEach(stu => {
              let cls = this.classList.find(x => x.classId == stu.classId)
              if (cls) {
                cls.students.push(stu)
              }
            })
          })
          .catch(err => {
            this.classList.forEach(cls => {
              cls.students = []
            })
            throw err
          })
      },
      fetchMarkers() {
        apiGetMarkersByClass(this.examSubjectId).then(map => {
          this.classList.forEach(cls => {
            cls.markers = map[cls.classId] || []
          })
        })
      },
      refreshSubject() {
        this.fetchTestDetailAndQuestions().then(() => {
          this.fetchStudents()
          this.fetchMarkers()
        })
      },

      /**
       * 同步
       */
      syncStudents() {
        this.$Modal.confirm({
          title: '同步考生',
          content:
            '本操作将：<br>1. 同步学生的姓名、性别、学籍号、班级、准考号到测练项目；<br>2. 添加班级中存在，测练中不存在的学生；<br>3. 删除班级中不存在的学生（已扫描的除外）',
          onOk: () => {
            apiSyncStudents(this.examSubjectId)
              .then(data => {
                setTimeout(() => {
                  this.$Modal.success({
                    title: '同步结果',
                    content:
                      '<span style="display: inline-block; width: 100%; max-height: 250px; ' +
                      'overflow-y: auto; margin-top: 10px;">' +
                      data.replace(/\n/g, '<br>') +
                      '</span>',
                  })
                }, 500)
              })
              .catch(err => {
                setTimeout(() => {
                  this.$Modal.error(
                    {
                      title: '同步考生失败',
                      content: (err && err.msg) || '',
                    },
                    500
                  )
                })
                throw err
              })
              .finally(() => {
                this.fetchStudents()
              })
          },
        })
      },
      syncTeachers() {
        this.$Modal.confirm({
          title: '同步评卷员',
          content: '本操作将添加科目任课老师为评卷员，并更新各评卷员的任务量',
          onOk: () => {
            apiSyncTeachers(this.examSubjectId)
              .then(data => {
                setTimeout(() => {
                  this.$Modal.success({
                    title: '同步结果',
                    content:
                      '<span style="display: inline-block; width: 100%; max-height: 250px; ' +
                      'overflow-y: auto; margin-top: 10px;">' +
                      data.replace(/\n/g, '<br>') +
                      '</span>',
                  })
                }, 500)
              })
              .catch(err => {
                setTimeout(() => {
                  this.$Modal.error(
                    {
                      title: '同步评卷员失败',
                      content: (err && err.msg) || '',
                    },
                    500
                  )
                })
                throw err
              })
              .finally(() => {
                this.fetchMarkers()
              })
          },
        })
      },

      /**
       * 其他操作
       */
      handleTBtnDeleteTestDetailClick() {
        this.$Modal.confirm({
          title: '注意',
          content: '确定要删除班级测练项目 ' + this.test.examName + ' ？',
          onOk: () => {
            apiDeleteExam(this.test.examId).then(() => {
              this.$Message.success({
                duration: 2,
                content: '已删除',
              })
              this.handleBtnBackToEmarkingHome()
            })
          },
        })
      },
      handleBtnBackToEmarkingHome() {
        this.$router.back()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-setting-new-test {
    position: relative;

    .header {
      @include flex(row, space-between, center);
      height: 32px;
      margin-top: 10px;
      margin-bottom: 20px;
      line-height: 32px;

      .nav {
        flex-grow: 1;
        flex-shrink: 1;

        .test-name {
          margin-left: 30px;
          font-weight: bold;
          font-size: $font-size-medium-x;
        }
      }
    }

    .section {
      padding: 20px;
      background-color: white;

      &:not(:first-child) {
        margin-top: 10px;
      }

      &.section-question,
      &.section-class {
        padding-top: 10px;

        .tabs-actions {
          @include flex(row, flex-start, flex-start);

          .tabs {
            flex-grow: 1;
          }

          .actions {
            flex-shrink: 0;
            padding: 7px 0 7px 30px;
            border-bottom: 1px solid $color-border;
          }
        }
      }
    }
  }
</style>
