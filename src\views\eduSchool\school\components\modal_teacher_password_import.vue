<template>
  <Modal
    :model-value="modelValue"
    class="modal-import-student"
    title="导入教师密码"
    width="730"
    footer-hide
    @on-visible-change="handleVisibilityChange"
  >
    <div class="section-instructions">
      <Alert type="info">
        <ol class="tips">
          <li>要导入重置密码的教师，其姓名、手机号码和学校名称必须与系统内的一致，否则导入失败</li>
          <li>“默认密码”将重置为手机号码尾6位</li>
        </ol>
      </Alert>
      <div class="template">
        <Button class="btn-download-template" type="default" @click="downloadImportExcelTemplate">
          <Icon type="md-download" size="30"></Icon>
          <br />
          <span>Excel 模板</span>
        </Button>
      </div>
    </div>
    <div class="section-file">
      <com-upload
        class="com-upload"
        :accepts="['.xlsx', '.xls']"
        :file-name="importFile && importFile.name"
        @on-selected="handleImportFileSelected"
      ></com-upload>
      <Button v-if="importFile" class="btn-import" type="primary" :loading="uploadingFile" @click="handleImportOK">{{
        uploadingFile ? '正在导入' : '导入'
      }}</Button>
    </div>
    <div v-if="showModalImportErrorAlert" class="section-result">
      <div class="status">
        <div v-if="importResult.success" class="status-success">
          <Icon type="md-checkmark-circle"></Icon>
        </div>
        <div v-else class="status-fail">
          <Icon type="md-close-circle"></Icon>
        </div>
      </div>
      <div class="detail" v-html="importResult && importResult.detail"></div>
    </div>
  </Modal>
</template>

<script>
  import ComUpload from '@/components/upload.vue'
  import { apiImportTeachersPassword } from '@/api/user/teacher'
  import { downloadUrl } from '@/utils/download'

  export default {
    components: {
      ComUpload,
    },
    props: {
      modelValue: Boolean,
    },
    emits: ['update:modelValue', 'on-refresh'],
    data() {
      return {
        importFile: null,
        uploadingFile: false,
        importResult: null,
        updateTeacherName: false,
      }
    },
    computed: {
      showModalImportErrorAlert() {
        return !this.uploadingFile && this.importResult
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.importFile = null
          this.uploadingFile = false
          this.importResult = null
        }
      },
    },
    methods: {
      handleCancel() {
        this.$emit('update:modelValue', false)
      },
      handleVisibilityChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
      downloadImportExcelTemplate() {
        let url = ''

        const emarkingBasicDataFiles = this.$store.getters['emarking/files']()
        const tempType = '导入教师密码模板'

        if (emarkingBasicDataFiles && emarkingBasicDataFiles.length) {
          const theTemp = emarkingBasicDataFiles.find(item => item.id === tempType)
          url = theTemp.url
        }

        if (!url) {
          return
        }

        downloadUrl(url, '导入教师密码' + '模板.xlsx')
      },
      handleImportFileSelected(file) {
        this.importFile = file
        this.importResult = null
      },
      async handleImportOK() {
        if (!this.importFile) {
          this.$Message.info({
            content: '未选择文件',
          })
          return
        }

        this.uploadingFile = true

        apiImportTeachersPassword(this.importFile)
          .then(successMessage => {
            this.importResult = {
              success: true,
              detail: successMessage,
            }
          })
          .catch(err => {
            if (err.code === -1 && err.msg === '网络连接超时') {
              this.importResult = {
                success: false,
                detail: err.msg + ' 或 文件在被选择后非法修改，请重新选择文件后进行导入操作',
              }
            } else {
              this.importResult = {
                success: false,
                detail: err.msg,
              }
            }
          })
          .finally(() => {
            this.uploadingFile = false
            // this.$store.dispatch('school/getGradeClassesAndSubjects')
            this.$emit('on-refresh')
          })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .section-instructions {
    @include flex(row, space-between, flex-start);
    margin-bottom: 15px;

    .tips {
      margin-right: 10px;
      margin-left: 10px;
      line-height: 1.5;
    }

    .btn-download-template {
      flex-shrink: 0;
      height: auto;
      margin-left: 20px;
      padding-top: 10px;
      padding-bottom: 10px;
    }

    .checkbox-template-wrapper {
      margin-top: 10px;
      margin-left: 20px;
      text-align: center;

      .checkbox-template {
        margin-right: 0;
      }
    }
  }

  .section-options {
    @include flex(row, space-between, center);
    width: 550px;
    margin-bottom: 10px;
    padding: 10px;
    color: $color-warning;
  }

  .excel-columns {
    width: 580px;
    padding: 10px;
    color: $color-second-title;
    font-size: $font-size-small;
  }

  .section-file {
    @include flex(row, space-between, center);
    margin-bottom: 20px;

    .com-upload {
      flex-grow: 0;
      width: 550px;
    }

    .btn-import {
      flex-grow: 0;
    }
  }

  .section-result {
    margin-bottom: 10px;
    border-top: 1px solid $color-border;

    .status {
      font-size: 40px;
      text-align: center;

      .status-success {
        color: $color-success;
      }

      .status-fail {
        color: $color-error;
      }
    }

    .detail {
      max-height: 160px;
      overflow-y: auto;
      white-space: pre;
    }
  }
</style>
