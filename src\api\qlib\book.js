import ajax from '@/api/ajax'

export function apiListBooks({ stageId, subjectId }) {
  return ajax.get({
    url: 'ques/book/list',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    requestName: '查教材',
  })
}

export function apiListBookCategories({ stageId, subjectId }) {
  return ajax.get({
    url: 'ques/book/listBookCategories',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    requestName: '查教材版本',
  })
}

export function apiAddBook({ stageId, subjectId, gradeId, term, bookName, categoryId, enabled }) {
  return ajax.post({
    url: 'ques/book/add',
    params: {
      gradeLevel: stageId,
      subjectId,
      gradeId,
      term,
      bookName,
      categoryId,
      enabled,
    },
    requestName: '新增教材',
  })
}

export function apiChangeBook({ id, bookName, gradeId, term, orderId, enabled }) {
  return ajax.put({
    url: 'ques/book/update',
    params: {
      id,
      bookName,
      gradeId,
      term,
      orderId,
      enabled,
    },
    requestName: '修改教材',
  })
}

export function apiChangeBookCategory({ stageId, subjectId, categoryId, categoryOrderId }) {
  return ajax.put({
    url: 'ques/book/updateBookCategoryOrder',
    params: {
      gradeLevel: stageId,
      subjectId,
      categoryId,
      categoryOrderId,
    },
    requestName: '修改教材版本',
  })
}

export function apiDeleteBook(id) {
  return ajax.delete({
    url: 'ques/book/delete',
    params: {
      id,
    },
    requestName: '删除教材',
  })
}
