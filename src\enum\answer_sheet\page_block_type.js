/**
 * 页面块类型
 */

import Enum from '@/enum/enum'

export default new Enum({
  ExamInfo: {
    id: 'ExamInfo',
    name: '考试信息块',
  },
  Description: {
    id: 'Description',
    name: '大题说明块',
  },
  Objective: {
    id: 'Objective',
    name: '客观题块',
  },
  FillBlank: {
    id: 'FillBlank',
    name: '填空题块',
  },
  Essay: {
    id: 'Essay',
    name: '解答题块',
  },
  ChineseWriting: {
    id: 'ChineseWriting',
    name: '语文作文块',
  },
  EnglishWriting: {
    id: 'EnglishWriting',
    name: '英语作文块',
  },
  Trunk: {
    id: 'Trunk',
    name: '材料块',
  },
  ContentObjective: {
    id: 'ContentObjective',
    name: '带题干客观题块',
  },
})
