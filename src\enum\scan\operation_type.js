import Enum from '@/enum/enum'

export default new Enum({
  Delete: {
    id: 'D',
    name: '删除',
  },
  Recover: {
    id: 'R',
    name: '恢复',
  },
  IgnoreSubject: {
    id: 'ISU',
    name: '忽略科目异常',
  },
  IgnoreCorner: {
    id: 'ICO',
    name: '忽略折角异常',
  },
  ChangeAdmissionNum: {
    id: 'CAD',
    name: '修改准考号',
  },
  VerifyAbsent: {
    id: 'VAB',
    name: '确认缺考',
  },
  ChangeAbsent: {
    id: 'CAB',
    name: '修改缺考',
  },
  VerifyObjective: {
    id: 'VOB',
    name: '忽略客观异常',
  },
  ChangeObjective: {
    id: 'COB',
    name: '修改客观题',
  },
  VerifySubjective: {
    id: 'VSU',
    name: '忽略打分异常',
  },
  ChangeSubjective: {
    id: 'CSU',
    name: '修改打分',
  },
  ChangeSelect: {
    id: 'CSE',
    name: '修改选做',
  },
  ChangeLocateNormal: {
    id: 'CAN',
    name: '转为定位正常',
  },
  ChangePageOrder: {
    id: 'CPO',
    name: '调整页面顺序',
  },
  RedoRecognize: {
    id: 'REC',
    name: '重新定位识别',
  },
})
