import { numberToChinese } from '@/utils/number'
import { groupArray, unGroupArray } from '@/utils/array'

/**
 * 试卷结构
 * 1. 题号为0表示小题展开（转为多题）
 * 2. vQuestions中将展开的小题虚拟为一道题
 * 3. 大题与题型无关
 */
export class PaperStructure {
  constructor() {
    Object.assign(this, {
      fullScore: 0,
      volumeEnabled: false,
      volumes: [],
      topics: [],
    })
  }

  static createNewTopic() {
    return {
      code: 0,
      codeInChinese: '',
      content: '',
      description: '',
      isQuestionsSameScore: false,
      name: '',
      questionCount: 0,
      questionEachScore: 0,
      showQuestionScore: false,
      questions: [],
      score: 0,
      title: '',
      vQuestions: [],
    }
  }

  static import(jsonStr, questionList) {
    let paperStructure = new PaperStructure()
    extractFromJson()
    matchQuestions()
    return paperStructure

    // 从json字符串中解析出volumes数组
    function extractFromJson() {
      let volumes
      try {
        volumes = JSON.parse(jsonStr).volumes
      } catch {
        volumes = []
      }

      if (!(volumes instanceof Array)) {
        volumes = []
      }

      if (volumes.length > 1) {
        paperStructure.volumeEnabled = true
      }

      volumes.forEach(v => {
        if (!(v.topics && v.topics.length > 0)) {
          return
        }

        v.topics.forEach(t => {
          let code = Number(t.code) || 0
          let name = t.name || ''
          let description = t.description || ''
          let showQuestionScore = t.showQuestionScore || false

          // 旧版试卷结构没有字段name、description，则从content中提取（不保证准确）
          if (!name && !description && t.content) {
            let matchedTopicName = t.content.match(/^[一二三四五六七八九十]+、([\u4e00-\u9fa5]+)/)
            if (matchedTopicName) {
              name = matchedTopicName[1]
            }

            let matchedDescriptionContent = t.content.match(/\d+\s*分\s*[)）](.*)$/)
            if (matchedDescriptionContent) {
              description = matchedDescriptionContent[1]
              if (description === name) {
                description = ''
              }
            }
          }

          let questions = (t.questions || []).map(q => ({
            id: q.id,
            code: Number(q.code),
            score: Number(q.score) || 0,
            alias: q.alias || '',
            branches: (q.branches || []).map(b => ({
              id: b.id,
              code: Number(b.code),
              score: Number(b.score) || 0,
              alias: b.alias || '',
            })),
          }))

          let newTopic = PaperStructure.createNewTopic()
          newTopic.code = code
          newTopic.name = name
          newTopic.description = description
          newTopic.showQuestionScore = showQuestionScore
          newTopic.questions = questions
          paperStructure.topics.push(newTopic)
        })
      })

      paperStructure.topics.sort((a, b) => a.code - b.code)
    }

    // 关联questionList中的题目
    function matchQuestions() {
      let topicQuestionIds = []
      paperStructure.topics.forEach(t => {
        for (let i = 0; i < t.questions.length; ) {
          let q = t.questions[i]
          let question = questionList.find(x => x.id === q.id)

          // 不匹配的删除，此时数据不一致
          if (!question) {
            t.questions.splice(i, 1)
            continue
          }

          question.code = q.code
          question.alias = q.alias
          question.score = q.score
          question.branches.forEach(branch => {
            // 找出branch更新
            let b = q.branches.find(x => x.id === branch.id)
            if (b) {
              branch.code = b.code
              branch.alias = b.alias
              branch.score = b.score
            }
          })

          t.questions[i] = question
          topicQuestionIds.push(question.id)
          i++
        }
      })

      // 删除空大题
      while (true) {
        let emptyTopicIndex = paperStructure.topics.findIndex(t => t.questions.length === 0)
        if (emptyTopicIndex >= 0) {
          paperStructure.topics.splice(emptyTopicIndex, 1)
        } else {
          break
        }
      }

      // 未包含在结构中的题目，若存在则说明数据不一致
      let notAddQuestions = questionList.filter(q => !topicQuestionIds.includes(q.id))
      if (notAddQuestions.length > 0) {
        notAddQuestions.forEach(q => {
          paperStructure.insertQuestionByCode(q)
        })
      }
    }
  }

  // 导出结构数据
  export() {
    return {
      volumes: this.volumes.map(v => ({
        content: v.content,
        topics: v.topics.map(t => ({
          code: t.code,
          codeInChinese: t.codeInChinese,
          name: t.name,
          description: t.description,
          content: t.content,
          showQuestionScore: t.showQuestionScore,
          questions: t.questions.map(q => ({
            id: q.id,
            code: q.code,
            score: q.score,
            alias: q.alias,
            isObjective: q.branches.length === 1 && q.branches[0].branchType.isObjective,
            branches: q.branches.map(b => ({
              id: b.id,
              code: b.code,
              score: b.score,
              alias: b.alias,
              isObjective: b.branchType.isObjective,
            })),
          })),
        })),
      })),
    }
  }

  // 重排序，题号为0的其小题继承题号
  sortQuestions() {
    let code = 1
    this.topics.forEach(topic => {
      topic.questions.forEach(q => {
        // 小题展开
        if (q.code === 0 && q.branches.length > 1) {
          q.code = 0
          q.branches.forEach(b => {
            b.code = code++
          })
        } else {
          q.code = code++
          q.branches.forEach((b, idx) => {
            b.code = idx + 1
          })
        }
      })
    })
  }

  // 更新分数
  updateQuestionScores() {
    this.topics.forEach(topic => {
      topic.questions.forEach(q => {
        q.score = q.branches.reduce((a, c) => a + c.score, 0)
      })
    })
  }

  // 虚拟题目
  updateVQuestions() {
    this.topics.forEach(topic => {
      let vQuestions = []
      topic.questions.forEach(q => {
        if (q.code) {
          vQuestions.push({
            id: q.id,
            code: q.code,
            alias: q.alias,
            score: q.score,
            questionId: q.id,
            branchId: null,
            branches: q.branches,
            originalQuestion: q,
            originalBranch: null,
          })
        } else {
          q.branches.forEach(branch => {
            vQuestions.push({
              id: branch.id,
              code: branch.code,
              alias: branch.alias,
              score: branch.score,
              questionId: q.id,
              branchId: branch.id,
              branches: [branch],
              originalQuestion: q,
              originalBranch: branch,
            })
          })
        }
      })
      vQuestions.forEach(q => {
        q.score = q.branches.reduce((a, c) => a + c.score, 0)
      })

      topic.vQuestions = vQuestions
    })
  }

  // 大题说明
  updateTopicContent() {
    this.topics.forEach((t, idx) => {
      t.code = idx + 1
      t.codeInChinese = numberToChinese(t.code)
      t.title = `${t.codeInChinese}、${t.name}`
      t.questionCount = t.vQuestions.length
      t.isQuestionsSameScore = t.vQuestions.every(q => q.score === t.vQuestions[0].score)
      t.questionEachScore = t.vQuestions[0].score
      t.score = t.vQuestions.reduce((a, c) => a + c.score, 0)

      let contentScoreStr = ''
      if (t.questionCount === 1) {
        contentScoreStr = `${t.score} 分`
      } else if (t.isQuestionsSameScore) {
        contentScoreStr += `共 ${t.questionCount} 题，每题 ${t.questionEachScore} 分，共 ${t.score} 分`
      } else {
        contentScoreStr += `共 ${t.questionCount} 题，${t.score} 分`
      }

      t.content = `${t.title}（${contentScoreStr}）${t.description}`
    })
  }

  // 分卷
  updateVolumes() {
    if (this.topics.length === 0) {
      this.volumes = []
      return
    }

    let volumes = []
    if (!this.volumeEnabled) {
      volumes.push({
        isObjective: undefined,
        content: '',
        topics: this.topics,
      })
    } else {
      let objectiveVolume = {
        isObjective: true,
        content: '选择题',
        topics: [],
      }
      let subjectiveVolume = {
        isObjective: false,
        content: '非选择题',
        topics: [],
      }
      this.topics.forEach(topic => {
        // 只有一个小题且小题题型为客观题
        if (topic.vQuestions.every(q => q.branches.length === 1 && q.branches[0].branchType.isObjective)) {
          objectiveVolume.topics.push(topic)
        } else {
          subjectiveVolume.topics.push(topic)
        }
      })
      if (objectiveVolume.topics.length > 0) {
        volumes.push(objectiveVolume)
      }
      if (subjectiveVolume.topics.length > 0) {
        volumes.push(subjectiveVolume)
      }

      if (volumes[0]) {
        volumes[0].content = '卷I ' + volumes[0].content
      }
      if (volumes[1]) {
        volumes[1].content = '卷II ' + volumes[1].content
      }
    }

    this.volumes = volumes
  }

  updateFullScore() {
    this.fullScore = this.topics.reduce((a, c) => a + c.score, 0)
  }

  // 更新试卷结构
  update() {
    this.sortQuestions()
    this.updateQuestionScores()
    this.updateVQuestions()
    this.updateTopicContent()
    this.updateVolumes()
    this.updateFullScore()
  }

  // 根据题号插入题目，如果前一个或后一个题目与之题型相同，则归入到其大题中，否则新建大题
  insertQuestionByCode(question) {
    let insertQuestionCode = question.code === 0 ? question.branches[0].code : question.code
    let insertQuestionTypeId = question.questionType.id

    let topicQuestions = unGroupArray(this.topics, t =>
      t.questions.map(q => ({
        topic: t,
        question: q,
      }))
    )

    let insertBefore = topicQuestions.find(q => {
      let code = q.question.code === 0 ? q.question.branches[0].code : q.question.code
      return code > insertQuestionCode
    })
    let insertAfter = topicQuestions[topicQuestions.findIndex(q => q === insertBefore) - 1]

    if (insertBefore) {
      if (insertAfter && insertAfter.question.questionType.id === insertQuestionTypeId) {
        let index = insertAfter.topic.questions.findIndex(q => q === insertAfter.question)
        insertAfter.topic.questions.splice(index + 1, 0, question)
      } else if (insertBefore.question.questionType.id === insertQuestionTypeId) {
        let index = insertBefore.topic.questions.findIndex(q => q === insertBefore.question)
        insertBefore.topic.questions.splice(index, 0, question)
      } else {
        let topicIndex = this.topics.findIndex(t => t === insertBefore.topic)
        let newTopic = PaperStructure.createNewTopic()
        newTopic.name = question.questionType.name
        newTopic.questions = [question]
        this.topics.splice(topicIndex, 0, newTopic)
      }
    } else {
      let lastQuestion = topicQuestions[topicQuestions.length - 1]
      if (lastQuestion && lastQuestion.question.questionType.id === insertQuestionTypeId) {
        let index = lastQuestion.topic.questions.findIndex(q => q === lastQuestion.question)
        lastQuestion.topic.questions.splice(index + 1, 0, question)
      } else {
        let newTopic = PaperStructure.createNewTopic()
        newTopic.name = question.questionType.name
        newTopic.questions = [question]
        this.topics.push(newTopic)
      }
    }

    // this.sortQuestions()
  }

  // 根据题型插入题目，如果存在相同题型的大题，则归入其中，否则新建大题，且按题型顺序插入
  insertQuestionByQuestionType(question) {
    let topic = this.topics.find(t => t.questions.every(q => q.questionType.id === question.questionType.id))
    if (topic) {
      topic.questions.push(question)
    } else {
      let newTopic = PaperStructure.createNewTopic()
      newTopic.name = question.questionType.name
      newTopic.questions = [question]

      // 使得大题按题型顺序排序
      let insertIndex = this.topics.findIndex(t => t.questions[0].questionType.order > question.questionType.order)
      if (insertIndex < 0) {
        insertIndex = this.topics.length
      }

      this.topics.splice(insertIndex, 0, newTopic)
    }
  }

  // 移除题目
  removeQuestion(qId) {
    let topic = this.topics.find(t => t.questions.some(q => q.id === qId))
    if (!topic) {
      return
    }

    let index = topic.questions.findIndex(q => q.id === qId)
    topic.questions.splice(index, 1)
    if (topic.questions.length === 0) {
      let topicIndex = this.topics.findIndex(t => t === topic)
      this.topics.splice(topicIndex, 1)
    }
  }

  // 替换题目
  replaceQuestion(oldQuestion, newQuestion) {
    let topic = this.topics.find(t => t.questions.some(q => q === oldQuestion))
    if (!topic) {
      return
    }

    let index = topic.questions.findIndex(q => q === oldQuestion)
    topic.questions.splice(index, 1, newQuestion)
  }

  // 改变大题顺序
  changeTopicOrder(oldIndex, newIndex) {
    let topic = this.topics.splice(oldIndex, 1)[0]
    if (topic) {
      this.topics.splice(newIndex, 0, topic)
    }
  }

  // 改大题名称
  changeTopicName(code, name) {
    let topic = this.topics.find(t => t.code === code)
    if (topic && name && name.length <= 20) {
      topic.name = name
    }
  }

  // 改大题说明
  changeTopicDescription(code, description) {
    if (description == null) {
      description = ''
    }

    let topic = this.topics.find(t => t.code === code)
    if (topic && description.length <= 100) {
      topic.description = description
    }
  }

  // 设置分卷
  changeVolumeEnabled(v) {
    this.volumeEnabled = Boolean(v)
  }

  // 重新划分大题
  changeQuestionsTopic(questions, questionList) {
    let newTopics = groupArray(questions, q => q.topicCode).map(g => {
      let newTopic = PaperStructure.createNewTopic()
      newTopic.code = g.group[0].topicCode
      newTopic.name = g.group[0].topicName
      newTopic.questions = g.group
      return newTopic
    })
    newTopics.forEach(t => {
      t.questions = t.questions.map(q => questionList.find(x => x.id === q.id))

      let oldTopic = this.topics.find(x => x.code === t.code)
      t.description = oldTopic ? oldTopic.description : ''
    })

    this.topics = newTopics
  }
}
