import ajax from '@/api/ajax'

export function apiGetInstitutionScanDeviceLisenceCount() {
  return ajax.get({
    url: 'user/scanDeviceLic/getLicenseNum',
    requestName: '查询本机构可授权的扫描仪数量',
  })
}

export function apiGetInstitutionUsedScanDeviceLisenceList() {
  return ajax.get({
    url: 'user/scanDeviceLic/list',
    requestName: '查询本机构已授权的扫描仪情况',
  })
}

export function apiSetInstitutionScanDeviceLisence(requestParams) {
  return ajax.post({
    url: 'user/scanDeviceLic/add',
    params: {
      deviceNo: requestParams.deviceNo,
      deviceName: requestParams.deviceName,
    },
    // requestName: '新增扫描仪授权',
  })
}

export function apiDeleteScanDeviceLisence(lisenceId) {
  return ajax.delete({
    url: 'user/scanDeviceLic/delete',
    params: {
      id: lisenceId, // *
    },
    requestName: '注销扫描仪授权',
  })
}
