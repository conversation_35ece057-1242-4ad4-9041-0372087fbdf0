export function importScanTemplate(resTemplate) {
  if (!resTemplate) {
    return null
  }

  resTemplate.selectRules = (resTemplate.selectRules || []).map(rule => {
    let ruleName = rule.ruleName
    let selectType = rule.selType
    let selectCount = rule.selectCount >= 1 ? rule.selectCount : 1
    let totalCount = rule.totalCount
    let groups = rule.selectMarks.map((s, idx) => {
      let questionCodes = []
      if (selectType == 1) {
        questionCodes = [rule.items[idx]]
      } else if (selectType == 2) {
        questionCodes = idx === 0 ? rule.items : idx === 1 ? rule.items2 : []
      }

      return {
        name: s.markName || '',
        rect: s.rect,
        sort: s.sort,
        questionCodes,
      }
    })

    return {
      ruleName,
      selectType,
      selectCount,
      totalCount,
      groups,
    }
  })

  return resTemplate
}

export function getResponseSelects(selects, template) {
  let selectDefines = (template && template.selectRules) || []
  return (selects || []).map(sel => {
    let selDefine = selectDefines.find(x => x.ruleName === sel.ruleName)
    let omr = sel.omr || '-'.repeat(selDefine.totalCount)
    let newOmr = omr
    let groupNames = transformOmrToGroupNames(omr, selDefine)
    let newGroupNames = transformOmrToGroupNames(newOmr, selDefine)
    let isAbnormal = checkSelect(newOmr, selDefine)

    return {
      ...selDefine,
      omr,
      newOmr,
      groupNames,
      newGroupNames,
      isAbnormal,
    }
  })
}

export function transformOmrToGroupNames(omr, rule) {
  if (!omr) {
    return []
  }
  let chars = omr.split(',')
  return (rule.groups || []).filter((group, idx) => chars[idx] && chars[idx] === '1').map(group => group.name)
}

export function transformGroupNamesToOmr(groupNames, rule) {
  return (rule.groups || []).map(group => (groupNames.includes(group.name) ? '1' : '-')).join(',')
}

export function checkSelect(omr, rule) {
  if (!omr) {
    return false
  }

  let chars = omr.split(',')
  if (chars.length !== rule.totalCount) {
    return false
  }
  if (chars.some(char => !['1', '-'].includes(char))) {
    return false
  }

  let selectedCharsLength = chars.filter(char => char === '1').length
  if (selectedCharsLength !== rule.selectCount) {
    return false
  }

  return true
}

export function getUncheckObjectives(selects, template) {
  let selectRules = template.selectRules
  if (selectRules.length === 0) {
    return []
  }

  let uncheckQuestionCodes = []
  selectRules.forEach(rule => {
    // 1. 获取选中的组
    let select = selects.find(x => x.ruleName === rule.ruleName)
    let omr = (select && select.omr) || ''
    let chars = omr.split(',')
    let selectedGroups = []

    // 1.1 添加直接选中的
    rule.groups.forEach((g, idx) => {
      if (chars[idx] === '1') {
        selectedGroups.push(g)
      }
    })

    // 1.2 剔除多选的
    selectedGroups = selectedGroups.slice(0, rule.selectCount)

    // 1.3 增加不够的
    if (selectedGroups.length < rule.selectCount) {
      rule.groups.forEach((g, idx) => {
        if (chars[idx] !== '1' && selectedGroups.length < rule.selectCount) {
          selectedGroups.push(g)
        }
      })
    }

    // 2. 获取未选中的题
    rule.groups
      .filter(g => !selectedGroups.includes(g))
      .forEach(g => g.questionCodes.forEach(q => uncheckQuestionCodes.push(q)))
  })

  return uncheckQuestionCodes.filter(q => q.startsWith('A')).map(q => Number(q.substring(1)))
}
