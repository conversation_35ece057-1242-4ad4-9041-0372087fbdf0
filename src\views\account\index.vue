<template>
  <div class="container-account">
    <div class="header">
      <span class="title">个人信息</span>
      <div class="btn-area">
        <Button v-if="isSystem" type="primary" @click="showModalTeacherQuery = true">教师查询</Button>
        <Button type="primary" @click="handleBtnChangePasswordClick">修改密码</Button>
      </div>
    </div>
    <div class="content">
      <com-user-info class="user-info"></com-user-info>
      <com-user-school class="user-school"></com-user-school>
    </div>

    <Modal
      v-model="showModalChangePassword"
      class="modal-change-password"
      width="500"
      title="修改密码"
      :mask-closable="false"
    >
      <div class="modal-content">
        <Form class="form-password" :label-width="90" label-position="left">
          <FormItem class="form-item" label="当前密码">
            <Input v-model="oldPassword" type="password" maxlength="20" clearable></Input>
          </FormItem>
          <FormItem class="form-item" label="新密码">
            <Input v-model="newPassword" type="password" password maxlength="20"></Input>
          </FormItem>
          <FormItem class="form-item" label="确认新密码">
            <Input v-model="newPasswordConfirm" type="password" password maxlength="20"></Input>
          </FormItem>
        </Form>
      </div>
      <template #footer>
        <div>
          <Button type="text" @click="showModalChangePassword = false">取消</Button>
          <Button type="primary" @click="handleModalChangePasswordOk">确定</Button>
        </div>
      </template>
    </Modal>

    <ModalTeacherQuery v-model="showModalTeacherQuery" />
  </div>
</template>

<script>
  import ComUserInfo from './components/user_info'
  import ComUserSchool from './components/user_school'
  import ModalTeacherQuery from '@/views/school/teacher/components/modal_teacher_query'

  import { apiChangePassword } from '@/api/user'

  import iView from '@/iview'
  import store from '@/store/index'

  export default {
    components: {
      ComUserInfo,
      ComUserSchool,
      ModalTeacherQuery,
    },
    beforeRouteEnter(to, from, next) {
      iView.LoadingBar.start()

      store.dispatch('school/getStageGradeSubjects').finally(() => {
        iView.LoadingBar.finish()
        next()
      })
    },
    data() {
      return {
        showModalChangePassword: false,
        oldPassword: '',
        newPassword: '',
        newPasswordConfirm: null,
        showModalTeacherQuery: false,
      }
    },
    computed: {
      user() {
        return this.$store.state.user
      },

      isSystem() {
        return this.$store.state.user.isSystem
      },
    },
    methods: {
      handleBtnChangePasswordClick() {
        this.oldPassword = ''
        this.newPassword = ''
        this.newPasswordConfirm = null
        this.showModalChangePassword = true
      },
      handleModalChangePasswordOk() {
        let message = ''
        if (!this.newPassword) {
          message = '请输入新密码'
        } else if (this.newPassword !== this.newPasswordConfirm) {
          message = '新密码两次输入不一致'
        } else if (this.user.mobile && this.user.mobile.slice(-6) === this.newPassword) {
          message = '请不要使用手机号后6位作为密码'
        }

        // 检查密码
        let minLength = 6
        if (this.newPassword.length < minLength) {
          message = `新密码至少${minLength}位`
        } else {
          let hasLetter = /[A-Za-z]/.test(this.newPassword)
          let hasNumber = /[0-9]/.test(this.newPassword)
          if (!hasLetter || !hasNumber) {
            message = '新密码必须同时包含数字和字母'
          }
        }

        if (message) {
          this.$Message.warning({
            content: message,
            duration: 5,
          })
          return
        }

        apiChangePassword({
          oldPassword: this.oldPassword,
          newPassword: this.newPassword,
          identity: this.user.identity,
        }).then(() => {
          this.$Message.success({
            content: '已修改密码，请重新登录',
            duration: 3,
          })
          this.showModalChangePassword = false
          setTimeout(() => {
            this.$store.dispatch('user/logout')
          }, 2000)
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-account {
    padding: 20px;
    background-color: white;
  }

  .header {
    @include flex(row, space-between, center);
    margin-bottom: 20px;
    padding-bottom: 5px;
    border-bottom: 1px solid $color-border;

    .title {
      font-size: 30px;
    }
  }

  .content {
    @include flex(row, flex-start, flex-start);

    .user-info {
      flex: 1;
    }

    .user-school {
      flex: 1;
    }
  }
</style>
