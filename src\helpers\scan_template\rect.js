import { randomId } from '@/utils/string'
export default class Rect {
  constructor(params) {
    this.id = params && params.id ? params.id : randomId()
    this.x = params && Number.isFinite(params.x) ? Math.round(params.x) : 0
    this.y = params && Number.isFinite(params.y) ? Math.round(params.y) : 0
    this.width = params && Number.isFinite(params.width) ? Math.round(params.width) : 0
    this.height = params && Number.isFinite(params.height) ? Math.round(params.height) : 0
  }

  changeX(x) {
    this.x = Math.round(x)
  }

  changeY(y) {
    this.y = Math.round(y)
  }

  changeWidth(width) {
    this.width = Math.round(width)
  }

  changeHeight(height) {
    this.height = Math.round(height)
  }

  copy() {
    let newRect = new Rect()
    Object.assign(newRect, this)
    return newRect
  }
}
