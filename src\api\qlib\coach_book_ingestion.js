import ajax from '@/api/ajax'

/**
 * 获取教辅导入信息
 */
export function apiCoachBookIngestionGetInfo(coachBookId) {
  return ajax.request({
    method: 'get',
    url: 'ques/coachBookIngestion/byCoachBookId',
    params: {
      coachBookId,
    },
    requestName: '获取教辅导入信息',
  })
}

/**
 * 创建教辅导入项目
 */
export function apiCoachBookIngestionCreate({ coachBookId, fileType }) {
  return ajax.request({
    method: 'post',
    url: 'ques/coachBookIngestion/create',
    params: {
      coachBookId,
      fileType,
    },
    requestName: '创建教辅导入项目',
  })
}

/**
 * 修改上传文件类型
 */
export function apiCoachBookIngestionChangeFileType({ ingestionId, fileType }) {
  return ajax.request({
    method: 'put',
    url: 'ques/coachBookIngestion/changeFileType',
    params: {
      ingestionId,
      fileType,
    },
    requestName: '修改上传文件类型',
  })
}

/**
 * 上传pdf
 */
export function apiCoachBookIngestionUploadPdf({ ingestionId, autoConvertImage, file, onUploadProgress }) {
  return ajax.request({
    method: 'post',
    url: 'ques/coachBookIngestion/uploadPdf',
    params: {
      ingestionId,
      // 是否自动转换图片为pdf
      autoConvertImage,
    },
    data: {
      file,
    },
    useFormData: true,
    onUploadProgress,
  })
}

/**
 * 转换pdf为图片
 */
export function apiCoachBookIngestionConvertPdfToImage(ingestionId) {
  return ajax.request({
    method: 'put',
    url: 'ques/coachBookIngestion/convertPdfToImage',
    params: {
      ingestionId,
    },
    requestName: '转换pdf为图片',
  })
}

/**
 * 获取上传图片批次号
 */
export function apiCoachBookIngestionGetUploadImagesBatchNumber(ingestionId) {
  return ajax.request({
    method: 'get',
    url: 'ques/coachBookIngestion/uploadImagesBatchNumber',
    params: {
      ingestionId,
    },
  })
}

/**
 * 上传图片
 */
export function apiCoachBookIngestionUploadImage({ ingestionId, batchNumber, index, file }) {
  return ajax.request({
    method: 'post',
    url: 'ques/coachBookIngestion/uploadImage',
    params: {
      ingestionId,
      batchNumber,
      index,
    },
    data: {
      file,
    },
    useFormData: true,
  })
}

/**
 * 完成上传图片
 */
export function apiCoachBookIngestionCompleteUploadImagesBatch({ ingestionId, batchNumber }) {
  return ajax.request({
    method: 'put',
    url: 'ques/coachBookIngestion/uploadImageComplete',
    params: {
      ingestionId,
      batchNumber,
    },
  })
}

/**
 * 调整已上传图片
 */
export function apiCoachBookIngestionChangeUploadedImages({ ingestionId, filePath }) {
  return ajax.request({
    method: 'put',
    url: 'ques/coachBookIngestion/changeImages',
    params: {
      ingestionId,
    },
    data: filePath,
    requestName: '调整已上传图片',
  })
}

/**
 * 删除上传文件
 */
export function apiCoachBookIngestionDeleteUploadedFile(ingestionId) {
  return ajax.request({
    method: 'delete',
    url: 'ques/coachBookIngestion/deleteUploadedFile',
    params: {
      ingestionId,
    },
    requestName: '删除上传文件',
  })
}

/**
 * 获取结构
 */
export function apiCoachBookIngestionGetStructure(ingestionId) {
  return ajax.request({
    method: 'get',
    url: 'ques/coachBookIngestion/structure',
    params: {
      ingestionId,
    },
    requestName: '获取结构',
  })
}

/**
 * 添加结构
 */
export function apiCoachBookIngestionAddStructure({ ingestionId, structures }) {
  return ajax.request({
    method: 'post',
    url: 'ques/coachBookIngestion/structure',
    params: {
      ingestionId,
    },
    data: structures,
    requestName: '添加结构',
  })
}

/**
 * 修改结构
 */
export function apiCoachBookIngestionChangeStructure({ ingestionId, structures }) {
  return ajax.request({
    method: 'put',
    url: 'ques/coachBookIngestion/structure',
    params: {
      ingestionId,
    },
    data: structures,
    requestName: '修改结构',
  })
}

/**
 * 删除结构
 */
export function apiCoachBookIngestionDeleteStructure({ ingestionId, structureIds }) {
  return ajax.request({
    method: 'delete',
    url: 'ques/coachBookIngestion/structure',
    params: {
      ingestionId,
      structureIds,
    },
    requestName: '删除结构',
  })
}

/**
 * 识别目录
 */
export function apiCoachBookIngestionRecognizeToc(ingestionId) {
  return ajax.request({
    method: 'put',
    url: 'ques/coachBookIngestion/recognizeTOCStructure',
    params: {
      ingestionId,
    },
    requestName: '识别目录',
  })
}

/**
 * 获取目录识别结果
 */
export function apiCoachBookIngestionGetRecognizeTocResult(ingestionId) {
  return ajax.request({
    method: 'get',
    url: 'ques/coachBookIngestion/recognizeTOCResult',
    params: {
      ingestionId,
    },
    requestName: '获取目录识别结果',
  })
}
