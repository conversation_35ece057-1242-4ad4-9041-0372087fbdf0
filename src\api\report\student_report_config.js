import ajax from '@/api/ajax'

export function apiGetSchoolStudentReportConfigList(requestParams) {
  return ajax.get({
    url: 'report/studentReportConfig/list',
    params: {
      pageNum: requestParams.currentPage || 1,
      pageSize: requestParams.pageSize || 10,
      schoolName: requestParams.schoolName || undefined,
      rangeType: requestParams.rangeType || undefined, // 传 x 则只查学生，否则只有学校年级配置
      semesterId: requestParams.semester, // [Number]
      term: requestParams.term, // [Number]
    },
    requestName: '获取学校定制报告配置',
  })
}

export function apiAddSchoolStudentReportConfig(requestParams) {
  return ajax.post({
    url: 'report/studentReportConfig/create',
    data: {
      // expireDownType: requestParams.expireDownType, // *required 服务到期后的下载方式: 1 - 免费下载 | 2 - 付费下载 | 3 - 禁止下载
      rangeType: requestParams.rangeType, // *required 范围： s - 学校 | g - 年级 | c - 班级 | x - 学生
      schoolId: requestParams.schoolId, // *required
      semesterId: requestParams.semester, // *required
      term: requestParams.term, // *required
      classId: requestParams.classId || undefined, // default: UUID-ZERO(全部班级)
      gradeId: requestParams.gradeId || undefined, // default: 0(全部年级)
      subjectId: requestParams.subjectId || undefined, // default: 0(全部科目)
      studentIds: requestParams.studentIds, // [Array(String)]
    },
    requestName: '创建学校定制报告配置',
  })
}

export function apiRemoveSchoolStudentReportConfig(configId) {
  return ajax.delete({
    url: 'report/studentReportConfig/delete',
    params: {
      id: configId, // *required [Number]
    },
    requestName: '删除学校定制报告配置',
  })
}

export function apiImportSchoolStudentReportConfig(requestParams) {
  return ajax.upload({
    url: 'report/studentReportConfig/importStudents',
    data: {
      file: requestParams.file,
      schoolId: requestParams.schoolId,
      semesterId: requestParams.semester,
      term: requestParams.term,
    },
    // requestName: '导入学校学生定制报告配置',
  })
}
