<template>
  <div class="analysis-container">
    <Card class="analysis-data-card">
      <template #title><span class="card-title">班级成绩</span></template>
      <div class="content">
        <div v-if="isLoading" class="loading-spin">
          <Spin />
        </div>

        <div class="content-section section-summary">
          <div class="section-content">
            <div class="data-item data-item-avg">
              <div class="data-label">平均分</div>
              <div class="data-value">{{ summary?.avgScore != null ? summary.avgScore.toFixed(1) : '--' }}</div>
            </div>
            <div class="data-item data-item-max">
              <div class="data-label">最高分</div>
              <div class="data-value">
                {{ summary?.maxScore != null ? summary.maxScore.toFixed(1) : '--' }}
              </div>
            </div>
            <div class="data-item data-item-min">
              <div class="data-label">最低分</div>
              <div class="data-value">{{ summary?.minScore != null ? summary.minScore.toFixed(1) : '--' }}</div>
            </div>
            <div class="data-item data-item-pass-rate">
              <div class="data-label">及格率</div>
              <div class="data-value">
                {{ summary?.passRate != null ? (summary.passRate * 100).toFixed(1) + '%' : '--' }}
              </div>
            </div>
          </div>
        </div>

        <div class="content-section section-student">
          <div class="section-title">学生成绩</div>
          <div class="section-content">
            <Table
              v-for="t in studentTables"
              :key="t.order"
              class="student-table"
              :columns="studentTableColumns"
              :data="t.data"
              stripe
              border
            ></Table>
          </div>
        </div>

        <div class="content-section section-question">
          <div class="section-title">题目得分率</div>
          <div class="section-content">
            <Table :columns="questionTableColumns" :data="questions" stripe border></Table>
          </div>
        </div>

        <div class="content-section section-knowledge">
          <div class="section-title">知识点得分率</div>
          <div class="section-content">
            <Table :columns="knowledgeTableColumns" :data="knowledges" stripe border></Table>
          </div>
        </div>
      </div>
    </Card>
    <AiAnalysis
      :load-last-analysis-func="apiGetLastAiClassAnalysis"
      :sse-url="sseUrl"
      :sse-params="sseParams"
      :analysis-title="analysisTitle"
    ></AiAnalysis>
  </div>
</template>

<script setup>
  import { ref, onBeforeMount, computed, watch } from 'vue'
  import { useAiStore } from '@/store/ai'

  import AiAnalysis from './ai_analysis.vue'

  import { apiGetAiClassAnalysisData, apiAiClassAnalysisUrl, apiGetLastAiClassAnalysis } from '@/api/ai/score_analysis'
  import { ApiBaseUrl } from '@/config/config'

  import { chunk } from '@/utils/array'

  const props = defineProps({
    analysisFilter: {
      type: Object,
      required: true,
    },
  })

  const aiStore = useAiStore()

  // 概览数据
  const summary = ref(null)

  // 学生成绩
  const students = ref([])
  // 学生成绩表格
  const studentTableColumns = computed(() => {
    let columns = [
      {
        title: '姓名',
        key: 'studentName',
      },
      {
        title: '分数',
        key: 'score',
        align: 'center',
      },
      {
        title: '等级',
        key: 'level',
        align: 'center',
      },
      {
        title: '班排名',
        key: 'classRank',
        align: 'center',
        render: (h, params) => {
          if (!params.row.stat) {
            return h('span', {}, '不统计')
          }
          return h('span', {}, params.row.classRank || '--')
        },
      },
    ]
    // 所有学生都没有分数，则显示等级
    let showLevel = students.value.length > 0 && students.value.every(x => x.score == null)
    if (showLevel) {
      columns = columns.filter(x => x.title != '分数')
    } else {
      columns = columns.filter(x => x.title != '等级')
    }
    return columns
  })
  const studentTables = computed(() => {
    let maxTables = 2
    let rowsPerTable = 10
    if (students.value.length > rowsPerTable) {
      rowsPerTable = Math.ceil(students.value.length / maxTables)
    }
    return chunk(students.value, rowsPerTable).map((item, index) => ({
      order: index + 1,
      data: item,
    }))
  })

  // 题目得分率
  const questions = ref([])
  const questionTableColumns = [
    {
      title: '小题',
      key: 'branchName',
    },
    {
      title: '班级得分率',
      key: 'classScoreRate',
      width: 110,
      align: 'center',
      sortable: true,
      render: (h, params) => {
        return h(
          'span',
          {
            class: {
              'text-warning': params.row.classScoreRate < params.row.schoolScoreRate,
            },
          },
          params.row.classScoreRate == null ? '--' : (params.row.classScoreRate * 100).toFixed(1) + '%'
        )
      },
    },
    {
      title: '年级得分率',
      key: 'schoolScoreRate',
      width: 110,
      align: 'center',
      sortable: true,
      render: (h, params) => {
        return h(
          'span',
          {},
          params.row.schoolScoreRate == null ? '--' : (params.row.schoolScoreRate * 100).toFixed(1) + '%'
        )
      },
    },
  ]

  // 知识点得分率
  const knowledges = ref([])
  const knowledgeTableColumns = [
    {
      title: '知识点',
      key: 'knowledgeName',
      minWidth: 200,
    },
    {
      title: '对应小题',
      key: 'branchNames',
      minWidth: 110,
    },
    {
      title: '班级得分率',
      key: 'classScoreRate',
      width: 110,
      align: 'center',
      sortable: true,
      render: (h, params) => {
        return h(
          'span',
          {
            class: {
              'text-warning': params.row.classScoreRate < params.row.schoolScoreRate,
            },
          },
          params.row.classScoreRate == null ? '--' : (params.row.classScoreRate * 100).toFixed(1) + '%'
        )
      },
    },
    {
      title: '年级得分率',
      key: 'schoolScoreRate',
      width: 110,
      align: 'center',
      sortable: true,
      render: (h, params) => {
        return h(
          'span',
          {},
          params.row.schoolScoreRate == null ? '--' : (params.row.schoolScoreRate * 100).toFixed(1) + '%'
        )
      },
    },
  ]

  // 加载班级分析数据
  const isLoading = ref(false)
  let currentRequestId = 0
  async function loadData() {
    let params = {
      classId: props.analysisFilter?.class?.classId,
      examId: props.analysisFilter?.exam?.examId,
      templateId: props.analysisFilter?.exam?.templateId,
      subjectId: props.analysisFilter?.subject?.subjectId,
    }
    if (!params.classId || !params.examId || !params.templateId || !params.subjectId) {
      resetData()
      return
    }

    isLoading.value = true
    aiStore.isLoadingAnalysisData = true
    let requestId = ++currentRequestId
    try {
      let res = await apiGetAiClassAnalysisData(params)
      if (requestId == currentRequestId) {
        summary.value = res.general
        students.value = res.studentScores
        questions.value = res.questionRates
        knowledges.value = res.knowledgeRates
      }
    } catch (err) {
      if (requestId == currentRequestId) {
        resetData()
      }
      throw err
    } finally {
      if (requestId == currentRequestId) {
        isLoading.value = false
        aiStore.isLoadingAnalysisData = false
      }
    }
  }

  // 重置数据
  function resetData() {
    summary.value = null
    students.value = []
    questions.value = []
    knowledges.value = []
  }

  // Ai分析sse参数
  const sseUrl = `${ApiBaseUrl}/${apiAiClassAnalysisUrl}`
  const sseParams = computed(() => {
    return {
      classId: props.analysisFilter.class.classId,
      examId: props.analysisFilter.exam.examId,
      templateId: props.analysisFilter.exam.templateId,
      subjectId: props.analysisFilter.subject.subjectId,
    }
  })

  // 分析标题
  const analysisTitle = computed(() => {
    let { exam, class: cls, subject } = props.analysisFilter
    return `${exam?.examName}-${cls?.className}${subject?.subjectName}-班级AI成绩分析`
  })

  // 创建时和props变化时加载数据
  onBeforeMount(() => {
    loadData()
  })

  watch(props, () => {
    loadData()
  })
</script>

<style lang="scss" scoped>
  .section-summary {
    padding-top: 8px;

    .section-content {
      @include flex(row, flex-start, center);
      column-gap: 20px;

      .data-item {
        flex: 1;
        min-width: 120px;
        border-top: 10px solid;
        border-radius: 6px;
        text-align: center;
        background-color: #f8f8f9;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
          transform: translateY(-5px);
        }

        &.data-item-avg {
          border-color: #2d8cf0;
          color: #2d8cf0;
        }

        &.data-item-max {
          border-color: #19be6b;
          color: #19be6b;
        }

        &.data-item-min {
          border-color: #ff9900;
          color: #ff9900;
        }

        &.data-item-pass-rate {
          border-color: #ed4014;
          color: #ed4014;
        }

        .data-label {
          margin-top: 16px;
          margin-bottom: 8px;
          color: $color-content;
          font-size: $font-size-medium-x;
          line-height: 1;
        }

        .data-value {
          margin-bottom: 16px;
          font-weight: bold;
          font-size: 28px;
          line-height: 1;
        }
      }
    }
  }

  .section-student {
    .section-content {
      @include flex(row, flex-start, flex-start);
      column-gap: 8px;

      .student-table {
        flex: 1;
      }
    }
  }

  .section-question,
  .section-knowledge {
    :deep(.text-warning) {
      color: $color-warning;
    }
  }
</style>
