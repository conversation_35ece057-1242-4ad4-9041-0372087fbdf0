import user from './user'
import qlib from './qlib/index'
import common from './common'
import emarking from './emarking/index'
import report from './report/index'
import school from './school'
import teaching from './teaching/situation'
import homework from './homework/index'
import scanTemplate from './scan_template'
import answerSheet from './answer_sheet'
import research from './teaching/research'
import wrongQuestionSheet from './wrong_question_sheet'
import schoolResources from './school_resources'
import scan from './scan'
import review from './review'
import practise from './practise'

const modules = {
  user,
  qlib,
  common,
  emarking,
  report,
  school,
  teaching,
  homework,
  scanTemplate,
  answerSheet,
  research,
  wrongQuestionSheet,
  schoolResources,
  scan,
  review,
  practise,
}

export default function registerModules(store) {
  Object.keys(modules).forEach(key => {
    store.registerModule(key, modules[key])
  })
}
