<template>
  <div class="container-com-filter">
    <div v-for="field in list" :key="field.name" class="filter-field">
      <span class="field-name">
        {{ field.name }}
      </span>
      <com-radio-group
        v-if="field.component === 'radio'"
        class="field-radio"
        :radioes="field.data"
        :model-value="field.value"
        @update:model-value="onChange(field.name, $event)"
      >
      </com-radio-group>
      <Cascader
        v-if="field.component === 'cascader'"
        class="field-cascader"
        :data="field.data"
        :model-value="field.value"
        trigger="hover"
        change-on-select
        @on-change="onCascaderValueChange(field.name, $event)"
        @on-visible-change="onCascaderVisibleChange(field.name, $event)"
      ></Cascader>
    </div>
  </div>
</template>

<script>
  import comRadioGroup from './radio_group.vue'

  export default {
    components: {
      comRadioGroup,
    },
    props: {
      fields: {
        type: Array,
        required: true,
      },
    },
    emits: ['on-change'],
    data() {
      return {
        cascaderValue: {},
      }
    },
    computed: {
      list() {
        return this.fields.map(f => {
          let name = f.name || f.id || ''
          let component = f.component === 'cascader' ? 'cascader' : 'radio'
          let data = []
          if (component === 'radio') {
            data = f.data.map(x => ({
              id: x.id,
              name: x.name,
            }))
          } else {
            data = f.data.map(function _transformNode(node) {
              let newNode = {
                value: node.value || node.id || '',
                label: node.label || node.name || '',
              }
              let children = node.children && node.children.length ? node.children.map(_transformNode) : []
              if (children.length) {
                newNode.children = children
              }
              return newNode
            })
          }
          let value = f.value
          if (f.value == null) {
            if (component === 'radio') {
              value = (data[0] && data[0].id) || ''
            } else {
              value = []
            }
          }

          return {
            name,
            component,
            data,
            value,
          }
        })
      },
    },
    methods: {
      onChange(fieldName, fieldValue) {
        this.$emit('on-change', {
          name: fieldName,
          value: fieldValue,
        })
      },
      onCascaderValueChange(fieldName, value) {
        this.cascaderValue[fieldName] = value
        if (value.length === 0) {
          this.$emit('on-change', {
            name: fieldName,
            value,
          })
        }
      },
      onCascaderVisibleChange(fieldName, visible) {
        if (visible) {
          return
        }

        this.$emit('on-change', {
          name: fieldName,
          value: this.cascaderValue[fieldName],
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .filter-field {
    @include flex(row, flex-start, center);
    min-height: 20px;
    margin-top: 10px;
  }

  .filter-field:first-child {
    margin-top: 0;
  }

  .field-name {
    flex-shrink: 0;
    min-width: 3em;
    margin-right: 5px;
    color: $color-icon;
  }

  .field-radio {
    flex-grow: 1;
  }

  .field-cascader {
    width: 200px;
  }
</style>
