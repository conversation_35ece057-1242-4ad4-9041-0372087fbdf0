import { apiDownloadScoreBreakdown } from '@/api/report'

import Store from '@/store/index'

// 学生得分明细
export function generateExcelReportScoreDetailBlob(subject, school, excelShowRank = false, category) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let requestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    examSubjectId: (subject && subject.examSubjectId) || undefined,
    reportName: 'sch_stuScoreDetail',
    excelShowRank: excelShowRank,
  }

  let fileName = `${subject.subjectName}/${Store.getters['report/examName']}_学生得分明细_`
  if (category && category.categoryName) {
    fileName = category.categoryName + '/' + fileName
  }
  if (isMultipleSchoolLevel) {
    fileName = `学校报表/${school.schoolName}/${fileName}${school.schoolName}`
    requestParams.schoolId = (school && school.schoolId) || undefined
  } else {
    fileName = `${fileName}${Store.getters['report/currentSchoolName']}`
    requestParams.schoolId = Store.getters['report/currentSchoolId'] || undefined
  }
  fileName = `${fileName}_${subject.subjectName}.xlsx`

  return apiDownloadScoreBreakdown(requestParams)
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}
