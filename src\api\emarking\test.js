import ajax from '@/api/ajax'

export function apiGetPersonalAnswerSheetList(requestParams) {
  return ajax.get({
    url: 'mark/exam/selfAnswerSheetList',
    params: {
      name: requestParams.answerSheetName,
      mode: requestParams.mode, // 1 - 线上阅卷 | 2 - 先阅后扫
      status: requestParams.status, //  0 - 正常 | 1- 回收站 | 2 - 删除
    },
    requestName: '获取个人答题卡列表',
  })
}

export function apiSetEnableDelayUpStudent(requestParams) {
  return ajax.put({
    url: 'mark/exam/setEnableDelayUpStudent',
    params: {
      examId: requestParams.examId, // String
      schoolId: requestParams.schoolId, // String
      enable: requestParams.enable, // Boolean
    },
    requestName: '设置学校是否可以延迟上报评考生',
  })
}

export function apiGetAnswerSheetDetail(requestParams) {
  return ajax.get({
    url: 'mark/exam/answerSheet',
    params: {
      answerSheetId: requestParams.answerSheetId,
    },
    requestName: '获取答题卡详情',
  })
}

export function apiGetTestList({
  semesterId,
  term,
  gradeId,
  subjectId,
  beginTime,
  endTime,
  keyword,
  pageSize,
  currentPage,
}) {
  return ajax.get({
    url: 'mark/exam/exams2',
    params: {
      semesterId,
      term,
      gradeId,
      subjectId,
      beginTime,
      endTime,
      key: keyword,
      size: pageSize,
      current: currentPage,
    },
    requestName: '获取手阅测试列表',
  })
}

export function apiCreateTest(requestParams) {
  return ajax.post({
    url: 'mark/exam/create3',
    data: {
      answerSheetId: requestParams.answerSheetId,
      beginTime: requestParams.beginTime,
      endTime: requestParams.endTime,
      examName: requestParams.examName,
      examMode: requestParams.examMode || 'normal',
      subjectId: requestParams.subjectId,
      gradeId: requestParams.gradeId,
      classIds: requestParams.classIds,
      objectives: requestParams.objectives,
      /*
        objectives: [{
          answer: [string],
          answerScoreList: [string],
          branchId: [string],
          branchName: [string],
          fullScore: [Number],
          id: [Number],
          isGiven: [Number],
          optionCount: [Number],
          questionCode: [Number],
          questionId: [string],
          questionName: [string],
          questionType: [Number],
          topicCode: [Number],
          topicName: [String]
        }]
      */
      subjectives: requestParams.subjectives,
      /*
        subjectives: [{
          blankScores: [string],
          branchCode: [Number],
          branchId: [string],
          branchName: [string],
          fullScore: [Number],
          id: [Number],
          isAdditional: [Boolean],
          questionCode: [Number],
          questionId: [string],
          questionName: [string],
          topicCode: [Number],
          topicName: [string]
        }]
      */
      blocks: requestParams.blocks,
      /*
        blocks: [{
          blockName: [string],
          subjectives: [Array(object)]
        }]
      */
    },
    requestName: '创建手阅测试',
  })
}

export function apiGetProjectDetail(requestParams) {
  return ajax.get({
    url: 'mark/exam/detail2',
    params: {
      examSubjectId: requestParams.examSubjectId,
    },
    // requestName: '获取手阅测试详情',
  })
}

export function apiUpdateAnswerSheetStatus(requestParams) {
  return ajax.post({
    url: 'mark/exam/updateAnswerSheetStatus',
    params: {
      id: requestParams.id,
      status: requestParams.status, //  0 - 正常 | 1- 回收站 | 2 - 删除
    },
    requestName: '更改答题卡状态',
  })
}
