/**
 * @param {Function} func 需防抖的函数
 * @param {Number} delay  延迟时间
 */
export function debounce(func, delay) {
  let timer = 0
  return function () {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => func.apply(this, arguments), delay)
  }
}

/**
 * @param {Function} func 需节流的函数
 * @param {Number} interval 间隔时间
 */
export function throttle(func, interval) {
  let timer = 0,
    lastThis,
    lastArgs
  return function () {
    lastThis = this
    lastArgs = arguments
    if (!timer) {
      timer = setTimeout(() => {
        timer = 0
        func.apply(lastThis, lastArgs)
      }, interval)
    }
  }
}

export function isFunction(f) {
  return Object.prototype.toString.call(f) === '[object Function]'
}
