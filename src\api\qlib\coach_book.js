import ajax from '@/api/ajax'
import { PaperInfo } from '@/helpers/qlib/paper_info'

export function apiGetCoachBookPage(params) {
  return ajax
    .get({
      url: 'ques/coachBook/page',
      params: {
        partner: params.partner || undefined,
        gradeLevel: params.stage || params.stageId,
        subjectId: params.subjectId,
        gradeId: params.gradeId || undefined,
        semesterId: params.semester || undefined,
        term: params.term || undefined,
        year: params.year || undefined,
        bookName: params.bookName || undefined,
        pressName: params.pressName || undefined,
        page: params.currentPage || 1,
        size: params.pageSize || 10,
        bookCode: params.bookCode || undefined,
      },
      requestName: '获取教辅列表',
    })
    .then(data => {
      data.records.forEach(item => {
        item.stageId = item.gradeLevel
      })
      return data
    })
}

export function apiGetCoachBookInfo(coachBookId) {
  return ajax
    .get({
      url: 'ques/coachBook/info',
      params: {
        coachBookId,
      },
      requestName: '获取教辅信息',
    })
    .then(data => {
      if (data) {
        data.stageId = data.gradeLevel
      }
      return data
    })
}

export function apiAddCoachBook(params) {
  let requestData = {
    partner: params.partner,
    gradeLevel: params.stageId,
    subjectId: params.subjectId,
    gradeId: params.gradeId || '',
    semesterId: params.semester || '',
    term: params.term || '',
    year: params.year,
    bookName: params.bookName,
    bookCode: params.bookCode || '',
    pressName: params.pressName,
  }

  if (params.coverFile) {
    requestData.coverFile = params.coverFile
  }

  return ajax.upload({
    url: 'ques/coachBook/add',
    data: requestData,
    requestName: '添加教辅',
  })
}

export function apiUpdateCoachBook(params) {
  return ajax.upload({
    url: 'ques/coachBook/update',
    params: {
      id: params.id,
      partner: params.partner,
      gradeLevel: params.stageId,
      subjectId: params.subjectId,
      gradeId: params.gradeId,
      semesterId: params.semester,
      term: params.term,
      year: params.year,
      bookName: params.bookName,
      bookCode: params.bookCode,
      pressName: params.pressName,
    },
    data: params.coverFile
      ? {
          coverFile: params.coverFile,
        }
      : undefined,
    isPostRequest: false,
    requestName: '编辑教辅',
  })
}

export function apiDeleteCoachBook(coachBookId) {
  return ajax.delete({
    url: 'ques/coachBook/delete',
    params: {
      coachBookId,
    },
    requestName: '删除教辅',
  })
}

export function apiExportCustomPaperPDF(requestParams) {
  return ajax.get({
    url: 'ques/coachBook/getCustomPaperPdf',
    params: {
      coachBookId: requestParams.coachBookId,
      paperId: requestParams.paperId,
    },
    // requestName: '获取练习卷PDF',
  })
}

/**
 * 教辅试卷
 */
export function apiGetCoachBookPapers(coachBookId, onlyOpen = false) {
  return ajax
    .get({
      url: 'ques/coachBook/papers',
      params: {
        coachBookId,
        onlyOpen,
      },
      requestName: '获取教辅包含单元试卷',
    })
    .then(papers => papers.map(transformCoachBookPaper))
}

export function transformCoachBookPaper(x) {
  let paper = PaperInfo.import(x)
  paper.year = x.year || null
  paper.coachBookId = x.coachBookId
  paper.coachBookName = x.coachBookName
  paper.coachBookCode = x.coachBookCode
  paper.sortCode = x.sortCode
  paper.createTime = x.createTime
  paper.standardQuestionCount = x.standardQuestionCount || 0
  paper.standardQuestionFinished = x.standardQuestionFinished
  paper.openTime = x.openTime
  paper.uploadType = x.uploadType
  paper.paperAlias = x.paperAlias
  paper.scoreAnalysis = x.scoreAnalysis
  paper.feedbackSheetPdfUrl = x.feedbackSheetPdfUrl
  paper.feedbackSheetImageUrls = x.feedbackSheetImageUrls
  paper.locked = x.locked
  paper.contentPDFUrl = x.contentPdfUrl
  paper.answerPDFUrl = x.answerPdfUrl
  paper.listeningAudioUrl = x.listeningAudioUrl
  return paper
}

export function apiAddCoachBookPapers(data) {
  return ajax.post({
    url: 'ques/coachBook/addPapers',
    params: {
      coachBookId: data.coachBookId,
      paperIds: data.paperIds,
    },
    requestName: '添加单元试卷',
  })
}

export function apiSortCoachBookPapers(data) {
  return ajax.put({
    url: 'ques/coachBook/sortPapers',
    data,
    requestName: '调整单元试卷顺序',
  })
}

export function apiRemoveCoachBookPapers(params) {
  return ajax.delete({
    url: 'ques/coachBook/removePapers',
    params: {
      coachBookId: params.coachBookId,
      paperIds: params.paperIds,
    },
    requestName: '移除单元试卷',
  })
}

export function apiSetCoachBookPaperAlias({ coachBookId, paperIdAliasList }) {
  return ajax.put({
    url: 'ques/coachBook/updatePaperAlias',
    params: {
      coachBookId,
    },
    data: paperIdAliasList,
    requestName: '设置试卷别名',
  })
}

export function apiSetCoachBookPaperScoreAnalysis({ coachBookId, paperIdScoreAnalysisList }) {
  return ajax.put({
    url: 'ques/coachBook/updateScoreAnalysis',
    params: {
      coachBookId,
    },
    data: paperIdScoreAnalysisList,
    requestName: '设置试卷显示分值分析',
  })
}

export function apiSetCoachBookPaperLocked({ coachBookId, paperId, locked }) {
  return ajax.put({
    url: 'ques/coachBook/setPaperLocked',
    params: {
      coachBookId,
      paperId,
      locked,
    },
    requestName: '设置试卷锁定状态',
  })
}

/**
 * 教辅定制试卷
 */
export function apiGetCoachBookCustomPapers(coachBookId) {
  return ajax
    .get({
      url: 'ques/coachBook/customPapers',
      params: {
        coachBookId: coachBookId,
      },
      requestName: '获取教辅包含定制试卷',
    })
    .then(papers =>
      papers.map(paper => {
        let transPaper = PaperInfo.import(paper)

        transPaper.year = paper.year || null
        transPaper.coachBookId = paper.coachBookId
        transPaper.coachBookName = paper.coachBookName
        transPaper.coachBookCode = paper.coachBookCode
        transPaper.sortCode = paper.sortCode
        transPaper.createTime = paper.createTime
        transPaper.standardQuestionCount = paper.standardQuestionCount || 0
        transPaper.openTime = paper.openTime

        return transPaper
      })
    )
}

export function apiAddCoachBookCustomPapers(requestParams) {
  return ajax.post({
    url: 'ques/coachBook/addCustomPapers',
    params: {
      paperIds: requestParams.paperIds,
      coachBookId: requestParams.coachBookId,
    },
    requestName: '添加定制试卷',
  })
}

export function apiSortCoachBookCustomPapers(requestParams) {
  return ajax.put({
    url: 'ques/coachBook/sortCustomPapers',
    data: requestParams,
    requestName: '调整定制试卷顺序',
  })
}

export function apiRemoveCoachBookCustomPapers(requestParams) {
  return ajax.delete({
    url: 'ques/coachBook/removeCustomPapers',
    params: {
      paperIds: requestParams.paperIds,
      coachBookId: requestParams.coachBookId,
    },
    requestName: '移除定制试卷',
  })
}

export function apiSetCoachBookPapersPublishTime(modifiedCoachBooks) {
  return ajax.post({
    url: 'ques/coachBook/setPaperOpenTime',
    data: modifiedCoachBooks, // Array[Object: { coachBookId: Number, openTime: String, paperId: String }]
    requestName: '设置教辅单元试卷开放显示时间',
  })
}

export function apiSetCoachBookCustomPapersPublishTime(modifiedCoachBooks) {
  return ajax.post({
    url: 'ques/coachBook/setCustomPaperOpenTime',
    data: modifiedCoachBooks, // Array[Object: { coachBookId: Number, openTime: String, paperId: String }]
    requestName: '设置教辅定制试卷开放显示时间',
  })
}

/**
 * 教辅反馈卡
 */
export function apiGetCoachBookSheets(coachBookId) {
  return ajax.get({
    url: 'ques/coachBook/sheets',
    params: {
      coachBookId,
    },
    requestName: '获取教辅包含错题反馈卡',
  })
}

export function apiSortCoachBookSheets(data) {
  return ajax.put({
    url: 'ques/coachBook/sortSheets',
    data: data.map(item => ({
      id: item.id,
      coachBookId: item.coachBookId,
      sortCode: item.sortCode,
    })),
    requestName: '调整反馈卡顺序',
  })
}

export function apiRemoveCoachBookSheets({ coachBookId, sheetIds }) {
  return ajax.delete({
    url: 'ques/coachBook/removeSheets',
    params: {
      coachBookId,
      sheetIds,
    },
    requestName: '删除反馈卡',
  })
}

// 关联反馈卡到试卷
export function apiLinkCoachBookSheetToPaper({ coachBookId, sheetId, paperId }) {
  return ajax.put({
    url: 'ques/coachBook/linkSheetToPaper',
    params: {
      coachBookId,
      sheetId,
      paperId,
    },
  })
}

// 下载错题反馈卡压缩包，仅已关联试卷的反馈卡
export function apiDownloadCoachBookFeedbackSheets(coachBookId, paperIds = []) {
  return ajax.download({
    url: 'ques/coachBook/downloadPaperFeedbackSheets',
    params: {
      coachBookId: coachBookId, // Number(interger)
    },
    data: paperIds,
    requestName: '下载教辅反馈卡',
  })
}

// 下载错题反馈卡压缩包，含未关联试卷的反馈卡
export function apiDownloadCoachBookAllFeedbackSheets(coachBookId, paperIds = []) {
  return ajax.download({
    url: 'ques/coachBook/downloadCoachBookFeedbackSheets',
    params: {
      coachBookId: coachBookId, // Number(interger)
    },
    data: paperIds,
    requestName: '下载教辅反馈卡',
  })
}

export function apiGetSemesterInstitutionActiveCoachBookSchoolList(requestParams) {
  return ajax.get({
    url: 'ques/coachBook/listSchoolsByOrg',
    params: {
      semesterId: requestParams.semester,
      term: requestParams.term,
    },
    requestName: '查询机构某学期已开通教辅的学校列表',
  })
}

export function apiSetCoachbookWrongQuestionMark(requestParams) {
  return ajax.post({
    url: 'ques/coachBook/setWrongQuesMark',
    data: requestParams, // Array [{id: Number, wrongQuesPaperMark: Boolean, wrongQuesQuickMark: Boolean }]
    requestName: '设置教辅错题标记方式',
  })
}

export function apiGetTeachingGradeSubjectCoachBooks({ semesterId, term }) {
  return ajax.get({
    url: 'ques/coachBook/teachingGradeSubjectCoachBooks',
    params: {
      semesterId,
      term,
    },
    requestName: '获取任教相关教辅',
  })
}

export function apiUploadPaperContentPDF(requestParams) {
  return ajax.upload({
    url: 'ques/coachBook/uploadPaperContentPdf',
    params: {
      coachBookId: requestParams.coachBookId, // *Number
      paperId: requestParams.paperId, // *String
    },
    data: {
      file: requestParams.file,
    },
    requestName: '上传教辅单元卷正文pdf',
    isPostRequest: false,
  })
}

export function apiUploadPaperAnswerPDF(requestParams) {
  return ajax.upload({
    url: 'ques/coachBook/uploadPaperAnswerPdf',
    params: {
      coachBookId: requestParams.coachBookId, // *Number
      paperId: requestParams.paperId, // *String
    },
    data: {
      file: requestParams.file,
    },
    requestName: '上传教辅单元卷答案pdf',
    isPostRequest: false,
  })
}

export function apiDownloadCoachBookCustomPapersContentPDFZip(coachBookId, paperIds = []) {
  return ajax.download({
    url: 'ques/coachBook/downloadCoachBookPaperContentPdfZip',
    params: {
      coachBookId: coachBookId, // *Number
    },
    data: paperIds,
    requestName: '下载教辅试卷正文pdf压缩包',
  })
}

export function apiDownloadCoachBookCustomPapersAnswerPDFZip(coachBookId, paperIds = []) {
  return ajax.download({
    url: 'ques/coachBook/downloadCoachBookPaperAnswerPdfZip',
    params: {
      coachBookId: coachBookId, // *Number
    },
    data: paperIds,
    requestName: '下载教辅试卷答案pdf压缩包',
  })
}

export function apiDeletePaperContentPDF(requestParams) {
  return ajax.delete({
    url: 'ques/coachBook/deletePaperContentPdf',
    params: {
      coachBookId: requestParams.coachBookId, // *Number
      paperId: requestParams.paperId, // *String
    },
    requestName: '删除教辅单元卷正文pdf',
  })
}

export function apiDeletePaperAnswerPDF(requestParams) {
  return ajax.delete({
    url: 'ques/coachBook/deletePaperAnswerPdf',
    params: {
      coachBookId: requestParams.coachBookId, // *Number
      paperId: requestParams.paperId, // *String
    },
    requestName: '删除教辅单元卷答案pdf',
  })
}

export function apiUploadPaperListeningAudio(requestParams) {
  return ajax.upload({
    url: 'ques/coachBook/uploadPaperListeningAudio',
    params: {
      coachBookId: requestParams.coachBookId, // *Number
      paperId: requestParams.paperId, // *String
    },
    data: { file: requestParams.file }, // * File
    isPostRequest: false,
    requestName: '上传教辅单元卷听力语音',
  })
}

export function apiDeletePaperListeningAudio(requestParams) {
  return ajax.delete({
    url: 'ques/coachBook/deletePaperListeningAudio',
    params: {
      coachBookId: requestParams.coachBookId, // *Number
      paperId: requestParams.paperId, // *String
    },
    requestName: '删除教辅单元卷听力语音',
  })
}

export function apiDownloadCoachBookCustomPapersListeningAudioZip(coachBookId, paperIds = []) {
  return ajax.download({
    url: 'ques/coachBook/downloadCoachBookPaperListeningAudioZip',
    params: {
      coachBookId: coachBookId, // *Number
    },
    data: paperIds,
    requestName: '下载教辅试卷听力语音压缩包',
  })
}
