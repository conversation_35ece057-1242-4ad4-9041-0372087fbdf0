import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

import { apiCoachBookIngestionGetInfo, apiCoachBookIngestionGetStructure } from '@/api/qlib/coach_book_ingestion'

import CommonProgressEnum from '@/enum/qlib/ingestion/common_progress'
import FileTypeEnum from '@/enum/qlib/ingestion/file_type'
import IngestionStepEnum from '@/enum/qlib/ingestion/step'

export const useCoachBookIngestionStore = defineStore('coach-book-ingestion', () => {
  // 教辅信息
  const coachBookInfo = ref(null)
  // 录入项目信息
  const ingestionInfo = ref(null)
  // 当前步骤
  const currentStep = ref('')
  // 结构
  const structures = ref([])

  function setCoachBookInfo(info) {
    coachBookInfo.value = info
  }

  function setIngestionInfo(info) {
    ingestionInfo.value = info
  }

  function reset() {
    coachBookInfo.value = null
    ingestionInfo.value = null
  }

  async function refreshIngestionInfo() {
    let ingestionInfo = null
    if (coachBookInfo.value) {
      ingestionInfo = await apiCoachBookIngestionGetInfo(coachBookInfo.value.id)
    }
    setIngestionInfo(ingestionInfo)
  }

  function setCurrentStep(step) {
    currentStep.value = step
  }

  function initCurrentStep() {
    if (!ingestionInfo.value) {
      currentStep.value = ''
      return
    }
    let { fileType, filePath, pageImages, pdfToImageStatus } = ingestionInfo.value
    if (!filePath || filePath.length == 0 || !pageImages || pageImages.length == 0) {
      currentStep.value = IngestionStepEnum.Upload.id
      return
    }
    let uploadCompleted = true
    if (fileType == FileTypeEnum.Pdf.id) {
      uploadCompleted = pdfToImageStatus == CommonProgressEnum.Completed.id
    }
    if (!uploadCompleted) {
      currentStep.value = IngestionStepEnum.Upload.id
      return
    }
    currentStep.value = IngestionStepEnum.Structure.id
  }

  async function refreshStructures() {
    if (!ingestionInfo.value) {
      structures.value = []
      return
    }
    structures.value = await apiCoachBookIngestionGetStructure(ingestionInfo.value.id)
  }

  return {
    coachBookInfo,
    ingestionInfo,
    currentStep,
    structures,
    setCoachBookInfo,
    setIngestionInfo,
    reset,
    refreshIngestionInfo,
    setCurrentStep,
    initCurrentStep,
    refreshStructures,
  }
})
