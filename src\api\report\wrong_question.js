import ajax from '@/api/ajax'

export function apiGetFeedbackListOfGrade({
  semesterId,
  term,
  gradeId,
  subjectId,
  keyword,
  scoreRate,
  currentPage,
  pageSize,
}) {
  return ajax.get({
    url: 'report/wrongQues/listGradeExam',
    params: {
      semesterId,
      term,
      gradeId,
      subjectId,
      examName: keyword,
      scoreRate,
      pageNum: currentPage,
      pageSize,
    },
    requestName: '获取年级错题项目列表',
  })
}

export function apiGetFeedbackListOfClass({
  semesterId,
  term,
  gradeId,
  classId,
  subjectId,
  keyword,
  scoreRate,
  currentPage,
  pageSize,
}) {
  return ajax.get({
    url: 'report/wrongQues/listClassExam',
    params: {
      semesterId,
      term,
      gradeId,
      classId,
      subjectId,
      examName: keyword,
      scoreRate,
      pageNum: currentPage,
      pageSize,
    },
    requestName: '获取班级错题项目列表',
  })
}

export function apiGetFeedbackListOfStudent({
  semesterId,
  term,
  gradeId,
  classId,
  studentId,
  subjectId,
  keyword,
  scoreRate,
  currentPage,
  pageSize,
}) {
  return ajax.get({
    url: 'report/wrongQues/listStudentExam',
    params: {
      semesterId,
      term,
      gradeId,
      classId,
      studentId,
      subjectId,
      examName: keyword,
      scoreRate,
      pageNum: currentPage,
      pageSize,
    },
    requestName: '获取学生错题项目列表',
  })
}

export function apiGetGradeWrongQuestions({ examSubjectIds, scoreRate }) {
  return ajax.get({
    url: 'report/wrongQues/listGradeQuestions',
    params: {
      examSubjectIds,
      scoreRate,
    },
    requestName: '获取项目年级错题',
  })
}

export function apiGetClassWrongQuestions({ examSubjectIds, scoreRate, classId }) {
  return ajax.get({
    url: 'report/wrongQues/listClassQuestions',
    params: {
      examSubjectIds,
      scoreRate,
      classId,
    },
    requestName: '获取项目班级错题',
  })
}

export function apiGetStudentWrongQuestions({ examSubjectIds, scoreRate, classId, studentId }) {
  return ajax.get({
    url: 'report/wrongQues/listStudentQuestions',
    params: {
      examSubjectIds,
      scoreRate,
      classId,
      studentId,
    },
    requestName: '获取项目学生错题',
  })
}
