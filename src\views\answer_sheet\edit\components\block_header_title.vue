<template>
  <div class="exam-info-title" :class="{ editable }">
    <div
      ref="content"
      class="content"
      style="height: 100%; overflow: hidden"
      :style="contentStyle"
      @mousedown="showTextArea"
    >
      {{ title }}<span v-if="editable && !title" class="placeholder">请输入答题卡标题</span>
    </div>
    <textarea
      v-if="editable"
      ref="textArea"
      class="input no-print"
      :style="contentStyle"
      @blur="hideTextArea"
    ></textarea>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { Title } from '@/helpers/answer_sheet/header'

  export default {
    props: {
      editable: {
        type: Boolean,
        required: true,
      },
    },
    computed: {
      ...mapGetters('answerSheet', ['title']),
      contentStyle() {
        return Title.getContentStyle()
      },
    },
    methods: {
      showTextArea(e) {
        // 找光标位置
        let offset = null
        if (document.caretRangeFromPoint) {
          let range = document.caretRangeFromPoint(e.clientX, e.clientY)
          let textNode = range && range.startContainer
          if (textNode && textNode.nodeType == 3) {
            offset = range.startOffset
            // 考虑文本节点内容空白前缀
            let textNodeValue = textNode.nodeValue
            let textNodeValueTrimStart = textNodeValue.trimStart()
            offset -= textNodeValue.length - textNodeValueTrimStart.length
          }
        }
        let elTextArea = this.$refs['textArea']
        if (elTextArea) {
          elTextArea.value = this.title
          elTextArea.style.display = 'block'
          setTimeout(() => {
            elTextArea.focus()
            if (offset != null) {
              elTextArea.setSelectionRange(offset, offset)
            }
          }, 0)
        }
      },
      hideTextArea() {
        let elTextArea = this.$refs['textArea']
        if (!elTextArea) {
          return
        }
        let value = elTextArea.value.trim()
        if (value != '请输入答题卡标题') {
          this.$store.commit('answerSheet/changeTitle', value)
        }
        if (elTextArea.scrollHeight > Title.getMaxHeight()) {
          this.$Message.warning({
            content: '标题内容过多',
            duration: 0,
            closable: true,
          })
        }
        elTextArea.style.display = 'none'
      },
      getTemplateElements() {
        if (!this.title) {
          return null
        }
        let elContent = this.$refs.content
        if (!elContent) {
          return null
        }
        let widthWithBlank = elContent.clientWidth
        let height = elContent.clientHeight
        elContent.style.display = 'inline-block'
        let width = elContent.clientWidth
        elContent.style.display = null
        return {
          left: Math.floor((widthWithBlank - width) / 2),
          top: 0,
          width,
          height,
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .exam-info-title.editable:hover {
    background-color: $color-iview-table-active-row;
    cursor: text;
  }

  .input {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    display: none;
    width: 100%;
    height: 100%;
    border: none;
    overflow: auto;
    background-color: $color-iview-table-active-row;
    outline: 1px solid $color-info;
  }

  .placeholder {
    color: $color-disabled;
  }
</style>
