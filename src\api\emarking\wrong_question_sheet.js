import ajax from '@/api/ajax'

// 由试卷Id获取错题反馈卡
export function apiGetWrongQuestionSheetByPaperId(paperId) {
  return ajax.get({
    url: `mark/feedbackSheet/byPaperId`,
    params: {
      paperId,
    },
    requestName: '加载错题反馈卡',
  })
}

// 由反馈卡Id获取错题反馈卡
export function apiGetWrongQuestionSheetBySheetId(sheetId) {
  return ajax.get({
    url: 'mark/feedbackSheet/bySheetId',
    params: {
      sheetId,
    },
    requestName: '加载错题反馈卡',
  })
}

// 获取新反馈卡ID
export function apiGetWrongQuestionSheetNewId() {
  return ajax.get({
    url: 'mark/feedbackSheet/newId',
    requestName: '获取新反馈卡Id',
  })
}

// 获取PDFUrl
export function apiGetWrongQuestionSheetPDFUrl(id) {
  return ajax.get({
    url: `mark/feedbackSheet/pdfUrl`,
    params: {
      id,
    },
    requestName: '下载错题反馈卡',
  })
}

// 保存
export function apiSaveWrongQuestionSheet(data) {
  return ajax.upload({
    url: `mark/feedbackSheet/save`,
    data,
    requestName: '保存错题反馈卡',
  })
}

export function apiSaveSchoolWrongQuestionSheet(requestParams) {
  return ajax.upload({
    url: 'mark/feedbackSheet/saveSchoolSheet',
    data: requestParams,
    requestName: '保存校本错题反馈卡',
  })
}

// 删除
export function apiPhysicalDeleteWrongQuestionSheet(sheetId) {
  return ajax.delete({
    url: 'mark/feedbackSheet/physicalDelete',
    params: {
      sheetId,
    },
    requestName: '删除错题反馈卡',
  })
}
