export default {
  name: 'review',
  path: 'review',
  meta: {
    title: '评审活动',
    menu: '评审活动',
    name: '评审活动',
    // roles: user => {
    //   return user.schoolType == 1
    // },
  },
  component: () => import('@/views/review/index.vue'),
  redirect: { name: 'review_home' },
  children: [
    {
      name: 'review_home',
      path: 'home',
      meta: {
        menu: '评审活动列表',
      },
      component: () => import('@/views/review/list/index.vue'),
    },
    {
      name: 'review_create',
      path: 'create',
      meta: {
        menu: '评审活动创建',
      },
      component: () => import('@/views/review/create/create.vue'),
    },
    {
      name: 'review_activity',
      path: 'activity/:activityId',
      meta: {
        menu: '评审活动',
        showHeader: false,
        pageWidth: 'no-max',
      },
      component: () => import('@/views/review/activity/activity.vue'),
      children: [
        {
          name: 'review_detail',
          path: 'detail',
          meta: {
            menu: '评审活动详情',
          },
          component: () => import('@/views/review/detail/detail.vue'),
        },
        {
          name: 'review_personnel_management',
          path: 'personnelManagement',
          meta: {
            menu: '人员管理',
          },
          component: () => import('@/views/review/activity/navs/personnel_management.vue'),
        },
        {
          name: 'review_registration_management',
          path: 'registrationManagement',
          meta: {
            menu: '报名管理',
          },
          component: () => import('@/views/review/activity/navs/registration_management.vue'),
        },
        {
          name: 'review_registration_approval',
          path: 'registrationApproval',
          meta: {
            menu: '报名审核',
          },
          component: () => import('@/views/review/activity/navs/registration_approval.vue'),
        },
        {
          name: 'review_setting',
          path: 'setting',
          meta: {
            menu: '评审设置',
          },
          component: () => import('@/views/review/activity/navs/config_management.vue'),
        },
        {
          name: 'review_monitor',
          path: 'monitor',
          meta: {
            menu: '评审监控',
          },
          component: () => import('@/views/review/activity/navs/monitor.vue'),
        },
        {
          name: 'review_judge',
          path: 'judge',
          meta: {
            menu: '评委管理',
          },
          component: () => import('@/views/review/activity/navs/judge_management.vue'),
        },
        {
          name: 'review_award',
          path: 'award',
          meta: {
            menu: '评审结果',
          },
          component: () => import('@/views/review/activity/navs/award_management.vue'),
        },
      ],
    },
    {
      name: 'review_mark',
      path: 'mark/:categoryId?',
      meta: {
        menu: '评审任务',
        showHeader: false,
        showFooter: false,
        pageWidth: 'no-max',
      },
      component: () => import('@/views/review/activity/mark.vue'),
    },
  ],
}
