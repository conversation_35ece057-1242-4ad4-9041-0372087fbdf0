/**
 * 考试科目状态
 */

import Enum from '@/enum/enum'

export default new Enum({
  UnDefined: {
    id: 0,
    name: '初始化',
  },
  Defined: {
    id: 1,
    name: '已定义',
  },
  Tasking: {
    id: 10,
    name: '分配任务中',
  },
  Tasked: {
    id: 11,
    name: '已分配任务',
  },
  Started: {
    id: 12,
    name: '正在评卷',
  },
  Suspended: {
    id: 13,
    name: '评卷已暂停',
  },
  Finished: {
    id: 14,
    name: '评卷已完成',
  },
  ReportWaiting: {
    id: 15,
    name: '待生成成绩',
  },
  Reporting: {
    id: 16,
    name: '成绩生成中',
  },
  ReportFailed: {
    id: 17,
    name: '成绩生成失败',
  },
  ReportSucceeded: {
    id: 18,
    name: '成绩已生成',
  },
})
