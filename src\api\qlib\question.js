import ajax from '@/api/ajax'
import { Question } from '@/helpers/qlib/question'

/**
 * 试题查询
 */
export function apiGetQuestionsByChapter(params) {
  let sendParams = {
    stage: params.stage,
    subject: params.subject,
    bookId: params.bookId || '',
    chapter: params.chapter || '',
    ques_type: params.questionType || '',
    source: params.paperType || '',
    difficulty: params.difficulty || '',
    year: params.year || '',
    region: (params.region && params.region[params.region.length - 1]) || '',
    keyword: params.keyword,
    sort_by: params.sortBy === 'general' ? undefined : params.sortBy, // String 排序规则 1. useCount(组卷次数) 2. answerCount(作答次数)
    isDesc: params.sortBy === 'general' || !params.sortOrder ? undefined : params.sortOrder === 'desc', // Boolean 默认为true
    size: params.pageSize,
    page: params.currentPage,
  }
  return ajax
    .get({
      url: `ques/question/byChapter`,
      params: sendParams,
      requestName: '加载试题',
    })
    .then(data => {
      return {
        total: data.total || 0,
        questionList: (data.records || []).map(item => {
          return Question.import(item)
        }),
      }
    })
}

export function apiGetQuestionsByKnowledge(params) {
  let sendParams = {
    stage: params.stage,
    subject: params.subject,
    knowledge: params.knowledge || '',
    ques_type: params.questionType || '',
    source: params.paperType || '',
    difficulty: params.difficulty || '',
    year: params.year || '',
    region: params.region[params.region.length - 1] || '',
    keyword: params.keyword || undefined,
    gradeId: params.gradeId || undefined,
    sort_by: params.sortBy === 'general' ? undefined : params.sortBy, // String 排序规则 1. useCount(组卷次数) 2. answerCount(作答次数)
    isDesc: params.sortBy === 'general' || !params.sortOrder ? undefined : params.sortOrder === 'desc', // Boolean 默认为true
    size: params.pageSize,
    page: params.currentPage,
  }
  return ajax
    .get({
      url: `ques/question/byKnowledge`,
      params: sendParams,
      requestName: '加载试题',
    })
    .then(data => {
      return {
        total: data.total || 0,
        questionList: (data.records || []).map(item => {
          return Question.import(item)
        }),
      }
    })
}

export function apiGetQuestionVariants(requestParams) {
  return ajax
    .post({
      url: 'ques/question/variants',
      params: {
        page: requestParams.currentPage || 1, // Number
        size: requestParams.pageSize || 10, // Number
        stage: requestParams.stageId, // Number
        subject: requestParams.subjectId, // Number
        gradeId: requestParams.gradeId, // Number
        year: requestParams.year, // Number
        difficulty: requestParams.difficulty, // Number
        ques_type: requestParams.questionType, // Number: 试题类型
        source: requestParams.paperType, // Number: 试题来源(试卷类型)
        region:
          (requestParams.region &&
            requestParams.region.length &&
            requestParams.region[requestParams.region.length - 1]) ||
          undefined, // Number: 地区
        ignorePaperId: requestParams.ignorePaperId, // 查询结构筛去某卷的原题和变式题
        keyword: requestParams.keyword || undefined,
      },
      data: {
        knowIds: requestParams.knowledgeIds, // Array[String]
        paperIds: requestParams.paperIds, // Array[String]
      },
      requestName: '搜索变式题',
    })
    .then(response => {
      return {
        total: response.total || 0,
        questionList: (response.records || []).map(record => {
          return Question.import(record)
        }),
      }
    })
}

export function apiSearchSimilarQuestion({ question, pageSize }) {
  return question
    .export()
    .then(data => {
      return ajax.post({
        url: 'ques/question/similarQuestions',
        params: {
          size: pageSize,
        },
        data,
      })
    })
    .then(data => {
      return (data || []).map(item => {
        return Question.import(item)
      })
    })
}

export function apiSearchSimilarQuestionKnowledges({ question, pageSize }) {
  return question.export().then(data => {
    return ajax.post({
      url: 'ques/question/similarQuestionsKnowledge',
      params: {
        size: pageSize,
      },
      data,
    })
  })
}

export function apiGetFavoriteQuestions(params) {
  let sendParams = {
    stage: params.stage,
    subject: params.subject,
    difficulty: params.difficulty || null,
    type: params.questionType || null,
    size: params.pageSize,
    page: params.currentPage,
  }
  return ajax
    .get({
      url: `ques/question/fav`,
      params: sendParams,
      requestName: '获取收藏试题',
    })
    .then(data => {
      return {
        total: data.total || 0,
        questions: (data.records || []).map(q => {
          return Question.import(q)
        }),
      }
    })
}

export function apiGetQuestionById(qid) {
  return ajax
    .get({
      url: `ques/question/byQid/${qid}`,
      requestName: '获取试题',
    })
    .then(q => {
      return Question.import(q)
    })
}

/**
 * 获取一批雪花Id
 */
export function apiGetNewIds(size) {
  return ajax.get({
    url: `ques/question/newIds`,
    params: {
      size,
    },
  })
}

/**
 * 收藏操作
 */
export function apiToggleFavorQuestion(qid) {
  return ajax.put({
    url: `ques/question/fav/${qid}`,
    requestName: '设置收藏试题',
  })
}

/**
 * 编辑操作
 */

/**
 * 管理员操作
 */
// 修改题目
export function apiChangeQuestion(question) {
  return question
    .export()
    .then(data => {
      return ajax.put({
        url: '/ques/question/adminSave',
        data,
      })
    })
    .then(data => {
      return Question.import(data)
    })
}

// 修改知识点
export function apiChangeQuestionsKnowledges(questions) {
  let data = questions.map(q => ({
    questionId: q.id,
    branches: q.branches.map(b => ({
      branchId: b.id,
      knowledgeIds: b.knowledges.map(k => k.id),
    })),
  }))
  return ajax.put({
    url: '/ques/question/adminChangeKnowledge',
    data,
    requestName: '修改知识点',
  })
}

// 修改章节
export function apiChangeQuestionsChapters(questions) {
  let data = questions.map(q => {
    let chapterIds = []
    q.chapters.forEach(book => {
      book.chapters.forEach(c => {
        chapterIds.push(c.id)
      })
    })
    return {
      questionId: q.id,
      chapterIds,
    }
  })
  return ajax.put({
    url: '/ques/question/adminChangeChapter',
    data,
    requestName: '修改知识点',
  })
}

/**
 * 组卷操作
 */
// 智能组卷
export function apiComposePaperIntelligently(data) {
  return ajax
    .post({
      url: `ques/question/intelligent`,
      data,
      requestName: '智能组卷',
    })
    .then(questions => {
      return (questions || []).map(q => Question.import(q))
    })
}

// 学情组卷
export function apiGetTrainQuestions({ trainType, questions }) {
  return ajax
    .post({
      url: 'ques/question/wrongQuesTrain',
      data: { trainType, questions },
      requestName: `获取${trainType == 'redo' ? '重做' : trainType == 'expand' ? '拓展' : ''}试题`,
    })
    .then(questions => (questions || []).map(q => Question.import(q)))
}
