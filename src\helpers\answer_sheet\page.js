/**
 * 页面
 */

import TemplatePage from '@/helpers/scan_template_define/template_page'
import TemplatePageParam from '@/helpers/scan_template_define/template_page_param'
import TemplateStudentInfo from '@/helpers/scan_template_define/template_student_info'
import TemplateRect from '@/helpers/scan_template_define/template_rect'
import TemplateSubjectiveArea from '@/helpers/scan_template_define/template_subjective_area'

import { groupArray } from '@/utils/array'

import PageBlockTypeEnum from '@/enum/answer_sheet/page_block_type'
import ScoreTypeEnum from '@/enum/answer_sheet/score_type'

import { PixelsPerMillimeter, SubjectiveBlockExpandMillimeters } from '@/const/answer_sheet'
export default class Page {
  constructor() {
    // 页面序号，从0开始
    this.pageIndex = 0
    // 页头，Vue实例
    this.header = null
    // 页脚，Vue实例
    this.footer = null
    // 页面块
    this.blocks = []
    // 最后一个页面块与顶部的距离
    this.top = 0
  }

  // 添加页面块
  addBlock(block) {
    block.id = `${block.id}_pageIndex-${this.pageIndex}`
    block.pageIndex = this.pageIndex
    block.top = this.top
    this.blocks.push(block)
    this.top += block.height
  }

  // 导出扫描模板数据
  exportScanTemplatePage(pageSize, pageLeft) {
    let page = new TemplatePage()
    page.pageParam = new TemplatePageParam()
    page.pageParam.anchorMarks = [null, null, null, null]
    page.studentInfo = new TemplateStudentInfo()

    // 页面参数和考生信息
    let instances = [
      this.header,
      this.footer,
      (this.blocks.find(x => x.type === PageBlockTypeEnum.ExamInfo.id) || {}).instance,
    ]
    instances.forEach(instance => {
      if (!instance) {
        return
      }
      let {
        anchorTopLeft,
        anchorTopRight,
        anchorBottomLeft,
        anchorBottomRight,
        titleArea,
        pageMark,
        admissionOmrArea,
        admissionBarcodeArea,
        admissionHandwritingArea,
        missingMarkArea,
        qrcodeArea,
        qrcodeText,
      } = instance.getTemplateElements(pageLeft)
      if (anchorTopLeft) {
        page.pageParam.anchorMarks[0] = anchorTopLeft
      }
      if (anchorTopRight) {
        page.pageParam.anchorMarks[1] = anchorTopRight
      }
      if (anchorBottomLeft) {
        page.pageParam.anchorMarks[3] = anchorBottomLeft
      }
      if (anchorBottomRight) {
        page.pageParam.anchorMarks[2] = anchorBottomRight
      }
      if (titleArea) {
        page.pageParam.titleArea = titleArea
      }
      if (pageMark) {
        page.pageParam.pageMark = pageMark
      }
      if (admissionOmrArea) {
        page.studentInfo.admissionOmrArea = admissionOmrArea
      }
      if (admissionBarcodeArea) {
        page.studentInfo.admissionBarcodeArea = admissionBarcodeArea
      }
      if (admissionHandwritingArea) {
        page.studentInfo.admissionHandwritingArea = admissionHandwritingArea
      }
      if (missingMarkArea) {
        page.studentInfo.missingMarkArea = missingMarkArea
      }
      if (qrcodeArea) {
        page.pageParam.qrcodeArea = qrcodeArea
        page.pageParam.qrcodeText = qrcodeText || ''
      }
    })

    // 客观题
    this.blocks
      .filter(p => p.type == PageBlockTypeEnum.Objective.id || p.type == PageBlockTypeEnum.ContentObjective.id)
      .forEach(block => {
        let omrAreas = block.instance.getTemplateElements(pageLeft)
        page.objectiveAreas.push(...omrAreas)
      })

    // 主观题
    let subjectiveBlockExpandPixels = PixelsPerMillimeter * SubjectiveBlockExpandMillimeters
    this.blocks
      .filter(p =>
        [
          PageBlockTypeEnum.FillBlank.id,
          PageBlockTypeEnum.Essay.id,
          PageBlockTypeEnum.ChineseWriting.id,
          PageBlockTypeEnum.EnglishWriting.id,
        ].includes(p.type)
      )
      .forEach(pageBlock => {
        // 区域位置
        let areaLeft = pageLeft + pageSize.left
        let areaTop = pageBlock.top + pageSize.top
        let rect = {
          x: areaLeft - subjectiveBlockExpandPixels,
          y: areaTop - subjectiveBlockExpandPixels,
          width: pageSize.width - pageSize.left - pageSize.right + subjectiveBlockExpandPixels * 2,
          height: pageBlock.height + subjectiveBlockExpandPixels * 2,
        }

        // 打分框
        let blockScoreAreas = pageBlock.instance.getScoreAreas(areaLeft, areaTop)
        blockScoreAreas.forEach(area => {
          if (area._extra.scoreType == ScoreTypeEnum.Number.id) {
            page.manualScoreAreas.push(area)
          } else if (area._extra.scoreType == ScoreTypeEnum.Bar.id) {
            page.barScoreAreas.push(area)
          } else if (area._extra.scoreType == ScoreTypeEnum.TrueFalse.id) {
            page.trueFalseScoreAreas.push(area)
          }
        })

        // 选做标记，只有解答题题组才有
        let isSelect = Boolean(pageBlock.questionGroup.selectGroupName)
        if (isSelect) {
          let selectMarkAreas =
            pageBlock.type == PageBlockTypeEnum.Essay.id ? pageBlock.instance.getSelectMarkAreas(areaLeft, areaTop) : []
          page.selectMarkAreas.push(...selectMarkAreas)
        }

        // 分题块，非选做题只有一个题块，选做题每题一个题块
        let questionsList = []
        if (isSelect) {
          questionsList = groupArray(pageBlock.questionGroup.questions, q => q.questionCode).map(g => g.group)
        } else {
          questionsList = [pageBlock.questionGroup.questions]
        }
        questionsList.forEach(questions => {
          // 题目名称拼接
          let questionNames = questions
            .map(q => (q.branchCode > 0 ? `${q.questionCode}.${q.branchCode}` : `${q.questionCode}`))
            .join(',')

          // 区域
          let subjectiveArea = new TemplateSubjectiveArea()
          subjectiveArea.id = `SubjectiveArea-${questionNames}-${pageBlock.part}`
          subjectiveArea.area = TemplateRect.createFromAnswerSheetPageRect(rect.x, rect.y, rect.width, rect.height)
          subjectiveArea._extra = {
            questions,
            questionNames,
          }
          page.subjectiveAreas.push(subjectiveArea)
        })
      })

    return page
  }
}
