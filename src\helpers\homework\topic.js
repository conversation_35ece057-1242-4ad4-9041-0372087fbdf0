import { Paper } from './paper'
import { numberToChinese } from '@/utils/number'
import { randomId } from '@/utils/string'
import QuestionTypeEnum from '@/enum/qlib/branch_type'

const MaxQuestionCode = 200
const MaxQuestionScore = 1000

export class Topic extends Paper {
  constructor() {
    super()
    this.allQuestions = []
  }

  get distinctTopicQuestionCodes() {
    let questions = []
    this.allQuestions.forEach(q => {
      questions.push({
        topicCode: q.topicCode,
        questionCode: q.questionCode,
      })
    })
    questions.sort((a, b) => a.questionCode - b.questionCode)
    return questions
  }

  addQuestionsByRules(rules) {
    if (rules.length === 0) {
      return {
        message: '请添加题目',
      }
    }

    let questionTypeIds = QuestionTypeEnum.getIds()
    let trueOrFalseQuestionTypeId = QuestionTypeEnum.TrueOrFalse.id

    // 检查字段本身约束
    for (let i = 0; i < rules.length; i++) {
      let rule = rules[i]
      let prefix = `第${i + 1}行：`

      if (!rule.topicCode) {
        return {
          message: prefix + '未选择大题',
        }
      }

      if (!(rule.questionTypeId && questionTypeIds.includes(rule.questionTypeId))) {
        return {
          message: prefix + '未选择题型',
        }
      }

      if (rule.isObjective && rule.questionTypeId !== trueOrFalseQuestionTypeId) {
        if (!(Number.isInteger(rule.optionCount) && rule.optionCount >= 2 && rule.optionCount <= 10))
          return {
            message: prefix + '未选择选项个数',
          }
      }

      rule.codeFrom = Number(rule.codeFrom)
      rule.codeTo = Number(rule.codeTo)
      rule.score = Number(rule.score)
      rule.optionCount = Number(rule.optionCount)

      if (
        !(Number.isInteger(rule.codeFrom) && rule.codeFrom >= 1) ||
        !(Number.isInteger(rule.codeTo) && rule.codeTo >= 1)
      ) {
        return {
          message: prefix + '题号必须是正整数',
        }
      }
      if (rule.codeFrom > rule.codeTo) {
        return {
          message: prefix + '开始题号不能大于结束题号',
        }
      }
      if (rule.codeFrom > MaxQuestionCode || rule.codeTo > MaxQuestionCode) {
        return {
          message: prefix + '题号过大',
        }
      }

      if (!(rule.score > 0)) {
        return {
          message: prefix + '分数必须是正数',
        }
      } else if (rule.score > MaxQuestionScore) {
        return {
          message: prefix + '分数过大',
        }
      } else if (Math.floor(rule.score * 10) != rule.score * 10) {
        return {
          message: prefix + '分数最多精确到1位小数',
        }
      }
    }

    // 检查重复
    let newQuestions = []
    rules.forEach(rule => {
      for (let i = rule.codeFrom; i <= rule.codeTo; i++) {
        newQuestions.push({
          topicBlockId: rule.topicBlockId,
          topicCode: rule.topicCode,
          topicName: rule.topicName,
          questionCode: i,
          questionType: QuestionTypeEnum.getEntryById(rule.questionTypeId),
          optionCount: rule.questionTypeId === trueOrFalseQuestionTypeId ? 2 : rule.optionCount,
          score: rule.score,
          optionList: rule.optionList.map(op => ({
            id: op.id,
            name: op.name,
            isSelected: op.isSelected,
          })),
          isObjective: rule.isObjective,
          blanks: rule.blanks.map(b => ({
            ...b,
          })),
          initialScore: rule.initialScore,
        })
      }
    })
    newQuestions.sort((a, b) => a.questionCode - b.questionCode)

    // 题目内部检查重复
    let newQuestionCodes = newQuestions.map(q => q.questionCode)
    let checkInnerDuplicateMessage = this.getDuplicatedCodesString(newQuestionCodes)
    if (checkInnerDuplicateMessage) {
      return {
        message: `题号${checkInnerDuplicateMessage}重复`,
      }
    }

    // 和现有题目对比检查重复
    let allQuestionCodes = [...newQuestionCodes, ...this.distinctTopicQuestionCodes.map(q => q.questionCode)]
    let checkAllDuplicatedMessage = this.getDuplicatedCodesString(allQuestionCodes)
    if (checkAllDuplicatedMessage) {
      return {
        message: `题号${checkAllDuplicatedMessage}和现有题号重复`,
      }
    }

    // 通过检查
    newQuestions = newQuestions.map(q => ({
      topicBlockId: q.topicBlockId,
      questionId: randomId(),
      topicCode: q.topicCode,
      topicCodeInChinese: numberToChinese(q.topicCode),
      topicName: q.topicName || numberToChinese(q.topicCode),
      questionCode: q.questionCode,
      questionType: q.questionType,
      fullScore: q.score,
      optionCount: q.optionCount,
      optionList: q.optionList,
      standardAnswer: '',
      answerScoreList: '',
      originalQuestionId: 0,
      originalBranchId: 0,
      isObjective: q.isObjective,
      blanks: q.blanks,
      initialScore: q.initialScore,
    }))
    this.allQuestions.push(...newQuestions)
    this.sortQuestionCodes()

    return {
      message: '',
      questions: newQuestions,
    }
  }
}
