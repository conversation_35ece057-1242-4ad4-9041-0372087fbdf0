import { duplicate, difference, differenceBy, groupArray } from '@/utils/array'
import { sortQuestionFunction } from './miscellaneous'

export class Paper {
  constructor(params) {
    this.examId = (params && params.examId) || ''
    this.examSubjectId = (params && params.examSubjectId) || ''
    // 绑定试卷的Id
    this.bindPaperId = (params && params.bindPaperId) || ''
    // 绑定试卷的名称
    this.bindPaperName = (params && params.bindPaperName) || ''
    // 客观题
    this.objectiveQuestions = (params && params.objectiveQuestions) || []
    // 主观题
    this.subjectiveQuestions = (params && params.subjectiveQuestions) || []
    // 题块
    this.blocks = (params && params.blocks) || []

    this.sortQuestionsByTopic()
  }

  /**
   * 所有题目
   */
  get questions() {
    return [...this.objectiveQuestions, ...this.subjectiveQuestions].sort(sortQuestionFunction)
  }

  /**
   * 是否有自定义题名
   */
  get hasCustomQuestionBranchName() {
    return this.questions.some(q => q.questionName !== q.defaultQuestionName || q.branchName !== q.defaultBranchName)
  }

  /**
   * 题目排序
   * 此函数将题名依次按大题号、题号、小题号排序，与仅按题号、小题号排序（sortQuestionFunction）不同
   */
  sortQuestionsByTopic() {
    let sortFunction = (a, b) => {
      if (a.topicCode < b.topicCode) {
        return -1
      } else if (a.topicCode > b.topicCode) {
        return 1
      } else {
        return sortQuestionFunction(a, b)
      }
    }
    this.objectiveQuestions.sort(sortFunction)
    this.subjectiveQuestions.sort(sortFunction)
  }

  /**
   * 检查新题
   */
  checkNewQuestions(newQuestions) {
    // 检查题目本身约束
    let message = newQuestions
      .map(q => {
        let err = q.check()
        return err ? `${q.fullCode}题：${err}` : ''
      })
      .filter(Boolean)
      .join('<br>')
    if (message) {
      return message
    }

    let err = this.getErrorsBetweenQuestions(newQuestions)
    if (err.questionCodeDuplicate.length > 0) {
      return `题号重复：${err.questionCodeDuplicate.join('、')}`
    }
    if (err.fullCodeDuplicate.length > 0) {
      return `小题号重复：${err.fullCodeDuplicate.join('、')}`
    }
    if (err.questionNameDuplicate.length > 0) {
      return `题名重复：${err.questionNameDuplicate.join('、')}`
    }
    if (err.branchNameDuplicate.length > 0) {
      return `小题名重复：${err.branchNameDuplicate.join('、')}`
    }
    if (err.questionNameDuplicateWithBranchName.length > 0) {
      return `小题名与题名重复：${err.questionNameDuplicateWithBranchName.join('、')}`
    }
    // 将与新旧题目关系检查无关的检查步骤调前
    if (err.branchCodeShouldBeZero.length > 0) {
      return `以下题号的题目没有小题，其小题号应为0，请检查：${err.branchCodeShouldBeZero.join('、')}`
    }
    if (err.branchNameShouldSameWithQuestionName.length > 0) {
      return `以下题号的题目没有小题，其小题名应与题名相同，请检查：${err.branchNameShouldSameWithQuestionName.join(
        '、'
      )}`
    }
    if (err.questionNamesShouldBeSame.length > 0) {
      return `以下题号的题目有多个小题，各小题间题名应相同，请检查：${err.questionNamesShouldBeSame.join('、')}`
    }
    if (err.branchCodesShouldStartWithOne.length > 0) {
      return `以下题号的题目有多个小题，各小题小题号应从1往下顺延，请检查：${err.branchCodesShouldStartWithOne.join(
        '、'
      )}`
    }

    err = this.getErrorsBetweenQuestions(newQuestions.concat(this.questions))
    if (err.questionCodeDuplicate.length > 0) {
      return `题号与现有题号重复：${err.questionCodeDuplicate.join('、')}`
    }
    if (err.fullCodeDuplicate.length > 0) {
      return `小题号与现有小题号重复：${err.fullCodeDuplicate.join('、')}`
    }
    if (err.questionNameDuplicate.length > 0) {
      return `题名与现有题名重复：${err.questionNameDuplicate.join('、')}`
    }
    if (err.branchNameDuplicate.length > 0) {
      return `小题名与现有小题名重复：${err.branchNameDuplicate.join('、')}`
    }
    if (err.questionNameDuplicateWithBranchName.length > 0) {
      let duplicateQuestionName = err.questionNameDuplicateWithBranchName.filter(name =>
        newQuestions.some(q => q.questionName === name)
      )
      if (duplicateQuestionName.length > 0) {
        return `题名与现有小题名重复：${duplicateQuestionName.join('、')}`
      }
      let duplicateBranchName = err.questionNameDuplicateWithBranchName.filter(name =>
        newQuestions.some(q => q.branchName === name)
      )
      if (duplicateBranchName.length > 0) {
        return `小题名与现有题名重复：${duplicateBranchName.join('、')}`
      }
    }
  }

  getErrorsBetweenQuestions(questions) {
    let questionGroups = groupArray(questions, q => q.questionCode).sort((a, b) => a.key - b.key)

    // 题号重复
    let questionCodeDuplicate = []
    // 小题号重复
    let fullCodeDuplicate = duplicate(questions.map(q => q.fullCode))
    // 题名重复
    let questionNameDuplicate = duplicate(questionGroups.map(g => g.group[0].questionName))
    // 小题名重复
    let branchNameDuplicate = duplicate(questions.map(q => q.branchName))
    // 题名与小题名重复
    let questionNameDuplicateWithBranchName = []
    // 无小题时小题号应为0
    let branchCodeShouldBeZero = []
    // 无小题时小题名应与题名相同
    let branchNameShouldSameWithQuestionName = []
    // 有小题时各小题题名应一致
    let questionNamesShouldBeSame = []
    // 有小题时小题号应从1往下顺延
    let branchCodesShouldStartWithOne = []

    let questionNameAndBranchNames = []
    questionGroups.forEach(g => {
      let firstQuestion = g.group[0]
      if (g.group.length === 1) {
        questionNameAndBranchNames.push(firstQuestion.questionName)

        if (firstQuestion.branchCode !== 0) {
          branchCodeShouldBeZero.push(firstQuestion.questionCode)
        }
        if (firstQuestion.questionName !== firstQuestion.branchName) {
          branchNameShouldSameWithQuestionName.push(firstQuestion.questionCode)
        }
      } else {
        questionNameAndBranchNames.push(firstQuestion.questionName)
        questionNameAndBranchNames.push(...g.group.map(q => q.branchName))

        if (g.group.some(q => q.branchCode === 0)) {
          questionCodeDuplicate.push(firstQuestion.questionCode)
        }
        if (g.group.some(q => q.questionName !== firstQuestion.questionName)) {
          questionNamesShouldBeSame.push(firstQuestion.questionCode)
        }
        let sortedBranchCodes = g.group.map(q => q.branchCode).sort((a, b) => a - b)
        if (sortedBranchCodes[0] !== 1 || sortedBranchCodes[sortedBranchCodes.length - 1] !== g.group.length) {
          branchCodesShouldStartWithOne.push(firstQuestion.questionCode)
        }
      }
    })
    questionNameDuplicateWithBranchName = duplicate(questionNameAndBranchNames)

    return {
      questionCodeDuplicate,
      fullCodeDuplicate,
      questionNameDuplicate,
      branchNameDuplicate,
      questionNameDuplicateWithBranchName,
      branchCodeShouldBeZero,
      branchNameShouldSameWithQuestionName,
      questionNamesShouldBeSame,
      branchCodesShouldStartWithOne,
    }
  }

  /**
   * 对比新旧题目变更
   */
  getChangedQuestions(oldQuestions, key) {
    let newQuestions = this[key]
    let result = {
      addQuestions: differenceBy(newQuestions, oldQuestions, q => q.fullCode),
      deleteQuestions: differenceBy(oldQuestions, newQuestions, q => q.fullCode),
    }

    let mayChangeQuestionPairs = newQuestions
      .map(q => {
        let oldQuestion = oldQuestions.find(x => x.fullCode === q.fullCode)
        return oldQuestion
          ? {
              newQuestion: q,
              oldQuestion,
            }
          : null
      })
      .filter(Boolean)

    let fields = {}
    if (key === 'objectiveQuestions') {
      fields = {
        changeQuestionNameQuestions: q => q.questionName,
        changeBranchNameQuestions: q => q.branchName,
        changeFullScoreQuestions: q => q.fullScore,
        changeIsAdditionalQuestions: q => q.isAdditional,
        changeBranchTypeIdQuestions: q => q.branchTypeId,
        changeOptionCountQuestions: q => q.optionCount,
        changeStandardAnswerQuestions: q => q.standardAnswer,
        changeAnswerScoreListQuestions: q => q.answerScoreList,
        changeAutoScoreTypeIdQuestions: q => q.autoScoreTypeId,
      }
    } else if (key === 'subjectiveQuestions') {
      fields = {
        changeQuestionNameQuestions: q => q.questionName,
        changeBranchNameQuestions: q => q.branchName,
        changeFullScoreQuestions: q => q.fullScore,
        changeIsAdditionalQuestions: q => q.isAdditional,
      }
    }

    let changedQuestions = []
    Object.keys(fields).forEach(key => {
      let valueFunction = fields[key]
      result[key] = mayChangeQuestionPairs
        .filter(item => valueFunction(item.newQuestion) !== valueFunction(item.oldQuestion))
        .map(item => {
          if (changedQuestions.every(q => q.questionId !== item.newQuestion.questionId)) {
            changedQuestions.push(item.newQuestion)
          }
          return {
            question: item.newQuestion,
            newValue: valueFunction(item.newQuestion),
            oldValue: valueFunction(item.oldQuestion),
          }
        })
        .sort(sortQuestionFunction)
    })
    result.changedQuestions = changedQuestions.sort(sortQuestionFunction)

    return result
  }

  /**
   * 添加题目
   */
  addQuestions(newQuestions, key) {
    let message = this.checkNewQuestions(newQuestions)
    if (message) {
      throw message
    }

    let oldQuestions = this[key]
    this[key].push(...newQuestions)
    this.sortQuestionsByTopic()
    return this.getChangedQuestions(oldQuestions, key)
  }

  /**
   * 按大题删除题目
   */
  deleteTopic(topicCode, key) {
    let oldQuestions = this[key]
    this[key] = this[key].filter(q => q.topicCode !== topicCode)
    this.sortQuestionsByTopic()
    return this.getChangedQuestions(oldQuestions, key)
  }

  /**
   * 按大题修改题目
   */
  changeTopicQuestions(topicCode, newQuestions, key) {
    // 先删除大题再添加
    let oldQuestions = this[key]
    this[key] = this[key].filter(q => q.topicCode !== topicCode)
    try {
      this.addQuestions(newQuestions, key)
    } catch (err) {
      this[key] = oldQuestions
      throw err
    }
    return this.getChangedQuestions(oldQuestions, key)
  }

  /**
   * 修改题目（题名、分数、答案等）
   */
  changeQuestions(newQuestions, key) {
    // 不能增加题目
    let unExistFullCodes = difference(
      newQuestions.map(q => q.fullCode),
      this[key].map(q => q.fullCode)
    )
    if (unExistFullCodes.length > 0) {
      throw `小题号${unExistFullCodes.join('，')}}不存在`
    }

    // 替换旧题目
    let oldQuestions = this[key]
    this[key] = differenceBy(this[key], newQuestions, q => q.fullCode)
    try {
      this.addQuestions(newQuestions, key)
    } catch (err) {
      this[key] = oldQuestions
      throw err
    }

    return this.getChangedQuestions(oldQuestions, key)
  }

  /**
   * 添加客观题
   */
  addObjectiveQuestions(newQuestions) {
    return this.addQuestions(newQuestions, 'objectiveQuestions')
  }

  /**
   * 删除客观题大题
   */
  deleteObjectiveTopic(topicCode) {
    return this.deleteTopic(topicCode, 'objectiveQuestions')
  }

  /**
   * 修改客观题大题
   */
  changeObjectiveTopicQuestions(topicCode, newQuestions) {
    return this.changeTopicQuestions(topicCode, newQuestions, 'objectiveQuestions')
  }

  /**
   * 改客观题题名分数答案等
   */
  changeObjectiveQuestions(newQuestions) {
    return this.changeQuestions(newQuestions, 'objectiveQuestions')
  }

  /**
   * 添加主观题
   */
  addSubjectiveQuestions(newQuestions) {
    return this.addQuestions(newQuestions, 'subjectiveQuestions')
  }

  /**
   * 删除主观题大题
   */
  deleteSubjectiveTopic(topicCode) {
    return this.deleteTopic(topicCode, 'subjectiveQuestions')
  }

  /**
   * 修改主观题大题
   */
  changeSubjectiveTopicQuestions(topicCode, newQuestions) {
    return this.changeTopicQuestions(topicCode, newQuestions, 'subjectiveQuestions')
  }

  /**
   * 改主观题题名分数等
   */
  changeSubjectiveQuestions(newQuestions) {
    return this.changeQuestions(newQuestions, 'subjectiveQuestions')
  }
}
