import ajax from '@/api/ajax'
import store from '@/store'

// 批量设置班级教学科目
export function apiSetClassSubjects(data) {
  return ajax.put({
    url: 'user/class/subjects',
    data,
  })
}

export function apiGetGradeClasses(params) {
  return ajax
    .get({
      url: 'user/class/list',
      params: {
        gradeLevel: params && params.stageId,
        gradeId: params && params.gradeId,
        isInSchool: params && params.isInSchool,
      },
      requestName: '获取班级',
    })
    .then(data => {
      let subjects = store.getters['school/subjects']

      return (data || []).map(c => ({
        stageId: Number(c.gradeLevel),
        gradeId: Number(c.gradeId),
        classId: c.classId,
        className: c.className,
        classCode: c.classCode,
        classType: Number(c.classType),
        teachingSubjects: (c.subjects || []).map(sid => ({
          id: sid,
          name: (subjects.find(s => s.id === sid) || {}).name,
        })),
        foreignSubjects: (c.foreignSubjects || []).map(sid => ({
          id: sid,
          name: (subjects.find(s => s.id === sid) || {}).name,
        })),
        studentCount: c.studentCount || 0,
        graduateYear: c.graduateYear,
      }))
    })
}

export function apiCreateClasses(data) {
  return ajax.post({
    url: 'user/class',
    params: {
      gradeId: data.gradeId,
    },
    data: data.classNos,
    requestName: '创建班级',
  })
}

export function apiCreateTeachingClasses(data) {
  return ajax.post({
    url: 'user/class/teachingClass',
    params: {
      gradeId: data.gradeId,
    },
    data: data.classes.map(cls => ({
      className: cls.className,
      classCode: cls.classCode,
      subjectIds: cls.subjectIds,
    })),
    requestName: '创建教学班',
  })
}

export function apiCreateTeachingPoints(data) {
  return ajax.post({
    url: 'user/class/teachingPoint',
    params: {
      gradeId: data.gradeId,
    },
    data: data.classes.map(cls => ({
      className: cls.className,
      classCode: cls.classCode,
      teachingPoint: cls.teachingPointName,
    })),
    requestName: '创建教学点',
  })
}

export function apiDeleteClasses(classIds) {
  return ajax.delete({
    url: 'user/class',
    data: classIds,
    requestName: '删除班级',
  })
}

export function apiChangeClass(data) {
  return ajax.put({
    url: 'user/class',
    data: {
      classId: data.classId,
      className: data.className,
      teachingPoint: data.teachingPointName || undefined,
      classCode: data.classCode,
      subjectIds: data.subjectIds,
    },
  })
}

export function apiChangeTeachingClass(data) {
  return ajax.put({
    url: 'user/class/teachingClass',
    params: {
      classId: data.classId,
      className: data.className,
      classCode: data.classCode,
    },
    data: data.subjectIds,
    requestName: '修改教学班',
  })
}

/**
 * 科任老师获取任教班级
 */
export function apiTeacherGetClassList() {
  return ajax
    .get({
      url: 'user/class/myClassList',
      requestName: '获取任教班级',
    })
    .then(data => {
      let subjects = store.getters['school/subjects']

      return (data || []).map(c => ({
        stageId: Number(c.gradeLevel),
        gradeId: Number(c.gradeId),
        classId: c.classId,
        className: c.className,
        classCode: c.classCode,
        classType: Number(c.classType),
        clsTeachingSubjects: (c.subjects || []).map(sid => ({
          id: sid,
          name: (subjects.find(s => s.id === sid) || {}).name,
        })),
        foreignSubjects: (c.foreignSubjects || []).map(sid => ({
          id: sid,
          name: (subjects.find(s => s.id === sid) || {}).name,
        })),
        studentCount: c.studentCount || 0,
        graduateYear: c.graduateYear,
      }))
    })
}

/**
 * 科任老师删除学生
 */
export function apiTeacherDeleteClassStudents(data) {
  return ajax.delete({
    url: 'user/class/deleteStudents',
    data,
  })
}

/**
 * 科任老师添加学生
 */
export function apiTeacherAddClassStudents(data) {
  return ajax.post({
    url: 'user/class/addStudents',
    data,
  })
}

/**
 * 科任老师编辑学生
 */
export function apiTeacherEditClassStudent({ studentId, studentName, sex, parents }) {
  return ajax.post({
    url: 'user/class/updateStudent',
    data: {
      studentId,
      studentName,
      sex,
      parents: parents, // Array[Object{realName[string], mobile[string]}] (空数组为删除)
    },
  })
}

/**
 * 高中选科统计
 */
export function apiGetStudentSubjectStats({ gradeId, classId }) {
  return ajax.get({
    url: 'user/class/studentSubjectStats',
    params: {
      gradeId,
      classId: classId || undefined,
    },
    requestName: '获取高中',
  })
}
