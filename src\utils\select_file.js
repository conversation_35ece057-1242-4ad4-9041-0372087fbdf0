import iView from '@/iview'

export function selectFile(props, callback) {
  props = getProps(props)
  let elInput = document.createElement('input')
  elInput.type = 'file'
  elInput.accept = props.accept.text
  elInput.multiple = props.multiple
  elInput.onchange = () => {
    let files = elInput.files
    if (!props.multiple) {
      files = files.slice(0, 1)
    }

    for (let file of files) {
      if (!checkFileType(file, props)) {
        iView.Message.warning({
          content: `请选择以下格式的文件：${props.accept.list.join('，')}`,
          duration: 3,
        })
        return
      }

      if (!checkFileSize(file, props)) {
        iView.Message.warning({
          content: `请选择不大于${props.maxSize.text}的文件`,
          duration: 3,
        })
        return
      }
    }

    callback(props.multiple ? files : files[0])
  }
  elInput.click()
}

function getProps(props) {
  let accept = (props && props.accept) || []
  if (typeof accept === 'string') {
    accept = accept
      .split(',')
      .map(item => item.trim().toLowerCase())
      .filter(Boolean)
  }
  accept = {
    list: accept,
    text: accept.join(','),
  }

  let maxSize
  let unit = {
    K: 1000,
    M: 1000 * 1000,
    G: 1000 * 1000 * 1000,
  }
  let maxSizeText = ((props && props.maxSize) || '').toUpperCase()
  let match = maxSizeText.match(/^(\d+(\.\d*)?)(K|KB|M|MB|G|GB)$/)
  if (match) {
    maxSize = {
      text: maxSizeText,
      value: Number(match[1]) * unit[match[3].substring(0, 1)],
    }
  } else {
    maxSize = {
      text: '10M',
      value: 10 * unit['M'],
    }
  }

  let multiple = (props && props.multiple) || false

  return {
    accept,
    maxSize,
    multiple,
  }
}

function checkFileType(file, props) {
  let dotIndex = file.name.lastIndexOf('.')
  if (dotIndex <= 0) {
    return false
  }

  let extension = file.name.substring(dotIndex).toLowerCase()
  return props.accept.list.includes(extension)
}

function checkFileSize(file, props) {
  return file.size <= props.maxSize.value
}
