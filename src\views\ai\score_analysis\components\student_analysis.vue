<template>
  <div class="analysis-container">
    <Card class="analysis-data-card">
      <template #title>
        <div class="card-header">
          <span class="card-title">学生成绩</span>
          <!-- 数据加载中禁用 -->
          <Select
            v-model="selectedStudentId"
            placeholder="选择学生"
            :disabled="disableSelectStudent"
            style="width: 180px"
            @on-change="changeStudent"
          >
            <Option v-for="s in classStudents" :key="s.studentId" :value="s.studentId">{{ s.studentName }}</Option>
          </Select>
        </div>
      </template>

      <div class="content">
        <div v-if="isLoadingClassStudents || isLoadingStudentAnalysisData" class="loading-spin">
          <Spin />
        </div>

        <div class="content-section section-summary">
          <div class="section-content">
            <div class="data-item data-item-score">
              <template v-if="showLevel">
                <div class="data-label">等级</div>
                <div class="data-value">{{ summary?.level != null ? summary.level : '--' }}</div>
              </template>
              <template v-else>
                <div class="data-label">总分</div>
                <div class="data-value">{{ summary?.score != null ? summary.score : '--' }}</div>
              </template>
            </div>
            <div class="data-item data-item-avg">
              <div class="data-label">班平均分</div>
              <div class="data-value">
                {{ summary?.classAvgScore != null ? summary.classAvgScore.toFixed(1) : '--' }}
              </div>
            </div>
            <div class="data-item data-item-rank">
              <div class="data-label">班排名</div>
              <div class="data-value">
                <span class="rank-value">{{ summary?.classRank != null ? summary.classRank : '--' }}</span>
                <span class="rank-total">{{ summary?.classRank != null ? '/ ' + summary.classStatCount : '' }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="content-section section-question">
          <div class="section-title">小题得分</div>
          <div class="section-content">
            <Table :columns="questionTableColumns" :data="questions" stripe border></Table>
          </div>
        </div>

        <div class="content-section section-knowledge">
          <div class="section-title">知识点得分</div>
          <div class="section-content">
            <Table :columns="knowledgeTableColumns" :data="knowledges" stripe border></Table>
          </div>
        </div>
      </div>
    </Card>
    <AiAnalysis
      :load-last-analysis-func="apiGetLastAiStudentAnalysis"
      :sse-url="sseUrl"
      :sse-params="sseParams"
      :auto-start-sse="false"
      :enable-start="enableStartAiAnalysis"
      :analysis-title="analysisTitle"
    ></AiAnalysis>
  </div>
</template>

<script setup>
  import { ref, onBeforeMount, computed, watch } from 'vue'
  import { useAiStore } from '@/store/ai'
  import { useStore } from 'vuex'

  import AiAnalysis from './ai_analysis.vue'

  import { apiGetTheStudentList } from '@/api/report/situation'
  import {
    apiGetAiStudentAnalysisData,
    apiAiStudentAnalysisUrl,
    apiGetLastAiStudentAnalysis,
  } from '@/api/ai/score_analysis'

  import { roundNumber } from '@/utils/math'
  import { ApiBaseUrl } from '@/config/config'

  const props = defineProps({
    analysisFilter: {
      type: Object,
      required: true,
    },
  })

  const store = useStore()
  const aiStore = useAiStore()

  /**
   * 选择学生
   */
  // 班级学生列表
  const classStudents = ref([])
  // 选中学生
  const selectedStudentId = ref('')
  const selectedStudent = computed(() => {
    return classStudents.value.find(s => s.studentId == selectedStudentId.value)
  })
  // 切换学生
  async function changeStudent(studentId) {
    if (classStudents.value.every(s => s.studentId !== studentId)) {
      studentId = classStudents.value[0]?.studentId || ''
    }
    selectedStudentId.value = studentId
    setSseParams()
    await loadStudentAnalysisData()
  }
  // 加载中禁止切换学生
  const disableSelectStudent = computed(() => {
    return (
      isLoadingClassStudents.value ||
      isLoadingStudentAnalysisData.value ||
      aiStore.isLoadingAnalysisData ||
      aiStore.isLoadingAiAnalysis
    )
  })
  // 加载学生列表
  const isLoadingClassStudents = ref(false)
  let currentRequestStudentsId = 0
  async function loadClassStudents() {
    let params = {
      schoolId: store.getters['user/info'].schoolId,
      gradeId: props.analysisFilter?.grade?.gradeId,
      classId: props.analysisFilter?.class?.classId,
      subjectId: props.analysisFilter?.subject?.subjectId,
      temps: [props.analysisFilter?.exam?.templateId],
    }
    if (!params.schoolId || !params.gradeId || !params.classId || !params.subjectId || !params.temps[0]) {
      classStudents.value = []
      selectedStudentId.value = ''
      changeStudent(selectedStudentId.value)
      return
    }

    isLoadingClassStudents.value = true
    let requestId = ++currentRequestStudentsId
    try {
      let res = await apiGetTheStudentList(params)
      if (requestId == currentRequestStudentsId) {
        classStudents.value = res
      }
    } catch (error) {
      if (requestId == currentRequestStudentsId) {
        classStudents.value = []
        selectedStudentId.value = ''
        changeStudent(selectedStudentId.value)
      }
      throw error
    } finally {
      if (requestId == currentRequestStudentsId) {
        isLoadingClassStudents.value = false
      }
    }
    changeStudent(selectedStudentId.value)
  }
  // 创建时、参数变化时加载学生列表
  onBeforeMount(() => {
    loadClassStudents()
  })
  watch(props, () => {
    loadClassStudents()
  })

  /**
   * 学生分析数据
   */
  // 概览数据
  const summary = ref(null)
  // 显示等级
  const showLevel = computed(() => {
    return summary.value != null && summary.value.score == null && Boolean(summary.value.level)
  })
  // 小题得分
  const questions = ref([])
  const questionTableColumns = [
    {
      title: '小题',
      key: 'branchName',
      minWidth: 200,
    },
    {
      title: '满分',
      key: 'fullScore',
      align: 'center',
      minWidth: 80,
    },
    {
      title: '得分',
      key: 'score',
      align: 'center',
      minWidth: 80,
      render: (h, params) => {
        return h(
          'span',
          {
            class: {
              'text-warning': params.row.score < params.row.classAvgScore,
            },
          },
          params.row.score == null ? '--' : roundNumber(params.row.score, 2)
        )
      },
    },
    {
      title: '班平均分',
      key: 'classAvgScore',
      align: 'center',
      minWidth: 80,
      render: (h, params) => {
        return h('span', {}, params.row.classAvgScore == null ? '--' : params.row.classAvgScore.toFixed(2))
      },
    },
  ]
  // 知识点得分
  const knowledges = ref([])
  const knowledgeTableColumns = [
    {
      title: '知识点',
      key: 'knowledgeName',
      minWidth: 200,
    },
    {
      title: '对应小题',
      key: 'branchNames',
      minWidth: 110,
      render: (h, params) => {
        return h('span', {}, params.row.branchNames)
      },
    },
    {
      title: '满分',
      key: 'fullScore',
      align: 'center',
      width: 80,
      render: (h, params) => {
        return h('span', {}, params.row.fullScore == null ? '--' : roundNumber(params.row.fullScore, 2))
      },
    },
    {
      title: '得分',
      key: 'score',
      align: 'center',
      width: 80,
      render: (h, params) => {
        return h(
          'span',
          {
            class: {
              'text-warning': params.row.score < params.row.classAvgScore,
            },
          },
          params.row.score == null ? '--' : roundNumber(params.row.score, 2)
        )
      },
    },
    {
      title: '班平均分',
      key: 'classAvgScore',
      align: 'center',
      width: 80,
      render: (h, params) => {
        return h('span', {}, params.row.classAvgScore == null ? '--' : params.row.classAvgScore.toFixed(2))
      },
    },
  ]

  // 加载学生分析数据
  const isLoadingStudentAnalysisData = ref(false)
  let currentRequestId = 0
  async function loadStudentAnalysisData() {
    let params = {
      studentId: selectedStudentId.value,
      classId: props.analysisFilter?.class?.classId,
      examId: props.analysisFilter?.exam?.examId,
      templateId: props.analysisFilter?.exam?.templateId,
      subjectId: props.analysisFilter?.subject?.subjectId,
    }
    if (!params.studentId || !params.classId || !params.examId || !params.templateId || !params.subjectId) {
      resetData()
      return
    }

    isLoadingStudentAnalysisData.value = true
    aiStore.isLoadingAnalysisData = true
    let requestId = ++currentRequestId
    try {
      const res = await apiGetAiStudentAnalysisData(params)
      if (requestId == currentRequestId) {
        summary.value = res.general
        questions.value = res.questionScores
        knowledges.value = res.knowledgeScores
      }
    } catch (error) {
      if (requestId == currentRequestId) {
        resetData()
      }
      throw error
    } finally {
      if (requestId == currentRequestId) {
        isLoadingStudentAnalysisData.value = false
        aiStore.isLoadingAnalysisData = false
      }
    }
  }
  function resetData() {
    summary.value = null
    questions.value = []
    knowledges.value = []
  }

  // Ai分析sse参数
  const sseUrl = `${ApiBaseUrl}/${apiAiStudentAnalysisUrl}`
  const sseParams = ref(null)
  function setSseParams() {
    if (!selectedStudentId.value) {
      sseParams.value = null
      return
    }
    sseParams.value = {
      studentId: selectedStudentId.value,
      classId: props.analysisFilter.class.classId,
      examId: props.analysisFilter.exam.examId,
      templateId: props.analysisFilter.exam.templateId,
      subjectId: props.analysisFilter.subject.subjectId,
    }
  }

  // 分析标题
  const analysisTitle = computed(() => {
    let { exam, class: cls, subject } = props.analysisFilter
    return `${exam?.examName}-${cls?.className}${subject?.subjectName}-${selectedStudent.value?.studentName}-学生AI成绩分析`
  })

  // 是否可以开始AI分析
  const enableStartAiAnalysis = computed(() => {
    return (
      !isLoadingClassStudents.value &&
      selectedStudent.value != null &&
      !aiStore.isLoadingAnalysisData &&
      !aiStore.isLoadingAiAnalysis
    )
  })
</script>

<style lang="scss" scoped>
  .content {
    max-height: calc(100vh - 116px) !important;
  }

  .section-summary {
    padding-top: 8px;

    .section-content {
      @include flex(row, flex-start, center);
      column-gap: 20px;

      .data-item {
        flex: 1;
        min-width: 120px;
        border-top: 10px solid;
        border-radius: 6px;
        text-align: center;
        background-color: #f8f8f9;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
          transform: translateY(-5px);
        }

        &.data-item-score {
          border-color: #2d8cf0;
          color: #2d8cf0;
        }

        &.data-item-avg {
          border-color: #19be6b;
          color: #19be6b;
        }

        &.data-item-rank {
          border-color: #ff9900;
          color: #ff9900;
        }

        .data-label {
          margin-top: 16px;
          margin-bottom: 8px;
          color: $color-content;
          font-size: $font-size-medium-x;
          line-height: 1;
        }

        .data-value {
          margin-bottom: 16px;
          font-weight: bold;
          font-size: 28px;
          line-height: 1;

          .rank-total {
            color: $color-content;
            font-weight: normal;
            font-size: $font-size-medium-x;
          }
        }
      }
    }
  }

  .section-question,
  .section-knowledge {
    :deep(.text-warning) {
      color: $color-warning;
    }
  }
</style>
