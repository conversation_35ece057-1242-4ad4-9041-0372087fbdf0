import {
  apiFetchRankInterval,
  apiFetchUnionRankInterval,
  apiFetchRankIntervalOfSeniorHigh,
  apiFetchUnionRankIntervalOfSeniorHigh,
} from '@/api/report'

import { generateExcelBlob } from '@/utils/excel_export'
import { getInstitutionName } from '../../tools/tools'
import { deepCopy } from '@/utils/object'

import Store from '@/store/index'

// 名次段统计
export function generateExcelReportRankIntervalBlob(
  subject = null,
  school = null,
  excelShowRank = false,
  category = null,
  combination = null,
  institution = null,
  isNextLevel = false
) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let requestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    ranges: [],
  }
  let fileName = Store.getters['report/examName'] + '_'

  if (category) {
    const CombinationName = (combination && combination.subjectName) || ''
    requestParams.categoryId = category.categoryId
    requestParams.subjectCode = combination && combination.subjectCode

    if (isMultipleSchoolLevel) {
      if (school) {
        requestParams.schoolId = school.schoolId

        fileName = `学校报表/${school.schoolName}/${category.categoryName}/${
          CombinationName ? CombinationName + '/' : ''
        }${fileName}名次段统计_${school.schoolName}_${category.categoryName}${
          CombinationName ? '_' + CombinationName : ''
        }`
      } else {
        requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']

        fileName = `${category.categoryName}/${
          CombinationName ? CombinationName + '/' : ''
        }${fileName}名次段统计_联考_${category.categoryName}${CombinationName ? '_' + CombinationName : ''}`
      }
    } else {
      requestParams.schoolId = Store.getters['report/currentSchoolId']

      fileName = `${category.categoryName}/${CombinationName ? CombinationName + '/' : ''}${fileName}名次段统计_${
        Store.getters['report/currentSchoolName']
      }_${category.categoryName}${CombinationName ? '_' + CombinationName : ''}`
    }
  } else {
    if (isMultipleSchoolLevel) {
      if (isNextLevel) {
        if (school) {
          fileName = subject
            ? `下级机构或学校报表/(学校)${school.schoolName}/${subject.subjectName}/${fileName}名次段统计_${subject.subjectName}`
            : `下级机构或学校报表/(学校)${school.schoolName}/${fileName}名次段统计_${school.schoolName}`
          requestParams.schoolId = school.schoolId
        } else {
          fileName = subject
            ? `下级机构或学校报表/(机构)${institution.name}/${subject.subjectName}/${fileName}名次段统计_${institution.name}_${subject.subjectName}`
            : `下级机构或学校报表/(机构)${institution.name}/${fileName}名次段统计_${institution.name}`
          requestParams.organizationId = institution.id
        }
      } else {
        if (school) {
          fileName = subject
            ? `学校报表/${school.schoolName}/${subject.subjectName}/${fileName}名次段统计_${subject.subjectName}`
            : `学校报表/${school.schoolName}/${fileName}名次段统计_${school.schoolName}`
          requestParams.schoolId = school.schoolId
        } else {
          fileName = subject
            ? `${subject.subjectName}/${fileName}名次段统计_联考_${subject.subjectName}`
            : `${fileName}名次段统计_联考`
          requestParams.organizationId = Store.getters['report/examCreatedInstitutionId']
        }
      }
    } else {
      fileName = subject
        ? `${subject.subjectName}/${fileName}名次段统计_${Store.getters['report/currentSchoolName']}_${subject.subjectName}`
        : `${fileName}名次段统计_${Store.getters['report/currentSchoolName']}`
      requestParams.schoolId = Store.getters['report/currentSchoolId']
    }
    if (subject) {
      requestParams.examSubjectId = subject.examSubjectId
    }
  }

  fileName += '.xlsx'

  let examAttributes = Store.getters['report/examAttributes']
  let studentNum = (examAttributes && examAttributes.studentNum) || 1000

  let templateSetting
  let localRankIntervalSetting = JSON.parse(window.localStorage.getItem('report_rank_interval'))
  if (localRankIntervalSetting) {
    let userSetting = localRankIntervalSetting.find(x => x.userId === Store.getters['user/info'].userId)
    if (userSetting) {
      let examSetting = userSetting.exams.find(x => x.examId === requestParams.examId)
      if (examSetting) {
        templateSetting = examSetting.templates.find(x => x.templateId === requestParams.templateId)
        if (templateSetting) {
          templateSetting = templateSetting.setting
          templateSetting.isFixedInterval = templateSetting.mode !== '自定义分段'
          templateSetting.isCumulativeInterval = templateSetting.mode === '固定值分段（累积）'
        }
      }
    }
  }

  if (!templateSetting) {
    let fixedInterval =
      Math.round(Number(studentNum.toString().slice(0, 2)) / 10) * Math.pow(10, studentNum.toString().length - 2)
    fixedInterval = fixedInterval > 2 ? fixedInterval : 2

    templateSetting = {
      mode: '固定值分段',
      isFixedInterval: true,
      isCumulativeInterval: false,
      fixedInterval: fixedInterval,
    }
  }

  // generate range
  const ReportTemplateSettingRankIntervals = Store.getters['report/templateSettingRankIntervals']
  const SubjectTemplateSetting = ReportTemplateSettingRankIntervals.find(
    x => x.subjectId === ((subject && subject.subjectId) || 0)
  )
  const SubjectTemplateSettingIntervals = (SubjectTemplateSetting && SubjectTemplateSetting.intervals) || []

  if (SubjectTemplateSettingIntervals.length) {
    requestParams.ranges = SubjectTemplateSettingIntervals.map((x, idx) => ({
      name: x.name,
      range: x.range,
      sortCode: idx + 1,
    }))
  } else if (templateSetting.mode === '自定义分段') {
    requestParams.ranges = templateSetting.customedRanges
  } else {
    let i = 0
    let j = 0
    let range = ''

    if (templateSetting.isCumulativeInterval) {
      for (i = templateSetting.fixedInterval; i < studentNum; i += templateSetting.fixedInterval, j++) {
        range = '[1, ' + i + ']'
        requestParams.ranges.push({
          sortCode: j,
          name: range,
          range: range,
        })
      }
      range = '[1, ' + studentNum + ']'
    } else {
      for (i = templateSetting.fixedInterval; i < studentNum; i += templateSetting.fixedInterval, j++) {
        range = '[' + (i - templateSetting.fixedInterval + 1) + ', ' + i + ']'
        requestParams.ranges.push({
          sortCode: j,
          name: range,
          range: range,
        })
      }
      range = '[' + (i + 1 - templateSetting.fixedInterval) + ', ' + studentNum + ']'
    }

    requestParams.ranges.push({
      sortCode: j,
      name: range,
      range: range,
    })
  }

  return fetchSheets(requestParams, SubjectTemplateSettingIntervals)
    .then(sheets => generateExcelBlob(sheets))
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}

async function fetchSheets(requestParams, defaultRankIntervals) {
  let requestAPI
  if ('categoryId' in requestParams) {
    requestAPI = requestParams.organizationId ? apiFetchUnionRankIntervalOfSeniorHigh : apiFetchRankIntervalOfSeniorHigh
  } else {
    requestAPI = requestParams.organizationId ? apiFetchUnionRankInterval : apiFetchRankInterval
  }

  let tableData = await requestAPI(requestParams)

  if (tableData && tableData.length) {
    tableData.sort((a, b) => a.sortCode - b.sortCode)
    tableData = tableData.map(x => ({
      name: getInstitutionName(x.cells[0].schoolId, x.cells[0].classId, x.cells[0].schoolName, x.cells[0].className),
      present: x.present || (x.cells[0] && x.cells[0].countNum),
      cells: x.cells,
    }))

    let columns = [
      {
        title: '',
        key: 'name',
        width: 'auto',
      },
      {
        title: '实考人数',
        key: 'present',
        width: 'auto',
      },
    ]
    if (defaultRankIntervals.length) {
      defaultRankIntervals.forEach(interval => {
        columns.push({
          title: interval.name,
          children: [
            {
              title: '人数',
              width: 12,
              key: row => {
                const Range = row.cells.find(cell => cell.sortCode === interval.sortCode)
                let value = '-'
                if (Range) {
                  value = (Range.num && Number(Range.num)) || 0
                }
                return value
              },
            },
            {
              title: '占比',
              width: 12,
              key: row => {
                const Range = row.cells.find(cell => cell.sortCode === interval.sortCode)
                let value = '-'
                if (Range) {
                  value = (Range.rate && Number(Range.rate)) || 0
                }
                return value
              },
              cellNumberFormat: value =>
                isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%',
            },
          ],
        })
      })
    } else if (tableData && tableData.length) {
      tableData[0].cells.forEach(interval => {
        columns.push({
          title: deepCopy(interval.range).replace(', ', '~').replace('[', '').replace(']', ''),
          children: [
            {
              title: '人数',
              width: 12,
              key: row => {
                const Range = row.cells.find(cell => cell.sortCode === interval.sortCode)
                let value = '-'
                if (Range) {
                  value = (Range.num && Number(Range.num)) || 0
                }
                return value
              },
            },
            {
              title: '占比',
              width: 12,
              key: row => {
                const Range = row.cells.find(cell => cell.sortCode === interval.sortCode)
                let value = '-'
                if (Range) {
                  value = (Range.rate && Number(Range.rate)) || 0
                }
                return value
              },
              cellNumberFormat: value =>
                isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%',
            },
          ],
        })
      })
    }

    return [
      {
        sheetName: '名次段统计',
        rows: tableData,
        columns: columns,
      },
    ]
  } else {
    throw {
      code: 9998,
      msg: '暂无数据',
    }
  }
}
