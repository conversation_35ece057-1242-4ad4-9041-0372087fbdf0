<template>
  <div class="teacher-edit-card">
    <Modal
      class="modal-edit-teacher"
      width="600"
      :model-value="show"
      :title="title"
      :mask-closable="false"
      @on-visible-change="handleVisibleChange"
    >
      <Form class="form" :model="teacherCopy" :rules="formValidateRules" :label-width="80">
        <FormItem class="form-item" label="姓名" prop="realName">
          <Input v-model="teacherCopy.realName" class="form-item-input" type="text" maxlength="20" clearable></Input>
        </FormItem>
        <FormItem class="form-item" label="性别">
          <RadioGroup v-model="teacherCopy.sex">
            <Radio label="男">男</Radio>
            <Radio label="女">女</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem class="form-item" label="手机号码" prop="mobile">
          <Input
            v-model="teacherCopy.mobile"
            class="form-item-input"
            type="tel"
            width="400"
            maxlength="11"
            clearable
          ></Input>
        </FormItem>
        <FormItem class="form-item" label="教研科目">
          <div v-for="(s, idx) in researchSubjects" :key="idx" class="teaching-item">
            <Select class="selector stage-name" :model-value="s.stageId" @on-change="changeResearchStage(s, $event)">
              <Option v-for="stage in schoolStageSubjects" :key="stage.stageId" :value="stage.stageId">{{
                stage.stageName
              }}</Option>
            </Select>
            <Select
              class="selector subject-name"
              :model-value="s.subjectId"
              @on-change="changeResearchSubject(s, $event)"
            >
              <Option
                v-for="subject in getSubjectsInStage(s.stageId)"
                :key="subject.subjectId"
                :value="subject.subjectId"
                >{{ subject.subjectName }}</Option
              >
            </Select>
            <div class="actions">
              <TextButton icon="md-close" type="error" @click="deleteResearchSubject(s)"></TextButton>
              <TextButton
                v-show="idx === researchSubjects.length - 1"
                icon="md-add"
                type="success"
                @click="addResearchSubject"
              ></TextButton>
            </div>
          </div>
          <TextButton
            v-if="researchSubjects.length == 0"
            icon="md-add"
            type="success"
            @click="addResearchSubject"
          ></TextButton>
        </FormItem>
        <FormItem class="form-item" label="教研领导">
          <i-switch :model-value="isResearchLeader" @on-change="changeIsResearchLeader"></i-switch>
        </FormItem>
      </Form>
      <template #footer>
        <div class="footer">
          <Button type="text" @click="handleCancel">取消</Button>
          <Button type="primary" @click="handleOK">确定</Button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
  import { deepCopy } from '@/utils/object'
  import { EduSchoolPositions } from '@/enum/user/role.js'

  export default {
    props: {
      show: Boolean,
      teacher: {
        type: Object,
        default: () => null,
      },
      title: String,
    },
    emits: ['cancel', 'ok'],
    data() {
      return {
        teacherCopy: {
          realName: '',
          sex: '',
          mobile: '',
          password: '',
          teachings: [],
          roles: [],
        },
        formTeacherStruct: {
          realName: '',
          sex: '',
          mobile: '',
          password: '',
          teachings: [],
          roles: [],
        },
        formValidateRules: {
          realName: [
            {
              required: true,
              message: '姓名不可为空',
              trigger: 'blur',
            },
          ],
          mobile: [
            {
              required: true,
              message: '手机号码不可为空',
              trigger: 'blur',
            },
          ],
        },
      }
    },
    computed: {
      schoolStageSubjectsFlat() {
        return this.$store.getters['school/schoolStageSubjectsFlat']
      },
      schoolStageSubjects() {
        return this.$store.getters['school/schoolStageSubjects']
      },
      researchSubjects() {
        if (!this.teacherCopy) {
          return []
        }
        return this.teacherCopy.roles.filter(x => x.roleId === EduSchoolPositions.Researcher.id)
      },
      isResearchLeader() {
        if (!this.teacherCopy) {
          return false
        }
        return this.teacherCopy.roles.some(x => x.roleId === EduSchoolPositions.ResearcherLeader.id)
      },
    },
    watch: {
      teacher() {
        this.teacherCopy = deepCopy(this.teacher || this.formTeacherStruct)
      },
    },
    methods: {
      getSubjectsInStage(stageId) {
        let stage = this.schoolStageSubjects.find(x => x.stageId == stageId)
        return (stage && stage.subjects) || []
      },
      handleCancel() {
        this.$emit('cancel')
      },
      handleVisibleChange(visible) {
        if (!visible) {
          this.$emit('cancel')
        }
      },
      handleOK() {
        let checkMessage = this.check()
        if (checkMessage) {
          this.$Message.info({
            content: checkMessage,
          })
          return
        }
        this.$emit('ok', this.teacherCopy)
      },
      check() {
        if (!this.teacherCopy.realName) {
          return '请输入姓名'
        }

        let mobile = this.teacherCopy.mobile
        if (!mobile || !mobile.length) {
          return '手机号码不能为空'
        }
        if (mobile && !/^1\d{10}$/.test(mobile)) {
          return '请输入正确的手机号码'
        }
        this.teacherCopy.password = mobile.slice(-6) // 密码默认为手机号码后六位

        if (this.researchSubjects.some(item => !item.stageId || !item.subjectId)) {
          return '请选择学段学科'
        }

        for (let item of this.researchSubjects) {
          let count = this.researchSubjects.filter(
            x => x.stageId == item.stageId && x.subjectId == item.subjectId
          ).length
          if (count > 1) {
            return `教研科目【${item.stageName}${item.subjectName}】重复`
          }
        }

        return ''
      },
      changeIsResearchLeader(value) {
        if (value && !this.isResearchLeader) {
          this.teacherCopy.roles.push({
            roleId: EduSchoolPositions.ResearcherLeader.id,
            roleName: EduSchoolPositions.ResearcherLeader.name,
            stageId: null,
            stageName: null,
            gradeId: null,
            gradeName: null,
            classId: null,
            className: null,
            subjectId: null,
            subjectName: null,
          })
        } else if (!value && this.isResearchLeader) {
          this.teacherCopy.roles = this.teacherCopy.roles.filter(
            r => r.roleId != EduSchoolPositions.ResearcherLeader.id
          )
        }
      },
      changeResearchStage(item, stageId) {
        let stage = this.schoolStageSubjects.find(s => s.stageId == stageId)
        if (!stage) {
          item.stageId = null
          item.stageName = null
          item.subjectId = null
          item.subjectName = null
          return
        }
        item.stageId = stage.stageId
        item.stageName = stage.stageName
        if (stage.subjects.every(s => s.subjectId != item.subjectId)) {
          item.subjectId = null
          item.subjectName = null
        }
      },
      changeResearchSubject(item, subjectId) {
        let subjects = this.getSubjectsInStage(item.stageId)
        let subject = subjects.find(s => s.subjectId == subjectId)
        if (!subject) {
          item.subjectId = null
          item.subjectName = null
          return
        }
        item.subjectId = subject.subjectId
        item.subjectName = subject.subjectName
      },
      deleteResearchSubject(item) {
        let idx = this.teacherCopy.roles.findIndex(
          x => x.roleId == item.roleId && x.stageId == item.stageId && x.subjectId == item.subjectId
        )
        if (idx >= 0) {
          this.teacherCopy.roles.splice(idx, 1)
        }
      },
      addResearchSubject() {
        this.teacherCopy.roles.push({
          roleId: EduSchoolPositions.Researcher.id,
          roleName: EduSchoolPositions.Researcher.name,
          stageId: null,
          stageName: null,
          gradeId: null,
          gradeName: null,
          classId: null,
          className: null,
          subjectId: null,
          subjectName: null,
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .form-item {
    margin-bottom: 20px;
  }

  .form-item-input {
    width: 400px;
  }

  .selector {
    width: 120px;
    margin-right: 10px;
  }

  .teaching-item {
    @include flex(row, flex-start, center);

    &:not(:first-child) {
      margin-top: 10px;
    }
  }
</style>
