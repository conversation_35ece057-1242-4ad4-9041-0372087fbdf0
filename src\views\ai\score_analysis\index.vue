<template>
  <div class="score-analysis-page">
    <div class="header">
      <Button type="text" @click="goBack">
        <Icon type="md-arrow-back" />
        返回
      </Button>
      <h1>AI成绩分析</h1>
    </div>

    <!-- 筛选条件组件 -->
    <FilterSection class="filter-section" @analyze="handleAnalyze" />

    <!-- 分析类型选择 -->
    <div ref="analysisTypeEl" class="analysis-type">
      <RadioGroup v-model="analysisType" type="button" button-style="solid">
        <Radio label="class" @click.prevent.stop="changeAnalysisType('class')">班级成绩分析</Radio>
        <Radio label="student" @click.prevent.stop="changeAnalysisType('student')">学生成绩分析</Radio>
      </RadioGroup>
    </div>

    <template v-if="analysisFilter">
      <Card class="analysis-filter">
        <p>班级：{{ analysisFilter.class.className }}</p>
        <p>科目：{{ analysisFilter.subject.subjectName }}</p>
        <p>考试：{{ analysisFilter.exam.examName }}</p>
      </Card>
      <ClassAnalysis
        v-if="analysisType === 'class'"
        class="analysis-component"
        :analysis-filter="analysisFilter"
      ></ClassAnalysis>
      <StudentAnalysis
        v-if="analysisType === 'student'"
        class="analysis-component"
        :analysis-filter="analysisFilter"
      ></StudentAnalysis>
    </template>
    <Card v-else class="empty-content">
      <Icon type="ios-alert-outline" size="20" /><span class="tip-text">请选择要分析的考试</span>
    </Card>
  </div>
</template>

<script setup>
  import { ref, useTemplateRef, onBeforeMount } from 'vue'
  import { useRouter } from 'vue-router'
  import { useAiStore } from '@/store/ai'

  import { Message } from 'view-ui-plus'
  import FilterSection from './components/filter_section.vue'
  import ClassAnalysis from './components/class_analysis.vue'
  import StudentAnalysis from './components/student_analysis.vue'

  import { backOrDefault } from '@/utils/router'
  import { scrollInto } from '@/utils/scroll'

  const router = useRouter()
  const aiStore = useAiStore()

  const analysisTypeEl = useTemplateRef('analysisTypeEl')

  // 分析类型: class-班级分析, student-学生分析
  let analysisType = ref('class')
  function changeAnalysisType(value) {
    if (checkCurrentAnalysis()) {
      return
    }
    analysisType.value = value
  }

  // 分析条件
  const analysisFilter = ref(null)
  // 开始分析
  function handleAnalyze(filter) {
    if (checkCurrentAnalysis()) {
      return
    }
    analysisFilter.value = filter
    setTimeout(() => {
      if (analysisTypeEl.value) {
        scrollInto(analysisTypeEl.value)
      }
    }, 100)
  }

  // 检查当前是否正在分析
  function checkCurrentAnalysis() {
    if (aiStore.isLoadingAnalysisData || aiStore.isLoadingAiAnalysis) {
      Message.warning({
        content: '请等待当前分析完成',
        duration: 3,
      })
      return true
    }
    return false
  }

  // 返回
  function goBack() {
    backOrDefault(router, {
      name: 'ai',
    })
  }

  // 重置loading状态
  onBeforeMount(() => {
    aiStore.isLoadingAnalysisData = false
    aiStore.isLoadingAiAnalysis = false
  })
</script>

<style lang="scss" scoped>
  .header {
    @include flex(row, flex-start, center);
    padding-bottom: 16px;
    line-height: 1;

    h1 {
      margin-left: 8px;
      color: $color-title;
      font-weight: bold;
      font-size: 22px;
    }
  }

  .analysis-type {
    margin-top: 8px;
    margin-bottom: 24px;
    padding-top: 24px;
    text-align: center;
  }

  .analysis-filter {
    margin-bottom: 24px;
    line-height: 2;
  }

  .empty-content {
    @include flex(row, center, center);
    height: 200px;
    color: $color-icon;
    background: #f9f9f9;

    .tip-text {
      margin-left: 10px;
    }
  }

  :deep(.analysis-component) {
    &.analysis-container {
      @include flex(row, flex-start, flex-start);
      column-gap: 24px;
    }

    .analysis-data-card {
      position: sticky;
      top: 0;
      flex-grow: 0;
      flex-shrink: 0;
      width: 600px;
    }

    .card-header {
      @include flex(row, space-between, center);
    }

    .card-title {
      color: $color-title;
      font-weight: bold;
      font-size: $font-size-medium-x;
    }

    .content {
      position: relative;
      max-height: calc(100vh - 100px);
      margin-right: -16px;
      padding-right: 16px;
      overflow-y: auto;
    }

    .loading-spin {
      @include flex(row, center, center);
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      max-height: 200px;
    }

    .content-section {
      margin-bottom: 24px;

      .section-title {
        @include flex(row, flex-start, center);
        margin-bottom: 16px;
        font-size: $font-size-medium-x;
        line-height: 1;
        column-gap: 8px;

        &:before {
          width: 4px;
          height: 16px;
          border-radius: 2px;
          background-color: $color-primary;
          content: '';
        }
      }
    }
  }
</style>
