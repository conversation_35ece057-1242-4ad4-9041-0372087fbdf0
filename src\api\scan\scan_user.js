import ajax from '@/api/ajax'

export function apiGetScanRange(examSubjectId) {
  return ajax.get({
    url: 'scan/scanUser/scanRange',
    params: {
      examSubjectId,
    },
    requestName: '获取扫描范围',
  })
}

// 查询当前用户可见的扫描点及其下所有扫描员
export function apiListStationScanners(examSubjectId) {
  return ajax.get({
    url: 'scan/scanUser/listScanner',
    params: {
      examSubjectId,
    },
    requestName: '获取扫描员列表',
  })
}
