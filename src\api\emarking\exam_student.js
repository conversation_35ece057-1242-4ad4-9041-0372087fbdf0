import ajax from '@/api/ajax'

export function apiGetExamStudentCount(requestParams) {
  return ajax.get({
    url: 'mark/examStudent/studentNum',
    params: {
      // [string]
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId, // 不传此参数返回全部科目人数
      schoolId: requestParams.schoolId,
    },
    requestName: '获取考试上报学生数',
  })
}

export function apiImportStudentPhoto(params) {
  return ajax.upload({
    url: 'mark/examStudent/importStudentPhoto',
    params: {
      examId: params.examId,
      classId: params.classId,
    },
    data: {
      file: params.file,
    },
    requestName: '导入学生证件照',
  })
}

export function apiSyncSchoolStudentPhoto(requestParams) {
  return ajax.put({
    url: 'mark/examStudent/syncStudentIdPhoto',
    params: {
      examId: requestParams.examId,
      schoolId: requestParams.schoolId,
    },
    requestName: '同步校内考生证件照',
  })
}

export function apiSyncExamStudentPhoto(examId) {
  return ajax.put({
    url: 'mark/examStudent/syncAllStudentIdPhoto',
    params: {
      examId: examId,
    },
    requestName: '同步考试项目考生证件照',
  })
}

export function apiGetExamStudentPhotoStatus(requestParams) {
  return ajax.get({
    url: 'mark/examStudent/photoStat',
    params: {
      examId: requestParams.examId,
      schoolId: requestParams.schoolId || undefined,
    },
    requestName: '获取考试项目考生证件照统计',
  })
}

export function apiGetExamSubjectStudentCount({ examSubjectId, schoolId }) {
  return ajax.get({
    url: 'mark/examStudent/subjectStudentNum',
    params: {
      examSubjectId,
      schoolId,
    },
    requestName: '查询考生人数',
  })
}
