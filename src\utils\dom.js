export function createHiddenContainer(containerStyle) {
  let outer = document.createElement('div')
  outer.style.position = 'fixed'
  outer.style.zIndex = -9999
  outer.style.left = 0
  outer.style.top = 0
  outer.style.width = '100px'
  outer.style.height = '100px'
  outer.style.overflow = 'hidden'
  outer.style.visibility = 'hidden'
  let inner = document.createElement('div')
  if (containerStyle) {
    Object.keys(containerStyle).forEach(key => {
      inner.style[key] = containerStyle[key]
    })
  }
  outer.append(inner)
  document.body.append(outer)
  return inner
}

export function removeHiddenContainer(inner) {
  inner.parentElement.remove()
}

export function getHTMLHeight(containerStyle, innerHTML) {
  let container = createHiddenContainer(containerStyle)
  container.innerHTML = innerHTML
  let height = container.offsetHeight
  removeHiddenContainer(container)
  return height
}

export function getHTMLWidth(containerStyle, innerHTML) {
  let container = createHiddenContainer(containerStyle)
  let inlineBlock = document.createElement('div')
  inlineBlock.style.display = 'inline-block'
  inlineBlock.innerHTML = innerHTML
  container.appendChild(inlineBlock)
  let width = inlineBlock.offsetWidth
  removeHiddenContainer(container)
  return width
}

export function createTextWidthMeasure(fontSize, fontFamily) {
  let container = createHiddenContainer()

  let inlineBlock = document.createElement('div')
  inlineBlock.style.display = 'inline-block'
  inlineBlock.style.fontFamily = fontFamily
  if (typeof fontSize == 'number') {
    inlineBlock.style.fontSize = `${fontSize}px`
  } else if (typeof fontSize == 'string') {
    inlineBlock.style.fontSize = fontSize
  }
  inlineBlock.style.whiteSpace = 'pre'
  container.appendChild(inlineBlock)

  let textNode = document.createTextNode('')
  inlineBlock.appendChild(textNode)

  return {
    measureWidth(text) {
      textNode.nodeValue = text
      return inlineBlock.offsetWidth
    },
    destroy() {
      removeHiddenContainer(container)
    },
  }
}

export function loadScript(src) {
  return new Promise((resolve, reject) => {
    let elScript = document.createElement('script')
    elScript.src = src
    elScript.onload = resolve
    elScript.onerror = reject
    document.head.appendChild(elScript)
  })
}

export function parseHtmlDom(html) {
  return new DOMParser().parseFromString(html, 'text/html')
}

export function getTableHtml({ columns, data }) {
  const getTh = content =>
    `<th style="background-color: #f8f8f9;border:1px solid #e8eaec;padding:8px 10px;text-align:center;">${content}</th>`
  const getTd = content => `<td style="border:1px solid #e8eaec;padding:8px 10px;text-align:center;">${content}</td>`
  let colgroup = columns.map(col => `<col width="${col.width || ''}">`).join('')
  let headerRow = columns.map(col => getTh(col.title)).join('')
  let bodyRows = data.map(row => '<tr>' + columns.map(col => getTd(row[col.key])).join('') + '</tr>').join('')
  return `<table style="border-collapse: collapse"><colgroup>${colgroup}</colgroup><thead><tr>${headerRow}</tr></thead><tbody>${bodyRows}</tbody></table>`
}
