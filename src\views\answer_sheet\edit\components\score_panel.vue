<template>
  <div v-if="isTypeBar" class="score-type-bar" :style="scoreTypeBarStyle">
    <div v-if="barCells.error" class="message-error no-print">{{ barCells.error }}</div>
    <template v-else>
      <div v-for="cell in barCells" :key="cell.index" class="cell" :style="cell.style" :data-cellindex="cell.index">
        <div class="cell-inner" style="transform: scale(0.85); transform-origin: center center">{{ cell.text }}</div>
      </div>
      <div class="half-score-setting no-print">
        <Checkbox :model-value="barScoreNoHeight" style="margin-right: 8px" @on-change="handleChangeBarScoreHeight"
          >打分条不占高度</Checkbox
        >
        <Checkbox :model-value="halfScore" :disabled="isQuestionGroupScoreHalf" @on-change="handleChangeHalfScore"
          >0.5分</Checkbox
        >
      </div>
    </template>
  </div>

  <div v-else-if="isTypeNumber" class="score-type-number" :style="scoreTypeNumberStyle">
    <div v-if="numberSquares.error" class="message-error no-print">{{ numberSquares.error }}</div>
    <template v-else>
      <div class="number-background" :style="scoreTypeNumberBackgroundStyle"></div>
      <div v-for="s in numberSquares" :key="s.value" :class="{ square: !s.isDot }" :style="s.style">
        <div
          v-if="s.isDot"
          class="dot-inner"
          style="
            position: absolute;
            bottom: 0;
            left: 6px;
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: var(--base-border-color);
          "
        ></div>
      </div>
      <div class="half-score-setting no-print">
        <Checkbox :model-value="halfScore" :disabled="isQuestionGroupScoreHalf" @on-change="handleChangeHalfScore"
          >0.5分</Checkbox
        >
      </div>
    </template>
  </div>

  <div v-else-if="isTypeTrueFalse" class="score-type-true-false" :style="scoreTypeTrueFalseStyle">
    <div class="true-false-background" :style="trueFalseBackgroundStyle"></div>
    <div class="true-false-square" :style="trueFalseSquareStyle"></div>
  </div>
</template>

<script>
  import TemplateBarScoreArea from '@/helpers/scan_template_define/template_bar_score_area'
  import TemplateBarScoreCell from '@/helpers/scan_template_define/template_bar_score_cell'
  import TemplateManualScoreArea from '@/helpers/scan_template_define/template_manual_score_area'
  import TemplateTrueFalseScoreArea from '@/helpers/scan_template_define/template_true_false_score_area'
  import TemplateRect from '@/helpers/scan_template_define/template_rect'
  import ScoreTypeEnum from '@/enum/answer_sheet/score_type'
  import { BarScoreHeight } from '@/const/answer_sheet'
  import { sumByDefaultZero } from '@/utils/math'
  import { uniq } from '@/utils/array'

  export default {
    props: {
      block: Object,
      // 打分条是否向上移一个边框
      moveTop: Boolean,
      // 打分条是否向右移一个边框
      moveRight: Boolean,
    },
    data() {
      return {
        // 打分条格子宽度
        barCellWidth: 24,
        // 打分条格子高度
        barCellHeight: BarScoreHeight,

        // 数字框宽度
        numberSquareWidth: 40,
        // 数字框高度
        numberSquareHeight: 40,
        // 数字框间距
        numberSquareGap: 8,
        // 小数点宽度
        numberDotWidth: 16,

        // 对错框宽度
        trueFalseSquareWidth: 36,
        // 对错框高度
        trueFalseSquareHeight: 36,
        // 对错框间距
        trueFalseSquareGap: 8,

        // 边框
        borderWidth: 1,
      }
    },
    computed: {
      scoreTypeTrueFalseEmptyDefault() {
        return this.$store.getters['answerSheet/scoreTypeTrueFalseEmptyDefault']
      },
      isTypeNumber() {
        return this.block.questionGroup.scoreType.name == ScoreTypeEnum.Number.id
      },
      isTypeBar() {
        return this.block.questionGroup.scoreType.name == ScoreTypeEnum.Bar.id
      },
      isTypeTrueFalse() {
        return this.block.questionGroup.scoreType.name == ScoreTypeEnum.TrueFalse.id
      },
      questionGroupScore() {
        let questions = this.block.questionGroup.questions
        let totalScore = sumByDefaultZero(questions, q => q.fullScore)
        // 考虑选做题（先阅后扫选做个数为1）
        if (this.block.questionGroup.selectGroupName) {
          let questionCount = uniq(questions.map(q => q.questionCode)).length
          totalScore /= questionCount
        }
        return totalScore
      },
      isQuestionGroupScoreHalf() {
        return this.questionGroupScore - Math.floor(this.questionGroupScore) == 0.5
      },
      halfScore() {
        return this.block.questionGroup.scoreType.halfScore || this.isQuestionGroupScoreHalf
      },
      barScoreNoHeight() {
        return !(this.block.questionGroup.scoreType.height > 0)
      },

      /**
       * 打分条相关
       */
      scoreTypeBarStyle() {
        return {
          position: 'absolute',
          top: this.moveTop ? '-1px' : '0',
          right: this.moveRight ? '-1px' : '0',
          zIndex: '1',
          textAlign: 'right',
          width: '100%',
          height: `${this.barCellHeight}px`,
          lineHeight: '1',
          fontSize: '12px',
        }
      },
      barCells() {
        if (!(this.questionGroupScore > 0)) {
          return {
            error: '未设置分数',
          }
        }
        if (!Number.isInteger(this.questionGroupScore * 2)) {
          return {
            error: '打分条不支持非整数或.5的分数',
          }
        }
        // 一行最多的格子数
        let contentWidth = this.$store.getters['answerSheet/pageContentWidth'] - 2 * this.borderWidth
        let maxCellCount = Math.floor((contentWidth - this.borderWidth) / this.barCellWidth)
        if (this.getBarScoreConsecutiveCellCount() < maxCellCount) {
          return this.getBarScoreConsecutiveCells()
        } else if (this.getBarScoreNonConsecutiveCellCount() < maxCellCount) {
          return this.getBarScoreNonConsecutiveCells()
        } else {
          return {
            error: '分值过大，打分条无法正常显示',
          }
        }
      },

      /**
       * 手写数字框相关
       */
      numberSquares() {
        if (!(this.questionGroupScore > 0)) {
          return {
            error: '未设置分数',
          }
        }
        if (this.questionGroupScore >= 100) {
          return {
            error: '分值过大，打分框无法正常显示',
          }
        }

        let values = []
        if (this.halfScore) {
          values.push(0.1)
          values.push(0)
        }
        values.push(1)
        if (this.questionGroupScore >= 10) {
          values.push(10)
        }

        let squares = []
        let right = this.numberSquareGap
        let top = this.numberSquareGap
        let height = this.numberSquareHeight
        values.forEach(value => {
          let width = this.numberSquareWidth
          let isDot = value == 0
          if (isDot) {
            width = this.numberDotWidth
            right -= this.numberSquareGap
          }

          let style = {
            position: 'absolute',
            top: top + 'px',
            right: right + 'px',
            width: width + 'px',
            height: height + 'px',
          }
          if (!isDot) {
            style.border = `${this.borderWidth}px solid var(--base-border-color)`
          }

          squares.push({
            value,
            style,
            top,
            right,
            width,
            height,
            isDot,
          })

          if (isDot) {
            right += width
          } else {
            right += width + this.numberSquareGap
          }
        })
        return squares
      },
      scoreTypeNumberStyle() {
        return {
          position: 'absolute',
          top: '0',
          right: '0',
          zIndex: '2',
          width: '100%',
        }
      },
      scoreTypeNumberBackgroundStyle() {
        return {
          position: 'absolute',
          top: this.borderWidth + 'px',
          right: this.borderWidth + 'px',
          width: Math.max(...this.numberSquares.map(s => s.width + s.right)) + this.numberSquareGap + 'px',
          height: this.numberSquareHeight + this.numberSquareGap * 2 + 'px',
          backgroundColor: 'white',
        }
      },

      /**
       * 对错框相关
       */
      scoreTypeTrueFalseStyle() {
        return {
          position: 'absolute',
          top: '0',
          right: '0',
          zIndex: '2',
          width: '100%',
        }
      },
      trueFalseBackgroundStyle() {
        return {
          position: 'absolute',
          top: this.borderWidth + 'px',
          right: this.borderWidth + 'px',
          width: this.trueFalseSquareWidth + this.trueFalseSquareGap * 2 + 'px',
          height: this.trueFalseSquareHeight + this.trueFalseSquareGap * 2 + 'px',
          backgroundColor: 'white',
        }
      },
      trueFalseSquareStyle() {
        return {
          position: 'absolute',
          top: this.trueFalseSquareGap + 'px',
          right: this.trueFalseSquareGap + 'px',
          width: this.trueFalseSquareWidth + 'px',
          height: this.trueFalseSquareHeight + 'px',
          border: `${this.borderWidth}px dashed`,
        }
      },
    },
    methods: {
      handleChangeHalfScore(v) {
        this.$store.commit('answerSheet/changeQuestionGroupScoreTypeHalfScore', {
          questionGroupId: this.block.questionGroup.id,
          halfScore: v,
        })
      },
      handleChangeBarScoreHeight(noHeight) {
        let height = noHeight ? 0 : BarScoreHeight
        this.block.questionGroup.scoreType.changeHeight(height)
      },

      /**
       * 打分条
       */
      // 连续分值格子总数
      getBarScoreConsecutiveCellCount() {
        return 1 + Math.floor(this.questionGroupScore) + (this.halfScore ? 2 : 0)
      },
      // 连续分值格子
      getBarScoreConsecutiveCells() {
        let values = []
        for (let i = 0; i <= this.questionGroupScore; i++) {
          values.push(i)
        }
        if (this.halfScore) {
          values.push(null)
          values.push(0.5)
        }
        return this.getBarCellsGivenValues(values)
      },
      // 分段分值格子总数
      getBarScoreNonConsecutiveCellCount() {
        if (this.questionGroupScore <= 10) {
          return this.getBarScoreConsecutiveCellCount()
        }
        let tens = Math.floor(this.questionGroupScore / 10)
        let ones = 10
        return tens + 1 + ones + (this.halfScore ? 2 : 0)
      },
      // 分段分值格子
      getBarScoreNonConsecutiveCells() {
        let values = []
        for (let i = 10; i <= this.questionGroupScore; i += 10) {
          values.push(i)
        }
        values.push(null)
        for (let i = 0; i <= 9; i++) {
          values.push(i)
        }
        if (this.halfScore) {
          values.push(null)
          values.push(0.5)
        }
        return this.getBarCellsGivenValues(values)
      },
      // 格子加样式
      getBarCellsGivenValues(values) {
        return values.map((value, idx) => {
          let cell = {
            index: idx,
            value,
            text: value == null ? String.fromCharCode(160) : value,
            style: {
              display: 'inline-block',
              borderTop: `${this.borderWidth}px solid var(--base-border-color)`,
              borderBottom: `${this.borderWidth}px solid var(--base-border-color)`,
              borderRight: `${this.borderWidth}px solid var(--base-border-color)`,
              width: `${this.barCellWidth}px`,
              height: `${this.barCellHeight}px`,
              textAlign: 'center',
              lineHeight: `${this.barCellHeight - this.borderWidth * 2}px`,
            },
          }
          if (idx == 0) {
            cell.style.borderLeft = `${this.borderWidth}px solid var(--base-border-color)`
          }
          return cell
        })
      },
      getBarScoreArea(areaLeft, areaTop) {
        let scoreArea = new TemplateBarScoreArea()
        let elRect = this.$el.getBoundingClientRect()
        let cellElements = this.$el.querySelectorAll('.cell')
        cellElements.forEach(cellElement => {
          let index = cellElement.dataset['cellindex']
          let cell = this.barCells[index]
          let value = cell && cell.value
          if (value == null) {
            return
          }
          let squareRect = cellElement.getBoundingClientRect()
          let barScoreCell = new TemplateBarScoreCell()
          barScoreCell.value = value
          barScoreCell.searchArea = TemplateRect.createFromAnswerSheetPageRect(
            squareRect.left - elRect.left + areaLeft + (this.moveRight ? 1 : 0),
            squareRect.top - elRect.top + areaTop + (this.moveTop ? -1 : 0),
            squareRect.width,
            squareRect.height
          )
          scoreArea.cells.push(barScoreCell)
        })
        scoreArea.cells.sort((a, b) => a.searchArea.x - b.searchArea.x)

        // 由于四舍五入带来误差，相邻格子无法保证pre.x + pre.width != next.x
        // 修改：pre.width = next.x - pre.x，保证上式成立
        // 修改后相邻格子宽度可能不等
        scoreArea.cells.forEach((cell, idx) => {
          let next = scoreArea.cells[idx + 1]
          if (!next) {
            return
          }
          if (cell.searchArea.x + cell.searchArea.width / 2 < next.searchArea.x) {
            return
          }
          cell.searchArea.width = next.searchArea.x - cell.searchArea.x
        })

        return scoreArea
      },

      /**
       * 手写框
       */
      getNumberScoreArea(areaLeft, areaTop) {
        let scoreArea = new TemplateManualScoreArea()
        scoreArea.digitCount = this.halfScore ? 1 : 0

        let elRect = this.$el.getBoundingClientRect()
        let squares = this.$el.getElementsByClassName('square')
        Array.from(squares).forEach(elSquare => {
          let squareRect = elSquare.getBoundingClientRect()
          let rect = TemplateRect.createFromAnswerSheetPageRect(
            squareRect.left - elRect.left + areaLeft,
            squareRect.top - elRect.top + areaTop,
            squareRect.width,
            squareRect.height
          )
          scoreArea.digits.push(rect)
        })

        let firstRect = scoreArea.digits[0]
        let lastRect = scoreArea.digits[scoreArea.digits.length - 1]
        scoreArea.searchArea = TemplateRect.createFromAnswerSheetPageRect(
          firstRect.x,
          firstRect.y,
          lastRect.x + lastRect.width - firstRect.x,
          lastRect.y + lastRect.height - firstRect.y
        )
        return scoreArea
      },

      /**
       * 对错框
       */
      getTrueFalseScoreArea(areaLeft, areaTop) {
        let scoreArea = new TemplateTrueFalseScoreArea()
        scoreArea.emptyDefault = this.scoreTypeTrueFalseEmptyDefault
        scoreArea.enableHalfScore = false
        let elRect = this.$el.getBoundingClientRect()
        let squareRect = this.$el.querySelector('.true-false-square').getBoundingClientRect()
        scoreArea.searchArea = TemplateRect.createFromAnswerSheetPageRect(
          squareRect.left - elRect.left + areaLeft,
          squareRect.top - elRect.top + areaTop,
          squareRect.width,
          squareRect.height
        )
        return scoreArea
      },

      getScoreAreas(areaLeft, areaTop) {
        // 选做题每题一个打分区域
        let { selectGroupName, questions } = this.block.questionGroup
        if (!selectGroupName) {
          questions = [questions[0]]
        }

        let scoreArea = null
        if (this.isTypeNumber) {
          scoreArea = this.getNumberScoreArea(areaLeft, areaTop)
        } else if (this.isTypeBar) {
          scoreArea = this.getBarScoreArea(areaLeft, areaTop)
        } else if (this.isTypeTrueFalse) {
          scoreArea = this.getTrueFalseScoreArea(areaLeft, areaTop)
        }

        return questions.map(({ questionCode, branchCode }) => {
          if (this.isTypeNumber) {
            scoreArea.id = `NumberScoreArea-${questionCode}.${branchCode}`
          } else if (this.isTypeBar) {
            scoreArea.id = `BarScoreArea-${questionCode}.${branchCode}`
          } else if (this.isTypeTrueFalse) {
            scoreArea.id = `TrueFalseScoreArea-${questionCode}.${branchCode}`
          }
          scoreArea._extra = {
            scoreType: this.block.questionGroup.scoreType.name,
            questionCode,
            branchCode,
          }
          return scoreArea
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .message-error {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 200px;
    color: red;
    font-size: 12px;
    line-height: 1;
    text-align: right;
    background-color: white;
  }

  .half-score-setting {
    position: absolute;
    top: 0;
    right: 0;
    display: none;
    padding-top: 20px;
    padding-right: 1px;
    color: $color-primary;

    .ivu-checkbox-wrapper {
      margin-right: 0;
      background-color: white;
    }
  }

  .score-type-bar:hover .half-score-setting {
    display: block;
  }

  .score-type-number:hover .half-score-setting {
    display: block;
  }
</style>
