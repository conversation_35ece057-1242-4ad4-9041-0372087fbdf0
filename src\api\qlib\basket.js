import ajax from '@/api/ajax'
import { Paper } from '@/helpers/qlib/paper'
import { Question } from '@/helpers/qlib/question'

export function apiGetQuestionBasket() {
  return ajax
    .get({
      url: `ques/basket/new`,
      requestName: '获取试题篮',
    })
    .then(data => {
      return new Paper(
        data.paperInfo,
        data.basketPaperStyle,
        data.paperStructure,
        (data.questions || []).map(q => Question.import(q))
      )
    })
}

export function apiChangeQuestionBasket(p) {
  let basketPaperStyle = p.paperStyle.export()
  let paperStructure = JSON.stringify(p.paperStructure.export())
  basketPaperStyle.questionCategoryJson = paperStructure

  return ajax.put({
    url: `ques/basket`,
    data: {
      basketPaperStyle,
      paperStructure,
    },
    requestName: '更改试题篮',
  })
}

export function apiEditBasketQuestion({ questionId, question, paperStructure }) {
  let structureJson = JSON.stringify(paperStructure.export())
  return question
    .export()
    .then(q => {
      return ajax.put({
        url: `ques/basket/ques`,
        params: {
          questionId,
        },
        data: {
          questions: [q],
          paperStructure: structureJson,
        },
        requestName: '修改题目',
      })
    })
    .then(newQuestion => {
      return Question.import(newQuestion)
    })
}

export function apiAddBasketQuestions({ questions, paperStructure }) {
  let structureJson = JSON.stringify(paperStructure.export())
  return Promise.all(questions.map(q => q.export()))
    .then(exportedQuestions => {
      return ajax.post({
        url: 'ques/basket/addQuestion',
        data: {
          questions: exportedQuestions,
          paperStructure: structureJson,
        },
        requestName: '添加题目',
      })
    })
    .then(resQuestions => {
      return resQuestions.map(q => Question.import(q))
    })
}

export function apiClearQuestionBasket() {
  return ajax.put({
    url: `ques/basket/clearNew`,
    requestName: '清空试题篮',
  })
}

export function apiSaveQuestionBasket(stageId, subjectId) {
  return ajax.put({
    url: `ques/basket/saveAsPaper`,
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    requestName: '保存试卷',
  })
}
