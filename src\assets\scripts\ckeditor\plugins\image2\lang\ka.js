/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'ka', {
	alt: 'სანაცვლო ტექსტი',
	btnUpload: 'სერვერისთვის გაგზავნა',
	captioned: 'Captioned image', // MISSING
	captionPlaceholder: 'Caption', // MISSING
	infoTab: 'სურათის ინფორმცია',
	lockRatio: 'პროპორციის შენარჩუნება',
	menu: 'სურათის პარამეტრები',
	pathName: 'image', // MISSING
	pathNameCaption: 'caption', // MISSING
	resetSize: 'ზომის დაბრუნება',
	resizer: 'Click and drag to resize', // MISSING
	title: 'სურათის პარამეტრები',
	uploadTab: 'აქაჩვა',
	urlMissing: 'სურათის URL არაა შევსებული.',
	altMissing: 'Alternative text is missing.' // MISSING
} );
