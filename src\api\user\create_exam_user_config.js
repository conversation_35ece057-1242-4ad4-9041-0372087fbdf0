import ajax from '@/api/ajax'

export function apiAddSchoolExamCreator(requestParams) {
  return ajax.post({
    url: 'user/examCreatorConfig/add',
    params: {
      rangeType: requestParams.rangeType, // s(school) | g(grade) | c(class)
      teacherId: requestParams.teacherId,
      gradeId: requestParams.gradeId,
      classId: requestParams.classId,
      subjectId: requestParams.subjectId,
    },
    requestName: '添加学校考试创建者配置',
  })
}

export function apiDeleteSchoolExamCreator(ids) {
  return ajax.delete({
    url: 'user/examCreatorConfig/delete',
    params: {
      ids: ids,
    },
    // requestName: '删除学校考试创建者配置',
  })
}

export function apiGetSchoolExamCreatorList() {
  return ajax.get({
    url: 'user/examCreatorConfig/list',
    requestName: '获取学校考试创建者配置列表',
  })
}
