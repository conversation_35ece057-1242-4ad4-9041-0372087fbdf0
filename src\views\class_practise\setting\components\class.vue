<template>
  <div class="cls">
    <div class="info">
      <span>测练学生共 {{ cls.students.length }} 人</span>
      <template v-if="needMark">
        <span>，</span>
        <span v-if="cls.markers.length > 0">评卷老师：{{ cls.markers.map(t => t.realName).join('、') }}</span>
        <span v-else class="warning">无评卷老师</span>
      </template>
    </div>
    <Table :columns="columns" :data="tableData" border></Table>
    <Page v-model="currentPage" class="page" :page-size="pageSize" :total="cls.students.length"></Page>
  </div>
</template>

<script>
  export default {
    props: {
      cls: Object,
      needMark: Boolean,
    },
    data() {
      return {
        pageSize: 10,
        currentPage: 1,
        columns: [
          {
            title: '姓名',
            key: 'studentName',
          },
          {
            title: '学籍号',
            key: 'studentCode',
            align: 'center',
          },
          {
            title: '班级',
            key: 'className',
            align: 'center',
          },
          {
            title: '准考号',
            key: 'admissionNumber',
            align: 'center',
          },
        ],
      }
    },
    computed: {
      tableData() {
        return this.cls.students.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize)
      },
    },
    watch: {
      cls() {
        this.currentPage = 1
      },
    },
  }
</script>

<style lang="scss" scoped>
  .info {
    margin-bottom: 10px;
  }

  .warning {
    color: $color-warning;
  }

  .page {
    margin-top: 16px;
    text-align: right;
  }
</style>
