import { apiGetBindPaperContent } from '@/api/emarking'

export default {
  namespaced: true,
  state: {
    paperContent: null,
  },
  getters: {
    paperContent(state) {
      return state.paperContent
    },
  },
  mutations: {},
  actions: {
    getPaperContent(context, params) {
      apiGetBindPaperContent({
        examId: params.examId,
        examSubjectId: params.examSubjectId,
      })
        .then(paper => {
          context.state.paperContent = paper
        })
        .catch(() => {
          context.state.paperContent = null
        })
    },
  },
}
