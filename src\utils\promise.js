/**
 * 延迟
 * @param {Number} milliseconds
 * @returns Promise
 */
export function sleep(milliseconds = 1000) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve()
    }, milliseconds)
  })
}

/**
 * 加载图片
 * @param {String} src
 * @returns Promise
 */
export function loadImage(src, crossOrigin = true) {
  return new Promise((resolve, reject) => {
    let img = document.createElement('img')
    img.src = src
    if (crossOrigin) {
      img.crossOrigin = 'anonymous'
    }
    img.onload = () => {
      resolve(img)
    }
    img.onerror = err => {
      reject(err)
    }
  })
}

/**
 * 用fetch方法获取图片 blob
 * @param {String} src
 * @returns Promise
 */
export function loadImageBlob(src) {
  return fetch(src).then(response => {
    if (!response.ok) {
      throw '加载图片失败'
    }
    return response.blob()
  })
}

/**
 * canvas导出blob
 * @param {HTMLCanvasElement} canvas
 * @param {string} type
 * @param {number} quality
 * @returns Promise
 */
export function canvasToBlob(canvas, type = 'image/jpeg', quality = 0.72) {
  return new Promise((resolve, reject) => {
    try {
      canvas.toBlob(
        blob => {
          resolve(blob)
        },
        type,
        quality
      )
    } catch (err) {
      reject(err)
    }
  })
}
