export default class ColorDetector {
  constructor() {
    this.canvas = document.createElement('canvas')
    this.canvas.width = 1
    this.canvas.height = 1
    this.ctx = this.canvas.getContext('2d')
  }

  getColorRGBAValue(colorString) {
    this.ctx.fillStyle = colorString
    this.ctx.fillRect(0, 0, 1, 1)
    let pixel = this.ctx.getImageData(0, 0, 1, 1).data
    return {
      r: pixel[0],
      g: pixel[1],
      b: pixel[2],
      a: pixel[3],
    }
  }
}
