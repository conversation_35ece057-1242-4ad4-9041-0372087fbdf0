import { apiDownloadStudentTopicAnalysis } from '@/api/report'

import Store from '@/store/index'

// 学生大题得分明细
export function generateExcelReportTopicDetailBlob(subject, school, excelShowRank = false, category) {
  let isMultipleSchoolLevel = Store.getters['report/isMultipleSchoolLevel']
  let fileName = `${subject.subjectName}/${Store.getters['report/examName']}_学生大题得分明细_`
  let requestParams = {
    examId: Store.getters['report/examId'],
    templateId: Store.getters['report/templateId'],
    examSubjectId: (subject && subject.examSubjectId) || undefined,
    excelShowRank: excelShowRank,
  }

  if (category && category.categoryName) {
    fileName = category.categoryName + '/' + fileName
  }
  if (isMultipleSchoolLevel) {
    fileName = `学校报表/${school.schoolName}/${fileName}${school.schoolName}`
    requestParams.schoolId = (school && school.schoolId) || undefined
  } else {
    fileName = `${fileName}${Store.getters['report/currentSchoolName']}`
    requestParams.schoolId = Store.getters['report/currentSchoolId'] || undefined
  }
  fileName = `${fileName}_${subject.subjectName}.xlsx`

  return apiDownloadStudentTopicAnalysis(requestParams)
    .then(blob => ({
      blob: blob,
      fileName: fileName,
    }))
    .catch(err => {
      throw {
        error: err,
        fileName: fileName,
      }
    })
}
