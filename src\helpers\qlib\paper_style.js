/**
 * 试卷样式
 */
export class PaperStyle {
  constructor() {
    Object.assign(this, {
      title: {
        enabled: true,
        content: '标题',
      },
      subTitle: {},
      gutter: {},
      secretMark: {},
      studentInfo: {
        enabled: true,
        content: '学校：___________姓名：___________班级：___________考号：___________',
      },
      cautions: {
        enabled: true,
        content: '（1）答题前填写好自己的姓名、班级、考号等信息\n（2）将答案正确填写在答题卡上',
      },
      situation: {
        enabled: false,
        content: '',
      },
      volume: {},
      scoreBox: {},
      examInfo: {
        enabled: true,
        examDuration: 0,
        content: '',
      },
    })
  }

  static import(s) {
    let paperStyle = new PaperStyle()
    if (!s) {
      return paperStyle
    }

    // 标题是必需的
    paperStyle.title = {
      name: '标题',
      enabled: true,
      content: s.title || '标题',
    }
    paperStyle.subTitle = {
      name: '副标题',
      enabled: !!s.subTitle,
      content: s.subTitle || '副标题',
    }
    paperStyle.gutter = {
      name: '装订线',
      enabled: !!Number(s.dispGutter),
      content: '学校：___________姓名：___________班级：___________考号：___________',
    }
    paperStyle.secretMark = {
      name: '保密标记',
      enabled: !!s.secretTag,
      content: s.secretTag || '绝密★启用前',
    }
    paperStyle.studentInfo = {
      name: '考生信息',
      enabled: !!s.studentInfo,
      content: s.studentInfo || '学校：___________姓名：___________班级：___________考号：___________',
    }
    paperStyle.cautions = {
      name: '注意事项',
      enabled: !!s.attentions,
      content: s.attentions || '（1）答题前填写好自己的姓名、班级、考号等信息\n（2）将答案正确填写在答题卡上',
    }
    paperStyle.situation = {
      name: '情境',
      enabled: Boolean(s.situation),
      content: s.situation || '',
    }
    paperStyle.volume = {
      name: '分卷',
      enabled: !!Number(s.dispVolume),
    }
    paperStyle.scoreBox = {
      name: '誊分栏',
      enabled: !!Number(s.dispScoreBox),
    }

    let matched = (s.paperInfo || '').match(/考试时间\s[0-9]+\s*分钟/)
    let examDuration = (matched && parseInt(matched[0].substring(5))) || ''
    paperStyle.examInfo = {
      name: '考试信息',
      enabled: !!s.paperInfo,
      examDuration,
      content: '',
    }

    return paperStyle
  }

  updateExamInfo(totalScore) {
    this.examInfo.content = `试卷满分 ${totalScore || 0} 分，考试时间 ${this.examInfo.examDuration || 0} 分钟`
  }

  updateScoreBox(topics) {
    let tableRows = [
      ['题目', ...topics.map(t => t.codeInChinese), '总分'],
      ['得分', ...topics.map(() => ''), ''],
    ]
    let htmlStr = '<table>'
    tableRows.forEach(r => {
      htmlStr += '<tr>'
      r.forEach(d => {
        htmlStr += `<td>${d}</td>`
      })
      htmlStr += '</tr>'
    })
    htmlStr += '</table>'
    this.scoreBox.content = htmlStr
  }

  export() {
    return {
      dispTitle: (this.title.enabled && this.title.content && 1) || 0,
      dispSubTitle: (this.subTitle.enabled && this.subTitle.content && 1) || 0,
      dispGutter: (this.gutter.enabled && 1) || 0,
      dispSecretTag: (this.secretMark.enabled && this.secretMark.content && 1) || 0,
      dispPaperInfo: (this.examInfo.enabled && this.examInfo.content && 1) || 0,
      dispStudentInfo: (this.studentInfo.enabled && this.studentInfo.content && 1) || 0,
      dispScoreBox: (this.scoreBox.enabled && 1) || 0,
      dispAttentions: (this.cautions.enabled && this.cautions.content && 1) || 0,
      dispSituation: this.situation.enabled && this.situation.content ? 1 : 0,
      dispVolume: (this.volume.enabled && 1) || 0,
      title: (this.title.enabled && this.title.content) || null,
      subTitle: (this.subTitle.enabled && this.subTitle.content) || null,
      secretTag: (this.secretMark.enabled && this.secretMark.content) || null,
      paperInfo: (this.examInfo.enabled && this.examInfo.content) || null,
      studentInfo: (this.studentInfo.enabled && this.studentInfo.content) || null,
      attentions: (this.cautions.enabled && this.cautions.content) || null,
      situation: (this.situation.enabled && this.situation.content) || null,
      template: null,
    }
  }
}
