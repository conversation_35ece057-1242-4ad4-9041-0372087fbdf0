<template>
  <div class="ckeditor-instance" contenteditable="true" @focus="handleFocus" @blur="handleBlur"></div>
</template>

<script>
  // ckeditor自定义插件处理粘贴用到清理dom方法
  import { QDOM } from '@/helpers/qlib/qdom'
  window._qdomClone = QDOM.clone

  export default {
    name: 'CkEditor',
    props: {
      type: {
        type: String,
        default: 'inline',
      },
      value: {
        type: String,
        default: '',
      },
      config: {
        type: Object,
        default: () => ({}),
      },
    },
    emits: ['change', 'ready', 'focus', 'blur'],
    data() {
      return {
        editor: null,
        lastEditorData: undefined,
      }
    },
    watch: {
      value() {
        if (this.value !== this.lastEditorData) {
          this.lastEditorData = this.value
          this.editor.setData(this.wrapMath(this.value))
        }
      },
    },
    mounted() {
      let mathValue = this.wrapMath(this.value)
      this.lastEditorData = mathValue
      this.$el.innerHTML = mathValue
      this.createEditor()
    },
    beforeUnmount() {
      if (this.editor) {
        this.editor.destroy()
        this.editor = null
      }
    },
    methods: {
      createEditor() {
        if (!window.CKEDITOR || window.CKEDITOR.status === 'unloaded') {
          return
        }

        this.editor = window.CKEDITOR[this.type === 'inline' ? 'inline' : 'replace'](this.$el, this.config)
        this.editor.on('change', () => {
          this.lastEditorData = this.unWrapMath(this.editor.getData())
          if (this.value !== this.lastEditorData) {
            this.$emit('change', this.lastEditorData)
          }
        })
        this.editor.on('instanceReady', () => {
          if (this.config.enterMode) {
            this.editor.setActiveEnterMode(this.config.enterMode, this.config.enterMode)
          }
          this.$emit('ready')
        })
      },
      // 识别出公式并包装，供ckeditor公式组件辨认
      wrapMath(value) {
        if (!value) {
          return ''
        }

        let newValue = value
        // 对于tex代码，仅支持行内模式，且不支持\$转义
        let replaceRegs = [/\$[^$]*\$/g, /<math.+?\/math>/gi]
        replaceRegs.forEach(reg => {
          newValue = newValue.replace(reg, match => {
            return `<span class="math-formula">${match}</span>`
          })
        })

        return newValue
      },
      unWrapMath(value) {
        if (!value) {
          return ''
        }

        return value.replace(/<span\s*class="math-formula".*?<\/span>/g, m => {
          let firstEndTagIndex = m.indexOf('>')
          let lastOpenTagIndex = m.lastIndexOf('<')
          return m.slice(firstEndTagIndex + 1, lastOpenTagIndex)
        })
      },
      handleFocus() {
        this.$emit('focus')
      },
      handleBlur() {
        this.$emit('blur')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .ckeditor-instance {
    padding: 10px;
  }

  .ckeditor-instance:focus {
    outline: none;
  }
</style>
