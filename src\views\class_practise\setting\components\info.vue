<template>
  <div class="test-info">
    <div class="rows">
      <div class="row">
        <span class="properties-item">
          <span class="item-label">测验科目：</span>
          <span class="item-content">{{ gradeSubjectName }}</span>
        </span>
        <span class="properties-item">
          <span class="item-label">满分：</span>
          <span class="item-content">{{ totalFullScore }} 分</span>
        </span>
        <span class="properties-item">
          <span class="item-label">创建人：</span>
          <span class="item-content">{{ creatorName }}</span>
        </span>
        <span class="properties-item">
          <span class="item-label">创建时间：</span>
          <span class="item-content">{{ createTime }}</span>
        </span>
      </div>
      <div class="row">
        <span class="properties-item">
          <span class="item-label">测验班级：</span>
          <span class="item-content">{{ classNames }}</span>
        </span>
      </div>
    </div>
    <div class="actions">
      <Button type="primary" :loading="downloadingAnswerSheet" @click="downloadAnswerSheetPdf">下载答题卡</Button>
    </div>
  </div>
</template>

<script>
  import { apiGetClassPractiseScanTemplate } from '@/api/emarking/practise'

  import { downloadUrl } from '@/utils/download'

  export default {
    props: {
      test: Object,
      totalFullScore: Number,
    },
    data() {
      return {
        // 下载答题卡
        downloadingAnswerSheet: false,
      }
    },
    computed: {
      gradeSubjects() {
        return this.$store.getters['emarking/gradeSubjects']()
      },
      gradeSubjectName() {
        let { gradeId, subjectName } = this.test
        let grade = this.gradeSubjects.find(x => x.id == gradeId)
        return `${(grade && grade.gradeName) || ''}${subjectName}`
      },
      creatorName() {
        return this.test.creatorName || '-'
      },
      createTime() {
        return this.test.createTime || '-'
      },
      classNames() {
        return this.test.classList.map(cls => cls.className).join('、')
      },
    },
    methods: {
      async downloadAnswerSheetPdf() {
        if (this.downloadingAnswerSheet) {
          return
        }
        this.downloadingAnswerSheet = true
        try {
          let scanTemplate = await apiGetClassPractiseScanTemplate(this.test.examSubjectId)
          downloadUrl(scanTemplate.pdfUrl, scanTemplate.name + '.pdf')
        } finally {
          this.downloadingAnswerSheet = false
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .test-info {
    @include flex(row, space-between, center);
  }

  .row + .row {
    margin-top: 12px;
  }

  .properties-item {
    margin-right: 40px;
  }
</style>
