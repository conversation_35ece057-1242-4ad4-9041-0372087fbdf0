import ajax from '@/api/ajax'

export function apiGetEMarkingBasicData() {
  return ajax
    .get({
      url: 'mark/base/message',
      requestName: '获取阅卷基础数据',
    })
    .then(data => ({
      examScopes: (data.examScope || []).map(x => ({
        id: Number(x.id),
        name: x.name || '',
      })),
      examModes: (data.examMode || []).map(x => ({
        id: x.id || '',
        name: x.name || '',
      })),
      examTypes: (data.examType || []).map(x => ({
        id: Number(x.id),
        name: x.name || '',
      })),
      examRoles: [
        {
          id: 0,
          name: '考试创建者',
        },
        {
          id: 1,
          name: '考试管理员',
        },
        {
          id: 2,
          name: '学科负责人',
        },
        {
          id: 3,
          name: '科目监控员',
        },
        {
          id: 4,
          name: '科目抽查员',
        },
      ],
      questionTypes: (data.questionType || []).map(x => ({
        id: Number(x.id),
        name: x.name || '',
      })),
      multipleSchoolMarkingModes: (data.multipleSchoolMarkingModes || []).map(x => ({
        id: Number(x.id),
        name: x.name || '',
      })),
      files: (data.files || []).map(x => ({
        id: x.id || '',
        name: x.name || '',
        url: x.url || '',
      })),
      scanModes: (data.scanMode || []).map(x => ({
        id: Number(x.id),
        name: x.name || '',
      })),
      uploadModes: (data.uploadMode || []).map(x => ({
        id: Number(x.id),
        name: x.name || '',
      })),
      stages: (data.gradeLevels || []).map(x => ({
        id: Number(x.id),
        name: x.name || '',
        grades: (x.grades || []).map(g => ({
          id: Number(g.id),
          name: g.name || '',
          subjects: (g.subjects || []).map(s => ({
            id: Number(s.id),
            name: s.name || '',
          })),
        })),
      })),
      markModes: (data.markWay || []).map(x => ({
        id: Number(x.id),
        name: x.name || '',
      })),
      assignModes: (data.assignWay || []).map(x => ({
        id: Number(x.id),
        name: x.name || '',
      })),
      examSubjectStatus: [
        {
          id: 12,
          name: '已开启',
        },
        {
          id: 13,
          name: '已暂停',
        },
        {
          id: 14,
          name: '已完成',
        },
      ],
    }))
}

export function apiGetServerTimeStamp() {
  return ajax.get({
    url: 'mark/base/serverTime',
    requestName: '获取服务器时间',
  })
}
