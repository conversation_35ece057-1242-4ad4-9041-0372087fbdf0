/**
 * 页面块
 */
export default class PageBlock {
  constructor() {
    // 块Id
    this.id = ''
    // 类型
    this.type = ''
    // 在第几页
    this.pageIndex = 0
    // 高度
    this.height = 0
    // 距顶部的高度
    this.top = 0
    // vue组件实例
    this.instance = null
    // 题目块包含的题目
    this.questions = []
  }

  static createExamInfoBlock(height = 0) {
    let block = new PageBlock()
    block.id = 'ExamInfo'
    block.type = 'ExamInfo'
    block.height = height
    return block
  }

  static createQuestionBlock(questions, height = 0) {
    let block = new PageBlock()
    let questionCodes = questions.map(q => `${q.questionCode}.${q.branchCode}`).join(',')
    block.id = `Question-${questionCodes}`
    block.type = 'Question'
    block.height = height
    block.questions = questions
    return block
  }
}
