<template>
  <div class="simple-page">
    <span v-if="showTotal" v-show="total > 0" class="page-total">共 {{ total }} 条</span>
    <Icon
      v-if="showFirstLast"
      type="ios-skip-backward"
      class="btn-first"
      :class="{ disabled: modelValue <= 1 }"
      title="第一页"
      @click="changePage(1)"
    />
    <Page
      size="small"
      :model-value="modelValue"
      :page-size="pageSize"
      simple
      :total="total"
      @on-change="changePage"
      @on-page-size-change="changePageSize"
    ></Page>
    <Icon
      v-if="showFirstLast"
      type="ios-skip-forward"
      class="btn-last"
      :class="{ disabled: modelValue >= totalPage }"
      title="最后一页"
      @click="changePage(totalPage)"
    />
    <Select v-if="showSizer" class="select-page-size" :model-value="pageSize" size="small" @on-change="changePageSize">
      <Option v-for="n in pageSizeOpts" :key="n" :value="n"
        ><span class="page-size-opt">{{ n }} 条/页</span></Option
      >
    </Select>
  </div>
</template>

<script>
  export default {
    props: {
      modelValue: {
        type: Number,
        default: 1,
      },
      total: {
        type: Number,
        default: 0,
      },
      pageSize: {
        type: Number,
        default: 10,
      },
      showTotal: {
        type: Boolean,
        default: true,
      },
      showSizer: {
        type: Boolean,
        default: true,
      },
      pageSizeOpts: {
        type: Array,
        default: () => [10, 20, 50, 100],
      },
      showFirstLast: {
        type: Boolean,
        default: true,
      },
    },
    emits: ['update:modelValue', 'on-change', 'on-page-size-change'],
    computed: {
      totalPage() {
        return Math.ceil(this.total / this.pageSize)
      },
    },
    methods: {
      changePage(page) {
        this.$emit('update:modelValue', page)
        this.$emit('on-change', page)
      },
      changePageSize(size) {
        this.$emit('on-page-size-change', size)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .simple-page {
    @include flex(row, flex-start, center);
    flex-grow: 0;
    flex-shrink: 0;

    :deep(input) {
      width: 50px;
    }

    .page-total {
      margin-right: 8px;
    }

    .select-page-size {
      width: auto;
      margin-left: 8px;
      text-align: center;
    }

    .btn-first {
      margin-left: 5px;
    }

    .btn-last {
      margin-right: 5px;
    }

    .btn-first,
    .btn-last {
      padding-top: 1px;
      font-size: 14px;
      cursor: pointer;
      transition: color 0.2s;

      &:hover {
        color: $color-primary;
      }

      &.disabled {
        color: $color-disabled;
      }
    }

    :deep(.ivu-page-prev, .ivu-page-next) {
      min-width: 24px;
    }
  }
</style>
