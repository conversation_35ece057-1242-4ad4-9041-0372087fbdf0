<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 427318967">
<g id="Group 427318837">
<g id="&#228;&#190;&#167;&#232;&#190;&#185;&#229;&#175;&#188;&#232;&#136;&#170;/&#230;&#149;&#136;&#232;&#131;&#189;&#231;&#173;&#150;&#231;&#149;&#165;&#232;&#174;&#190;&#231;&#189;&#174;">
<rect id="Rectangle 346240753" x="1.5" y="3" width="20" height="14" rx="2" fill="url(#paint0_linear_2271_8066)"/>
<g id="Vector 102" filter="url(#filter0_d_2271_8066)">
<path d="M5 6H13M5 9H9" stroke="url(#paint1_linear_2271_8066)" stroke-width="1.5" stroke-linecap="round" shape-rendering="crispEdges"/>
</g>
<g id="Rectangle 346240754" filter="url(#filter1_b_2271_8066)">
<path d="M4.5 20.5C4.5 19.6716 5.17157 19 6 19H17C17.8284 19 18.5 19.6716 18.5 20.5C18.5 21.3284 17.8284 22 17 22H6C5.17157 22 4.5 21.3284 4.5 20.5Z" fill="#5B9BFF" fill-opacity="0.4"/>
<path d="M4.75 20.5C4.75 19.8096 5.30964 19.25 6 19.25H17C17.6904 19.25 18.25 19.8096 18.25 20.5C18.25 21.1904 17.6904 21.75 17 21.75H6C5.30964 21.75 4.75 21.1904 4.75 20.5Z" stroke="url(#paint2_linear_2271_8066)" stroke-width="0.5"/>
</g>
</g>
</g>
<g id="Group 427318826">
<path id="Vector" d="M16.0776 14.4565C15.7571 14.7771 15.2296 14.7771 14.909 14.4565L13.7404 13.2879C13.4199 12.9674 13.4199 12.4399 13.7404 12.1193C14.061 11.7988 14.5885 11.7988 14.909 12.1193L16.0776 13.2879C16.3982 13.6189 16.3982 14.136 16.0776 14.4565Z" fill="white"/>
<path id="Vector_2" d="M18.1253 12.409L16.0777 14.4567C15.7571 14.7773 15.2297 14.7773 14.9091 14.4567C14.5885 14.1361 14.5885 13.6087 14.9091 13.2881L16.9567 11.2404C17.2773 10.9199 17.8047 10.9199 18.1253 11.2404C18.4459 11.5714 18.4459 12.0885 18.1253 12.409Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_2271_8066" x="3.25" y="4.25" width="13.5" height="8.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.28 0 0 0 0 0.46 0 0 0 0 1 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2271_8066"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2271_8066" result="shape"/>
</filter>
<filter id="filter1_b_2271_8066" x="0.5" y="15" width="22" height="11" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2271_8066"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_2271_8066" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2271_8066" x1="1.73305" y1="17" x2="23.47" y2="13.2128" gradientUnits="userSpaceOnUse">
<stop stop-color="#73BCFF"/>
<stop offset="1" stop-color="#3366FF"/>
</linearGradient>
<linearGradient id="paint1_linear_2271_8066" x1="5.49524" y1="6.33975" x2="7.16419" y2="11.7289" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint2_linear_2271_8066" x1="18.7294" y1="13.9725" x2="22.9524" y2="23.6249" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#2F63FF" stop-opacity="0.2"/>
</linearGradient>
</defs>
</svg>
