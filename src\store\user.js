import Role from '@/enum/user/role'
import { formatDate } from '@/utils/date'
import {
  apiLogin,
  apiGetUserInfo,
  apiGetUserModules,
  apiGetTeacherPositionsAndTeachings,
  apiLogout,
  apiLoginByTheThird,
  apiBindAccountToThird,
  apiLoginBySmsCode,
} from '@/api/user'

function checkInfo(info) {
  return info.userId && info.realName && info.roles && info.roles.length
}

/**
 * 其他标签页登录、退出或切换学校，本标签页回到首页并刷新
 */
// window.addEventListener('storage', e => {
//   if (e.key == 'token' && e.oldValue != e.newValue) {
//     window.location.href = '/'
//   }
// })

export default {
  namespaced: true,
  state: {
    userId: '',
    userName: '',
    realName: '',
    identity: '',
    sex: '',
    mobile: '',
    email: '',
    avatarUrl: '',
    roles: [],
    schoolId: '',
    schoolName: '',
    schoolType: 0,
    teachings: [],
    schoolPositions: [],
    isSystem: false,
    needChangePassword: false,
    needUpdatePassword: false,
    modules: [],
    userType: 0, // 0 - normal user | 1 - reference book scanner
  },
  getters: {
    info(state) {
      return {
        ...state,
      }
    },
    isBureauInstitution(state) {
      return state.schoolType == 1
    },
    isTeacher(state) {
      return state.roles.some(role => role === Role.Teacher.id)
    },
    isSchoolAdministrator(state) {
      return state.roles.includes(Role.SchoolAdministrator.id)
    },
    isSystemSchoolAdministrator(state, getters) {
      return getters.isSchoolAdministrator && state.isSystem
    },
    isSystem(state) {
      return state.isSystem
    },
    isSchoolLeader(state) {
      return state.roles.includes(Role.SchoolLeader.id)
    },
    isAgent(state) {
      return state.roles.some(role => role === Role.Agent.id)
    },
    isSystemAdministrator(state) {
      return state.roles.some(role => role === Role.SystemAdministrator.id)
    },
    isQuestionLibraryAdministrator(state) {
      return state.roles.some(role => role === Role.QuestionLibraryAdministrator.id)
    },
    isQuestionLibraryEditor(state) {
      return state.roles.some(role => role === Role.QuestionLibraryEditor.id)
    },
    isQuestionLibrarySystemUser(state, getters) {
      return getters.isQuestionLibraryAdministrator || getters.isQuestionLibraryEditor
    },
    canViewSchoolData(state) {
      return state.roles.some(role => role === Role.SchoolAdministrator.id || role === Role.SchoolLeader.id)
    },
    canEditSchoolData(state) {
      return state.roles.some(role => role === Role.SchoolAdministrator.id)
    },
    isModuleNormal(state) {
      return moduleId => {
        let module = state.modules.find(x => x.module === moduleId)
        return Boolean(module && module.isExpired == false)
      }
    },
    isModuleExpired(state) {
      return moduleId => {
        let module = state.modules.find(x => x.module === moduleId)
        return Boolean(module && module.isExpired)
      }
    },
    isModuleNotActive(state) {
      return moduleId => {
        let module = state.modules.find(x => x.module === moduleId)
        return Boolean(!module || module.isExpired == null)
      }
    },
    moduleEffectiveGradeIds(state) {
      return moduleId => {
        const Module = state.modules.find(x => x.module === moduleId)
        return Module.gradeIds ? Module.gradeIds.split(',').map(item => Number(item)) : null
      }
    },
    currentUserSchoolId(state) {
      return state.schoolId || ''
    },
    moduleEndDate(state) {
      return moduleId => {
        let module = state.modules.find(x => x.module === moduleId)
        if (module && module.endTime) {
          return formatDate(new Date(module.endTime))
        } else {
          return ''
        }
      }
    },
    isIgradeShowcaseSchool(state, getters) {
      return ['edf28716-f396-40f0-ade7-ece46952503a', '1oq0hr4a603k', '1oq09cd9xtds'].includes(
        // 测试： 同级生六校、同级生智慧学校 | 生产： 同级生智慧学校
        getters.currentUserSchoolId
      )
    },
    isReferenceBookScanner(state) {
      return state.userType === 1
    },
  },
  mutations: {
    updateInfo(state, newInfo) {
      let stateKeys = Object.keys(state)
      Object.keys(newInfo).forEach(key => {
        if (stateKeys.includes(key)) {
          state[key] = newInfo[key]
        }
      })
    },
    updateModules(state, modules) {
      state.modules = modules
    },
    clearInfo(state) {
      state.userId = ''
      state.userName = ''
      state.realName = ''
      state.identity = ''
      state.sex = ''
      state.mobile = ''
      state.email = ''
      state.avatarUrl = ''
      state.roles = []
      state.schoolId = ''
      state.schoolName = ''
      state.schoolType = 0
      state.teachings = []
      state.schoolPositions = []
      state.isSystem = false
      state.needChangePassword = false
      state.needUpdatePassword = false
      state.modules = []
    },
  },
  actions: {
    bindAccountToThird(context, params) {
      return apiBindAccountToThird(params).then(res => {
        if (res.token) {
          context.commit('updateInfo', res.info)
          localStorage.setItem('token', res.token)
          apiGetUserModules().then(modules => {
            context.commit('updateModules', modules)
            if (context.getters.isTeacher || context.getters.isQuestionLibraryEditor) {
              return context.dispatch('getTeacherPositionsAndTeachings')
            }
          })
        }
        return res
      })
    },
    loginByThird(context, loginInfo) {
      return apiLoginByTheThird(loginInfo).then(res => {
        if (res.token) {
          context.commit('updateInfo', res.info)
          localStorage.setItem('token', res.token)
          apiGetUserModules().then(modules => {
            context.commit('updateModules', modules)
            if (context.getters.isTeacher || context.getters.isQuestionLibraryEditor) {
              return context.dispatch('getTeacherPositionsAndTeachings')
            }
          })
        }
        return res
      })
    },
    loginBySms(context, loginInfo) {
      return apiLoginBySmsCode(loginInfo).then(res => {
        if (res.token) {
          context.commit('updateInfo', res.info)
          localStorage.setItem('token', res.token)
          apiGetUserModules().then(modules => {
            context.commit('updateModules', modules)
            if (context.getters.isTeacher || context.getters.isQuestionLibraryEditor) {
              return context.dispatch('getTeacherPositionsAndTeachings')
            }
          })
        }
        return res
      })
    },
    login(context, logInInfo) {
      return apiLogin(logInInfo)
        .then(res => {
          if (!checkInfo(res.info)) {
            throw {
              code: 11,
              msg: '响应用户信息内容错误',
            }
          }
          if (!res.token) {
            throw {
              code: 12,
              msg: '响应token错误',
            }
          }

          context.commit('updateInfo', res.info)
          localStorage.setItem('token', res.token)
          return apiGetUserModules()
        })
        .then(modules => {
          context.commit('updateModules', modules)
          if (context.getters.isTeacher || context.getters.isQuestionLibraryEditor) {
            return context.dispatch('getTeacherPositionsAndTeachings')
          }
        })
        .catch(err => {
          context.commit('clearInfo')
          localStorage.removeItem('token')
          window.sessionStorage && window.sessionStorage.clear()
          // context.dispatch('logout')
          // 避免logout刷新页面，导致登录失败信息无法显示

          throw err
        })
    },

    // 只在没有用户信息时才调用接口
    getInfo(context) {
      if (checkInfo(context.state)) {
        return Promise.resolve(true)
      }

      return context.dispatch('refreshInfo').catch(err => {
        context.dispatch('logout')
        throw err
      })
    },
    refreshInfo(context) {
      return Promise.all([apiGetUserInfo(), apiGetUserModules()]).then(([resInfo, modules]) => {
        context.commit('updateInfo', resInfo)
        context.commit('updateModules', modules)
        if (context.getters.isTeacher || context.getters.isQuestionLibraryEditor) {
          return context.dispatch('getTeacherPositionsAndTeachings')
        }
      })
    },
    getTeacherPositionsAndTeachings(context) {
      return apiGetTeacherPositionsAndTeachings()
        .then(data => {
          context.state.schoolPositions = data.positions
          context.state.teachings = data.teachings
        })
        .catch(() => {
          // 避免接口出错无法登录系统 空数组对应角色无权限
          context.state.schoolPositions = []
          context.state.teachings = []
        })
    },
    logout(context, params) {
      // 本地退出不调退出登录接口，适用于接口报未登录错误
      let request = params && params.locally ? Promise.resolve() : apiLogout()
      return request.finally(() => {
        context.commit('clearInfo')
        localStorage.removeItem('token')
        sessionStorage.clear()
        if (params && params.reason) {
          sessionStorage.setItem('reLoginReason', params.reason)
        }

        // 刷新页面，保证刷新路由
        window.location.href = '/login'
      })
    },
  },
}
