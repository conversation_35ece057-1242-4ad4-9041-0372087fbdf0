<template>
  <Modal
    class="modal-change-type no-print"
    :model-value="modelValue"
    :title="modalTitle"
    :mask-closable="false"
    @on-visible-change="handleVisibleChange"
  >
    <Form :label-width="80" label-position="left">
      <FormItem label="题块题型">
        <div class="radiogroup-wrap">
          <comRadioGroup v-model="blockType" button :radioes="blockTypes"></comRadioGroup>
        </div>
      </FormItem>
      <FormItem v-if="isChineseWriting" label="作文字数">
        <Select :model-value="words" clearable :disabled="isCustomWords" @on-change="changeWords">
          <Option v-for="w in wordsList" :key="w.id" :value="w.id">{{ w.name }}</Option>
        </Select>
        <div class="custom-words">
          <span>自定义字数</span><i-switch v-model="isCustomWords" class="switch-custom-words"></i-switch
          ><InputNumber
            :model-value="words"
            class="input-words"
            :disabled="!isCustomWords"
            :min="1"
            :max="2000"
            :step="10"
            :precision="0"
            controls-outside
            @on-change="changeWords"
          ></InputNumber>
        </div>
      </FormItem>
      <FormItem v-if="isEnglishWriting" label="作文行数">
        <Select v-model="englishWritingRows">
          <Option v-for="r in englishWritingRowList" :key="r.id" :value="r.id">{{ r.name }}</Option>
        </Select>
      </FormItem>
      <FormItem v-if="isEssay" label="每题行数">
        <InputNumber v-model="essayRows" :min="0" :max="20" :precision="0" class="input-short"></InputNumber>
        <span style="margin-left: 2em">生成答题线</span>
        <i-switch v-model="essayShowLine"></i-switch>
      </FormItem>
      <FormItem v-if="isFillBlank" label="每行空数">
        <InputNumber v-model="blanksPerRow" :min="1" :max="5" :precision="0" class="input-short"></InputNumber>
      </FormItem>
    </Form>
    <template #footer>
      <div>
        <Button type="text" @click="handleModalCancel">取消</Button>
        <Button type="primary" @click="handleModalOK">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import comRadioGroup from '@/components/radio_group.vue'

  import { showScrollbarAfterHideModal } from '@/utils/iview'
  import QuestionGroupTypeEnum from '@/enum/answer_sheet/question_group_type'

  // 语文作文字数
  let wordsList = []
  for (let i = 1; i <= 30; i++) {
    wordsList.push({
      id: i * 50,
      name: `${i * 50}字`,
    })
  }
  // 英语作文行数
  let englishWritingRowList = []
  for (let i = 1; i <= 15; i++) {
    englishWritingRowList.push({
      id: i,
      name: `${i}行`,
    })
  }

  export default {
    components: {
      comRadioGroup,
    },
    props: {
      modelValue: Boolean,
      questionGroup: Object,
    },
    emits: ['update:modelValue'],
    data() {
      return {
        blockType: '',
        wordsList: wordsList.slice(),
        words: 900,
        isCustomWords: false,
        englishWritingRowList: englishWritingRowList.slice(),
        englishWritingRows: 9,
        essayRows: 3,
        essayShowLine: false,
        blanksPerRow: 3,
      }
    },
    computed: {
      isSystemSchoolAdministrator() {
        return this.$store.getters['user/isSystemSchoolAdministrator']
      },
      isQuestionLibrarySystemUser() {
        return this.$store.getters['user/isQuestionLibrarySystemUser']
      },
      modalTitle() {
        if (this.questionGroup) {
          let blockName = this.$store.getters['answerSheet/getBlockName'](this.questionGroup.questions[0].questionId)
          return `题块设置（${blockName}）`
        } else {
          return '题块设置'
        }
      },
      isPostScan() {
        return this.$store.getters['answerSheet/isPostScan']
      },
      questionNames() {
        return this.questionGroup.questions.map(q => q.fullCode)
      },
      blockTypes() {
        // 选做时只能是解答题
        if (this.questionGroup.selectGroupName) {
          return [
            {
              id: QuestionGroupTypeEnum.Essay.id,
              name: QuestionGroupTypeEnum.Essay.name,
            },
          ]
        }
        let types = [
          {
            id: QuestionGroupTypeEnum.FillBlank.id,
            name: QuestionGroupTypeEnum.FillBlank.name,
          },
          {
            id: QuestionGroupTypeEnum.Essay.id,
            name: QuestionGroupTypeEnum.Essay.name,
          },
        ]
        let subjectName = this.$store.getters['answerSheet/subject'].name
        if (subjectName == '语文' || this.blockType == QuestionGroupTypeEnum.ChineseWriting.id) {
          types.push({
            id: QuestionGroupTypeEnum.ChineseWriting.id,
            name: QuestionGroupTypeEnum.ChineseWriting.name,
          })
        }
        if (subjectName == '英语' || this.blockType == QuestionGroupTypeEnum.EnglishWriting.id) {
          types.push({
            id: QuestionGroupTypeEnum.EnglishWriting.id,
            name: QuestionGroupTypeEnum.EnglishWriting.name,
          })
        }
        if (this.isPostScan && !this.isSystemSchoolAdministrator && !this.isQuestionLibrarySystemUser) {
          types = types.filter(t => t.id != QuestionGroupTypeEnum.FillBlank.id)
        }
        return types
      },
      isChineseWriting() {
        return this.blockType == QuestionGroupTypeEnum.ChineseWriting.id
      },
      isEnglishWriting() {
        return this.blockType == QuestionGroupTypeEnum.EnglishWriting.id
      },
      isEssay() {
        return this.blockType == QuestionGroupTypeEnum.Essay.id
      },
      isFillBlank() {
        return this.blockType == QuestionGroupTypeEnum.FillBlank.id
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.resetData()
          this.initQuestionGroupData()
        }
      },
    },
    methods: {
      resetData() {
        this.blockType = ''
        this.wordsList = wordsList.slice()
        this.words = 900
        this.isCustomWords = false
        this.englishWritingRowList = englishWritingRowList.slice()
        this.englishWritingRows = 9
        this.essayRows = 3
        this.essayShowLine = false
        this.blanksPerRow = 3
      },
      initQuestionGroupData() {
        this.blockType = this.questionGroup.type
        // 语文作文
        if (this.questionGroup.type == QuestionGroupTypeEnum.ChineseWriting.id) {
          if (!this.wordsList.some(x => x.id === this.questionGroup.words)) {
            this.wordsList.push({
              id: this.questionGroup.words,
              name: this.questionGroup.words + '字',
            })
          }
          this.words = this.questionGroup.words
        }
        // 英语作文
        if (this.questionGroup.type == QuestionGroupTypeEnum.EnglishWriting.id) {
          this.englishWritingRows = this.questionGroup.rows
        }
        // 解答题
        if (this.questionGroup.type == QuestionGroupTypeEnum.Essay.id) {
          this.essayRows = this.questionGroup.rows
          this.essayShowLine = this.questionGroup.showLine
        }
        // 填空题
        if (this.questionGroup.type == QuestionGroupTypeEnum.FillBlank.id) {
          this.blanksPerRow = this.questionGroup.blanksPerRow
        }
      },
      changeWords(value) {
        if (typeof value != 'number') {
          return
        }
        this.words = value
      },
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.handleModalCancel()
        }
      },
      handleModalCancel() {
        this.$emit('update:modelValue', false)
        setTimeout(() => {
          showScrollbarAfterHideModal()
        }, 500)
      },
      handleModalOK() {
        let data
        try {
          data = this.checkAndGetChangeData()
        } catch (err) {
          this.$Message.info({
            content: err,
            duration: 5,
            closable: true,
          })
          return
        }

        let doChangeType = () => {
          this.handleModalCancel()
          this.$store.commit('answerSheet/changeQuestionGroupType', data)
          this.$store.commit('answerSheet/generatePages')
        }
        // 仅更改作文字数不会清除内容
        if (data.type == this.questionGroup.type && data.type == QuestionGroupTypeEnum.ChineseWriting.id) {
          doChangeType()
        } else {
          this.$Modal.confirm({
            title: '确认修改',
            content: '本区域内的内容将重新生成',
            onOk: doChangeType,
          })
        }
      },
      checkAndGetChangeData() {
        if (!this.blockTypes.some(t => t.id == this.blockType)) {
          throw '请选择题块类型'
        }

        let data = {
          questionGroupId: this.questionGroup.id,
          type: this.blockType,
        }
        let changed = this.blockType != this.questionGroup.type

        if (this.blockType == QuestionGroupTypeEnum.ChineseWriting.id) {
          if (this.questionNames.length > 1) {
            throw `本题块包含多道小题，无法设置为语文作文题：${this.questionNames.join('、')}`
          }
          if (typeof this.words != 'number') {
            throw '请选择作文字数'
          }
          if (!Number.isInteger(this.words)) {
            throw '作文字数必须是整数'
          }
          if (!(this.words > 0 && this.words <= 2000)) {
            throw '作文字数须在2000字以内'
          }
          data.words = this.words
          if (data.words != this.questionGroup.words) {
            changed = true
          }
        } else if (this.blockType === QuestionGroupTypeEnum.EnglishWriting.id) {
          if (this.questionNames.length > 1) {
            throw `本题块包含多道小题，无法设置为英语作文题：${this.questionNames.join('、')}`
          }
          if (!(this.englishWritingRows > 0)) {
            throw '请选择作文行数'
          }
          data.rows = this.englishWritingRows
          if (data.rows != this.questionGroup.rows) {
            changed = true
          }
        } else if (this.blockType == QuestionGroupTypeEnum.Essay.id) {
          if (!(this.essayRows >= 0)) {
            throw '请输入每题行数'
          }
          if (
            changed &&
            this.isPostScan &&
            this.questionGroup.type == QuestionGroupTypeEnum.FillBlank.id &&
            this.questionGroup.questions.length > 1
          ) {
            throw `先阅后扫解答题按小题打分，本题块包含多道小题，请先拆分`
          }
          data.rows = this.essayRows
          data.showLine = this.essayShowLine
          if (data.rows != this.questionGroup.rows || data.showLine != this.questionGroup.showLine) {
            changed = true
          }
        } else if (this.blockType === QuestionGroupTypeEnum.FillBlank.id) {
          if (!(this.blanksPerRow >= 1 && this.blanksPerRow <= 5)) {
            throw '请选择每行空数'
          }
          data.blanksPerRow = this.blanksPerRow
          if (data.blanksPerRow != this.questionGroup.blanksPerRow) {
            changed = true
          }
        } else {
          throw '类型错误'
        }

        if (!changed) {
          throw '未作更改'
        }

        return data
      },
    },
  }
</script>

<style lang="scss" scoped>
  .custom-words {
    margin-top: 8px;

    .switch-custom-words {
      margin-right: 16px;
      margin-left: 4px;
    }

    .input-words {
      width: 130px;
    }
  }
</style>
