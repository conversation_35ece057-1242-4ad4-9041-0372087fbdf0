import { sortQuestionFunction } from '@/helpers/emarking/miscellaneous'
import SubjectiveArea from '@/helpers/scan_template/subjective_area'
import { groupArray } from '@/utils/array'
import LocateModeEnum from '@/enum/scan_template/locate_mode'
import AdmissionTypeEnum from '@/enum/scan_template/admission_type'
import ScoreTypeEnum from '@/enum/answer_sheet/score_type'

export function setPages({ imageUrls, template, objectiveDefines, blockDefines }) {
  let { objectives: templateObjectives, subjectives: templateSubjectives } = template.questions

  let objAreaIdQuestionNamesMap = getObjAreaIdQuestionNamesMap(templateObjectives, objectiveDefines)
  let subjectBlocks = getSubjectBlocks(blockDefines)
  let { subjAreaIdBlockMap, selectAreaIdBlockMap } = getSubjAreaIdBlockMapAndSelectAreaIdBlockMap(
    templateSubjectives,
    subjectBlocks
  )
  let blockIdAreasMap = getBlockIdSortedAreasMap(subjAreaIdBlockMap, template.pages)
  let sameAreaSelectGroups = getSameAreaSelectGroups(subjectBlocks, blockIdAreasMap)
  let scoreAreaIdQuestionNameMap = getScoreAreaIdQuestionNameMap(templateSubjectives, blockDefines)

  return template.pages.map(p => {
    let name = template.isDoubleSide
      ? `第${Math.ceil(p.pageIndex / 2)}张${p.pageIndex % 2 == 1 ? '正面' : '背面'}`
      : `第${p.pageIndex}张`
    return {
      pageIndex: p.pageIndex,
      name,
      imageUrl: imageUrls[p.pageIndex - 1] || p.fileUrl,
      elements: getPageElements(p),
      admissionOmrCells: getPageAdmissionOmrCells(p),
      objectiveOmrCells: getPageObjectiveOmrCells(p, objAreaIdQuestionNamesMap),
      subjectiveAreas: getPageSubjectiveAreas(p, subjAreaIdBlockMap, sameAreaSelectGroups, blockIdAreasMap),
      selectMarkAreas: getPageSelectMarkAreas(p, selectAreaIdBlockMap),
      scoreAreas: getPageScoreAreas(p, scoreAreaIdQuestionNameMap),
    }
  })
}

// 客观题名称映射
function getObjAreaIdQuestionNamesMap(templateObjectives, subjectObjectives) {
  let objAreaIdQuestionNamesMap = new Map()
  templateObjectives.forEach(obj => {
    let questionNames = obj.questions.map(q => {
      let question = subjectObjectives.find(x => x.questionCode == q.questionCode)
      return question ? question.questionName : `${q.questionCode}`
    })
    objAreaIdQuestionNamesMap.set(obj.areaId, questionNames)
  })
  return objAreaIdQuestionNamesMap
}

// 科目题块定义
function getSubjectBlocks(blocks) {
  return blocks.map(block => {
    let blockQuestions = block.questions.slice()
    blockQuestions.sort(sortQuestionFunction)
    let mergedCodes = blockQuestions.map(q => `${q.questionCode}.${q.branchCode}`).join(',')
    let questionNameScoresText = blockQuestions
      .map(q => {
        let name
        if (q.branchName) {
          name = q.branchName
        }
        if (q.branchCode == 0) {
          name = `${q.questionCode}题`
        } else {
          name = `${q.questionCode}.${q.branchCode}题`
        }
        return `${name}:${q.fullScore}分`
      })
      .join('，')
    return {
      blockId: block.blockId,
      blockName: block.blockName,
      selectGroupName: block.selectGroupName,
      selectCount: block.selectCount,
      questions: block.questions,
      mergedCodes,
      questionNameScoresText,
    }
  })
}

// 主观题区域与题块映射、选做题区域与题块映射
function getSubjAreaIdBlockMapAndSelectAreaIdBlockMap(templateSubjectives, subjectBlocks) {
  let subjAreaIdBlockMap = new Map()
  let selectAreaIdBlockMap = new Map()
  templateSubjectives.forEach(subj => {
    // 按照包含题目找对应题块
    let subjQuestions = subj.questions.slice()
    subjQuestions.sort(sortQuestionFunction)
    let mergedCodes = subjQuestions.map(q => `${q.questionCode}.${q.branchCode}`).join(',')
    let block = subjectBlocks.find(x => x.mergedCodes == mergedCodes)

    let blockId = block ? block.blockId : ''
    let blockName = block ? block.blockName : ''
    let questionNameScoresText = block ? block.questionNameScoresText : ''
    let firstQuestionCode = block ? block.questions[0].questionCode : ''
    subj.areaIds.forEach(areaId => {
      subjAreaIdBlockMap.set(areaId, {
        blockId,
        blockName,
        questionNameScoresText,
      })
    })
    if (subj.selectMarkId) {
      selectAreaIdBlockMap.set(subj.selectMarkId, {
        blockId,
        blockName,
        firstQuestionCode,
      })
    }
  })

  return { subjAreaIdBlockMap, selectAreaIdBlockMap }
}

// 相同区域的选做题组
function getSameAreaSelectGroups(subjectBlocks, blockIdAreasMap) {
  // 选做题组
  let selectGroups = groupArray(
    subjectBlocks.filter(block => block.selectGroupName),
    block => block.selectGroupName
  ).map(g => {
    return {
      selectGroupName: g.group[0].selectGroupName,
      selectCount: g.group[0].selectCount,
      blocks: g.group,
    }
  })
  if (selectGroups.length == 0) {
    return []
  }

  return selectGroups.filter(({ blocks }) => {
    let blockAreasList = blocks.map(block => blockIdAreasMap.get(block.blockId))
    if (blockAreasList.some(areas => !areas)) {
      return false
    }
    let areaCount = blockAreasList[0].length
    if (blockAreasList.some(areas => areas.length != areaCount)) {
      return false
    }
    // 同一题块的多个区域排序
    if (areaCount > 1) {
      blockAreasList.forEach(areas => {
        areas.sort((a, b) => {
          if (a.pageIndex == b.pageIndex) {
            if (a.rect.y == b.rect.y) {
              return a.rect.x - b.rect.x
            } else {
              return a.rect.y - b.rect.y
            }
          } else {
            return a.pageIndex - b.pageIndex
          }
        })
      })
    }

    for (let areaIdx = 0; areaIdx < areaCount; areaIdx++) {
      let firstBlockArea = blockAreasList[0][areaIdx]
      for (let blockIdx = 1; blockIdx < blockAreasList.length; blockIdx++) {
        if (!SubjectiveArea.isSameRect(firstBlockArea, blockAreasList[blockIdx][areaIdx])) {
          return false
        }
      }
    }
    return true
  })
}

// 打分区域与小题名映射
function getScoreAreaIdQuestionNameMap(templateSubjectives, blockDefines) {
  let subjectSubjectives = []
  blockDefines.forEach(block => {
    subjectSubjectives.push(...block.questions)
  })
  let map = new Map()
  templateSubjectives.forEach(subj => {
    subj.questions.forEach(q => {
      let question = subjectSubjectives.find(x => x.questionCode == q.questionCode && x.branchCode == q.branchCode)
      let branchName =
        (question && question.branchName) ||
        (q.branchCode == 0 ? `${q.questionCode}` : `${q.questionCode}.${q.branchCode}`)
      // 填空题有多个对错框则加填空序号
      if (q.scoreAreaIds.length > 1 && q.scoreType == ScoreTypeEnum.TrueFalse.id) {
        q.scoreAreaIds.forEach((id, idx) => {
          map.set(id, `${branchName}_${idx + 1}`)
        })
      } else {
        q.scoreAreaIds.forEach(id => {
          map.set(id, branchName)
        })
      }
    })
  })
  return map
}

// 题块Id与主观题区域Id映射，主观题区域按顺序排
function getBlockIdSortedAreasMap(subjAreaIdBlockMap, templatePages) {
  let map = new Map()
  templatePages.forEach(page => {
    page.subjectiveAreas.forEach(area => {
      let block = subjAreaIdBlockMap.get(area.id)
      if (!block) {
        return
      }
      let areas = map.get(block.blockId)
      if (!areas) {
        areas = []
        map.set(block.blockId, areas)
      }
      areas.push({
        id: area.id,
        pageIndex: page.pageIndex,
        rect: area.area,
      })
    })
  })
  return map
}

// 页面元素
function getPageElements(p) {
  let elements = []
  let {
    correctType,
    anchorMarks,
    lineArea,
    textArea,
    textArea2,
    titleArea,
    pageMark,
    pageMark2,
    subjectArea,
    qrcodeArea,
    blockDetectSkipAreas,
    studentInfoAreas,
  } = p.pageParam
  if (correctType == LocateModeEnum.Anchor.id) {
    anchorMarks.forEach(anchor => {
      if (anchor) {
        elements.push({
          id: `anchor-${p.pageIndex}-${anchor.id}`,
          label: '定位点',
          rect: anchor.matchArea,
        })
      }
    })
  }
  if (correctType == LocateModeEnum.Line.id && lineArea) {
    elements.push({
      id: `line-${p.pageIndex}`,
      label: '定位线',
      rect: lineArea,
    })
  }
  if (correctType == LocateModeEnum.Text.id) {
    if (textArea) {
      elements.push({
        id: `text-${p.pageIndex}`,
        label: '定位文字',
        rect: textArea,
      })
    }
    if (textArea2) {
      elements.push({
        id: `text2-${p.pageIndex}`,
        label: '定位文字',
        rect: textArea2,
      })
    }
  }
  if (titleArea) {
    elements.push({
      id: `title-${p.pageIndex}`,
      label: '标题',
      rect: titleArea,
    })
  }
  if (pageMark) {
    elements.push({
      id: `pageMark-${p.pageIndex}`,
      label: '图形页码标记',
      rect: pageMark.matchArea,
    })
  }
  if (pageMark2) {
    elements.push({
      id: `pageMark2-${p.pageIndex}`,
      label: '文字页码标记',
      rect: pageMark2.matchArea,
    })
  }
  if (subjectArea) {
    elements.push({
      id: `subjectArea-${p.pageIndex}`,
      label: '科目标记',
      rect: subjectArea,
    })
  }
  if (qrcodeArea) {
    elements.push({
      id: `qrcodeArea-${p.pageIndex}`,
      label: '科目二维码',
      rect: qrcodeArea,
    })
  }
  if (blockDetectSkipAreas && blockDetectSkipAreas.length > 0) {
    blockDetectSkipAreas.forEach((area, idx) => {
      elements.push({
        id: `blockDetectSkipArea-${p.pageIndex}-${idx + 1}`,
        label: '遮挡忽略区域',
        rect: area,
      })
    })
  }
  if (studentInfoAreas && studentInfoAreas.length > 0) {
    studentInfoAreas.forEach((area, idx) => {
      elements.push({
        id: `studentInfoArea-${p.pageIndex}-${idx + 1}`,
        label: '考生信息屏蔽区域',
        rect: area,
      })
    })
  }

  let { admissionType, admissionBarcodeArea, admissionHandwritingArea, missingMarkArea } = p.studentInfo
  if (admissionType == AdmissionTypeEnum.Barcode.id && admissionBarcodeArea) {
    elements.push({
      id: `barcode-${p.pageIndex}`,
      label: '考号条码',
      rect: admissionBarcodeArea.searchArea,
    })
  }
  if (admissionHandwritingArea) {
    admissionHandwritingArea.cells.forEach((cell, idx) => {
      elements.push({
        id: `handwriting-${idx + 1}-${p.pageIndex}`,
        label: idx == 0 ? '手写考号' : '',
        rect: cell,
      })
    })
  }
  if (missingMarkArea) {
    elements.push({
      id: `missingMark-${p.pageIndex}`,
      label: '缺考标记',
      rect: missingMarkArea.matchArea,
    })
  }
  return elements
}

// 填涂考号
function getPageAdmissionOmrCells(p) {
  let cells = []
  let { admissionType, admissionOmrArea } = p.studentInfo
  if (admissionType == AdmissionTypeEnum.Omr.id && admissionOmrArea) {
    let qIdx = 0
    let optIdx = 0
    admissionOmrArea.options.forEach(opt => {
      cells.push({
        id: `admission-${p.pageIndex}-${qIdx}-${optIdx}`,
        content: `${optIdx}`,
        rect: opt,
      })
      optIdx++
      if (optIdx == admissionOmrArea.optionCount) {
        optIdx = 0
        qIdx++
      }
    })
  }
  return cells
}

// 客观题
function getPageObjectiveOmrCells(p, objAreaIdQuestionNamesMap) {
  let cells = []
  p.objectiveAreas.forEach(area => {
    let questionNames = objAreaIdQuestionNamesMap.get(area.id) || []
    let qIdx = 0
    let optIdx = 0
    area.options.forEach(opt => {
      let optChar = String.fromCharCode(65 + optIdx)
      let cell = {
        id: `obj-${area.id}-${qIdx}-${optIdx}`,
        content: optChar,
        rect: opt,
      }
      if (optIdx == 0) {
        cell.questionName = questionNames[qIdx] || ''
        cell.direction = area.direction
      }
      cells.push(cell)

      optIdx++
      if (optIdx == area.optionCount) {
        optIdx = 0
        qIdx++
      }
    })
  })
  return cells
}

// 主观题
function getPageSubjectiveAreas(p, subjAreaIdBlockMap, sameAreaSelectGroups, blockIdAreasMap) {
  let areas = []
  p.subjectiveAreas.forEach(area => {
    let block = subjAreaIdBlockMap.get(area.id)
    let selectGroup = null
    if (block) {
      selectGroup = sameAreaSelectGroups.find(g => g.blocks.some(b => b.blockId == block.blockId))
    }
    // 无关选做，正常加入
    if (!selectGroup) {
      areas.push({
        id: `subj-${area.id}`,
        blockId: block ? block.blockId : '',
        blockName: block ? block.blockName : '',
        partInfo: getPartInfo(area.id, block.blockId, blockIdAreasMap),
        questionNameScoresText: block ? block.questionNameScoresText : '',
        rect: area.area,
      })
      return
    }
    // 若同一选做组已加入其他题块的区域，则跳过
    let selectGroupBlockIds = selectGroup.blocks.map(block => block.blockId)
    if (areas.some(a => selectGroupBlockIds.includes(a.blockId) && a.blockId != block.blockId)) {
      return
    }
    areas.push({
      id: `subj-${area.id}`,
      blockId: block.blockId,
      blockName: selectGroup.blocks.map(block => block.blockName).join('；'),
      partInfo: getPartInfo(area.id, block.blockId, blockIdAreasMap),
      questionNameScoresText: selectGroup.blocks.map(block => block.questionNameScoresText).join('；'),
      rect: area.area,
    })
  })
  return areas
}

// 第几块
function getPartInfo(areaId, blockId, blockIdAreasMap) {
  let areas = blockIdAreasMap.get(blockId)
  if (!areas || areas.length <= 1) {
    return ''
  }
  let index = areas.findIndex(x => x.id == areaId)
  return `【第 ${index + 1} 块】`
}

// 选做标记
function getPageSelectMarkAreas(p, selectAreaIdBlockMap) {
  return p.selectMarkAreas.map(area => {
    let block = selectAreaIdBlockMap.get(area.id)
    return {
      id: `select-${area.id}`,
      content: block ? block.firstQuestionCode : '',
      rect: area.matchArea,
    }
  })
}

// 打分
function getPageScoreAreas(p, scoreAreaIdQuestionNameMap) {
  let list = []
  p.manualScoreAreas.forEach(area => {
    let questionName = scoreAreaIdQuestionNameMap.get(area.id) || ''
    area.digits.forEach((digit, idx) => {
      list.push({
        id: `${area.id}-${idx}-${p.pageIndex}`,
        label: idx == 0 ? questionName : '',
        rect: digit,
      })
    })
  })
  p.trueFalseScoreAreas.forEach(area => {
    let questionName = scoreAreaIdQuestionNameMap.get(area.id) || ''
    list.push({
      id: `${area.id}-${p.pageIndex}`,
      label: questionName,
      rect: area.searchArea,
    })
  })
  p.barScoreAreas.forEach(area => {
    let questionName = scoreAreaIdQuestionNameMap.get(area.id) || ''
    area.cells.forEach((cell, idx) => {
      list.push({
        id: `${area.id}-${idx}-${p.pageIndex}`,
        label: idx == 0 ? questionName : '',
        rect: cell.searchArea,
      })
    })
  })
  return list
}
