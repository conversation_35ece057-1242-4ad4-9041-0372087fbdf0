import ajax from '@/api/ajax'

export function apiGetSemesterInstitutionCoachBookStat(requestParams) {
  return ajax.get({
    url: 'ques/coachStuCard/activeStatByOrg',
    params: {
      semesterId: requestParams.semester,
      term: requestParams.term, // 1 - 第一学期 | 2 - 第二学期
      gradeId: requestParams.gradeId,
      subjectId: requestParams.subjectId,
      coachBookName: requestParams.coachBookName, // 教辅名称或征订代码
      schoolId: requestParams.schoolId,
      regionId: requestParams.regionId,
    },
    requestName: '查询机构某学期教辅激活统计',
  })
}

export function apiGetSemesterInstitutionCoachBookCountStat(requestParams) {
  return ajax.get({
    url: 'ques/coachStuCard/countStatByOrg',
    params: {
      semesterId: requestParams.semester,
      term: requestParams.term, // 1 - 第一学期 | 2 - 第二学期
      gradeId: requestParams.gradeId,
      subjectId: requestParams.subjectId,
      coachBookName: requestParams.coachBookName, // 教辅名称或征订代码
      schoolId: requestParams.schoolId,
      regionId: requestParams.regionId,
    },
    requestName: '查询机构某学期教辅激活总数统计',
  })
}

export function apiGetSemesterSchoolCoachBookStat(requestParams) {
  return ajax.get({
    url: 'ques/coachStuCard/activeStatBySchool',
    params: {
      semesterId: requestParams.semester,
      term: requestParams.term, // 1 - 第一学期 | 2 - 第二学期
      gradeId: requestParams.gradeId,
      subjectId: requestParams.subjectId,
      coachBookName: requestParams.coachBookName, // 教辅名称或征订代码
      regionId: requestParams.regionId,
    },
    requestName: '查询本校某学期教辅激活统计',
  })
}

export function apiGetSemesterSchoolCoachBookCountStat(requestParams) {
  return ajax.get({
    url: 'ques/coachStuCard/countStatBySchool',
    params: {
      semesterId: requestParams.semester,
      term: requestParams.term, // 1 - 第一学期 | 2 - 第二学期
      gradeId: requestParams.gradeId,
      subjectId: requestParams.subjectId,
      coachBookName: requestParams.coachBookName, // 教辅名称或征订代码
      regionId: requestParams.regionId,
    },
    requestName: '查询本校某学期教辅激活总数统计',
  })
}

export function apiGetSemesterInstitutionSchoolStat(requestParams) {
  return ajax.get({
    url: 'ques/coachStuCard/schoolStatByOrg',
    params: {
      semesterId: requestParams.semester,
      term: requestParams.term, // 1 - 第一学期 | 2 - 第二学期
      gradeId: requestParams.gradeId,
      subjectId: requestParams.subjectId,
      coachBookName: requestParams.coachBookName,
      schoolId: requestParams.schoolId,
      regionId: requestParams.regionId,
    },
    requestName: '查询机构激活教辅学校统计',
  })
}

export function apiGetSemesterSchoolStat(requestParams) {
  return ajax.get({
    url: 'ques/coachStuCard/schoolStatBySchool',
    params: {
      semesterId: requestParams.semesterId,
      term: requestParams.term, // 1 - 第一学期 | 2 - 第二学期
      gradeId: requestParams.gradeId,
      subjectId: requestParams.subjectId,
      coachBookName: requestParams.coachBookName,
    },
    requestName: '查询学校激活教辅统计',
  })
}

export function apiGetInstitutionActivationCodeList(requestParams) {
  return ajax.get({
    url: 'ques/coachStuCard/listCardByOrg',
    params: {
      semesterId: requestParams.semester,
      term: requestParams.term,
      gradeId: requestParams.gradeId,
      subjectId: requestParams.subjectId,
      coachBookName: requestParams.coachBookName,
      coachBookId: requestParams.coachBookId,
      status: requestParams.status, // 1 - 第一学期 | 2 - 第二学期
      cardNo: requestParams.cardNo,
      studentName: requestParams.studentName,
      pageNum: requestParams.currentPage || 1,
      pageSize: requestParams.pageSize || 10,
      beginTime: requestParams.beginTime,
      endTime: requestParams.endTime,
      schoolId: requestParams.schoolId,
    },
    requestName: '查询机构激活码列表',
  })
}

export function apiGetSchoolActivationCodeList(requestParams) {
  return ajax.get({
    url: 'ques/coachStuCard/listCardBySchool',
    params: {
      semesterId: requestParams.semester,
      term: requestParams.term,
      gradeId: requestParams.gradeId,
      subjectId: requestParams.subjectId,
      coachBookName: requestParams.coachBookName,
      coachBookId: requestParams.coachBookId,
      status: requestParams.status, // 1 - 第一学期 | 2 - 第二学期
      cardNo: requestParams.cardNo,
      studentName: requestParams.studentName,
      pageNum: requestParams.currentPage || 1,
      pageSize: requestParams.pageSize || 10,
      beginTime: requestParams.beginTime,
      endTime: requestParams.endTime,
    },
    requestName: '查询本校激活码列表',
  })
}
