import ajax from '@/api/ajax'

// 小题分析对比
export function apiOrgGetSameLevelQuesAnalyseCompare(requestParams) {
  return ajax.get({
    url: 'report/org/orgBrotherQuesAnalyseCompare',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    requestName: '获取同级机构小题分析对比',
  })
}

export function apiOrgGetNextLevelQuesAnalyseCompare(requestParams) {
  return ajax.get({
    url: 'report/org/orgSonQuesAnalyseCompare',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    requestName: '获取下级机构小题分析对比',
  })
}

// 大题分析对比
export function apiOrgGetSameLevelQuesTopicAnalyseCompare(requestParams) {
  return ajax.get({
    url: 'report/org/orgBrotherQuesTopicAnalyseCompare',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    requestName: '获取同级机构大题分析对比',
  })
}

export function apiOrgGetNextLevelQuesTopicAnalyseCompare(requestParams) {
  return ajax.get({
    url: 'report/org/orgSonQuesTopicAnalyseCompare',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    requestName: '获取下级机构大题分析对比',
  })
}

// 名次段统计对比
export function apiOrgGetSameLevelRankIntervalCompare(requestParams) {
  return ajax.post({
    url: 'report/org/orgBrotherRankIntervalCompare',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    data: requestParams.ranges,
    requestName: '获取同级机构名次段统计对比',
  })
}

export function apiOrgGetNextLevelRankIntervalCompare(requestParams) {
  return ajax.post({
    url: 'report/org/orgSonRankIntervalCompare',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    data: requestParams.ranges,
    requestName: '获取下级机构名次段统计对比',
  })
}
export function apiOrgGetNextLevelRankIntervalCompareOfSeniorHigh(requestParams) {
  return ajax.post({
    url: 'report/org/orgSonRankIntervalCompare/v2',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
      categoryId: requestParams.categoryId,
      subjectCode: requestParams.subjectCode,
    },
    data: requestParams.ranges || [],
    requestName: '获取下级机构名次段统计对比（高中选科）',
  })
}

// 分数段统计对比
export function apiOrgGetSameLevelScoreIntervalCompare(requestParams) {
  return ajax.post({
    url: 'report/org/orgBrotherScoreIntervalCompare',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    data: requestParams.ranges,
    requestName: '获取同级机构分数段统计对比',
  })
}

export function apiOrgGetNextLevelScoreIntervalCompare(requestParams) {
  return ajax.post({
    url: 'report/org/orgSonScoreIntervalCompare',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    data: requestParams.ranges || [],
    requestName: '获取下级机构分数段统计对比',
  })
}
export function apiOrgGetNextLevelScoreIntervalCompareOfSeniorHigh(requestParams) {
  return ajax.post({
    url: 'report/org/orgSonScoreIntervalCompare/v2',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
      categoryId: requestParams.categoryId,
      subjectCode: requestParams.subjectCode,
    },
    data: requestParams.ranges || [],
    requestName: '获取下级机构分数段统计对比（高中选科）',
  })
}

// 学校对比
export function apiOrgGetSameLevelSubjCompare(requestParams) {
  return ajax.get({
    url: 'report/org/orgBrotherSubjCompare',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
      sid: requestParams.subjectId,
    },
    requestName: '获取同级机构学校科目对比',
  })
}

export function apiOrgGetNextLevelSubjCompare(requestParams) {
  return ajax.get({
    url: 'report/org/orgSonSubjCompare',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
      sid: requestParams.subjectId,
    },
    requestName: '获取下级机构学校科目对比',
  })
}
export function apiOrgGetNextLevelSubjCompareOfSeniorHigh(requestParams) {
  return ajax.get({
    url: 'report/org/orgSonSubjCompare/v2',
    params: {
      orgSchId: requestParams.organizationId, // String
      subjectCategory: requestParams.subjectCategory, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
      subjectCode: requestParams.subjectCode, // String
      templateId: requestParams.templateId, // String
    },
    requestName: '获取高中子机构科目对比',
  })
}

// 知识点分析对比
export function apiOrgGetSameLevelKnowledgeAnalyseCompare(requestParams) {
  return ajax.get({
    url: 'report/org/orgBrotherKnowAnalyseCompare',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    requestName: '获取同级机构知识点分析对比',
  })
}

export function apiOrgGetNextLevelKnowledgeAnalyseCompare(requestParams) {
  return ajax.get({
    url: 'report/org/orgSonKnowAnalyseCompare',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    requestName: '获取下级机构知识点分析对比',
  })
}

// （子）机构科目组合对比
export function apiOrgGetNextLevelSubjCombinationCompare(requestParams) {
  return ajax.get({
    url: 'report/org/orgSonSubjectCombinationSubjCompare',
    params: {
      orgSchId: requestParams.organizationId,
      templateId: requestParams.templateId,
      subjectCode: requestParams.subjectCode,
    },
    requestName: '获取下级机构科目组合对比',
  })
}

// 获取模板下的机构树
export function apiGetExamInstitutionTree(requestParams) {
  return ajax.get({
    url: 'report/org/tree',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
    },
    requestName: '获取考试模板下的机构树',
  })
}

// 获取考试中心校 - 教学点
// export function apiGetCenterSchoolSpot(templateId) {
//   return ajax.get({
//     url: 'report/org/centerSpot',
//     params: {
//       templateId: templateId,
//     },
//     requestName: '获取考试中心校和教学点的映射关系',
//   })
// }

export function apiGetExamCenterSpotSchoolList(requestParams) {
  return ajax.get({
    url: 'report/org/examSchoolTree',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
    },
    requestName: '获取考试中心校教学点',
  })
}
