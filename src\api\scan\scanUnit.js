import ajax from '@/api/ajax'
import { formatDateTime } from '@/utils/date'
import ScanUnitRecognizeStatusEnum from '@/enum/scan/scan_unit_recognize_status'

export function transformScanUnit(unit) {
  unit.adjustImgUrls = splitByComma(unit.adjustImgUrls)
  unit.pageNos = splitByComma(unit.pageNos).map(Number)
  // 匹配失败，pagenos不排序
  if (unit.recogStatus != ScanUnitRecognizeStatusEnum.MatchFail.id) {
    unit.pageNos.sort()
  }
  unit.pageNoText = unit.pageNos.join(',')
  unit.paperIds = splitByComma(unit.paperIds)
  unit.papers.forEach(paper => {
    paper.imgUrls = splitByComma(paper.imgUrls)
  })
  unit.objectives = unit.objectives ? JSON.parse(unit.objectives) : []
  unit.objectives.sort((a, b) => a.questionId - b.questionId)
  unit.scores = unit.scores
    ? JSON.parse(unit.scores)
    : {
        manualScores: [],
        barScores: [],
        trueFalseScores: [],
      }
  // 接口可能未返回打分条分数
  ;((unit.scores && unit.scores.barScores) || []).forEach(s => {
    if (s.score === undefined) {
      s.score = null
    }
  })
  unit.selects = unit.selects ? JSON.parse(unit.selects) : []
  unit.batchNumberText = `${unit.scanClient}-${unit.batchNo}`
  unit.paperNoText = unit.papers
    .map(paper => paper.paperNo)
    .sort()
    .join(',')
  let scanTime = new Date(unit.insertTime)
  unit.scanTimeText = formatDateTime(scanTime, 'MM-DD HH:mm:ss')
  unit.scanDateTimeText = formatDateTime(scanTime)
  return unit
}

function splitByComma(str) {
  if (!str) {
    return []
  }
  return str.split(',').filter(Boolean)
}

export function apiGetScanUnitDetail({ examSubjectId, scanUnitId }) {
  return ajax
    .get({
      url: '/scan/scanUnit/detail',
      params: {
        examSubjectId,
        scanUnitId,
      },
      requestName: '获取扫描单元详情',
    })
    .then(data => {
      if (data) {
        return transformScanUnit(data)
      } else {
        return data
      }
    })
}

export function apiDeleteScanUnit(params) {
  return ajax.delete({
    url: 'scan/scanUnit/delete',
    params: {
      examSubjectId: params.examSubjectId,
      scanUnitId: params.scanUnitId,
    },
    requestName: '删除答卷',
  })
}

export function apiDeleteScanUnitBatch(data) {
  return ajax.delete({
    url: 'scan/scanUnit/deleteBatch',
    params: {
      examSubjectId: data.examSubjectId,
    },
    data: data.scanUnitIds,
    requestName: '删除答卷',
  })
}

export function apiRecoverScanUnit(params) {
  return ajax.put({
    url: 'scan/scanUnit/recover',
    params: {
      examSubjectId: params.examSubjectId,
      scanUnitId: params.scanUnitId,
    },
    requestName: '恢复已删除答卷',
  })
}

export function apiIgnoreSubjectException({ examSubjectId, scanUnitId }) {
  return ajax.put({
    url: 'scan/scanUnit/ignoreSubjectEx',
    params: {
      examSubjectId,
      scanUnitId,
    },
    requestName: '忽略科目异常',
  })
}

export function apiIgnoreSubjectExceptionBatch({ examSubjectId, scanUnitIds }) {
  return ajax.put({
    url: 'scan/scanUnit/ignoreSubjectExBatch',
    params: {
      examSubjectId,
    },
    data: scanUnitIds,
    requestName: '批量忽略科目异常',
  })
}

export function apiIgnoreCornerException({ examSubjectId, scanUnitId }) {
  return ajax.put({
    url: 'scan/scanUnit/ignoreCornerEx',
    params: {
      examSubjectId,
      scanUnitId,
    },
    requestName: '忽略折角异常',
  })
}

export function apiIgnoreCornerExceptionBatch({ examSubjectId, scanUnitIds }) {
  return ajax.put({
    url: 'scan/scanUnit/ignoreCornerExBatch',
    params: {
      examSubjectId,
    },
    data: scanUnitIds,
    requestName: '批量忽略折角异常',
  })
}

export function apiChangeAdmissionNum(params) {
  return ajax.put({
    url: 'scan/scanUnit/admissionNum',
    params: {
      examSubjectId: params.examSubjectId,
      scanUnitId: params.scanUnitId,
      admissionNum: params.admissionNum,
    },
    requestName: '修改准考号',
  })
}

export function apiChangeAbsent(params) {
  return ajax.put({
    url: 'scan/scanUnit/absent',
    params: {
      examSubjectId: params.examSubjectId,
      scanUnitId: params.scanUnitId,
      isAbsent: params.isAbsent,
    },
    requestName: '修改缺考',
  })
}

export function apiVerifyAbsentException({ examSubjectId, scanUnitId, autoVerifyObjective }) {
  return ajax.put({
    url: 'scan/scanUnit/absentEx/verify',
    params: {
      examSubjectId,
      scanUnitId,
      autoVerifyObjective,
    },
    requestName: '确认缺考异常',
  })
}

export function apiChangeObjectives(data) {
  return ajax.put({
    url: 'scan/scanUnit/objective',
    params: {
      examSubjectId: data.examSubjectId,
      scanUnitId: data.scanUnitId,
    },
    data: data.objectives,
    requestName: '修改客观题',
  })
}

export function apiVerifyObjectiveException(params) {
  return ajax.put({
    url: 'scan/scanUnit/objectiveEx/verify',
    params: {
      examSubjectId: params.examSubjectId,
      scanUnitId: params.scanUnitId,
    },
    requestName: '忽略客观题异常',
  })
}

export function apiVerifyAllObjectiveException({ examSubjectId, isOnlyEmptyChoise }) {
  return ajax.put({
    url: 'scan/scanUnit/ignoreObjectiveExBySubject',
    params: {
      examSubjectId,
      isOnlyEmptyChoise,
    },
    requestName: '忽略所有客观题异常',
  })
}

export function apiChangeSubjectives(data) {
  return ajax.put({
    url: 'scan/scanUnit/subjective',
    params: {
      examSubjectId: data.examSubjectId,
      scanUnitId: data.scanUnitId,
    },
    data: data.scores,
    requestName: '修改主观题打分',
  })
}

export function apiVerifySubjectiveException(params) {
  return ajax.put({
    url: 'scan/scanUnit/subjectiveEx/verify',
    params: {
      examSubjectId: params.examSubjectId,
      scanUnitId: params.scanUnitId,
    },
    requestName: '忽略打分异常',
  })
}

export function apiChangeSelects(data) {
  return ajax.put({
    url: 'scan/scanUnit/select',
    params: {
      examSubjectId: data.examSubjectId,
      scanUnitId: data.scanUnitId,
    },
    data: data.selects,
    requestName: '修改选做题',
  })
}

export function apiFixAllSelectEx({
  examSubjectId,
  scanStationId,
  scanUserId,
  fixEmptySelect,
  fixLessSelect,
  fixMultiSelect,
}) {
  return ajax.put({
    url: 'scan/scanUnit/select/fix',
    params: {
      examSubjectId,
      scanStationId,
      scanUserId,
      fixEmptySelect,
      fixLessSelect,
      fixMultiSelect,
    },
    requestName: '清除所有已扫答卷选做异常',
  })
}

// 强制转为定位正常
export function apiSetLocateNormal(data) {
  return ajax.put({
    url: 'scan/scanUnit/toAnchorNormal',
    params: {
      examSubjectId: data.examSubjectId,
      scanUnitId: data.scanUnitId,
    },
    data: data.pages,
  })
}

// 调整页面顺序
export function apiChangePageOrder(data) {
  return ajax.put({
    url: 'scan/scanUnit/changePageOrder',
    params: {
      examSubjectId: data.examSubjectId,
      scanUnitId: data.scanUnitId,
    },
    data: data.pages,
  })
}

export function apiRedoRecognize(data) {
  data.pages.forEach(page => {
    page.anchors.forEach(anchor => {
      if (anchor) {
        anchor.x = Math.round(anchor.x)
        anchor.y = Math.round(anchor.y)
        anchor.width = Math.round(anchor.width)
        anchor.height = Math.round(anchor.height)
      }
    })
  })
  return ajax.put({
    url: 'scan/scanUnit/redoRecognize',
    params: {
      examId: data.examId,
      examSubjectId: data.examSubjectId,
      scanUnitId: data.scanUnitId,
    },
    data: data.pages,
    requestName: '重新定位识别',
  })
}

// 获取是否存在扫描异常或未上传
export function apiGetExistsExceptionOrUnupload(examSubjectId) {
  return ajax.get({
    url: 'scan/scanUnit/existsScanUnitExceptionOrUnupload',
    params: { examSubjectId },
    requestName: '获取是否存在扫描异常',
  })
}
