import ajax from '@/api/ajax'
import BranchTypeEnum from '@/enum/qlib/branch_type'

function transformBasicData(data) {
  let difficulties = (data.difficulty || [])
    .map(d => ({
      id: Number(d.pmco),
      name: d.pmnm,
    }))
    .sort((a, b) => a.id - b.id)

  let stages = (data.stages || [])
    .map(s => ({
      id: Number(s.id),
      name: s.name,
      grades: (s.grades || [])
        .map(g => ({
          id: Number(g.id),
          name: g.name,
        }))
        .sort((a, b) => a.id - b.id),
      subjects: (s.subjects || [])
        .map(ss => ({
          id: Number(ss.id),
          name: ss.name,
          // 教材已有序
          books: (ss.books || []).map(b => ({
            id: Number(b.id),
            name: b.name,
            pressId: Number(b.pressId),
            pressName: b.press,
          })),
          questionTypes: (ss.types || [])
            .map(qt => {
              // 默认小题题型是解答题
              let branchTypeId = Number(qt.categoryId)
              if (!BranchTypeEnum.hasId(branchTypeId)) {
                branchTypeId = BranchTypeEnum.Essay.id
              }
              // 默认所有小题都有题干，选择题都有选项
              let branchHasStem = true
              let branchHasOptions = true
              if (qt.extra) {
                let extra = JSON.parse(qt.extra)
                if (extra.branchHasStem === false) {
                  branchHasStem = false
                }
                if (extra.branchHasOptions === false) {
                  branchHasOptions = false
                }
              }
              return {
                id: Number(qt.id),
                name: qt.name || '',
                order: Number(qt.orderId) || 0,
                groupName: qt.groupName || '',
                branchTypeId,
                branchTypeName: BranchTypeEnum.getNameById(branchTypeId),
                branchHasStem,
                branchHasOptions,
              }
            })
            .sort((a, b) => a.order - b.order),
        }))
        .sort((a, b) => a.id - b.id),
      paperTypes: (s.paperTypes || [])
        .map(pt => ({
          id: Number(pt.id),
          name: pt.name,
        }))
        .sort((a, b) => a.id - b.id),
      terms: (s.terms || [])
        .map(term => ({
          id: Number(term.id),
          name: term.name,
        }))
        .sort((a, b) => a.id - b.id),
    }))
    .sort((a, b) => a.id - b.id)

  return {
    difficulties,
    stages,
  }
}

function transformRegionData(data) {
  return data.region.map(x => ({
    value: x.id,
    label: x.name,
    children: x.cities.map(y => ({
      value: y.id,
      label: y.name,
      children: y.counties.map(z => ({
        value: z.id,
        label: z.name,
      })),
    })),
  }))
}

export function apiGetQlibBasicData() {
  return ajax
    .get({
      url: 'ques/base/message',
      requestName: '获取题库基础数据',
    })
    .then(data => {
      return transformBasicData(data)
    })
}

export function apiDeleteQlibBasicDataCache() {
  return ajax.delete({
    url: 'ques/base/deleteMessageCache',
    requestName: '清除题库基础数据缓存',
  })
}

export function apiGetRegions() {
  return ajax
    .get({
      url: 'ques/base/region',
      requestName: '请求区域数据',
    })
    .then(data => {
      return transformRegionData(data)
    })
}

export function apiDeleteRegionCache() {
  return ajax.delete({
    url: 'ques/base/deleteRegionCache',
    requestName: '清除区域数据缓存',
  })
}
