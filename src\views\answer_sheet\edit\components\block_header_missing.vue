<template>
  <div class="missing">
    <div class="label" :style="labelStyle">缺考标记</div>
    <div class="missing-omr" :style="omrStyle"></div>
    <div class="tip" :style="tipStyle">（由监考员填涂）</div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { MissingMark } from '@/helpers/answer_sheet/header'

  export default {
    props: {
      size: {
        type: Object,
        required: true,
      },
    },
    computed: {
      ...mapGetters('answerSheet', ['headerGroupLayout']),
      labelStyle() {
        return MissingMark.getLabelStyle(this.size.width)
      },
      omrStyle() {
        return MissingMark.getOmrStyle(this.size.width)
      },
      tipStyle() {
        return MissingMark.getTipStyle(this.size.width)
      },
    },
    methods: {
      getTemplateElements() {
        let rect = MissingMark.getOmrPosition()
        rect.left += this.size.left + this.headerGroupLayout.borderWidth
        rect.top += this.size.top + this.headerGroupLayout.borderWidth
        return {
          rect,
        }
      },
    },
  }
</script>
