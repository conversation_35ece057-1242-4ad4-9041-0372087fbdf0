/**
 * 小题题型
 */

import Enum from '@/enum/enum'

const BranchTypeEnum = new Enum({
  SingleChoice: {
    id: 11,
    name: '单选题',
    isChoice: true,
    isObjective: true,
  },
  MultipleChoice: {
    id: 12,
    name: '多选题',
    isChoice: true,
    isObjective: true,
  },
  TrueOrFalse: {
    id: 13,
    name: '判断题',
    isChoice: false,
    isObjective: true,
  },
  FillBlank: {
    id: 21,
    name: '填空题',
    isChoice: false,
    isObjective: false,
  },
  Essay: {
    id: 22,
    name: '解答题',
    isChoice: false,
    isObjective: false,
  },
  ChineseWriting: {
    id: 23,
    name: '语文作文',
    isChoice: false,
    isObjective: false,
  },
  EnglishWriting: {
    id: 24,
    name: '英语作文',
    isChoice: false,
    isObjective: false,
  },
})

export default BranchTypeEnum

export const ObjectiveBranchTypeEnum = BranchTypeEnum.subEnum(
  BranchTypeEnum.getEntries()
    .filter(entry => entry.isObjective)
    .map(entry => entry.key)
)

export const SubjectiveBranchTypeEnum = BranchTypeEnum.subEnum(
  BranchTypeEnum.getEntries()
    .filter(entry => !entry.isObjective)
    .map(entry => entry.key)
)
