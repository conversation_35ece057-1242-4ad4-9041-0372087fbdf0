import ajax from '@/api/ajax'

export function apiGetPaperLog(requestParams) {
  return ajax.get({
    url: 'ques/quesLog/listQuesLog',
    params: {
      schoolId: requestParams.schoolId || undefined,
      type: requestParams.type || undefined, // [Number] 1 - 浏览； 2 - 下载
      beginTime: requestParams.beginTime || undefined,
      endTime: requestParams.endTime || undefined,
      paperId: requestParams.paperId || undefined,
      paperName: requestParams.paperName || undefined,
      pageNum: requestParams.currentPage || 1,
      pageSize: requestParams.pageSize || 10,
    },
    requestName: '试卷日志列表',
  })
}

export function apiGetPaperStats(requestParams) {
  return ajax.get({
    url: 'ques/quesLog/statistic',
    params: {
      beginTime: requestParams.beginTime || undefined,
      endTime: requestParams.endTime || undefined,
      gradeLevel: requestParams.gradeLevel || undefined,
      subjectId: requestParams.subjectId || undefined,
      paperName: requestParams.paperName || undefined,
      pageNum: requestParams.currentPage || 1,
      pageSize: requestParams.pageSize || 10,
    },
    requestName: '试卷统计',
  })
}
