<template>
  <div :class="containerClass">
    <span
      v-for="radio in list"
      :key="radio.id"
      class="radio-item"
      :class="{ 'radio-item-active': radio.active }"
      :style="{ cursor: disabled ? 'not-allowed' : 'pointer' }"
      @click="changeRadio(radio.id)"
      >{{ radio.name }}</span
    >
  </div>
</template>

<script>
  export default {
    props: {
      radioes: {
        type: Array,
      },
      modelValue: {
        type: [String, Number, Array],
      },
      multiple: {
        type: Boolean,
        default: false,
      },
      button: {
        type: Boolean,
        default: false,
      },
      hasOptionAll: {
        type: Boolean,
        default: false,
      },
      optionAllName: {
        type: String,
        default: '全部',
      },
      canSelectNone: {
        type: Boolean,
        default: false,
      },
      multipleCanSelectNone: {
        type: Boolean,
        default: true,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['update:modelValue'],

    computed: {
      valueArray() {
        return this.modelValue instanceof Array ? this.modelValue : this.modelValue == null ? [] : [this.modelValue]
      },

      list() {
        let radioArray = (this.radioes || []).map(x => ({
          id: x.id,
          name: x.name,
          active: this.valueArray.includes(x.id),
        }))

        if (this.hasOptionAll && this.radioes.length != 0) {
          radioArray.unshift({
            id: -1,
            name: this.optionAllName,
            active: radioArray.every(x => x.active === true),
          })
        }

        return radioArray
      },

      containerClass() {
        return {
          'com-radio-group': true,
          'com-radio-group-button': this.button,
          'disabled-status': this.disabled,
        }
      },
    },

    methods: {
      changeRadio(radioId) {
        if (this.disabled) {
          return
        }

        let radio = this.list.find(x => x.id === radioId)

        if (this.multiple) {
          if (this.hasOptionAll) {
            let isSelected = !radio.active
            if (radioId === -1) {
              this.list.forEach(x => (x.active = isSelected))
            } else {
              radio.active = !radio.active
            }
            let exceptAll = this.list.slice(1)

            this.$emit(
              'update:modelValue',
              exceptAll.filter(x => x.active).map(x => x.id)
            )
          } else {
            if (this.multipleCanSelectNone || !(this.list.filter(x => x.active).length == 1 && radio.active)) {
              radio.active = !radio.active
              this.$emit(
                'update:modelValue',
                this.list.filter(x => x.active).map(x => x.id)
              )
            }
          }
        } else {
          if (radio) {
            // 检测已选项数组, 若大于1特殊处理
            let selectedItems = (this.list || []).filter(x => x.active)
            if (selectedItems.length > 1) {
              let firstSelectItem = selectedItems.find(x => x.id !== radioId && x.active)
              this.$emit('update:modelValue', firstSelectItem.id)
            } else if (!radio.active) {
              this.$emit('update:modelValue', radioId)
            } else if (this.canSelectNone) {
              this.$emit('update:modelValue')
            }
          }
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .com-radio-group {
    @include flex;
    flex-wrap: wrap;
    user-select: none;

    .radio-item {
      box-sizing: border-box;
      height: 24px;
      margin-right: 10px;
      padding: 0 8px;
      line-height: 24px;
    }

    .radio-item.radio-item-active {
      border-radius: 3px;
      color: white;
      background-color: $color-info;
    }

    .radio-item.radio-item-active:hover {
      color: white;
    }

    .radio-item:hover {
      color: $color-primary;
    }
  }

  .com-radio-group.com-radio-group-button {
    .radio-item {
      height: auto;
      margin-right: 0;
      padding: 3px 8px;
      border: 1px solid $color-info;
      border-radius: 0;
      line-height: auto;

      &:not(:first-child) {
        border-left: none;
      }

      &:first-child {
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
      }

      &:last-child {
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
      }
    }
  }

  .disabled-status {
    opacity: 0.4;
  }
</style>
