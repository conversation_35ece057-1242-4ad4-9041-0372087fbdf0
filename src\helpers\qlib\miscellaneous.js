import BranchTypeEnum from '@/enum/qlib/branch_type'

export function getOptionLabelByIndex(index) {
  if (index < 26) {
    return String.fromCharCode(65 + index)
  } else {
    return ''
  }
}

export function getOptionIndexByLabel(label) {
  let _label = String(label).toUpperCase()
  return _label.charCodeAt(0) - 65
}

export function needSelectChapter(subjectName) {
  // 只有语文英语需要关联题目到章节
  if (subjectName === '语文' || subjectName === '英语') {
    return true
  } else {
    return false
  }
}

export function getDifficultyNumber(q) {
  if (q.scoringRate > 0) {
    return q.scoringRate
  } else {
    if (q.difficulty.id === 1) {
      return 0.8
    } else if (q.difficulty.id === 2) {
      return 0.7
    } else if (q.difficulty.id === 3) {
      return 0.6
    } else if (q.difficulty.id === 4) {
      return 0.45
    } else if (q.difficulty.id === 5) {
      return 0.3
    } else {
      return 0.6
    }
  }
}

export function getKnowledgesFromQuestions(quesitons) {
  let knowledges = []
  quesitons.forEach(q => {
    q.branches.forEach(b => {
      b.knowledges.forEach(k => {
        if (knowledges.every(x => x.id != k.id)) {
          knowledges.push({
            id: k.id,
            name: k.name,
          })
        }
      })
    })
  })
  knowledges.sort((a, b) => a.id - b.id)
  return knowledges
}

/**
 * 题目题型支持的小题题型Id
 * 解答题下可包含单选题、多选题、判断题、填空题、解答题，其他题型只能包含相同题型
 */
export function getQuestionTypeValidBranchTypeIds(questionType) {
  if (questionType.branchTypeId == BranchTypeEnum.Essay.id) {
    return [
      BranchTypeEnum.SingleChoice.id,
      BranchTypeEnum.MultipleChoice.id,
      BranchTypeEnum.TrueOrFalse.id,
      BranchTypeEnum.FillBlank.id,
      BranchTypeEnum.Essay.id,
    ]
  } else {
    return [questionType.branchTypeId]
  }
}

/**
 * 拆解填空题各空分数
 */
export function splitBlankScores(fullScore, blankCount) {
  let blankScores = []
  let scorePerBlank = Math.floor((fullScore * 2) / blankCount) / 2
  let scoreSum = 0
  for (let i = 0; i < blankCount - 1; i++) {
    blankScores.push(scorePerBlank)
    scoreSum += scorePerBlank
  }
  blankScores.push(fullScore - scoreSum)
  return blankScores
}
