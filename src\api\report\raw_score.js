import ajax from '@/api/ajax'

export function apiGetRawScoreList({ examId, examSubjectId, schoolId, currentPage, pageSize }) {
  return ajax.get({
    url: 'report/rawScore/listBySubject',
    params: {
      examId,
      examSubjectId,
      schoolId,
      pageNum: currentPage,
      pageSize,
    },
    requestName: '查询原始分',
  })
}

export function apiExportRawScoreExcel({ examId, examSubjectId, schoolId }) {
  return ajax.download({
    url: 'report/rawScore/export',
    params: {
      examId,
      examSubjectId,
      schoolId,
    },
    requestName: '导出原始分',
  })
}
