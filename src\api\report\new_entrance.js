import ajax from '@/api/ajax'

// 获取高中大类科目
export function apiGetHighSchoolGradeSubjectList(requestParams) {
  return ajax.get({
    url: 'report/tem/listCategorySubject',
    params: {
      schoolId: requestParams.schoolId, // String
      templateId: requestParams.templateId, // String
    },
    requestName: '获取高中大类科目列表',
  })
}

// 新高考报表
export function apiGetNewEntranceGroupList(params) {
  return ajax.get({
    url: 'report/newEntrance/groupList',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      schoolId: params.schoolId,
      classId: params.classId,
    },
    requestName: '获取新高考报告列表',
  })
}

export function apiGetThreeOnePage(params) {
  return ajax.get({
    url: 'report/newEntrance/threeOnePage',
    params: {
      current: params.currentPage,
      size: params.pageSize,
      examId: params.examId,
      templateId: params.templateId,
      schoolId: params.schoolId,
      classId: params.classId,
      orgSchId: params.organizationId,
    },
    requestName: '获取新高考3+1报告',
  })
}

export function apiExportThreeOnePage(params) {
  return ajax.download({
    url: 'report/newEntrance/export/stuThreeOne',
    params: {
      examId: params.examId,
      classId: params.classId,
      schoolId: params.schoolId,
      templateId: params.templateId,
      orgSchId: params.organizationId,
    },
    requestName: '导出新高考3+1报告',
  })
}

export function apiGetThreeOneOnePage(params) {
  return ajax.get({
    url: 'report/newEntrance/threeOneOnePage',
    params: {
      current: params.currentPage,
      size: params.pageSize,
      examId: params.examId,
      templateId: params.templateId,
      schoolId: params.schoolId,
      classId: params.classId,
      group: params.subjectIds,
      orgSchId: params.organizationId,
    },
    requestName: '获取新高考3+1+1报告',
  })
}

export function apiExportThreeOneOnePage(params) {
  return ajax.download({
    url: 'report/newEntrance/export/stuThreeOneOne',
    params: {
      examId: params.examId,
      classId: params.classId,
      schoolId: params.schoolId,
      templateId: params.templateId,
      orgSchId: params.organizationId,
      // group: params.group,
    },
    requestName: '导出新高考3+1+1报告',
  })
}

export function apiGetThreeOneTwoPage(params) {
  return ajax.get({
    url: 'report/newEntrance/threeOneTwoPage',
    params: {
      current: params.currentPage,
      size: params.pageSize,
      examId: params.examId,
      templateId: params.templateId,
      schoolId: params.schoolId,
      classId: params.classId,
      group: params.subjectIds,
      orgSchId: params.organizationId,
    },
    requestName: '获取新高考3+1+2报告',
  })
}

export function apiExportThreeOneTwoPage(params) {
  return ajax.download({
    url: 'report/newEntrance/export/stuThreeOneTwo',
    params: {
      examId: params.examId,
      classId: params.classId,
      schoolId: params.schoolId,
      templateId: params.templateId,
      orgSchId: params.organizationId,
      group: params.group,
    },
    requestName: '导出新高考3+1+2报告',
  })
}

export function apiGetThreeOneFivePage(params) {
  return ajax.get({
    url: 'report/newEntrance/threeOneFivePage',
    params: {
      current: params.currentPage,
      size: params.pageSize,
      examId: params.examId,
      templateId: params.templateId,
      schoolId: params.schoolId,
      classId: params.classId,
      group: params.subjectIds,
      orgSchId: params.organizationId,
    },
    requestName: '获取新高考3+1+5报告',
  })
}

export function apiExportThreeOneFivePage(params) {
  return ajax.download({
    url: 'report/newEntrance/export/stuThreeOneFive',
    params: {
      examId: params.examId,
      classId: params.classId,
      schoolId: params.schoolId,
      templateId: params.templateId,
      orgSchId: params.organizationId,
      // group: params.group,
    },
    requestName: '导出新高考3+1+5报告',
  })
}
