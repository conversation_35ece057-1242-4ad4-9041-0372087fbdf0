import ajax from '@/api/ajax'

export function apiGetStudentRegistrationStats(requestParams) {
  return ajax.get({
    url: 'review/registration/studentStats',
    params: {
      activityId: requestParams.activityId, // *String
      categoryId: requestParams.categoryId, // Number
      schoolId: requestParams.schoolId, // String
      gradeId: requestParams.gradeId, // Number
      classId: requestParams.classId, // String
      groupByCategory: requestParams.groupByCategory, // Boolean default false
      groupBySchool: requestParams.groupBySchool, // Boolean default false
      groupByGrade: requestParams.groupByGrade, // Boolean default false
      groupByClass: requestParams.groupByClass, // Boolean default false
    },
    requestName: '查询学生报名统计',
  })
}

export function apiGetTeacherRegistrationStats(requestParams) {
  return ajax.get({
    url: 'review/registration/teacherStats',
    params: {
      activityId: requestParams.activityId, // *String
      categoryId: requestParams.categoryId, // Number
      schoolId: requestParams.schoolId, // String
      groupByCategory: requestParams.groupByCategory, // Boolean default false
      groupBySchool: requestParams.groupBySchool, // Boolean default false
    },
    requestName: '查询教师报名统计',
  })
}

export function apiGetStudentRegistrationList(requestParams) {
  return ajax.get({
    url: 'review/registration/studentPage',
    params: {
      page: requestParams.currentPage || 1,
      size: requestParams.pageSize || 10,
      activityId: requestParams.activityId,
      categoryId: requestParams.categoryId,
      schoolId: requestParams.schoolId,
      gradeId: requestParams.gradeId,
      classId: requestParams.classId,
      status: requestParams.status,
      keyword: requestParams.keyword,
    },
    requestName: '按页查询学生报名名单',
  })
}

export function apiGetTeacherRegistrationList(requestParams) {
  return ajax.get({
    url: 'review/registration/teacherPage',
    params: {
      page: requestParams.currentPage || 1,
      size: requestParams.pageSize || 10,
      activityId: requestParams.activityId,
      categoryId: requestParams.categoryId,
      schoolId: requestParams.schoolId,
      status: requestParams.status,
      keyword: requestParams.keyword,
    },
    requestName: '按页查询教师报名名单',
  })
}

export function apiSubmitRegistration(requestParams) {
  return ajax.put({
    url: 'review/registration/submit',
    params: {
      activityId: requestParams.activityId, // *Number
      categoryId: requestParams.categoryId, // *Number
      schoolId: requestParams.schoolId, // *String
      userId: requestParams.userId, // *String
    },
    requestName: '提交报名',
  })
}

export function apiWithdrawRegistration(requestParams) {
  return ajax.put({
    url: 'review/registration/withdraw',
    params: {
      activityId: requestParams.activityId, // *Number
      categoryId: requestParams.categoryId, // *Number
      schoolId: requestParams.schoolId, // *String
      userId: requestParams.userId, // *String
    },
    requestName: '撤回报名',
  })
}

export function apiDeleteRegistrationRecord(requestParams) {
  return ajax.delete({
    url: 'review/registration/delete',
    params: {
      activityId: requestParams.activityId, // *Number
      categoryId: requestParams.categoryId, // *Number
      schoolId: requestParams.schoolId, // *String
      userId: requestParams.userId, // *String
    },
    requestName: '删除报名记录',
  })
}

export function apiCreateRegistrationRecord(requestParams) {
  return ajax.post({
    url: 'review/registration/register',
    params: {
      activityId: requestParams.activityId, // *Number
      categoryId: requestParams.categoryId, // *Number
      schoolId: requestParams.schoolId, // *String
      userId: requestParams.userId, // *String
      submit: requestParams.submit, // *Boolean
    },
    // data: requestParams.formJson, // *String JSON
    data: requestParams.requestData, // *Object {formJson, file1, file2, file3...}
    requestName: '新增报名记录',
    useFormData: true,
  })
}

export function apiEditRegistrationRecord(requestParams) {
  return ajax.post({
    url: 'review/registration/editRegisterData',
    params: {
      activityId: requestParams.activityId, // *Number
      categoryId: requestParams.categoryId, // *Number
      schoolId: requestParams.schoolId, // *String
      userId: requestParams.userId, // *String
      submit: requestParams.submit, // *Boolean
    },
    // data: requestParams.formJson, // *String JSON
    data: requestParams.requestData, // *Object {formJson, file1, file2, file3...}
    requestName: '编辑报名记录',
    useFormData: true,
  })
}

export function apiGetStudentRegistrationDetail(requestParams) {
  return ajax.get({
    url: 'review/registration/oneStudent',
    params: {
      activityId: requestParams.activityId, // *Number
      categoryId: requestParams.categoryId, // *Number
      schoolId: requestParams.schoolId, // *String
      userId: requestParams.userId, // *String
    },
    requestName: '获取指定学生报名信息',
  })
}

export function apiGetTeacherRegistrationDetail(requestParams) {
  return ajax.get({
    url: 'review/registration/oneTeacher',
    params: {
      activityId: requestParams.activityId, // *Number
      categoryId: requestParams.categoryId, // *Number
      schoolId: requestParams.schoolId, // *String
      userId: requestParams.userId, // *String
    },
    requestName: '获取指定教师报名信息',
  })
}

export function apiRegistrationAudit(auditResult) {
  return ajax.put({
    url: 'review/registration/audit',
    data: auditResult, // *Object:
    /**
     *  {
     *    activityId: Number,
     *    categoryId: Number，
     *    comments: String,
     *    decision: String('approved' | 'rejected')
     *    submissionId: Number,
     *    userId: String,
     *  }
     */
    requestName: '审核报名',
  })
}
