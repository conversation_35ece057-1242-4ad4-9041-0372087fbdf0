import xlsx from 'xlsx'
import iView from '@/iview'
import { groupArray } from '@/utils/array'

/**
 * 从excel导入数据。需要改写，第一部分提取数据，第二部分检查数据，简化参数
 * @param {Function} callback 回调函数
 * @param {Object} options 导入选项
 * options: {
 *   columns: [
 *     {
 *       title: String,                                 // excel表格中的列名称，如：学生姓名
 *       key: String,                                   // 单元格数据对应行对象的键，如：className
 *       required: Boolean,                             // 列是否必需，必需的列：1，标题一定存在；2，单元格值不能为空。这是一种常见检查规则
 *       uniq: Boolean,                                 // 列中各单元格数据是否不能重复。这是一种常见检查规则
 *       range: Array,                                  // 列中各单元格数据是否只能取该集合中的值。这是一种常见检查规则
 *       cellRules: [String function(row, cellValue)]   // 自定义单元格检查规则。每个规则都是一个接收行对象、单元格值并返回字符串的函数，如果返回字符串不为空，则弹窗报错
 *     }
 *   ],
 *   rowRules: [String function(row)],                  // 自定义行检查规则。每个规则都是一个接收行对象并返回字符串的函数，如果返回字符串不为空，则弹窗报错
 *   tableRules: [String | Array function(rows)]        // 自定义表检查规则。每个规则都是一个接收行对象数组并返回字符串的函数，如果返回字符串不为空，则弹窗报错
 * }
 */

/**
 * 流程
 * 1. 选择文件
 * 2. 提取数据
 * 3. 检查数据
 * 4. 转换数据
 */

export default function excelImport(callback, options) {
  selectFile(async file => {
    try {
      let arr = await getDataArrayFromFile(file)
      addRowNum(arr)
      checkData(arr, options)
      let result = transFormData(arr, options)
      callback(result)
    } catch (err) {
      let title = '',
        message = ''
      if (err && typeof err === 'object') {
        title = err.title
        message = err.message
      } else {
        message = err
      }

      iView.Modal.error({
        title: '导入失败' + (title ? '：' + title : ''),
        content: message || '',
      })
    }
  })
}

function selectFile(callback) {
  let elInput = document.createElement('input')
  elInput.type = 'file'
  elInput.accept = '.xls,.xlsx'
  elInput.multiple = false
  elInput.onchange = () => {
    let files = elInput.files
    let file = files[0]
    if (!file) {
      return
    }

    if (!/\.xls$|\.xlsx/.test(file.name)) {
      iView.Message.error({
        content: '请选择excel文件',
      })
      return
    }

    if (file.size > 1024 * 100) {
      iView.Message.error({
        content: '文件大小不能超过100K',
      })
      return
    }

    callback(file)
  }
  elInput.click()
}

function getDataArrayFromFile(file) {
  return new Promise((resolve, reject) => {
    let reader = new FileReader()
    reader.onerror = () => {
      reject('打开文件失败')
    }

    reader.onload = () => {
      let arr = []
      try {
        let wb = xlsx.read(reader.result, {
          type: 'array',
        })
        let ws = wb.Sheets[wb.SheetNames[0]]
        arr = xlsx.utils.sheet_to_json(ws)
      } catch (err) {
        reject(err)
      }

      if (!arr || arr.length === 0) {
        reject('所选excel文件无内容，请重新选择<br>若excel文件内有多张表，请将要导入的内容放在第一张表')
      }

      resolve(arr)
    }

    reader.readAsArrayBuffer(file)
  })
}

function checkData(arr, options) {
  function _addRule(rules, rule, type, column) {
    if (typeof rule === 'function') {
      rules.push({
        type,
        column,
        title: '',
        priority: 0,
        checkFunction: rule,
      })
    } else {
      rules.push({
        type,
        column,
        title: rule.title,
        priority: rule.priority || 0,
        checkFunction: rule.checkFunction,
      })
    }
  }

  let columns = (options && options.columns) || []
  columns.forEach(col => {
    if (!col.cellRules) {
      col.cellRules = []
    }
  })
  let rowRules = (options && options.rowRules) || []
  let tableRules = (options && options.tableRules) || []

  // 添加典型规则
  addTipicalRules(columns, rowRules, tableRules)

  // 整理规则
  let rules = []
  columns.forEach(col => {
    col.cellRules.forEach(rule => _addRule(rules, rule, 'cell', col))
  })
  rowRules.forEach(rule => _addRule(rules, rule, 'row'))
  tableRules.forEach(rule => _addRule(rules, rule, 'table'))

  // 排序，保持稳定
  for (let i = 0; i < rules.length - 1; i++) {
    for (let j = 0; j < rules.length - 1 - i; j++) {
      if (rules[j].priority > rules[j + 1].priority) {
        let t = rules[j]
        rules[j] = rules[j + 1]
        rules[j + 1] = t
      }
    }
  }

  // 应用规则
  rules.forEach(rule => {
    // 单元格检查
    if (rule.type === 'cell') {
      let rows = arr.map(row => rule.checkFunction(row, row[rule.column.title])).filter(Boolean)
      if (rows.length > 0) {
        throw {
          title: rule.title,
          message: buildErrorMessage(rows),
        }
      }
    }
    // 行检查
    else if (rule.type === 'row') {
      let rows = arr.map(rule.checkFunction).filter(Boolean)
      if (rows.length > 0) {
        throw {
          title: rule.title,
          message: buildErrorMessage(rows),
        }
      }
    }
    // 表检查
    else if (rule.type === 'table') {
      let result = rule.checkFunction(arr)
      if (result instanceof Array) {
        if (result.length > 0) {
          throw {
            title: rule.title,
            message: buildErrorMessage(result),
          }
        }
      } else if (result) {
        throw {
          title: rule.title,
          message: result,
        }
      }
    }
  })
}

function transFormData(arr, options) {
  return arr.map(row => {
    let newRow = {
      _rowNum_: row.__rowNum__ + 1,
    }
    options.columns.forEach(col => {
      newRow[col.key] = row[col.title]
    })
    return newRow
  })
}

// 添加行号，从1开始
function addRowNum(arr) {
  arr.forEach(row => {
    row._rowNum_ = row.__rowNum__ + 1
  })
}

// 添加典型规则
function addTipicalRules(columns, rowRules, tableRules) {
  let requiredColumns = columns.filter(col => col.required)
  if (requiredColumns.length > 0) {
    tableRules.push({
      title: '缺少列',
      priority: -100,
      checkFunction: arr => {
        let excelColumns = Object.keys(arr[0])
        let missingColumns = requiredColumns.filter(x => !excelColumns.includes(x.title))
        if (missingColumns.length > 0) {
          return `缺少列：${missingColumns.map(x => x.title).join('、')}`
        }
      },
    })

    requiredColumns.forEach(col => {
      col.cellRules.unshift({
        title: `${col.title}不能为空`,
        checkFunction: row => (row[col.title] == null ? `第${row._rowNum_}行` : ''),
      })
    })
  }

  columns.forEach(col => {
    if (col.range) {
      col.cellRules.push({
        title: `${col.title}不存在`,
        checkFunction: row => (col.range.includes(row[col.title]) ? '' : `第${row._rowNum_}行：${row[col.title]}`),
      })
    }

    if (col.unique) {
      tableRules.push({
        title: `${col.title}重复`,
        checkFunction: arr => {
          let duplicatedGroup = groupArray(arr, row => row[col.title]).filter(x => x.group.length > 1)
          if (duplicatedGroup.length > 0) {
            return buildErrorMessage(
              duplicatedGroup.map(
                g =>
                  `${g.key}：` +
                  g.group
                    .slice(0, 3)
                    .map(x => `第${x._rowNum_}行`)
                    .join('、') +
                  (g.group.length > 3 ? '...' : '')
              )
            )
          }
        },
      })
    }
  })
}

function buildErrorMessage(rows, displayRows = 5) {
  return (
    rows.slice(0, displayRows).join('<br>') + (rows.length > displayRows ? `<br>...（共${rows.length}条错误）` : '')
  )
}
